package com.gumtree.web.cookie;

import com.google.common.collect.Maps;
import com.gumtree.web.cookie.CookieSerializer;
import org.junit.Test;

import java.util.Map;

import static org.hamcrest.CoreMatchers.allOf;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.collection.IsMapContaining.hasEntry;
import static org.junit.Assert.assertThat;

public class CookieSerializerTest {

    @Test
    public void serializeString() {
        String serialized = CookieSerializer.serialize("dummystring");

        assertThat(serialized, is("ZHVtbXlzdHJpbmc="));
    }

    @Test
    public void deserializeString() {
        String deserialized = CookieSerializer.deserialize("ZHVtbXlzdHJpbmc=");

        assertThat(deserialized, is("dummystring"));
    }

    @Test
    public void serializeCookieMap() {
        Map<String, String> map = Maps.newTreeMap();
        map.put("k1", "v1");
        map.put("k2", "v2");
        map.put("k3", "v3");

        String serialized = CookieSerializer.serializeCookieMap(map);

        assertThat(serialized, is("k1:djE=|k2:djI=|k3:djM="));
    }

    @Test
    public void deserializeCookieMap() {
        Map<String, String> map = CookieSerializer.deserializeCookieMap("k1:djE=|k2:djI=|k3:djM=");

        assertThat(map.size(), is(3));
        assertThat(map, allOf(
                hasEntry("k1", "v1"),
                hasEntry("k2", "v2"),
                hasEntry("k3", "v3")));

    }
}
