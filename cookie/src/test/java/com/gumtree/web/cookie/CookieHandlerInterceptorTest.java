package com.gumtree.web.cookie;

import com.gumtree.common.properties.Env;
import com.gumtree.common.properties.GtEnvConfiguration;
import com.netflix.config.ConcurrentCompositeConfiguration;
import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.Properties;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class CookieHandlerInterceptorTest {

    private CookieHandlerInterceptor interceptor;

    @Mock
    private CookieCutter cookieCutter;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Before
    public void setUp() {
        ConcurrentCompositeConfiguration finalConfig = new ConcurrentCompositeConfiguration();
        finalConfig.addConfiguration(new GtEnvConfiguration("appName", Env.PROD, "envName", "instanceName"), "envConfig");
        ConfigurationManager.loadPropertiesFromConfiguration(finalConfig);
        Properties properties = new Properties();
        properties.setProperty(GumtreeCookieProperty.COOKIES_SECURE.getPropertyName(), "false");
        ConfigurationManager.loadProperties(properties);

        MockitoAnnotations.initMocks(this);

        interceptor = new CookieHandlerInterceptor();

        when(cookieCutter.getBaseName()).thenReturn("TEST_COOKIE");

        ReflectionTestUtils.setField(interceptor, "cookieCutters", new CookieCutter[]{cookieCutter});
    }

    @Test
    public void postHandleShouldWriteCookiesInRequest() {
        BaseCookie baseCookie = mock(BaseCookie.class);
        when(baseCookie.getValueAsString()).thenReturn("ABCDEF");
        when(baseCookie.getDomain()).thenReturn("gumtree.com");
        when(baseCookie.getMaxAge()).thenReturn(9999999);
        when(baseCookie.getPath()).thenReturn("/");

        when(request.getAttribute("TEST_COOKIE")).thenReturn(baseCookie);

        interceptor.postHandle(request, response, null, null);

        verify(request).getAttribute("TEST_COOKIE");

        ArgumentCaptor<Cookie> argument = ArgumentCaptor.forClass(Cookie.class);
        verify(response).addCookie(argument.capture());
        assertThat(argument.getValue().getDomain(), is("gumtree.com"));
        assertThat(argument.getValue().getMaxAge(), is(9999999));
        assertThat(argument.getValue().getPath(), is("/"));
        assertThat(argument.getValue().getValue(), is("ABCDEF"));
    }
}
