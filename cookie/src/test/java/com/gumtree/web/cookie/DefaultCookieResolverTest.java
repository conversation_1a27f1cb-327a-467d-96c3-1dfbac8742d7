package com.gumtree.web.cookie;

import com.gumtree.common.properties.Env;
import com.gumtree.common.properties.GtEnvConfiguration;
import com.netflix.config.ConcurrentCompositeConfiguration;
import com.netflix.config.ConfigurationManager;
import org.apache.commons.codec.binary.Base64;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.servlet.http.Cookie;

import static org.fest.assertions.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class DefaultCookieResolverTest {
    @InjectMocks private DefaultCookieResolver cookieResolver;
    private MockHttpServletRequest request = new MockHttpServletRequest();

    @Before
    public void beforeEach() {
        ConcurrentCompositeConfiguration finalConfig = new ConcurrentCompositeConfiguration();
        finalConfig.addConfiguration(new GtEnvConfiguration("appName", Env.PROD, "envName", "instanceName"), "envConfig");
        ConfigurationManager.loadPropertiesFromConfiguration(finalConfig);
        TestCookieCutter[] cookieCutters = { new TestCookieCutter("") };
        cookieResolver.setCookieCutters(cookieCutters);
    }

    @Test
    public void shouldResolveNonExistingCookie() {
        // when
        TestCookie resolvedCookie = cookieResolver.resolve(request, TestCookie.class);

        // then
        assertThat(resolvedCookie.getId()).isEqualTo(null);
        assertThat(resolvedCookie.getOther()).isEqualTo(null);

        // then
        assertThat(request.getAttribute(TestCookie.NAME)).isEqualTo(resolvedCookie);
    }

    @Test
    public void shouldResolveExistingCookie() {
        // given
        request.setCookies(new Cookie(TestCookie.NAME, "id:" + base64Encode(99) + "|other:" + base64Encode(66)));

        // when
        TestCookie resolvedCookie = cookieResolver.resolve(request, TestCookie.class);

        // then
        assertThat(resolvedCookie.getId()).isEqualTo("99");
        assertThat(resolvedCookie.getOther()).isEqualTo("66");

        // then
        assertThat(request.getAttribute(TestCookie.NAME)).isEqualTo(resolvedCookie);
    }

    public static class TestCookie extends MultiValueCookie {

        public static final String NAME = "test-cookie-name";
        private static final Boolean HTTP_ONLY = false;

        public TestCookie(String domain, int maxAge, String path) {
            super(domain, maxAge, path, HTTP_ONLY);
        }

        public TestCookie(String domain, int maxAge, String path, Cookie cookie) {
            super(domain, maxAge, path, HTTP_ONLY, cookie.getValue());
        }

        @Override
        public String getName() {
            return NAME;
        }

        public String getId() {
            return get("id");
        }

        public String getOther() {
            return get("other");
        }
    }

    public class TestCookieCutter extends CookieCutter<TestCookie> {
        public TestCookieCutter(String domain) {
            super(domain);
        }

        @Override
        protected String getBaseName() {
            return TestCookie.NAME;
        }

        @Override
        protected TestCookie cutExisting(Cookie existingCookie) {
            return new TestCookie("localhost", MAX_AGE_COOKIE, "/", existingCookie);
        }

        @Override
        protected TestCookie cutNew() {
            return new TestCookie("localhost", MAX_AGE_COOKIE, "/");
        }

        @Override
        protected Class<TestCookie> getSupportedCookieType() {
            return TestCookie.class;
        }
    }

    public static String base64Encode(Integer value) {
        return new String(Base64.encodeBase64(value.toString().getBytes()));
    }
}
