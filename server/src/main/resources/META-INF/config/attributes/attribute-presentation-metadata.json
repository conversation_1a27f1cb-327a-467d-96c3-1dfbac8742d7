{"metadata": [{"categoryName": "for-sale", "groups": [{"id": "for-sale-price-group", "label": "How much do you want for your item?", "attributes": [{"id": "price"}], "index": 0, "panelId": "price"}]}, {"categoryName": "treadmills", "groups": [{"id": "treadmills-brands", "label": "Select Brand", "attributes": [{"id": "treadmills_brand"}], "index": 1, "panelId": "brands"}]}, {"categoryName": "exercise-bikes", "groups": [{"id": "exercise-bikes-brands", "label": "Select Brand", "attributes": [{"id": "exercise_bikes_brand"}], "index": 1, "panelId": "brands"}]}, {"categoryName": "rowing-machines", "groups": [{"id": "rowing-machine-brands", "label": "Select Brand", "attributes": [{"id": "rowing_machine_brand"}], "index": 1, "panelId": "brands"}]}, {"categoryName": "iphone", "groups": [{"id": "for-sale-price-group", "label": "How much do you want for your item?", "attributes": [{"id": "price"}], "index": 0, "panelId": "price"}, {"id": "iphone-details-group", "label": "iPhone details", "panelId": "attribute-panel", "attributes": [{"id": "model"}, {"id": "condition"}, {"id": "flash_storage"}, {"id": "colour"}, {"id": "network"}]}]}, {"categoryName": "mobile-phones", "groups": [{"id": "mobile-phones-details-group", "label": "Your phone information", "panelId": "mobile-phones-attributes-panel", "index": 1, "attributes": [{"id": "mobile_model_apple"}, {"id": "mobile_model_samsung"}, {"id": "mobile_model_google"}, {"id": "mobile_model_xiaomi"}, {"id": "mobile_model_huawei"}, {"id": "mobile_condition"}, {"id": "mobile_storage_capacity"}, {"id": "mobile_colour"}]}]}, {"categoryName": "diy-tools-materials", "groups": [{"id": "diy-tools-materials-details-group", "label": "diy tools materials information", "panelId": "diy-tools-materials-attributes-panel", "index": 1, "attributes": [{"id": "diy_tools_materials_condition"}]}]}, {"categoryName": "home-garden", "groups": [{"id": "home-garden-details-group", "label": "Your sale stuff information", "panelId": "home-garden-attributes-panel", "index": 1, "attributes": [{"id": "common_for_sale_condition"}, {"id": "common_for_sale_colour"}, {"id": "common_for_sale_material"}]}]}, {"categoryName": "tickets", "groups": [{"id": "for-sale-ticket-date-group", "label": "When is the event?", "attributes": [{"id": "ticket_date"}], "index": 0, "panelId": "attribute-panel"}, {"id": "for-sale-ticket-face-value-group", "label": "What is the face value?", "attributes": [{"id": "ticket_face_value"}], "index": 1, "panelId": "attribute-panel"}]}, {"categoryName": "travel-tickets", "groups": [{"id": "for-sale-ticket-date-group", "label": "When are you travelling?", "attributes": [{"id": "travel_date"}], "index": 1, "panelId": "attribute-panel"}]}, {"categoryName": "events-gigs-nightlife", "groups": [{"id": "events-date-group", "label": "When is the event?", "attributes": [{"id": "event_date"}], "index": 0, "panelId": "attribute-panel"}]}, {"categoryName": "rideshare-car-pooling", "groups": [{"id": "ride-share-travel-date-group", "label": "When are you travelling?", "attributes": [{"id": "travel_date"}], "index": 0, "panelId": "attribute-panel"}]}, {"categoryName": "cars-vans-motorbikes", "groups": [{"id": "car-vans-motorbikes-price-group", "label": "How much do you want for your vehicle?", "attributes": [{"id": "price"}, {"id": "vat"}], "index": 0, "panelId": "price"}, {"id": "cars-vans-motorbikes-details-group", "label": "Vehicle specification", "highPriority": true, "attributes": [{"id": "vehicle_make"}, {"id": "motorbike_make"}, {"id": "vehicle_registration_year"}, {"id": "vehicle_model"}, {"id": "vehicle_fuel_type"}, {"id": "vehicle_body_type"}, {"id": "vehicle_transmission"}, {"id": "vehicle_colour"}, {"id": "vehicle_engine_size"}, {"id": "vehicle_mileage"}, {"id": "vehicle_doors"}], "index": 1, "panelId": "vehicle-specifications"}]}, {"categoryName": "cars", "groups": [{"id": "cars-additional-features", "label": "Vehicle standard features", "attributes": [{"id": "additional_features_available"}, {"id": "alloy_wheels"}, {"id": "radio_with_speakers"}, {"id": "power_windows"}, {"id": "air_conditioning"}, {"id": "leather_seats"}, {"id": "navigation_system"}, {"id": "dual_front_airbags_package"}, {"id": "side_airbags"}, {"id": "passenger_airbags"}, {"id": "curtain_airbags"}, {"id": "airbag_knee_driver"}, {"id": "anti_lock_braking"}, {"id": "automatic_air_climate_control"}, {"id": "tinted_glass"}, {"id": "cruise_control"}, {"id": "central_locking_remote_control"}, {"id": "engine_immobiliser"}, {"id": "safety_belt_pretensioners"}, {"id": "alarm_system_remote_anti_theft"}, {"id": "electronic_stability_program"}, {"id": "trip_computer"}, {"id": "power_mirrors"}, {"id": "power_assisted_steering"}, {"id": "power_front_seats"}, {"id": "adjustable_steering_wheel"}, {"id": "aux_usb_input_socket"}, {"id": "split_folding_rear_seat"}, {"id": "multi_function_steering_wheel"}, {"id": "bluetooth_connectivity"}, {"id": "heated_seats"}, {"id": "heated_door_mirrors"}, {"id": "parking_sensors"}, {"id": "leather_steering_wheel"}, {"id": "daytime_running_lights"}, {"id": "android_auto"}, {"id": "apple_car_play"}, {"id": "traction_control"}, {"id": "child_proof_rear_door_locks"}, {"id": "automatic_headlights_with_dusk_sensor"}, {"id": "led_headlights"}, {"id": "sunroof"}, {"id": "automatic_stop_start"}], "index": 0, "panelId": "additional-features"}]}, {"categoryName": "motors-parts", "groups": [{"id": "car-vans-motorbikes-price-group", "label": "How much do you want for your item?", "attributes": [{"id": "price"}], "index": 1, "panelId": "price"}]}, {"categoryName": "motors-accessories", "groups": [{"id": "car-vans-motorbikes-price-group", "label": "How much do you want for your item?", "attributes": [{"id": "price"}], "index": 0, "panelId": "price"}]}, {"categoryName": "pets", "groups": [{"id": "pets-price-group", "label": "How much do you want for your item?", "attributes": [{"id": "price"}], "index": 0, "panelId": "price"}]}, {"categoryName": "dogs", "groups": [{"id": "dogs-breed-group", "label": "Select Dog Breed:", "panelId": "dogs-breed-panel", "attributes": [{"id": "dog_breed"}]}, {"id": "pet-health-group", "label": "Pet health and documentation", "panelId": "pet-health-panel", "attributes": [{"id": "vaccinated"}, {"id": "neutered_or_spayed"}, {"id": "deflead"}, {"id": "microchipped"}]}]}, {"categoryName": "cats", "groups": [{"id": "cats-breed-group", "label": "Select Cat Breed:", "panelId": "cats-breed-panel", "attributes": [{"id": "cat_breed"}]}, {"id": "pet-health-group", "label": "Pet health and documentation", "panelId": "pet-health-panel", "attributes": [{"id": "vaccinated"}, {"id": "neutered_or_spayed"}, {"id": "deflead"}, {"id": "microchipped"}]}]}, {"categoryName": "petsitters-dogwalkers", "groups": [{"id": "pets-price-group", "label": "How much do you want for your service?", "attributes": [{"id": "price"}], "index": 0, "panelId": "price"}]}, {"categoryName": "pets-for-sale", "groups": [{"id": "pets-group", "label": "Pet age and rehome date:", "attributes": [{"id": "pet_date_of_birth"}, {"id": "pet_rehome_ready_date"}], "index": 0, "panelId": "pets-birthday"}, {"id": "pets-price-group", "label": "How much do you want for your pet?", "panelId": "price"}]}, {"categoryName": "jobs", "groups": [{"id": "job-contract-type-group", "label": "Additional details", "attributes": [{"id": "job_contract_type"}, {"id": "recruiter_type"}, {"id": "salary_min"}, {"id": "salary_max"}, {"id": "salary_period"}], "panelId": "attribute-panel"}]}, {"categoryName": "flats-houses", "groups": [{"id": "flats-houses-details-group", "label": "Property details", "panelId": "attribute-panel", "attributes": [{"id": "price"}, {"id": "price_frequency"}, {"id": "available_date"}, {"id": "property_type"}, {"id": "property_number_beds"}, {"id": "property_room_type"}, {"id": "property_couples"}]}]}, {"categoryName": "property-wanted", "groups": [{"id": "property-wanted-date-group", "panelId": "attribute-panel", "label": "When do you want this by?", "attributes": [{"id": "wanted_date"}]}]}, {"categoryName": "lawn-mowers", "groups": [{"id": "lawn-mowers-group", "panelId": "attribute-panel", "label": "What type of lawnmower?", "attributes": [{"id": "lawn_mower_type"}]}]}]}