openapi: 3.0.1
info:
  title: Advertisement counters
  description: Counter aggregations for advertisements based on odin events
  version: 1.0.0
  contact:
    name: CData team
    email: <EMAIL>

servers:
  - url: https://api.cd-odin-dev.ams1.cloud/mim-advertisement-counters/v1
    description: Development environment
  - url: https://api.cd-odin-pre-prod.ams1.cloud/mim-advertisement-counters/v1
    description: Pre production environment
  - url: https://api.cd-mim-prod.ams1.cloud/mim-advertisement-counters/v1
    description: Production environment Amsterdam datacenter
  - url: https://api.cd-mim-prod.dus1.cloud/mim-advertisement-counters/v1
    description: Pre production environment Dusseldorf datacenter

tags:
  - name: ad-counter
    description: advertisements counter endpoints

paths:
  /counters:
    get:
      tags:
        - ad-counter
      summary: load counters by name
      parameters:
        - name: X-Request-ID
          in: header
          description: Unique request id
          required: false
          schema:
            type: string

        - name: X-Experiments
          in: header
          description: Active experiments
          required: false
          schema:
            type: string

        - name: include
          in: query
          description: Comma separated list of names of counters to retrieve
          required: true
          schema:
            type: string

        - name: ecgProperty
          in: query
          description: Ecg property to fetch data for
          required: false
          schema:
            type: string

      responses:
        '200':
          description: Counters
          headers:
            X-Request-ID:
              schema:
                type: string
              description: the request id
          content:
            'application/json':
              schema:
                type: object
                additionalProperties:
                  type: integer
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'

      security:
        - BasicAuth: []

  /counters/_search:
    post:
      tags:
        - ad-counter
      summary: load counters by name
      parameters:
        - name: X-Request-ID
          in: header
          description: Unique request id
          required: false
          schema:
            type: string

        - name: X-Experiments
          in: header
          description: Active experiments
          required: false
          schema:
            type: string

      requestBody:
        description: The search request
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchRequest'

      responses:
        '200':
          description: Counters
          headers:
            X-Request-ID:
              schema:
                type: string
              description: the request id
          content:
            'application/json':
              schema:
                type: object
                additionalProperties:
                  type: integer
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'

      security:
        - BasicAuth: []

components:

  schemas:
    SearchRequest:
      type: object
      required:
        - include
      properties:
        include:
          type: array
          description: name of counters to retrieve
          items:
            type: string

        ecgProperty:
          type: string
          description: Ecg property to fetch data for

  securitySchemes:
    BasicAuth:
      type: http
      scheme: basic

  responses:
    UnauthorizedError:
      description: Authentication information is missing or invalid

    BadRequestError:
      description: Bad request