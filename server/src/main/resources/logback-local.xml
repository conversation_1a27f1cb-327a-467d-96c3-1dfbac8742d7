<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</Pattern>
        </layout>
    </appender>

    <!--    <logger name="org.springframework" level="debug"/> -->

    <!-- logging level for SSO SF -->
    <logger name="com.gumtree.gas" level="INFO"/>

    <!-- logging level for SSO SF -->
    <logger name="org.springframework.web.servlet.view" level="TRACE"/>

    <logger name="feign.Logger" level="DEBUG" />

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
