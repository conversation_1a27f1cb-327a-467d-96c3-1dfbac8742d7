package {{package}};

{{#legacyDates}}
    import {{invokerPackage}}.ParamExpander;
{{/legacyDates}}

{{#imports}}import {{import}};
{{/imports}}

import com.gumtree.feign.OptionalExpander;

{{^fullJavaUtil}}
    import java.util.ArrayList;
    import java.util.HashMap;
    import java.util.List;
    import java.util.Map;
    import java.util.Optional;
{{/fullJavaUtil}}
import feign.*;
import rx.Single;

{{>generatedAnnotation}}
public interface {{classname}} extends com.gumtree.api.CommonApiClient.Api {

{{#operations}}{{#operation}}

    /**
    * {{summary}}
    * {{notes}}
    {{#allParams}}
        {{^isHeaderParam}}
            * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{#defaultValue}}, default to {{{.}}}{{/defaultValue}}){{/required}}
        {{/isHeaderParam}}
    {{/allParams}}
    {{#returnType}}
        * @return {{returnType}}
    {{/returnType}}
    {{#externalDocs}}
        * {{description}}
        * @see <a href="{{url}}">{{summary}} Documentation</a>
    {{/externalDocs}}
    */
    @RequestLine("{{httpMethod}} {{{path}}}{{#hasQueryParams}}?{{/hasQueryParams}}{{#queryParams}}{{baseName}}={{=<% %>=}}{<%paramName%>}<%={{ }}=%>{{#hasMore}}&{{/hasMore}}{{/queryParams}}")
    @Headers({
    "Content-Type: {{vendorExtensions.x-contentType}}",
    "Accept: {{vendorExtensions.x-accepts}}"
    })
    {{#returnType}}Single<{{{returnType}}}> {{/returnType}}{{^returnType}}void {{/returnType}}{{nickname}}({{#bodyParams}}{{{dataType}}} {{paramName}}{{#hasMore}}, {{/hasMore}}{{/bodyParams}}{{#hasPathParams}}{{^bodyParams.isEmpty}}, {{/bodyParams.isEmpty}}{{#pathParams}}@Param("{{paramName}}") {{{dataType}}} {{paramName}}{{#hasMore}}, {{/hasMore}}{{/pathParams}}{{/hasPathParams}}{{#hasQueryParams}}{{^bodyParams.isEmpty}}, {{/bodyParams.isEmpty}}{{#bodyParams.isEmpty}}{{#hasPathParams}}, {{/hasPathParams}}{{/bodyParams.isEmpty}}{{#queryParams}}@Param(value="{{paramName}}"{{^required}}, expander=OptionalExpander.class{{/required}}) {{#required}}{{{dataType}}}{{/required}}{{^required}}Optional<{{{dataType}}}>{{/required}}  {{paramName}}{{#hasMore}}, {{/hasMore}}{{/queryParams}}{{/hasQueryParams}});

{{/operation}}
{{/operations}}
}
