package {{invokerPackage}};

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import com.gumtree.api.locations.infrastructure.auth.HttpBasicAuth;

{{#threetenbp}}
import org.threeten.bp.*;
{{/threetenbp}}

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.MapperFeature;
{{#joda}}
import com.fasterxml.jackson.datatype.joda.JodaModule;
{{/joda}}
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolProperties;
{{#java8}}
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
{{/java8}}
{{#threetenbp}}
import com.fasterxml.jackson.datatype.threetenbp.ThreeTenModule;
{{/threetenbp}}

import feign.Feign;
import feign.RequestInterceptor;
import feign.Param;
import feign.Request;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Retryer;
import feign.codec.ErrorDecoder;
import feign.hystrix.FallbackFactory;
import feign.hystrix.HystrixFeign;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.okhttp.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.gumtree.api.CommonApiClient.Api;

{{>generatedAnnotation}}
public class ApiClient {

  protected ObjectMapper objectMapper;
  private String basePath = "{{{basePath}}}";
  private Feign.Builder feignBuilder;

  public ApiClient(String basePath, String clientId, int connectionTimeOut, int readTimeOut, String commandGroupName, int poolSize) {
    this.basePath = basePath;
    objectMapper = createObjectMapper();
    feignBuilder = HystrixFeign.builder()
            .client(new OkHttpClient())
            .decoder(new JacksonDecoder(objectMapper))
            .encoder(new JacksonEncoder(objectMapper))
            .options(new Request.Options(connectionTimeOut, readTimeOut))
            .retryer(new Retryer.Default(0L, 0L, 1))
            .requestInterceptor(new ClientIdRequestInterceptor(clientId))
            .setterFactory((t, method) -> createCommandGroupSetter(commandGroupName, method.getName(), poolSize));
  }

  public ApiClient(Feign.Builder feignBuilder) {
    this.basePath = basePath;
    objectMapper = createObjectMapper();
    feignBuilder = feignBuilder;
  }

  public String getBasePath() {
    return basePath;
  }

  private ObjectMapper createObjectMapper() {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
    objectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
    objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    objectMapper.disable(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE);
    objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
    // swagger-coedgen is adding JsonProperty annotation using camel case names but we want to use snake case names so annotations need
    // to be disabled otherwise it will not work
    objectMapper.disable(MapperFeature.USE_ANNOTATIONS);
    // objectMapper.setDateFormat(new RFC3339DateFormat());
    {{#joda}}
    objectMapper.registerModule(new JodaModule());
    {{/joda}}
    {{#java8}}
    objectMapper.registerModule(new JavaTimeModule());
    {{/java8}}
    {{#threetenbp}}
    ThreeTenModule module = new ThreeTenModule();
    module.addDeserializer(Instant.class, CustomInstantDeserializer.INSTANT);
    module.addDeserializer(OffsetDateTime.class, CustomInstantDeserializer.OFFSET_DATE_TIME);
    module.addDeserializer(ZonedDateTime.class, CustomInstantDeserializer.ZONED_DATE_TIME);
    objectMapper.registerModule(module);
    {{/threetenbp}}
    return objectMapper;
  }

  public ObjectMapper getObjectMapper(){
    return objectMapper;
  }

  private HystrixCommand.Setter createCommandGroupSetter(String commandGroupname, String commandName, int poolSize) {
      return HystrixCommand.Setter
              .withGroupKey(HystrixCommandGroupKey.Factory.asKey(commandGroupname))
              .andCommandKey(HystrixCommandKey.Factory.asKey(commandName))
              .andCommandPropertiesDefaults(HystrixCommandProperties.Setter()
                      .withExecutionTimeoutEnabled(false)
                      .withCircuitBreakerEnabled(true))
              .andThreadPoolPropertiesDefaults(HystrixThreadPoolProperties.Setter()
                      .withCoreSize(poolSize)
                      .withMaxQueueSize(poolSize * 10)
                      .withQueueSizeRejectionThreshold(poolSize * 10));
    }

  private static class ClientIdRequestInterceptor implements RequestInterceptor {
      private final String clientId;

      public ClientIdRequestInterceptor(String clientId) {
          this.clientId = clientId;
      }

      @Override
      public void apply(RequestTemplate template) {
          template.header("Client-Id", clientId);
      }
  }

  /**
   * Creates a feign client for given API interface.
   *
   * Usage:
   *    ApiClient apiClient = new ApiClient();
   *    apiClient.setBasePath("http://localhost:8080");
   *    XYZApi api = apiClient.buildClient(XYZApi.class);
   *    XYZResponse response = api.someMethod(...);
   * @param <T> Type
   * @param clientClass Client class
   * @return The Client
   */
  public <T extends Api> T buildClient(Class<T> clientClass) {
    return feignBuilder.target(clientClass, basePath);
  }

    public <T extends Api> T buildClient(Class<T> clientClass,
          Optional<String> username,
          Optional<String> password) {
          if (username.isPresent()) {
              HttpBasicAuth httpBasicAuth = new HttpBasicAuth();
              httpBasicAuth.setCredentials(username.get(), password.orElse(""));
              return feignBuilder
              .requestInterceptor(httpBasicAuth)
              .target(clientClass, basePath);
          } else {
              return feignBuilder
              .target(clientClass, basePath);
          }
      }

  public <T extends Api> T buildClient(Class<T> clientClass, ErrorDecoder errorDecoder) {
    return feignBuilder.errorDecoder(errorDecoder).target(clientClass, basePath);
  }
}
