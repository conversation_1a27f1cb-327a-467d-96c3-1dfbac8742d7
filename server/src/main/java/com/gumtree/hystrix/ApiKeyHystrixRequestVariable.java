package com.gumtree.hystrix;

import com.netflix.hystrix.strategy.concurrency.HystrixRequestVariableDefault;

/**
 * https://github.com/Netflix/Hystrix/issues/92#issuecomment-260548068
 */
public final class ApiKeyHystrixRequestVariable {

    private static final HystrixRequestVariableDefault<String> API_KEY_HOLDER = new HystrixRequestVariableDefault<>();

    private ApiKeyHystrixRequestVariable() {
    }

    public static String get() {
        return API_KEY_HOLDER.get();
    }

    public static void set(String apiKey) {
        API_KEY_HOLDER.set(apiKey);
    }
}
