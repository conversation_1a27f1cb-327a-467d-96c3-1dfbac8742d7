package com.gumtree;

import com.gumtree.shared.jetty.JettyServer;
import com.gumtree.zeno.core.SLF4JEventHandler;
import io.micrometer.core.instrument.Clock;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.binder.jetty.JettyConnectionMetrics;
import io.micrometer.core.instrument.binder.jetty.JettyServerThreadPoolMetrics;
import io.micrometer.prometheus.PrometheusConfig;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import io.prometheus.client.CollectorRegistry;
import org.eclipse.jetty.server.Server;

import java.util.Collections;
import java.util.List;

public class GumtreeJettyMain extends JettyServer {

    public static final MeterRegistry METER_REGISTRY = new PrometheusMeterRegistry(
            PrometheusConfig.DEFAULT,
            CollectorRegistry.defaultRegistry,
            Clock.SYSTEM);

    public static void main(String[] args) throws Exception {
        SLF4JEventHandler.configureLoggerFor("/logback-zeno.xml");
        SLF4JEventHandler.configureLoggerFor("/logback-seller.xml");

        GumtreeJettyMain jetty = new GumtreeJettyMain();
        if (jetty.isDev()) {
            if (System.getProperty("gumtree.http.port") == null) {
                System.setProperty("gumtree.http.port", "8181");
            }
        }
        jetty.startJetty();
    }

    @Override
    protected Server amendServer(Server server) {
        List<Tag> tags = Collections.emptyList();
        JettyConnectionMetrics.addToAllConnectors(server, METER_REGISTRY, tags);
        new JettyServerThreadPoolMetrics(server.getThreadPool(), tags).bindTo(METER_REGISTRY);
        return super.amendServer(server);
    }
}
