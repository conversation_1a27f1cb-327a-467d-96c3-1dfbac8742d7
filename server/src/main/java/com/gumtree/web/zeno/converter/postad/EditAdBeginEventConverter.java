package com.gumtree.web.zeno.converter.postad;

import com.gumtree.web.zeno.HierarchyUtil;
import com.gumtree.web.zeno.event.postad.EditAdBeginEvent;
import com.gumtree.zeno.core.domain.AdvertData;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.HierarchicalData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EditAdBeginEventConverter extends AbstractPostAdEventConverter<EditAdBeginEvent> {

    @Autowired
    public EditAdBeginEventConverter(ZenoConverterService zenoConverterService,
                                     RequestDetailsService requestDetailsService, HierarchyUtil hierarchyUtil) {
        super(EditAdBeginEvent.class, zenoConverterService, requestDetailsService, hierarchyUtil);
    }

    @Override
    protected EditAdBeginEvent createEvent(UserData userData, DeviceData deviceData, HierarchicalData categoryData,
                                           HierarchicalData locationData, AdvertData advertData) {
        return new EditAdBeginEvent(requestDetailsService.getPageData(PageType.EditAd),
                userData,
                deviceData,
                categoryData,
                locationData,
                advertData);
    }
}
