package com.gumtree.web.zeno.userregistration;

import com.google.common.base.Objects;
import com.gumtree.api.User;
import com.gumtree.zeno.core.event.ZenoEvent;

public class UserActivationBeginZenoEvent implements ZenoEvent {

    private User user;

    public UserActivationBeginZenoEvent(User user) {
        this.user = user;
    }

    public User getUser() {
        return user;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserActivationBeginZenoEvent that = (UserActivationBeginZenoEvent) o;
        return Objects.equal(user, that.user);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(user);
    }
}