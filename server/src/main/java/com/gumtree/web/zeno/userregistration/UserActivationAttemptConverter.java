package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.converter.AbstractEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserActivationAttempt;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserActivationAttemptConverter extends AbstractEventConverter<String[], String, UserActivationAttempt> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public UserActivationAttemptConverter(ZenoConverterService zenoConverterService,
                                          RequestDetailsService requestDetailsService) {
        super(UserActivationAttempt.class, zenoConverterService);
        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public UserActivationAttempt convertToEvent(String[] input, String output) {
        PageData pageData = requestDetailsService.getPageData(PageType.Unknown);
        UserData userData = requestDetailsService.getUserData();
        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new UserActivationAttempt(pageData, userData, deviceData);
    }

}
