package com.gumtree.web.zeno;

import com.gumtree.api.Ad;
import com.gumtree.zeno.core.converter.AbstractEventConverter;
import com.gumtree.zeno.core.domain.AdvertData;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.HierarchicalData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.postad.AbstractPostAdEvent;
import com.gumtree.zeno.core.event.user.sellerside.postad.EditAdEvent;
import com.gumtree.zeno.core.event.user.sellerside.postad.PostAdEvent;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 */
@Component
public class PostAdEventConverter extends AbstractEventConverter<Boolean, Ad, AbstractPostAdEvent> {
    private RequestDetailsService requestDetailsService;
    private ToAdvertDataConverter adConverter;
    private HierarchyUtil hierarchyUtil;

    @Autowired
    public PostAdEventConverter(ZenoConverterService zenoConverterService, RequestDetailsService requestDetailsService,
                                ToAdvertDataConverter adConverter, HierarchyUtil hierarchyUtil) {
        super(AbstractPostAdEvent.class, zenoConverterService);
        this.requestDetailsService = requestDetailsService;
        this.adConverter = adConverter;
        this.hierarchyUtil = hierarchyUtil;
    }

    @Override
    public AbstractPostAdEvent convertToEvent(Boolean isCreate, Ad ad) {
        UserData userData = requestDetailsService.getUserData();
        DeviceData deviceData = requestDetailsService.getDeviceData();
        HierarchicalData categoryData = hierarchyUtil.forCategory(ad.getCategoryId());
        HierarchicalData locationData = hierarchyUtil.forLocation(ad.getLocationId(), ad.getPostcode(), null, null, null);
        AdvertData advertData = adConverter.adToAdvertData(ad);

        if (isCreate) {
            return new PostAdEvent(requestDetailsService.getPageData(PageType.PostAdSuccess),
                    userData,
                    deviceData,
                    categoryData,
                    locationData,
                    advertData);
        } else {
            return new EditAdEvent(requestDetailsService.getPageData(PageType.EditAd),
                    userData,
                    deviceData,
                    categoryData,
                    locationData,
                    advertData);
        }
    }
}
