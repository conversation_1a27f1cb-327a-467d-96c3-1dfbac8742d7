package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.converter.AbstractZenoEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.Event;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationBegin;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserRegistrationBeginEventConverter extends AbstractZenoEventConverter<UserRegistrationBeginZenoEvent, Event> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public UserRegistrationBeginEventConverter(ZenoConverterService zenoConverterService,
                                               RequestDetailsService requestDetailsService) {
        super(UserRegistrationBeginZenoEvent.class, zenoConverterService);

        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public Event convert(UserRegistrationBeginZenoEvent userRegistrationBeginZenoEvent) {

        PageData pageData = requestDetailsService.getPageData(PageType.UserRegistrationForm);
        UserData userData = requestDetailsService.getUserData();
        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new UserRegistrationBegin(pageData, userData, deviceData);
    }
}
