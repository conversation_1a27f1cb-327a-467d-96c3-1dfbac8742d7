package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.converter.AbstractZenoEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.Event;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserActivationSuccess;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserActivationSuccessEventConverter extends AbstractZenoEventConverter<UserActivationSuccessZenoEvent, Event> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public UserActivationSuccessEventConverter(ZenoConverterService zenoConverterService,
                                               RequestDetailsService requestDetailsService) {
        super(UserActivationSuccessZenoEvent.class, zenoConverterService);
        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public Event convert(UserActivationSuccessZenoEvent userActivationSuccessZenoEvent) {

        PageData pageData = requestDetailsService.getPageData(PageType.UserActivationSuccess);
        // as the user isn't logged in, requestDetailsService.getUserData() is empty
        UserData userData = UserData.aUser()
                .withLoggedIn(false)
                .withLoginFailure(false)
                .withUserId(userActivationSuccessZenoEvent.getUser().getId())
                .withUserEmail(userActivationSuccessZenoEvent.getUser().getEmail())
                .withAccountType(UserData.AccountType.Standard)
                .build();

        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new UserActivationSuccess(pageData, userData, deviceData);
    }
}
