package com.gumtree.web.zeno.userregistration;

import com.gumtree.zeno.core.converter.AbstractEventConverter;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationCancel;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserRegistrationCancelConverter extends AbstractEventConverter<String[], String, UserRegistrationCancel> {

    private RequestDetailsService requestDetailsService;

    @Autowired
    public UserRegistrationCancelConverter(ZenoConverterService zenoConverterService,
                                           RequestDetailsService requestDetailsService) {
        super(UserRegistrationCancel.class, zenoConverterService);
        this.requestDetailsService = requestDetailsService;
    }

    @Override
    public UserRegistrationCancel convertToEvent(String[] input, String output) {
        PageData pageData = requestDetailsService.getPageData(PageType.UserRegistrationForm);
        UserData userData = requestDetailsService.getUserData();
        DeviceData deviceData = requestDetailsService.getDeviceData();

        return new UserRegistrationCancel(pageData, userData, deviceData);
    }

}
