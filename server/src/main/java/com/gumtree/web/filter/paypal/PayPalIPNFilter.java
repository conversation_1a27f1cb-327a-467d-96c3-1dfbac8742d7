package com.gumtree.web.filter.paypal;

import com.gumtree.web.seller.page.payment.controller.PayPalIPNCallbackController;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * This servlet filter ensures that the PayPal IPN callback controller can get the unaltered POST request body.
 * The unadulterated request body is required to verify that the IPN callback is valid. However, if we allow
 * Spring to process the body we will no longer get the parameters in the correct order.
 *
 * Note that this filter reads the request input stream, which means that anything that comes afterwards can no longer
 * read the request body.
 */
@Component
public class PayPalIPNFilter implements Filter {

    private static final Logger LOGGER = LoggerFactory.getLogger(PayPalIPNFilter.class);

    @Autowired
    private PayPalIPNRequest payPalIPNRequest;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        WebApplicationContext springContext =
                WebApplicationContextUtils.getWebApplicationContext(filterConfig.getServletContext());
        payPalIPNRequest = (PayPalIPNRequest) springContext.getBean("payPalIPNRequest");
    }

    @Override
    public final void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        String requestURI = ((HttpServletRequest) request).getRequestURI();
        if (requestURI.endsWith("/")) {
            requestURI = requestURI.substring(0, requestURI.length() - 1);
        }

        // We don't strictly speaking need to check the URL, because the url-mapping in web.xml should only pass us IPN
        // calls. But it seems safer to to check here in case the filter is ever misconfigured.
        if (PayPalIPNCallbackController.PAGE_PATH.equals(requestURI)) {

            try {
                // This is an IPN callback so grab the request body and stash it away for the controller. This is
                // destructive - further Spring code that examines the body will see an empty string.
                String body = IOUtils.toString(request.getInputStream());
                payPalIPNRequest.setBody(body);
                payPalIPNRequest.setContentType(request.getContentType());
            } catch (Exception e) {
                // seeing errors in access logs, but nothing in app logs
                LOGGER.error("Failed to process Paypal IPN callback", e);
                throw e;
            }
        }

        chain.doFilter(request, response);

    }

    @Override
    public void destroy() {
    }
}
