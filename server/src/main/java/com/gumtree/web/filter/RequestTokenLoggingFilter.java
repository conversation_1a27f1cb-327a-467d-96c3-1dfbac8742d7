package com.gumtree.web.filter;

import com.gumtree.common.logging.service.requesttoken.RequestTokenService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;
import java.util.UUID;

/**
 * Request token logging filter.
 */
public class RequestTokenLoggingFilter implements Filter {
    @Autowired
    private RequestTokenService requestTokenService;

    /**
     * Constructor.
     * @param requestTokenService the service to use to initialise request tokens
     */
    public RequestTokenLoggingFilter(RequestTokenService requestTokenService) {
        this.requestTokenService = requestTokenService;
    }

    @Override
    public final void destroy() {

    }

    @Override
    public final void doFilter(ServletRequest servletRequest, ServletResponse servletResponse,
                               Fi<PERSON><PERSON>hain filterChain) throws IOException, ServletException {
        requestTokenService.initialiseRequestToken(UUID.randomUUID().toString());

        try {
            filterChain.doFilter(servletRequest, servletResponse);
        } finally {
            // Remove from ThreadLocal
            requestTokenService.resetRequestToken();
        }
    }

    @Override
    public final void init(FilterConfig filterConfig) throws ServletException {

    }
}
