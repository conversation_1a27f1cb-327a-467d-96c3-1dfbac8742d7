package com.gumtree.web.filter;

import org.apache.commons.io.IOUtils;
import org.eclipse.jetty.io.EofException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;

/**
 * Spring exception handling is not handling exception thrown from 'Views' and we would like to have special handling for some of them.
 * - Using web.xml error-page config was not sufficient as we could not control how those exception are being logged.
 * - Using jetty bad request servlet was not sufficient as we could not control how those exception are being logged
 */
public class GlobalErrorHandlerFilter implements Filter {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalErrorHandlerFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            chain.doFilter(request, response);
        } catch (Exception ex) {
            handleException(ex, (HttpServletRequest) request, (HttpServletResponse) response);
        }
    }

    private void handleException(Exception ex, HttpServletRequest request, HttpServletResponse response) {
        if (isClientAbortedException(ex)) {
            String requestURI = request.getRequestURI();
            LOGGER.info("Client aborted connection. URI: " + requestURI, ex.getMessage());
            LOGGER.error(ex.getMessage(), ex);
            response.setStatus(418); // will be tracked as other request changed to 418 by SO
            // no point setting any body as connection is closed
        } else {
            handleUnexpectedException(ex, request, response);
        }
    }

    private boolean isClientAbortedException(Exception ex) {
        Throwable cause = ex;

        while(cause != null) {
            if (cause.getClass().equals(EofException.class)) {
                return true;
            } else if (cause.getClass().equals(IllegalStateException.class) && "STREAM".equals(cause.getMessage())) {
                // NestedServletException: Request processing failed; nested exception is java.lang.IllegalStateException: STREAM
                return true;
            }

            cause = cause.getCause();
        }

        return false;
    }

    private void handleUnexpectedException(Exception ex, HttpServletRequest request, HttpServletResponse response) {
        LOGGER.error("Unexpected error while processing request", ex);
        response.setStatus(500);
        try (InputStream errorPage = request.getServletContext().getResourceAsStream("/WEB-INF/error.html")){
            response.setContentType("text/html");
            response.setCharacterEncoding("utf-8");

            PrintWriter out = response.getWriter();
            out.write(IOUtils.toString(errorPage));
            out.close();
            //CHECKSTYLE:OFF
        } catch (Throwable t) {
            // Ignore, debug level to avoid spamming logs
        }
        //CHECKSTYLE:ON
    }

    @Override
    public void destroy() {
    }
}
