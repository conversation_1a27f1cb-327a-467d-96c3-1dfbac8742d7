package com.gumtree.web.seller.page.password.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.domain.user.beans.UpdatePasswordBean;
import com.gumtree.api.client.executor.ApiCall;

/**
 * Api call to rest a users password
 */
public class UpdatePasswordApiCall implements ApiCall<Void> {

    private String userName;
    private UpdatePasswordBean updatePasswordBean;

    /**
     * Default constructor, validation then updated password
     * @param userName needed for validation and account lookup
     * @param updatePasswordBean updated password for the user
     */
    public UpdatePasswordApiCall(String userName, UpdatePasswordBean updatePasswordBean) {
        this.userName = userName;
        this.updatePasswordBean = updatePasswordBean;
    }

    @Override
    public final Void execute(BushfireApi api) {
        api.create(UserApi.class).updatePassword(userName, updatePasswordBean);
        return null;
    }
}
