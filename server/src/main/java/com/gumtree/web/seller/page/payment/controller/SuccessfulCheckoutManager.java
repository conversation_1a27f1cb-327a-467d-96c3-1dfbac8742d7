package com.gumtree.web.seller.page.payment.controller;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.seller.domain.order.status.OrderStatus;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.service.CheckoutContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Handles successful checkouts by informing bushfire api of all necessary steps to be done.
 *
 * <AUTHOR>
 */
@Component
class SuccessfulCheckoutManager {

    private final BushfireApi bushfireApi;
    private final UserSession authenticatedUserSession;
    private final CheckoutContainer checkoutContainer;

    @Autowired
    SuccessfulCheckoutManager(BushfireApi bushfireApi,
                              UserSession authenticatedUserSession,
                              CheckoutContainer checkoutContainer) {
        this.bushfireApi = bushfireApi;
        this.authenticatedUserSession = authenticatedUserSession;
        this.checkoutContainer = checkoutContainer;
    }

    /**
     * returns true if the payment was confirmed and therefore one can assume the
     * features in the cart to be booked correctly
     */
    boolean paymentConfirmed(ApiOrder apiOrder) {
        return apiOrder.getId() == null || !apiOrder.getStatus().equals(OrderStatus.UNPAID);
    }

    ApiOrder processPayment(ApiOrder apiOrder) {
        ApiOrder result = apiOrder;
        BushfireApiKey apiKey = authenticatedUserSession.getApiKey();
        if (apiOrder.getId() != null) {
            result = bushfireApi.create(OrderApi.class, apiKey).executeTransaction(apiOrder.getId());
        }
        return result;
    }

    /**
     * mark checkout as complete and send invoice if required.
     */
    public void finishCheckout(Checkout checkout) {
        checkoutContainer.markCheckoutAsComplete(checkout.getKey());
    }


}
