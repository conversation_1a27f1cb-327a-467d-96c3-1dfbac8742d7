package com.gumtree.web.seller.page.postad.model;

import com.gumtree.web.seller.page.common.SelectableValue;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


public class PostAdAttributeValue implements SelectableValue {

    private String value;
    private String displayValue;
    private boolean selected;
    private boolean disabled;

    /**
     * Default Constructor.
     */
    public PostAdAttributeValue() {
    }

    /**
     * Constructor.
     *
     * @param value        the attribute value
     * @param displayValue the attribute display value
     */
    public PostAdAttributeValue(String value, String displayValue) {
        this.value = value;
        this.displayValue = displayValue;
    }

    public PostAdAttributeValue(String value, String displayValue, boolean selected, boolean disabled) {
        this.value = value;
        this.displayValue = displayValue;
        this.selected = selected;
        this.disabled = disabled;
    }

    public boolean isSelected() {
        return selected;
    }

    public boolean isDisabled() {
        return disabled;
    }

    public final String getDisplayValue() {
        return displayValue;
    }

    public final void setDisplayValue(String displayValue) {
        this.displayValue = displayValue;
    }

    public final String getValue() {
        return value;
    }

    public final void setValue(String value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        return EqualsBuilder.reflectionEquals(this, o);
    }

    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this);
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
