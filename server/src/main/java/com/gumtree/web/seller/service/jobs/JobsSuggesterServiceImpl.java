package com.gumtree.web.seller.service.jobs;

import com.gumtree.api.category.UnfilteredCategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.util.GuavaUtils;
import com.gumtree.web.seller.page.ajax.category.SuggestedCategory;
import org.apache.commons.lang.WordUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.OptionalInt;
import java.util.stream.IntStream;

@Service
public class JobsSuggesterServiceImpl implements JobsSuggesterService {
    private static final int DEDICATED_JOBS_ID = -1;
    private static final String JOBS_CATEGORY_NAME = "Jobs";

    private final UnfilteredCategoryModel unfilteredCategoryModel;

    @Autowired
    public JobsSuggesterServiceImpl(UnfilteredCategoryModel unfilteredCategoryModel) {
        this.unfilteredCategoryModel = unfilteredCategoryModel;
    }

    public List<SuggestedCategory> getSuggestedJobCategories(String input, List<Long> suggestedCatIds) {
        /*
         * Get List, with block words removed, Set provides non duplicates
         * Get as a set, to remove duplicates
         * */
        Optional<String> suggestedJobKeywords = getJobSuggestion(input, suggestedCatIds);

        Optional<String> suggestedFilteredJobKeywords = suggestedJobKeywords
                .map(x -> removeBlockedWordsFromPhrase(x))
                .map(y -> WordUtils.capitalizeFully(y));

        return suggestedFilteredJobKeywords
                .map(x -> Collections.singletonList(new SuggestedCategory(DEDICATED_JOBS_ID, x, JOBS_CATEGORY_NAME)))
                .orElse(Collections.EMPTY_LIST);
    }

    private Optional<String> getJobSuggestion(String input, List<Long> suggestedCatIds) {
        // We are doing an extremely simple logic here, if the suggestion hits, we just display what is typed
        if ((input.toLowerCase().contains("job")) || hasJobCategories(suggestedCatIds)) {
            return Optional.of(input);
        } else {
            return Optional.empty();
        }

    }

    public OptionalInt getIndexPositionToInsert(List<SuggestedCategory> sCatsFinal, String beginningPhraseToLookupPosition) {
        return IntStream.range(0, sCatsFinal.size())
                .filter(i -> sCatsFinal.get(i).getTree().startsWith(beginningPhraseToLookupPosition))
                .findFirst();
    }

    private String removeBlockedWordsFromPhrase(String phrase) {
        List<String> blockedList = Arrays.asList(new String[]{"\\x", "\\u", "_", "u2013"});

        String result = Arrays.asList(phrase.split(" ")).stream()
                .filter(x -> !blockedList.stream().anyMatch(word -> x.startsWith(word)))
                .reduce((s1, s2) -> s1 + " " + s2)
                .orElse("");
        return result;
    }

    private boolean hasJobCategories(List<Long> suggestedCatIds) {
        return suggestedCatIds
                .stream()
                .map(this::getCategoryById)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(this::getL1Category)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .anyMatch(CategoryConstants.WellKnown.JOBS::is);
    }

    private Optional<Category> getCategoryById(Long categoryId) {

        return GuavaUtils.optionOf(unfilteredCategoryModel.getCategory(categoryId));
    }

    private Optional<Category> getL1Category(Category category) {
        return GuavaUtils.optionOf(unfilteredCategoryModel.getCategory(category.getL1ParentId()));
    }

}
