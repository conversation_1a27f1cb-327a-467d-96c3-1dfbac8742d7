package com.gumtree.web.seller.page.password.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.Page;

public class UpdatePasswordResult extends CommonModel {

    private String formAction;
    private String loginLink;
    private String forgottenPasswordLink;
    private String resetConfirmationUrl;
    private String id;
    private String key;
    private UpdatePasswordForm updatePasswordBean;
    private boolean valid;
    private String actionName;
    private boolean reload;

    public UpdatePasswordResult() {
        // required for Spring
    }

    private UpdatePasswordResult(UpdatePasswordResult.Builder builder) {
        this.formAction = builder.formAction;
        this.loginLink = builder.loginLink;
        this.forgottenPasswordLink = builder.forgottenPasswordLink;
        this.resetConfirmationUrl = builder.resetConfirmationUrl;
        this.updatePasswordBean = builder.updatePasswordBean;
        this.valid = builder.valid;
        this.id = builder.id;
        this.key = builder.key;
        this.reload = builder.reload;
        this.actionName = builder.actionName;
    }

    public void setForm(UpdatePasswordForm form) {
        this.updatePasswordBean = form;
    }

    public UpdatePasswordForm getForm() {
        return updatePasswordBean;
    }

    public String getFormAction() {
        return formAction;
    }

    public String getLoginLink() {
        return loginLink;
    }

    public String getForgottenPasswordLink() {
        return forgottenPasswordLink;
    }

    public boolean isErrorPage() {
        return !valid;
    }

    public String getId() {
        return id;
    }

    public String getKey() {
        return key;
    }

    public boolean isReload() {
        return reload;
    }

    public String getActionName() {
        return actionName;
    }

    public String getResetConfirmationUrl() {
        return resetConfirmationUrl;
    }

    public static UpdatePasswordResult.Builder builder() {
        return new UpdatePasswordResult.Builder();
    }

    public static final class Builder {

        private UpdatePasswordForm updatePasswordBean;
        private String id;
        private String key;
        private String formAction;
        private String loginLink;
        private String forgottenPasswordLink;
        private String resetConfirmationUrl;
        private boolean valid = true;
        private String actionName;
        private boolean reload;
        private Page page;

        public UpdatePasswordResult.Builder withFormAction(String formAction) {
            this.formAction = formAction;
            return this;
        }

        public UpdatePasswordResult.Builder withActionName(String actionName) {
            this.actionName = actionName;
            return this;
        }

        public UpdatePasswordResult.Builder withLoginLink(String loginLink) {
            this.loginLink = loginLink;
            return this;
        }

        public UpdatePasswordResult.Builder withForgottenPasswordLink(String forgottenPasswordLink) {
            this.forgottenPasswordLink = forgottenPasswordLink;
            return this;
        }

        public UpdatePasswordResult.Builder withResetConfirmationUrl(String resetConfirmationUrl) {
            this.resetConfirmationUrl = resetConfirmationUrl;
            return this;
        }

        public UpdatePasswordResult.Builder withValidationErrors() {
            this.valid = false;
            return this;
        }

        public UpdatePasswordResult.Builder withUpdatePasswordBean(UpdatePasswordForm updatePasswordBean) {
            this.updatePasswordBean = updatePasswordBean;
            return this;
        }

        public UpdatePasswordResult.Builder withId(String userName) {
            this.id = userName;
            return this;
        }

        public UpdatePasswordResult.Builder withKey(String passwordKey) {
            this.key = passwordKey;
            return this;
        }

        public UpdatePasswordResult.Builder withReload(Boolean reload) {
            this.reload = reload;
            return this;
        }

        public UpdatePasswordResult.Builder withPage(Page page) {
            this.page = page;
            return this;
        }

        public UpdatePasswordResult build() {
            return new UpdatePasswordResult(this);
        }

    }
}
