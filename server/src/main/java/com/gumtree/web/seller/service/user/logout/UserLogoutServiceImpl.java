package com.gumtree.web.seller.service.user.logout;

import com.gumtree.web.cookie.MadgexCookieHelper;
import com.gumtree.web.cookie.MessageCentreCookieHelper;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.model.ManageAdsWorkspace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Service
public class UserLogoutServiceImpl implements UserLogoutService {

    private ManageAdsWorkspace manageAdsWorkspace;
    private UserSession userSession;
    private MessageCentreCookieHelper messageCentreCookieHelper;
    private MadgexCookieHelper madgexCookieHelper;

    @Autowired
    public UserLogoutServiceImpl(ManageAdsWorkspace manageAdsWorkspace,
                                 UserSession userSession,
                                 MessageCentreCookieHelper messageCentreCookieHelper,
                                 MadgexCookieHelper madgexCookieHelper) {
        this.manageAdsWorkspace = manageAdsWorkspace;
        this.userSession = userSession;
        this.messageCentreCookieHelper = messageCentreCookieHelper;
        this.madgexCookieHelper = madgexCookieHelper;
    }

    @Override
    public void logoutUser(HttpServletRequest request, HttpServletResponse response) {
        manageAdsWorkspace.removeFilterForm();
        userSession.logout();
        messageCentreCookieHelper.removeMessageCentreCookie(request, response);
        madgexCookieHelper.removeMadgexCookie(request,response);
    }

}
