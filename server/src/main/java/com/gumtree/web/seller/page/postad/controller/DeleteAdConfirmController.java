package com.gumtree.web.seller.page.postad.controller;

import com.gumtree.api.Ad;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.Map;

/**
 *
 */
@Controller
@GumtreePage(PageType.DeleteAdConfirm)
public class DeleteAdConfirmController extends BaseSellerController {

    public static final String PAGE_PATH = "/postad/delete-ad-confirm";

    public static final String VIEW_NAME = "deleteadconfirm";

    public static final String ADVERT_ID = "advertId";

    public static final String ADVERT_TITLE = "advertTitle";

    private BushfireApi bushfireApi;

    @Autowired
    public DeleteAdConfirmController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                     ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                     UrlScheme urlScheme, BushfireApi bushfireApi,
                                     UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.bushfireApi = bushfireApi;
    }

    /**
     * @param advertId -
     * @return page
     */
    @RequestMapping(value = PAGE_PATH + "/{id}", method = RequestMethod.GET)
    public final ModelAndView deleteAdConfirm(@PathVariable("id") String advertId) {
        Map<String, Object> map = new HashMap<String, Object>();

        Ad ad = bushfireApi.advertApi().getAdvert(new Long(advertId));

        map.put(ADVERT_ID, ad.getId());
        map.put(ADVERT_TITLE, ad.getTitle());

        return new ModelAndView(VIEW_NAME, map);
    }


}
