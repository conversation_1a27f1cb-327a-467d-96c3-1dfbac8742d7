package com.gumtree.web.seller.page.postad.controller;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.google.common.collect.Lists;
import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.common.error.json.JsonValidationResponse;
import com.gumtree.web.common.error.mvc.MvcReportableErrors;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.manageads.api.CreateOrderApiCall;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.metric.Metric;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.controller.PaymentCheckoutController;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.BumpUpFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdBumpUpModel;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.SingleAdvertShoppingCart;
import com.gumtree.web.seller.page.postad.model.path.PostAdStatePath;
import com.gumtree.web.seller.page.postad.model.products.AnimalType;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.web.seller.service.CheckoutMetaInjector;
import com.gumtree.web.seller.service.PostAdWorkspace;
import com.gumtree.web.seller.service.pricing.PricingContextImpl;
import com.gumtree.web.seller.service.pricing.PricingService;
import com.gumtree.web.seller.service.threatmetrix.ThreatMetrixService;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.checkout.CheckoutPostAdEvent;
import com.gumtree.zeno.core.service.ZenoService;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.codahale.metrics.MetricRegistry.name;
import static com.gumtree.web.seller.page.postad.controller.PostAdBumpUpController.PAGE_PATH;

/**
 * The final step of the post ad flow. This will automatically post the ad in create mode and show bump up options in
 * edit mode.
 */
@Controller
@RequestMapping(PAGE_PATH)
@GoogleAnalytics
public final class PostAdBumpUpController extends BasePostAdController {

    public static final String EDIT_PAGE_PATH = "/postad/{editorId}";
    public static final String PAGE_PATH = "/postad/{editorId}/bumpup";
    public static final String REPOST_ADVERT_PATH = "/postad/repost?advertId=";
    private static final Logger log = LoggerFactory.getLogger(PostAdBumpUpController.class);

    private CheckoutContainer checkoutContainer;

    private PricingService pricingService;

    private ZenoService zenoService;

    private MetricRegistry metricRegistry;

    private Counter postAdFlowRegistrationCounter;

    private CheckoutMetaInjector checkoutMetaInjector;

    private CustomMetricRegistry metrics;

    private ThreatMetrixService threatMetrixService;

    @Autowired
    public PostAdBumpUpController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                  ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                  UrlScheme urlScheme, PostAdWorkspace postAdWorkspace,
                                  UserSession authenticatedUserSession, CategoryService categoryService,
                                  CheckoutContainer checkoutContainer, PricingService pricingService,
                                  GumtreePageContext pageContext,
                                  LocationService locationService, ZenoService zenoService,
                                  MetricRegistry metricRegistry, UserSessionService userSessionService,
                                  CheckoutMetaInjector checkoutMetaInjector, CustomMetricRegistry customMetricRegistry,
                                  ThreatMetrixService threatMetrixService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, postAdWorkspace,
                authenticatedUserSession, categoryService, pageContext, locationService, userSessionService);
        this.checkoutContainer = checkoutContainer;
        this.pricingService = pricingService;
        this.metricRegistry = metricRegistry;
        this.zenoService = zenoService;
        this.checkoutMetaInjector = checkoutMetaInjector;
        this.metrics = customMetricRegistry;
        this.threatMetrixService = threatMetrixService;
    }

    @PostConstruct
    public void initCounters() {
        postAdFlowRegistrationCounter = metricRegistry.counter(name(PostAdBumpUpController.class, "postAdFlowRegistration"));
    }

    /**
     * Get the bump up page
     *
     * @param editorId the ID of the editor for the current ad
     * @return name of page to go to
     */
    @GumtreePage(PageType.EditAdBumpUpPromo)
    @RequestMapping(method = RequestMethod.GET)
    public ModelAndView viewBumpUpPage(
            HttpServletRequest request,
            HttpServletResponse response,
            @PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId,
            @RequestParam(value = "autoRepost", required = false, defaultValue = "false") Boolean autoRepost,
            GumtreePageContext context, RemoteIP remoteIP) throws IOException {

        AdvertEditor editor = metrics.bumpUpPageTimer("getEditor").record(() -> getPostAdWorkspace().getEditor(editorId));

        if (editor.isValid()) {
            if (!editor.supportsBumpUp() || autoRepost) {
                return postAdvert(editor, false, context, getPermanentCookie(request), remoteIP);
            }
            return viewBumpUpPage(new BumpUpFormBean(), null, editor, request);
        } else if (autoRepost && editor.getAdvertId() != null) {
            // This logic shouldn't be specific to just autoRepost, but due to the lack of QA/integration tests in this
            // area, and a slight possibility the user may end up on a legacy bumpup page somehow - we've put this in
            // a specific 'if (autoRepost)' condition to isolate ourselves to this particular case.
            String editPath = getHost() + REPOST_ADVERT_PATH + editor.getAdvertId();
            response.sendRedirect(editPath);
            return redirect(editPath);
        } else {
            return redirect(new PostAdStatePath(editorId).getPath());
        }
    }

    /*
    It is only used for the routing interaction between the front-end and the APP.
     */
    @GumtreePage(PageType.EditAdBumpUpPromo)
    @RequestMapping(value = "/phoneverification/route", method = RequestMethod.GET)
    @ResponseBody
    public String bumpUpPageForPhoneVerify(HttpServletRequest request,
            @PathVariable String editorId) {
        return "Success!";
    }

    @GumtreePage(PageType.EditAdBumpUpPromo)
    @RequestMapping(value = "/phoneverification", method = RequestMethod.GET)
    @ResponseBody
    public String submitBumpUpFormAjaxForPhoneVerification( HttpServletRequest request,
            @PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId,
            GumtreePageContext context, RemoteIP remoteIP) {

        AdvertEditor editor = getPostAdWorkspace().getEditor(editorId);
        if (editor != null && editor.isValid()) {
            Map<String, Object> model = postAdvertForPhoneVerification(editor, false, context, getPermanentCookie(request), remoteIP).getModel();
            if (model.get("adId") != null) {
                return "{\"adId\":\""  + model.get("adId") +   "\",\"editorId\":\""  + model.get("editorId")+ "\"}";
            }
        }
        return "{\"redirectUrl\":\"" + new PostAdStatePath(editorId).getPath() + "\"}";
    }

    private Optional<PermanentCookie> getPermanentCookie(HttpServletRequest request) {
        return Optional.ofNullable(cookieResolver.resolve(request, PermanentCookie.class));
    }

    /**
     * Handle Bump UP cancellation requests.
     *
     * @param editorId the ID of the editor for the current ad
     * @return name of teh next page to go to
     */
    @RequestMapping(method = RequestMethod.POST, params = {"cancel"})
    public ModelAndView cancelBumpUp(@PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId) {
        return redirect(new PostAdStatePath(editorId).getPath());
    }

    /**
     * Submit bump up form.
     *
     * @param bumpUpFormBean the form bean
     * @param bindingResult  binding result
     * @param editorId       editor id
     * @return name of page to go to
     */
    @GumtreePage(PageType.EditAdBumpUpPromo)
    @RequestMapping(method = RequestMethod.POST)
    public ModelAndView submitBumpUpForm(
            @Valid BumpUpFormBean bumpUpFormBean,
            BindingResult bindingResult, HttpServletRequest request,
            @PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId,
            GumtreePageContext context, RemoteIP remoteIP) {
        AdvertEditor editor = getPostAdWorkspace().getEditor(editorId);

        // Only show errors if the ad doesn't have to be bumped up but there was an error binding object
        if (bindingResult.hasErrors() && !editor.requiresBumpUp()) {

            ReportableErrorsMessageResolvingErrorSource errorModelObject =
                    populateErrors(new MvcReportableErrors(bindingResult));
            return viewBumpUpPage(bumpUpFormBean, errorModelObject, editor, request);
        }

        boolean bumpup = BooleanUtils.isTrue(bumpUpFormBean.getBumpUp()) || editor.requiresBumpUp();

        return postAdvert(editor, bumpup, context, getPermanentCookie(request), remoteIP);
    }

    /**
     * Submit bump up form.
     *
     * @param bumpUpFormBean the form bean
     * @param bindingResult  binding result
     * @param editorId       editor id
     * @return name of page to go to
     */
    @GumtreePage(PageType.EditAdBumpUpPromo)
    @RequestMapping(method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public String submitBumpUpFormAjax(
            @Valid @RequestBody BumpUpFormBean bumpUpFormBean,
            BindingResult bindingResult, HttpServletRequest request,
            @PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId,
            GumtreePageContext context, RemoteIP remoteIP) {

        AdvertEditor editor = getPostAdWorkspace().getEditor(editorId);
        editor.setCheckoutVariant(bumpUpFormBean.getCheckoutVariationId());
        getPostAdWorkspace().updateEditor(editorId, editor);

        // Only show errors if the ad doesn't have to be bumped up but there was an error binding object
        if (bindingResult.hasErrors() && !editor.requiresBumpUp()) {

            ReportableErrorsMessageResolvingErrorSource errorModelObject =
                    populateErrors(new MvcReportableErrors(bindingResult));

            viewBumpUpPage(bumpUpFormBean, errorModelObject, editor, request).getModel();
            return "{\"errorMessage\": \"Please select a feature option\"}";
        }

        boolean bumpup = BooleanUtils.isTrue(bumpUpFormBean.getBumpUp()) || editor.requiresBumpUp();

        Map<String, Object> model = postAdvert(editor, bumpup, context, getPermanentCookie(request), remoteIP).getModel();
        if (model.get("checkoutKey") != null) {
            return "{\"redirectUrl\": \"/checkout/" + model.get("checkoutKey") + "\"}";
        } else {
            return "{\"errorMessage\": \"An unexpected error occurred\"}";
        }
    }

    /**
     * AJAX JSON validation handler for bump up ad form.
     *
     * @param bumpUpFormBean the updated bean
     * @param editorId       which editor we are in
     * @param result         the binding result
     * @return validation JSON response
     */
    @RequestMapping(method = RequestMethod.POST, headers = "X-Requested-With=XMLHttpRequest", params = "validate=true")
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public JsonValidationResponse validateForm(
            @Valid BumpUpFormBean bumpUpFormBean,
            BindingResult result,
            @PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId) {

        return createJsonValidationResponse(new MvcReportableErrors(result));
    }

    private ModelAndView viewBumpUpPage(BumpUpFormBean formBean, ReportableErrorsMessageResolvingErrorSource
            errorModelObject, AdvertEditor editor, HttpServletRequest request) {

        PricingContextImpl pricingContext = new PricingContextImpl(editor, categoryModel);
        PricingMetadata pricingMetadata = metrics.bumpUpPageTimer("getPriceInformation").record(() ->
                pricingService.getPriceInformation(pricingContext));

        zenoService.logEvent(editor, PageType.EditAdBumpUpPromo, CheckoutPostAdEvent.class);

        CoreModel.Builder coreModelBuilder = getCoreModelBuilder(request);
        coreModelBuilder.withGaEvents(Lists.newArrayList("PostAdBumpUpEvent"));

        PostAdBumpUpModel.Builder modelBuilder = PostAdBumpUpModel.builder();
        modelBuilder.withRequiresBumpUp(editor.requiresBumpUp());
        modelBuilder.withBumpUpFormAction(PAGE_PATH.replaceFirst(EDITOR_ID_REPLACEMENT, editor.getEditorId()));
        modelBuilder.withForm(formBean);
        modelBuilder.withPricingMetadata(pricingMetadata);
        modelBuilder.withVerb(editor.getDisplayActionVerb());
        modelBuilder.withErrors(errorModelObject);
        modelBuilder.withEditPageUrl(EDIT_PAGE_PATH.replaceFirst(EDITOR_ID_REPLACEMENT, editor.getEditorId()));

        return modelBuilder.build(coreModelBuilder.withUserType(authenticatedUserSession.isProUser()));
    }

    private ModelAndView postAdvert(final AdvertEditor editor, boolean bumpUp,
                                    GumtreePageContext context, Optional<PermanentCookie> resolve, RemoteIP remoteIP) {
        PostAdDetail adDetail = (editor != null) ? editor.getAdvertDetail() : null;
        UserSession userSession = getAuthenticatedUserSession();

        boolean postAdWithRegistration = userSession.getUserType().equals(UserLoginStatus.NEW_UNREGISTERED);
        if (postAdWithRegistration) {
            // If the user needs to be registered first, then we do it here.
            ApiCallResponse<User> apiResponse = userSession.registerNewUser(editor.toRegisterUserBean());
            if (apiResponse.isErrorResponse()) {
                return redirect(new PostAdStatePath(editor.getEditorId()).getPath());
            }
            handleSuccessfulRegistration(apiResponse);
        }

        ApiCallResponse<Ad> apiResponse = execute(editor);
        if (apiResponse.isErrorResponse()) {
            return redirect(new PostAdStatePath(editor.getEditorId()).getPath());
        } else {
            invalidateThreatmetrixSession(context);
        }

        ApiCallResponse<ApiOrder> response = metrics.bumpUpPageTimer("createOrder").record(() ->
                createOrder(apiResponse.getResponseObject(), editor, bumpUp));
        metrics.bumpUpPageTimer("removeEditor").record(() ->
                getPostAdWorkspace().removeEditor(editor.getEditorId(), editor.isCreateMode()));

        if (response.isErrorResponse()) {
            return getRedirectToErrorPage(response);
        }

        breedMetrics(adDetail);

        AdStatus originalStatus = (adDetail != null) ? adDetail.getStatus() : apiResponse.getResponseObject().getStatus();
        Checkout checkout = metrics.bumpUpPageTimer("createCheckout").record(() -> checkoutContainer
                .createCheckout(response.getResponseObject(), apiResponse.getResponseObject(), true));

        long start = System.currentTimeMillis();
        Checkout finalCheckout = (originalStatus != null) ?
                (checkoutMetaInjector.injectTrackingForEditorUpdate(checkout,
                        apiResponse.getResponseObject(), originalStatus)) : (checkoutMetaInjector.injectTrackingForPost(checkout,
                apiResponse.getResponseObject()));

        metrics.bumpUpPageTimer("injectTracking").record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);

        return getRedirectForCheckout(PaymentCheckoutController.PAGE_PATH, finalCheckout);
    }

    private Long handleSuccessfulRegistration(ApiCallResponse<User> apiResponse) {
        Long userId = apiResponse.getResponseObject().getId();
        postAdFlowRegistrationCounter.inc();
        return userId;
    }

    private void invalidateThreatmetrixSession(GumtreePageContext context) {
        HttpServletRequest httpServletRequest = context.getHttpServletRequest();
        HttpServletResponse httpServletResponse = context.getHttpServletResponse();


        threatMetrixService.invalidateThreatMetrixCookie(httpServletRequest, httpServletResponse);
    }

    private ApiCallResponse<ApiOrder> createOrder(Ad ad, AdvertEditor editor, boolean bumpUp) {
        SingleAdvertShoppingCart cart = new SingleAdvertShoppingCart(ad.getId(), getAccountId());
        editor.populateShoppingCart(cart);

        if (bumpUp) {
            cart.addProduct(ProductName.BUMP_UP);
            metrics.metricCounter(Metric.BUMP_UP.name());
        }

        CreateOrderApiCall apiCall = new CreateOrderApiCall(cart.toOrderBean(), getAuthenticatedUserSession());
        return execute(apiCall);
    }

    private ModelAndView getRedirectToErrorPage(ApiCallResponse<ApiOrder> response) {
        if (ApiErrorCode.DUPLICATED_ADVERT.equals(response.getErrorCode())) {
            return redirect(DuplicatedAdController.PAGE_PATH);
        } else {
            return createRedirectTo(OrderErrorController.PAGE_PATH);
        }
    }

    private void breedMetrics(PostAdDetail adDetail) {
        Optional<AnimalType> animalType = AnimalType.fromCategoryId(adDetail.getCategoryId());
        if (animalType.isPresent()) {
            AnimalType type = animalType.get();
            Map<String, String> attributes = adDetail.getPostAdFormBean().getAttributes();
            metrics.counter(type.name().toLowerCase() + "BreedSelected",
                    Metric.BREED_SELECTED.name(), attributes.get(type.getBreedAttributeParentName())).increment();
        }
    }

    /**
     * Posts an advert for phone verification
     * @param editor
     * @param bumpUp
     * @param context
     * @param resolve
     * @param remoteIP
     * @return
     */
    private ModelAndView postAdvertForPhoneVerification(final AdvertEditor editor, boolean bumpUp,
                                                        GumtreePageContext context, Optional<PermanentCookie> resolve, RemoteIP remoteIP) {
        PostAdDetail adDetail = editor.getAdvertDetail();
        adDetail.setStatus(AdStatus.AWAITING_PHONE_VERIFIED);

        getPostAdWorkspace().updateEditorByEditorId(editor.getEditorId(),editor);

        try{
            getPostAdWorkspace().removeEditor(editor.getEditorId(),editor.isCreateMode());
        }catch (Exception e){
            log.error("PostAdWorkspace removeEditor error", e);
        }

        UserSession userSession = getAuthenticatedUserSession();
        boolean postAdWithRegistration = userSession.getUserType().equals(UserLoginStatus.NEW_UNREGISTERED);
        if (postAdWithRegistration) {
            // If the user needs to be registered first, then we do it here.
            ApiCallResponse<User> apiResponse = userSession.registerNewUser(editor.toRegisterUserBean());
            if (apiResponse.isErrorResponse()) {
                return redirect(new PostAdStatePath(editor.getEditorId()).getPath());
            }
            handleSuccessfulRegistration(apiResponse);
        }

        ApiCallResponse<Ad> apiResponse = execute(editor);
        if (apiResponse.isErrorResponse()) {
            log.error("phoneverification:post, execute(editor) fail! errorCode:" + apiResponse.getErrorCode());
            return redirect(new PostAdStatePath(editor.getEditorId()).getPath());
        } else {
            invalidateThreatmetrixSession(context);
            ModelAndView modelAndView = new ModelAndView();
            modelAndView.addObject("adId", apiResponse.getResponseObject().getId());
            modelAndView.addObject("editorId", editor.getEditorId());

            editor.getAdvertDetail().setAdvertId(apiResponse.getResponseObject().getId());
            getPostAdWorkspace().updateEditorByEditorId(editor.getEditorId(),editor);

            return modelAndView;
        }
    }
}
