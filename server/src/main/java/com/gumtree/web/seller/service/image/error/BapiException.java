package com.gumtree.web.seller.service.image.error;

import com.gumtree.bapi.model.ApiErrors;
import com.netflix.hystrix.exception.ExceptionNotWrappedByHystrix;

public class BapiException extends RuntimeException implements ExceptionNotWrappedByHystrix {

    private final String apiMethod;
    private final Integer status;
    private final ApiErrors apiErrors;

    public BapiException(String apiMethod, Integer status, ApiErrors apiErrors) {
        super("Error method: {} " + apiMethod + " status:" + status+ " errors:" + apiErrors);
        this.apiMethod = apiMethod;
        this.status = status;
        this.apiErrors = apiErrors;
    }

    public String getApiMethod() {
        return apiMethod;
    }

    public Integer getStatus() {
        return status;
    }

    public ApiErrors getApiErrors() {
        return apiErrors;
    }

    @Override
    public String toString() {
        return "BapiApiException{" +
                "method=" + getApiMethod() +
                ", httpStatus=" + getStatus() +
                ", apiErrors=" + getApiErrors() +
                '}';
    }
}
