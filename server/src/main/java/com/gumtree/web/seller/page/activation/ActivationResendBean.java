package com.gumtree.web.seller.page.activation;

import com.gumtree.web.seller.page.common.model.Form;
import org.hibernate.validator.constraints.NotEmpty;

import static com.gumtree.api.domain.user.UserMessageCodes.Registration.EMAIL_MISSING;

public class ActivationResendBean extends Form {

    @NotEmpty(message = EMAIL_MISSING)
    private String username;

    public final String getUsername() {
        return username;
    }

    public final void setUsername(String username) {
        this.username = username;
    }
}
