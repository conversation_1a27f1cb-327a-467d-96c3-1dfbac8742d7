package com.gumtree.web.seller.page.postad.model;

import com.gumtree.web.seller.page.common.model.Form;

import javax.validation.constraints.NotNull;

/**
 * For choosing whether to bump up or not from the Bump Up page.
 */
public final class BumpUpFormBean extends Form {

    @NotNull(message = "postad.bumpup.select.option")
    private Boolean bumpUp;
    private String checkoutVariationId;

    public Boolean getBumpUp() {
        return bumpUp;
    }

    public void setBumpUp(Boolean bumpUp) {
        this.bumpUp = bumpUp;
    }

    public String getCheckoutVariationId() {
        return checkoutVariationId;
    }

    public void setCheckoutVariationId(String checkoutVariationId) {
        this.checkoutVariationId = checkoutVariationId;
    }
}
