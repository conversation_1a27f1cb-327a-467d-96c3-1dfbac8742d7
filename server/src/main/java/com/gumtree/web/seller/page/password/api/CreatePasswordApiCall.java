package com.gumtree.web.seller.page.password.api;

import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.domain.user.beans.CreatePasswordBean;
import com.gumtree.api.client.executor.ApiCall;

/**
 * Api call to rest a users password
 */
public class CreatePasswordApiCall implements ApiCall<User> {

    private String userName;
    private CreatePasswordBean createPasswordBean;

    /**
     * Default constructor, validation then create password
     * @param userName needed for validation and account lookup
     * @param createPasswordBean new password for the user
     */
    public CreatePasswordApiCall(String userName, CreatePasswordBean createPasswordBean) {
        this.userName = userName;
        this.createPasswordBean = createPasswordBean;
    }

    @Override
    public final User execute(BushfireApi api) {
        api.create(UserApi.class).createPassword(userName, createPasswordBean);
        return null;
    }
}
