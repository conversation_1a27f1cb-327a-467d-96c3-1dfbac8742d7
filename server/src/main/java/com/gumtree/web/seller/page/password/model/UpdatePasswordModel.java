package com.gumtree.web.seller.page.password.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import org.springframework.web.servlet.ModelAndView;

public class UpdatePasswordModel extends CommonModel {

    private String formAction;
    private String loginLink;
    private String forgottenPasswordLink;
    private String id;
    private String key;
    private UpdatePasswordForm updatePasswordBean;
    private boolean valid;
    private String actionName;
    private boolean reload;

    public UpdatePasswordModel() {
        // required for Spring
    }

    private UpdatePasswordModel(CoreModel core, Builder builder) {
        super(core);
        this.formAction = builder.formAction;
        this.loginLink = builder.loginLink;
        this.forgottenPasswordLink = builder.forgottenPasswordLink;
        this.updatePasswordBean = builder.updatePasswordBean;
        this.valid = builder.valid;
        this.id = builder.id;
        this.key = builder.key;
        this.reload = builder.reload;
        this.actionName = builder.actionName;
    }

    public void setForm(UpdatePasswordForm form) {
        this.updatePasswordBean = form;
    }

    public UpdatePasswordForm getForm() {
        return updatePasswordBean;
    }

    public String getFormAction() {
        return formAction;
    }

    public String getLoginLink() {
        return loginLink;
    }

    public String getForgottenPasswordLink() {
        return forgottenPasswordLink;
    }

    public boolean isErrorPage() {
        return !valid;
    }

    public String getId() {
        return id;
    }

    public String getKey() {
        return key;
    }

    public boolean isReload() {
        return reload;
    }

    public String getActionName() {
        return actionName;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private UpdatePasswordForm updatePasswordBean;
        private String id;
        private String key;
        private String formAction;
        private String loginLink;
        private String forgottenPasswordLink;
        private boolean valid = true;
        private String actionName;
        private boolean reload;
        private Page page;

        public Builder withFormAction(String formAction) {
            this.formAction = formAction;
            return this;
        }

        public Builder withActionName(String actionName) {
            this.actionName = actionName;
            return this;
        }

        public Builder withLoginLink(String loginLink) {
            this.loginLink = loginLink;
            return this;
        }

        public Builder withForgottenPasswordLink(String forgottenPasswordLink) {
            this.forgottenPasswordLink = forgottenPasswordLink;
            return this;
        }

        public Builder withValidationErrors() {
            this.valid = false;
            return this;
        }

        public Builder withUpdatePasswordBean(UpdatePasswordForm updatePasswordBean) {
            this.updatePasswordBean = updatePasswordBean;
            return this;
        }

        public Builder withId(String userName) {
            this.id = userName;
            return this;
        }

        public Builder withKey(String passwordKey) {
            this.key = passwordKey;
            return this;
        }

        public Builder withReload(Boolean reload) {
            this.reload = reload;
            return this;
        }

        public Builder withPage(Page page) {
            this.page = page;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            coreModelBuilder.withTitle(actionName + " Password | My Gumtree - Gumtree");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new UpdatePasswordModel(coreModelBuilder.build(page), this));
        }
    }
}
