package com.gumtree.web.seller.page.postad.controller.steps;

import com.gumtree.api.Account;
import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdFormPanel;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.storage.ratelimit.RateCheckResult;
import com.gumtree.web.storage.ratelimit.RateLimiter;
import com.gumtree.zeno.core.service.ZenoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;

import static java.util.Collections.singletonList;

@Component
public class RateLimiterPostAdStep implements PostAdStep {
    public static final Integer ORDER = LocationAndSellerTypePostAdStep.ORDER + 1;

    @Autowired
    private BushfireApi bushfireApi;

    @Autowired
    private ZenoService zenoService;

    @Autowired
    @Qualifier("postAdRateLimiter")
    private RateLimiter rateLimiter;

    @Autowired
    private UserSession authenticatedUserSession;

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public boolean execute(PostAdSubmitModel.Builder model, AdvertEditor editor) {
        Optional<String> username = emailToRateLimit(editor);
        if (editor.isCreateMode() && isNonProAccount() && exceedsRateLimit(username)) {
            editor.getPostAdFormBean()
                    .addError("email.already.registered", singletonList("You are posting ads too frequently - please try again later"));

            // to make the above error visible to user we need to add confirmation button
            model.addPanels(singletonList(PostAdFormPanel.CONFIRMATION));
            return false;
        } else if (!authenticatedUserEmail().isPresent() && isUserAlreadyRegistered(username)) {
            editor.getPostAdFormBean().addError("email.already.registered",
                    singletonList("The email address is already registered - <a href=\"/login\">please sign in</a>"));

            //panelAttributes for vehicle is null at this stage
            model.removePanels(Collections.singletonList(PostAdFormPanel.VEHICLE_SPECIFICATIONS));
            // to make the above error visible to user we need to add confirmation button
            model.addPanels(singletonList(PostAdFormPanel.CONFIRMATION));
            return false;
        } else {
            return true;
        }
    }

    private Boolean exceedsRateLimit(Optional<String> email) {
        return email.map(this::exceedsPostRate).orElse(false);
    }

    /**
     * Users were bypassing our rate limiter by posting multiple ads via posting with auto registration by not activating they account
     * after 1st posted ad but after they posted many of them. For that reason we decide to not allow to post 2nd ad without completing
     * activation flow.
     *
     * @param username the username to check
     * @return trus if user is already registered
     */
    private boolean isUserAlreadyRegistered(Optional<String> username) {
        return username
                .flatMap(email -> getUserSafely(email))
                .isPresent();
    }

    private Optional<User> getUserSafely(String email) {
        try {
            return Optional.of(bushfireApi.userApi().getUser(email));
        } catch (Exception ex) {
            return Optional.empty();
        }
    }

    private boolean exceedsPostRate(String email) {
        Boolean exceeds = false;
        RateCheckResult rateCheckResult = rateLimiter.checkRate(email);
        if (rateCheckResult.isRateLimitExceeded()) {
            zenoService.logBackendEvent("PostAdRateExceededEvent", email, rateCheckResult.getTtl());
            exceeds = true;
        }
        return exceeds;
    }

    private Optional<String> emailToRateLimit(AdvertEditor editor) {
        Optional<String> authenticatedUserEmail = authenticatedUserEmail();
        Optional<String> postAdRegisteringEmail = Optional.ofNullable(editor.getPostAdFormBean().getEmailAddress());
        return Optional.ofNullable(authenticatedUserEmail.orElse(postAdRegisteringEmail.orElse(null)));
    }

    private Optional<String> authenticatedUserEmail() {
        String username = authenticatedUserSession.getUsername();
        return username.isEmpty() ? Optional.empty() : Optional.of(username);
    }

    private boolean isNonProAccount() {
        return getAccount().map(a -> !a.isPro()).orElse(true);
    }

    private Optional<Account> getAccount() {
        return getAccountId() != null ? Optional.ofNullable(bushfireApi.accountApi().getAccount(getAccountId())) : Optional.empty();
    }

    private Long getAccountId() {
        return authenticatedUserSession.getSelectedAccountId();
    }
}
