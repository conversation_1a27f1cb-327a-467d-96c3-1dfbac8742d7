package com.gumtree.web.seller.page.manageads.marketing;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.seller.page.manageads.reporting.ManageAdsGoogleAnalyticsConfigurer;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import static com.gumtree.web.seller.page.manageads.marketing.MarketingPreferenceController.PAGE_PATH;

@Controller
@GumtreePage(PageType.MyAds)
@GoogleAnalytics(configurer = ManageAdsGoogleAnalyticsConfigurer.class)
@RequestMapping(PAGE_PATH)
public class MarketingPreferenceController extends BaseSellerController {

    public static final String PAGE_PATH = "/manage/marketing";
    public static final String UPDATE_MRK = "/update";

    private UserSession userSession;
    private ManageAdsHelper manageAdsHelper;
    private UserServiceFacade userServiceFacade;
    private static final String SUCCESS_REASON = "successNotice";
    private static final String UPDATE_SUCCESS_MSG = "Thanks - your details have now been updated";


    @Autowired
    public MarketingPreferenceController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                         ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                         UrlScheme urlScheme, UserSession userSession, ManageAdsHelper manageAdsHelper,
                                         UserSessionService userSessionService, UserServiceFacade userServiceFacade){
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.userSession = userSession;
        this.manageAdsHelper = manageAdsHelper;
        this.userServiceFacade = userServiceFacade;
    }

    /**
     * Update the preference
     *
     * @return model/view
     */
    @RequestMapping(value = UPDATE_MRK,
            method = RequestMethod.POST,
            headers = "X-Requested-With=XMLHttpRequest")
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public String updatePreferences(@RequestParam(value = "optin-marketing", defaultValue = "false") boolean optIn, Model model) {
        User user = manageAdsHelper.getSessionUser(userSession);
        ApiResponse<Boolean> response = userServiceFacade.setMarketingPreference(user.getId(), optIn);

        JsonObject body = new JsonObject();
        if (!response.isDefined()) {
            body = reportableErrorToJsonObject(new ReportableErrorsMessageResolvingErrorSource(
                    new ReportableUserApiErrors(response.getError()), this.messageResolver));
        } else {
            body.add(SUCCESS_REASON, new JsonPrimitive(UPDATE_SUCCESS_MSG));
        }

        return new Gson().toJson(body);
    }
}
