package com.gumtree.web.seller.page.postad.model;

import java.util.Objects;

public class PriceGuidanceAd {
    private final Long id;
    private final String title;
    private final String price;
    private final String primaryImageURL;
    private final String sellerType;
    private final String mileage;

    public PriceGuidanceAd(Long id, String title, String price, String primaryImageURL, String sellerType, String mileage) {
        this.id = id;
        this.title = title;
        this.price = price;
        this.primaryImageURL = primaryImageURL;
        this.sellerType = sellerType;
        this.mileage = mileage;
    }

    public Long getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public String getPrice() {
        return price;
    }

    public String getPrimaryImageURL() {
        return primaryImageURL;
    }

    public String getSellerType() {
        return sellerType;
    }

    public String getMileage() {
        return mileage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PriceGuidanceAd that = (PriceGuidanceAd) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(title, that.title) &&
                Objects.equals(price, that.price) &&
                Objects.equals(primaryImageURL, that.primaryImageURL) &&
                Objects.equals(sellerType, that.sellerType) &&
                Objects.equals(mileage, that.mileage);
    }

    @Override
    public int hashCode() {

        return Objects.hash(id, title, price, primaryImageURL, sellerType, mileage);
    }
}
