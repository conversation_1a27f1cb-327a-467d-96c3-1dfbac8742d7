package com.gumtree.web.seller.page.manageads.mydetails;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.domain.user.beans.ChangePasswordBean;
import com.gumtree.api.domain.user.beans.CreatePasswordBean;
import com.gumtree.api.domain.user.beans.UpdateUserBean;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.seller.domain.deactivation.entity.DeactivationReason;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.ChangePasswordRequest;
import com.gumtree.user.service.model.GumtreeAccessToken;
import com.gumtree.user.service.model.UserApiErrorCode;
import com.gumtree.user.service.model.VerificationKeyType;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.common.error.mvc.MvcReportableErrors;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.mydetails.api.UpdateUserApiCall;
import com.gumtree.web.seller.page.password.api.CreatePasswordApiCall;
import com.gumtree.web.seller.page.postad.model.MyDetailsModel;
import com.gumtree.web.seller.page.reviews.model.UserRating;
import com.gumtree.web.seller.page.reviews.service.UserReviewsService;
import com.gumtree.web.seller.service.securetoken.SecureToken;
import com.gumtree.web.service.ContactEmailService;
import com.gumtree.zeno.core.domain.PageType;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Optional;

import static com.gumtree.web.security.shiro.GumtreeRealm.MWEB_CLIENT;


/**
 * My Details page controller for non-pro users
 */
@Controller
@GoogleAnalytics
@GumtreePage(PageType.MyAccount)
@RequestMapping(value = "/manage-account")
public class MyDetailsController extends BaseSellerController {
    public static final String VIEW_NAME = "pages/manage-account/manage-account-responsive";

    private final ManageAdsHelper manageAdsHelper;
    private final UserSession userSession;
    private final ContactEmailService contactEmailService;
    private final UserServiceFacade userServiceFacade;
    private final LoginUtils loginUtils;
    private final UserReviewsService userReviewsService;
    private final CustomMetricRegistry metrics;

    @Autowired
    public MyDetailsController(CookieResolver cookieResolver, CategoryModel categoryModel,
                               ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                               UrlScheme urlScheme, UserSession userSession, ManageAdsHelper manageAdsHelper,
                               ContactEmailService contactEmailService, UserSessionService userSessionService,
                               UserServiceFacade userServiceFacade, LoginUtils loginUtils, UserReviewsService userReviewsService,
                               CustomMetricRegistry metrics) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.manageAdsHelper = manageAdsHelper;
        this.userSession = userSession;
        this.contactEmailService = contactEmailService;
        this.userServiceFacade = userServiceFacade;
        this.loginUtils = loginUtils;
        this.userReviewsService = userReviewsService;
        this.metrics = metrics;
    }

    @SecureToken(value = VIEW_NAME)
    @RequestMapping(value = "/", method = RequestMethod.GET)
    public ModelAndView showPage(HttpServletRequest request, @RequestParam(required = false) String tab) {

        User user = metrics.myAccountPageTimer("getSessionUser").record(() -> manageAdsHelper.getSessionUser(userSession));
        MyDetailsModel.Builder modelBuilder = MyDetailsModel.builder(getCoreModelBuilder(request))
                .withIsProUser(metrics.myAccountPageTimer("isProUser").record(() -> userSession.isProUser()))
                .withPasswordExist(user.getPasswordExists())
                .withUpdateUserBean(getUpdateUserBean(user))
                .withContactEmails(metrics.myAccountPageTimer("contactEmailService.getEditable").record(() ->
                        contactEmailService.getEditable(user)))
                .withTabToOpen(tab)
                .withUserEmail(user.getEmail());

        if (userSession.isProUser()) {
            modelBuilder.withAccountSelectionForm(manageAdsHelper.getAccountSelectionForm(userSession));
            modelBuilder.withManageAdsUrls(manageAdsHelper, getUrlScheme());
            modelBuilder.withAccount(metrics.myAccountPageTimer("getSelectedAccount").record(() -> manageAdsHelper.getSelectedAccount(userSession)));
        } else {
            Optional<Long> accountId = Optional.ofNullable(userSession.getSelectedAccountId());
            accountId.ifPresent(id -> {
                Optional<UserRating> userRating = metrics.myAccountPageTimer("getUserRating").record(() ->
                        userReviewsService.getUserRating(id).toBlocking().value());
                modelBuilder.withUserReviewRating(userRating);
                if (userRating.isPresent()) {
                    modelBuilder.withAccount(metrics.myAccountPageTimer("getSelectedAccount").record(() -> manageAdsHelper.getSelectedAccount(userSession)));
                }
            });
        }

        return modelBuilder.build();
    }

    /**
     * AJAX JSON update handler
     *
     * @param updateUserBean    form bean
     * @return                  JSON response
     */
    @SecureToken(value = VIEW_NAME)
    @RequestMapping(value = "/update", method = RequestMethod.POST, headers = "X-Requested-With=XMLHttpRequest")
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public String updateUserDetails(@Valid UpdateUserBean updateUserBean) {

        User user = manageAdsHelper.getSessionUser(userSession);
        updateUserBean.setOptInMarketing(user.isOptInMarketing());
        ApiCallResponse<User> response = execute(new UpdateUserApiCall(user.getId(), updateUserBean));

        JsonObject body = new JsonObject();
        if (response.isErrorResponse()) {
            body = reportableErrorToJsonObject(populateErrors(response));
        } else {
            body.add("successNotice", new JsonPrimitive(resolveMessage("account.manage.details.update.success")));
        }

        return new Gson().toJson(body);
    }

    /**
     * AJAX change request handler
     *
     * @param changePasswordFormBean    form bean
     * @param bindingResult             binding result for errors
     * @param subject                   subject for logging purposes
     * @return                          JSON response
     */
    @SecureToken(value = VIEW_NAME)
    @RequestMapping(value = "/change-password", method = RequestMethod.POST, headers = "X-Requested-With=XMLHttpRequest")
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public String postChangePasswordForm(@Valid ChangePasswordBean changePasswordFormBean, BindingResult bindingResult, Subject subject) {

        User user = manageAdsHelper.getSessionUser(userSession);

        ChangePasswordRequest changePasswordRequest = new ChangePasswordRequest();
        changePasswordRequest.setUsername(user.getEmail());
        changePasswordRequest.setVerificationKeyType(VerificationKeyType.CURRENT_USER_PASSWORD);
        changePasswordRequest.setVerificationKey(changePasswordFormBean.getCurrentPassword());
        changePasswordRequest.setPassword(changePasswordFormBean.getPassword());
        changePasswordRequest.setConfirmedPassword(changePasswordFormBean.getConfirmedPassword());
        changePasswordRequest.setClient(MWEB_CLIENT);
        ApiResponse<GumtreeAccessToken> response = userServiceFacade.changePassword(changePasswordRequest);

        JsonObject body = new JsonObject();
        if (!response.isDefined()) {
            body = reportableErrorToJsonObject(populateChangePasswordErrors(bindingResult, response));
        } else {
            loginUtils.login(subject, user.getEmail(), changePasswordFormBean.getPassword());
            body.add("successNotice", new JsonPrimitive(resolveMessage("account.manage.password.change.success")));
        }

        return new Gson().toJson(body);
    }

    /**
     * AJAX create password handler
     *
     * @param createPasswordFormBean    form bean
     * @param bindingResult             binding result for errors
     * @return                          JSON response
     */
    @SecureToken(value = VIEW_NAME)
    @RequestMapping(value = "/create-password", method = RequestMethod.POST, headers = "X-Requested-With=XMLHttpRequest")
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public String postCreatePasswordForm(@Valid CreatePasswordBean createPasswordFormBean, BindingResult bindingResult) {

        User user = manageAdsHelper.getSessionUser(userSession);

        ApiCallResponse<User> response = execute(new CreatePasswordApiCall(user.getEmail(), createPasswordFormBean));

        JsonObject body = new JsonObject();
        if (response.isErrorResponse()) {
            body = reportableErrorToJsonObject(populateCreatePasswordErrors(bindingResult, response));
        } else {
            body.add("successNotice", new JsonPrimitive(resolveMessage("account.manage.password.create.success")));
        }

        return new Gson().toJson(body);
    }


    /**
     * AJAX validation handler
     *
     * @param optIn optIn   parameter
     * @return              JSON response
     */
    @SecureToken(value = VIEW_NAME)
    @RequestMapping(value = "/subscribe", method = RequestMethod.POST, headers = "X-Requested-With=XMLHttpRequest")
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public String updateSubscriptionDetails(@RequestParam("optInMarketing") boolean optIn) {

        User user = manageAdsHelper.getSessionUser(userSession);

        ApiResponse<Boolean> response = userServiceFacade.setMarketingPreference(user.getId(), optIn);

        JsonObject body = new JsonObject();
        if (!response.isDefined()) {
            body = reportableErrorToJsonObject(new ReportableErrorsMessageResolvingErrorSource(
                    new ReportableUserApiErrors(response.getError()), this.messageResolver));
        } else {
            body.add("successNotice", new JsonPrimitive(resolveMessage("account.manage.subscribe.update.success")));
        }

        return new Gson().toJson(body);
    }

    /**
     * Deactivates user adverts and account, logs out on success
     *
     * @param deactivateAccountBean deactivate account bean
     * @param bindingResult         binding result for errors
     * @param request               http request
     * @return                      redirect no success, page with errors on failure
     */
    @SecureToken(value = VIEW_NAME)
    @RequestMapping(value = "/deactivate", method = RequestMethod.POST)
    public ModelAndView initiateDeactivateAccount(@ModelAttribute("deactivateAccountBean") DeactivateAccountBean deactivateAccountBean,
                                                  BindingResult bindingResult, HttpServletRequest request) {

        User user = manageAdsHelper.getSessionUser(userSession);

        MyDetailsModel.Builder builder = MyDetailsModel.builder(getCoreModelBuilder(request))
                .withUpdateUserBean(getUpdateUserBean(user))
                .withContactEmails(contactEmailService.getEditable(userSession.getUser()))
                .withUserEmail(user.getEmail());

        DeactivationReason reason;
        try {
            reason = DeactivationReason.valueOf(deactivateAccountBean.getDeactivationReason());
        } catch (Exception ex) {
            bindingResult.rejectValue("deactivationReason", resolveMessage("account.manage.deactivate.invalid-reason"));
            builder.withErrors(populateErrors(new MvcReportableErrors(bindingResult)));
            return builder.build();
        }

        ApiResponse<Boolean> response = userServiceFacade.initiateAccountDeactivation(user.getId(), reason);
        if (!response.isDefined()) {
            builder.withErrors(new ReportableErrorsMessageResolvingErrorSource(
                    new ReportableUserApiErrors(response.getError()),
                    this.messageResolver));
            return builder.build();
        } else {
            return redirect(getUrlScheme().urlFor(Actions.BUSHFIRE_MANAGE_ACCOUNT));
        }
    }

    private ReportableErrorsMessageResolvingErrorSource populateCreatePasswordErrors(BindingResult bindingResult,
                                                                                     ApiCallResponse<User> response) {
        ReportableErrorsMessageResolvingErrorSource body;
        if (response.getErrorCode().equals(ApiErrorCode.INCORRECT_CREDENTIALS)) {
            bindingResult.rejectValue("currentPassword", resolveMessage("account.manage.password.create.invalid-password"));
            body = populateErrors(new MvcReportableErrors(bindingResult));
        } else {
            body = populateErrors(response);
        }
        return body;
    }

    private ReportableErrorsMessageResolvingErrorSource populateChangePasswordErrors(BindingResult bindingResult,
                                                                                     ApiResponse<GumtreeAccessToken> response) {
        UserApiErrorCode errorCode = UserServiceFacade.safeValueOf(response.getError().getErrorCode());
        switch (errorCode){
            case INCORRECT_CREDENTIALS:
                bindingResult.rejectValue("currentPassword", resolveMessage("account.manage.password.change.invalid-password"));
                break;
            case VALIDATION_ERROR:
                bindingResult.rejectValue("confirmedPassword", resolveMessage("account.manage.password.change.passwords-dont-match"));
                break;
            default:
                // do nothing
        }
        return populateErrors(new MvcReportableErrors(bindingResult));
    }

    private UpdateUserBean getUpdateUserBean(User user) {
        UpdateUserBean updateUserBean = new UpdateUserBean();
        updateUserBean.setFirstName(user.getFirstName());
        updateUserBean.setLastName(user.getLastName());
        updateUserBean.setContactPhone(user.getPhone());
        updateUserBean.setOptInMarketing(user.isOptInMarketing());
        return updateUserBean;
    }
}
