package com.gumtree.web.seller.page.postad.model;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.mobile.web.category.BrowseCategory;
import com.gumtree.web.common.path.Path;
import com.gumtree.web.seller.page.postad.controller.SellerTypePanel;
import com.gumtree.web.seller.page.postad.model.location.BrowseLocation;
import com.gumtree.web.seller.page.postad.model.location.MapDetails;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.service.legal.LegalBean;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

public class PostAdSubmitModel {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostAdSubmitModel.class);
    private static final String PANEL_ATTRIBUTE_BRANDS = "brands";

    @JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
    private final String redirectTo;
    private final List<String> panels;
    private final PostAdFormBean form;
    private final PricingMetadata pricingMetadata;
    private final List<PostAdImage> postAdImages;
    private final Integer maxNumberOfImages;
    private final Map<String, PostAdAttributeGroup> panelAttributes;
    private final List<PostAdAttributeGroup> postAdAttributeGroups;
    private final SellerTypePanel sellerType;
    private final BigDecimal totalPrice;
    private final Boolean showInsertionPrice;
    private final Boolean supportsChangeCategory;
    private final Boolean supportsChangeLocation;
    private final Boolean supportsContactUrl;
    private final Boolean supportsChangeVisibleOnMap;
    private final Boolean supportsPostToAnyLocation;
    private final List<BrowseLocation> locationCrumb;
    private final MapDetails mapDetails;
    private final List<BrowseCategory> categoryCrumb;
    private final Boolean validCategorySelected;
    private final Boolean validLocationSelected;
    private final LegalBean legal;
    private final String descriptionHint;
    private final String imagesHint;
    private final Long advertId;
    private final PostAdFormStatus status;
    private final Iterable<String> contactEmails;
    private final boolean userLoggedIn;
    private final Long alternativeCategoryId;
    private final java.util.Optional<PriceGuidance> priceGuidance;
    private Iterable<String> gaEvents;

    public PostAdSubmitModel(Builder builder) {
        this.redirectTo = builder.redirectTo != null ? builder.redirectTo.getPath() : null;
        this.form = builder.postAdFormBean;
        this.panels = ImmutableList.copyOf(Lists.transform(builder.panels, toId()));
        this.pricingMetadata = builder.pricingMetadata;
        this.postAdImages = builder.postAdImages;
        this.maxNumberOfImages = builder.maxNumberOfImages;
        this.panelAttributes = builder.panelAttributes;
        this.postAdAttributeGroups = builder.postAdAttributeGroups;
        this.totalPrice = builder.totalPrice;
        this.showInsertionPrice = builder.showInsertionPrice;
        this.supportsChangeCategory = builder.supportsChangeCategory;
        this.supportsChangeLocation = builder.supportsChangeLocation;
        this.supportsChangeVisibleOnMap = builder.supportsChangeVisibleOnMap;
        this.supportsPostToAnyLocation = builder.supportsPostToAnyLocation;
        this.supportsContactUrl = builder.supportsContactUrl;
        this.locationCrumb = builder.locationCrumb;
        this.mapDetails = builder.mapDetails;
        this.categoryCrumb = builder.categoryCrumb;
        this.validCategorySelected = builder.validCategorySelected;
        this.validLocationSelected = builder.validLocationSelected;
        this.legal = builder.legal;
        this.descriptionHint = builder.descriptionHint;
        this.imagesHint = builder.imagesHint;
        this.advertId = builder.advertId;
        this.sellerType = builder.sellerTypePanel;
        this.status = builder.status;
        this.userLoggedIn = builder.userLoggedIn;
        this.contactEmails =
                builder.contactEmails == null ? ImmutableList.<String>of() : ImmutableList.copyOf(builder.contactEmails);
        this.alternativeCategoryId = builder.alternativeCategoryId;
        this.priceGuidance = builder.priceGuidance;
        this.gaEvents = builder.gaEvents.orNull();
    }

    private Function<PostAdFormPanel, String> toId() {
        return new Function<PostAdFormPanel, String>() {

            @Nullable
            @Override
            public String apply(@Nullable PostAdFormPanel input) {
                return input.getId();
            }
        };
    }

    public String getRedirectTo() {
        return redirectTo;
    }

    public List<String> getPanels() {
        return panels;
    }

    public SellerTypePanel getSellerType() {
        return sellerType;
    }

    public PostAdFormBean getForm() {
        return form;
    }

    public PricingMetadata getPricingMetadata() {
        return pricingMetadata;
    }

    public List<PostAdImage> getPostAdImages() {
        return postAdImages;
    }

    public Integer getMaxNumberOfImages() {
        return maxNumberOfImages;
    }

    public Map<String, PostAdAttributeGroup> getPanelAttributes() {
        return panelAttributes;
    }

    public List<PostAdAttributeGroup> getPostAdAttributeGroups() {
        return postAdAttributeGroups;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public Boolean isShowInsertionPrice() {
        return showInsertionPrice;
    }

    public Boolean isSupportsChangeCategory() {
        return supportsChangeCategory;
    }

    public Boolean isSupportsChangeLocation() {
        return supportsChangeLocation;
    }

    public Boolean isSupportsChangeVisibleOnMap() {
        return supportsChangeVisibleOnMap;
    }

    public Boolean isSupportsPostToAnyLocation() {
        return supportsPostToAnyLocation;
    }

    public Boolean isSupportsContactUrl() {
        return supportsContactUrl;
    }

    public List<BrowseLocation> getLocationCrumb() {
        return locationCrumb;
    }

    public MapDetails getMapDetails() {
        return mapDetails;
    }

    public List<BrowseCategory> getCategoryCrumb() {
        return categoryCrumb;
    }

    public Boolean isValidCategorySelected() {
        return validCategorySelected;
    }

    public Boolean isValidLocationSelected() {
        return validLocationSelected;
    }

    public LegalBean getLegal() {
        return legal;
    }

    public String getDescriptionHint() {
        return descriptionHint;
    }

    public String getImagesHint() {
        return imagesHint;
    }

    public static Builder builder() {
        return new Builder();
    }

    public Long getAdvertId() {
        return advertId;
    }

    public PostAdFormStatus getStatus() {
        return status;
    }

    public Iterable<String> getContactEmails() {
        return contactEmails;
    }

    public boolean isUserLoggedIn() {
        return userLoggedIn;
    }

    public Long getAlternativeCategoryId() {
        return alternativeCategoryId;
    }

    public Iterable<String> getGaEvents() {
        return gaEvents;
    }

    public PriceGuidance getPriceGuidance() {
        return priceGuidance.orElse(null);
    }

    public static final class Builder {

        private List<PostAdFormPanel> panels;
        private PostAdFormBean postAdFormBean;
        private PricingMetadata pricingMetadata;
        private List<PostAdImage> postAdImages;
        private Integer maxNumberOfImages;
        private Map<String, PostAdAttributeGroup> panelAttributes;
        private List<PostAdAttributeGroup> postAdAttributeGroups;
        private BigDecimal totalPrice;
        private Boolean showInsertionPrice;
        private Boolean supportsChangeCategory;
        private Boolean supportsChangeLocation;
        private Boolean supportsChangeVisibleOnMap;
        private Boolean supportsPostToAnyLocation;
        private Boolean supportsContactUrl;
        private List<BrowseLocation> locationCrumb;
        private MapDetails mapDetails;
        private List<BrowseCategory> categoryCrumb;
        private Path redirectTo;
        private Boolean validCategorySelected;
        private Boolean validLocationSelected;
        private LegalBean legal;
        private String descriptionHint;
        private String imagesHint;
        private Long advertId;
        private SellerTypePanel sellerTypePanel;
        private PostAdFormStatus status;
        private Iterable<String> contactEmails;
        private boolean userLoggedIn;
        private com.google.common.base.Optional<Category> l1Category;
        private Long alternativeCategoryId;
        private java.util.Optional<PriceGuidance> priceGuidance = java.util.Optional.empty();
        private Optional<List<String>> gaEvents = Optional.absent();

        public Builder withForm(PostAdFormBean postAdFormBean) {
            this.postAdFormBean = postAdFormBean;
            return this;
        }

        public Builder withUserLoggedIn(boolean loggedIn) {
            this.userLoggedIn = loggedIn;
            return this;
        }

        public Builder withL1Category(com.google.common.base.Optional<Category> l1Category) {
            this.l1Category = l1Category;
            return this;
        }

        public Builder withPanels(List<PostAdFormPanel> panels) {
            this.panels = panels;
            return this;
        }

        public Builder addPanels(List<PostAdFormPanel> panels) {
            if (this.panels == null) {
                this.panels = new ArrayList<>();
            }
            try {
                panels.stream()
                        .filter(p -> !this.panels.contains(p))
                        .forEach(p -> this.panels.add(p));
            } catch (UnsupportedOperationException e) {
                LOGGER.error("Could not add new panels to the existing panels." +
                        " The collection is probably immutable, check how the previous panels have been inserted", e);
            }
            return this;
        }

        public Builder removePanels(List<PostAdFormPanel> panels) {
            if (this.panels == null) {
                this.panels = new ArrayList<>();
            }
            try {
                panels.stream()
                        .filter(p -> this.panels.contains(p))
                        .forEach(p -> this.panels.remove(p));
            } catch (UnsupportedOperationException e) {
                LOGGER.error("Could not remove new panels to the existing panels." +
                        " The collection is probably immutable, check how the previous panels have been removed", e);
            }
            return this;
        }

        public Builder withPanel(PostAdFormPanel panel) {
            if (this.panels == null) {
                this.panels = new ArrayList<>();
            }
            this.panels.add(panel);
            return this;
        }

        public Builder withPricingMetadata(PricingMetadata pricingMetadata) {
            this.pricingMetadata = pricingMetadata;
            return this;
        }

        public Builder withImages(List<PostAdImage> postAdImages) {
            this.postAdImages = postAdImages;
            return this;
        }

        public Builder withMaxImages(Integer maxNumberOfImages) {
            this.maxNumberOfImages = maxNumberOfImages;
            return this;
        }

        public Builder withPanelAttributes(List<PostAdAttributeGroup> postAdAttributeGroups) {

            /*
                We support two kinds of attribute panels that are displaying on SYI.
                1) Generic ATTRIBUTE_PANEL which actually represents sequence of pannels groupped in AttributeGroups.
                   The data required to populate these panels is provided in 'panelAttributes' list
                2) Custom panels like PRICE, PETS_BIRTHDAY or VEHICLE_SPECIFICATIONS. These panels
                   are rendered using custom templates. Metadata of attributes on panels are provided as a
                   'postAdAttributeGroups' property.
             */

            this.panelAttributes = postAdAttributeGroups.stream().
                    filter(g -> (!g.getPanelId().equals(PostAdFormPanel.ATTRIBUTE_PANEL.getId()))).
                    collect(Collectors.toMap(PostAdAttributeGroup::getPanelId, m -> m));

            this.postAdAttributeGroups = postAdAttributeGroups.stream().
                    filter(g -> (g.getPanelId().equals(PostAdFormPanel.ATTRIBUTE_PANEL.getId()))).
                    collect(toList());

            return this;
        }

        public Builder withTotalPrice(BigDecimal totalPrice) {
            this.totalPrice = totalPrice;
            return this;
        }

        public Builder withShowInsertionPrice(boolean showInsertionPrice) {
            this.showInsertionPrice = showInsertionPrice;
            return this;
        }

        public Builder supportsChangeCategory(boolean supportsChangeCategory) {
            this.supportsChangeCategory = supportsChangeCategory;
            return this;
        }

        public Builder supportsChangeLocation(boolean supportsChangeLocation) {
            this.supportsChangeLocation = supportsChangeLocation;
            return this;
        }

        public Builder supportsChangeVisibleOnMap(boolean supportsChangeVisibleOnMap) {
            this.supportsChangeVisibleOnMap = supportsChangeVisibleOnMap;
            return this;
        }

        public Builder withLocationCrumb(List<BrowseLocation> locationCrumb) {
            this.locationCrumb = locationCrumb;
            return this;
        }

        public Builder withMapDetails(Location location) {
            this.mapDetails = new MapDetails(location.getLatitude(), location.getLongitude(), location.getRadius());
            return this;
        }

        public Builder withCategoryCrumb(List<BrowseCategory> categoryCrumb) {
            this.categoryCrumb = categoryCrumb;
            return this;
        }

        public Builder redirectTo(Path path) {
            redirectTo = path;
            return this;
        }

        public Builder validCategorySelected(Boolean validCategorySelected) {
            this.validCategorySelected = validCategorySelected;
            return this;
        }

        public Builder validLocationSelected(Boolean leafLocationSelected) {
            this.validLocationSelected = leafLocationSelected;
            return this;
        }

        public PostAdSubmitModel build() {
            if (Categories.MOTORS.is(l1Category)) {
                this.alternativeCategoryId = Categories.OTHER_VEHICLES.getId();
            }

            return new PostAdSubmitModel(this);
        }

        public Builder withLegal(LegalBean legal) {
            this.legal = legal;
            return this;
        }

        public Builder withDescriptionHint(String descriptionHint) {
            this.descriptionHint = descriptionHint;
            return this;
        }

        public Builder withImagesHint(String imagesHint) {
            this.imagesHint = imagesHint;
            return this;
        }

        public Builder withAdvertId(Long advertId) {
            this.advertId = advertId;
            return this;
        }

        public Builder supportsContactUrl(Boolean supportsContactUrl) {
            this.supportsContactUrl = supportsContactUrl;
            return this;
        }

        public Builder supportsPostToAnyLocation(Boolean supportsPostToAnyLocation) {
            this.supportsPostToAnyLocation = supportsPostToAnyLocation;
            return this;
        }

        public Builder withSellerType(SellerTypePanel sellerTypePanel) {
            this.sellerTypePanel = sellerTypePanel;
            return this;
        }

        public Builder withStatus(PostAdFormStatus status) {
            this.status = status;
            return this;
        }

        public Builder withContactEmails(Iterable<String> contactEmails) {
            this.contactEmails = contactEmails;
            return this;
        }

        public Builder withGaEvents(List<String> gaEvents) {
            this.gaEvents = Optional.of(gaEvents);
            return this;
        }

        public Builder withPriceGuidance(java.util.Optional<PriceGuidance> priceGuidance) {
            if (priceGuidance != null) {
                this.priceGuidance = priceGuidance;
            }
            return this;
        }

        public boolean hasGaEvents() {
            return this.gaEvents.isPresent() && !this.gaEvents.get().isEmpty();
        }

        public boolean hasBrands(){
            return (this.panelAttributes!=null && this.panelAttributes.containsKey(PANEL_ATTRIBUTE_BRANDS));
        }

        public boolean isSellerTypeSelected() {
            return sellerTypePanel != null && sellerTypePanel.isSellerTypeSelected();
        }

    }
}
