package com.gumtree.web.seller.page.postad.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.client.executor.ApiCall;

/**
 * API call to validate an advert via the Bushfire API.
 */
public final class ValidateAdApiCall implements ApiCall<Void> {

    private PostAdvertBean postAdvertBean;

    /**
     * Constructor.
     *
     * @param postAdvertBean the bean to validate
     */
    public ValidateAdApiCall(PostAdvertBean postAdvertBean) {
        this.postAdvertBean = postAdvertBean;
    }

    @Override
    public Void execute(BushfireApi api) {
        api.advertApi().validateAd(postAdvertBean);
        return null;
    }

    public PostAdvertBean getPostAdvertBean() {
        return postAdvertBean;
    }
}
