package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class AdTitleHasPhoneNumberValidator implements ConstraintValidator<AdTitleHasPhoneNumberValidation, PostAdDetail> {
    private final ErrorMessageResolver errorMessageResolver;

    private String[] fieldList;
    private String message;

    @Autowired
    public AdTitleHasPhoneNumberValidator(ErrorMessageResolver errorMessageResolver) {
        this.errorMessageResolver = errorMessageResolver;
    }

    @Override
    public void initialize(AdTitleHasPhoneNumberValidation constraintAnnotation) {
        this.fieldList = constraintAnnotation.fieldList();
        this.message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(PostAdDetail value, ConstraintValidatorContext context) {
        final String errorMessage = errorMessageResolver.getMessage(message, "");
        final String title = ObjectUtils.firstNonNull(value.getPostAdFormBean().getTitle(), "");

        if (ValidationUtils.matches(title, ValidationUtils.ValidationRegexp.PHONE_NUMBER_REGEXP)) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(errorMessage)
                    .addPropertyNode(fieldList[0])
                    .addConstraintViolation();
            return false;
        }
        return true;
    }
}
