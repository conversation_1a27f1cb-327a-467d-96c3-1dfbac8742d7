package com.gumtree.web.seller.page.payment.converter;

import com.gumtree.api.domain.payment.ApiPayPalIPNBean;
import com.gumtree.web.filter.paypal.PayPalIPNRequest;
import org.springframework.stereotype.Component;

/**
 * Converts a request-scoped PayPal IPN request bean into a BAPI bean.
 */
@Component
public class PayPalIPNRequestToApiPayPalIPNBeanConverter {

    public ApiPayPalIPNBean convert(PayPalIPNRequest request) {
        ApiPayPalIPNBean apiPayPalIPNBean = new ApiPayPalIPNBean();
        apiPayPalIPNBean.setRequestBody(request.getBody());
        apiPayPalIPNBean.setContentType(request.getContentType());
        return apiPayPalIPNBean;
    }
}
