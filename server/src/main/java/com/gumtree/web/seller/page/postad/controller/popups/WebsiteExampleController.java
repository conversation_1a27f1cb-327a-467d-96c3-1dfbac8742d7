package com.gumtree.web.seller.page.postad.controller.popups;

import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import static com.gumtree.web.seller.page.postad.controller.popups.WebsiteExampleController.PAGE_PATH;


/**
 * Controller for the pop up example for the website url feature
 */
@Controller
@GumtreePage(PageType.PostAdFeatureWebsiteExample)
@RequestMapping(PAGE_PATH)
@GoogleAnalytics
public class WebsiteExampleController {

    public static final String PAGE_PATH = "/website-example";
    public static final String VIEW_NAME = "website-example";


    /**
     *
     * @return the view
     */
    @RequestMapping(method = RequestMethod.GET)
    public String showPage() {
        return VIEW_NAME;
    }
}
