package com.gumtree.web.seller.storage;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.mobile.web.storage.DefaultSessionDataService;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSessionBean;
import com.gumtree.web.security.UserSessionBeanImpl;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.manageads.model.ManageAdsFilterFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.storage.strategy.SessionPersistenceStrategy;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.util.StringUtils;

import static com.codahale.metrics.MetricRegistry.name;
import static com.gumtree.common.util.json.JsonSerializeUtils.readFromString;
import static com.gumtree.common.util.json.JsonSerializeUtils.writeToString;

public final class DefaultSellerSessionDataService extends DefaultSessionDataService implements SellerSessionDataService {
    private Counter adsCounter;
    private Counter adsWriteCounter;
    private Counter adsReadCounter;

    private Counter chkCounter;
    private Counter chkWriteCounter;
    private Counter chkReadCounter;

    private ObjectMapper mapper;
    private SessionPersistenceStrategy syiPersistenceStrategy;
    private SessionPersistenceStrategy syiPersistenceSimpleStrategy;
    private SessionPersistenceStrategy checkoutPersistenceStrategy;
    private SessionPersistenceStrategy checkoutPersistenceSimpleStrategy;
    private SessionPersistenceStrategy userPersistenceStrategy;
    private SessionPersistenceStrategy madFilterPersistenceStrategy;

    private int timeToLiveSeconds;
    private int completeCheckoutTimeToLiveSeconds;

    public DefaultSellerSessionDataService(ObjectMapper mapper, SessionPersistenceStrategy syiPersistenceStrategy,
                                           SessionPersistenceStrategy syiPersistenceSimpleStrategy,
                                           SessionPersistenceStrategy checkoutPersistenceStrategy,
                                           SessionPersistenceStrategy checkoutPersistenceSimpleStrategy,
                                           SessionPersistenceStrategy userPersistenceStrategy,
                                           SessionPersistenceStrategy madFilterPersistenceStrategy,
                                           int timeToLiveSeconds, int completeCheckoutTimeToLiveSeconds,
                                           MetricRegistry metricRegistry, UserSessionService userSessionService) {
        super(userSessionService);
        this.mapper = mapper;
        this.syiPersistenceStrategy = syiPersistenceStrategy;
        this.syiPersistenceSimpleStrategy = syiPersistenceSimpleStrategy;
        this.checkoutPersistenceStrategy = checkoutPersistenceStrategy;
        this.checkoutPersistenceSimpleStrategy = checkoutPersistenceSimpleStrategy;
        this.userPersistenceStrategy = userPersistenceStrategy;
        this.madFilterPersistenceStrategy = madFilterPersistenceStrategy;
        this.timeToLiveSeconds = timeToLiveSeconds;
        this.completeCheckoutTimeToLiveSeconds = completeCheckoutTimeToLiveSeconds;

        this.adsCounter = metricRegistry.counter(name(PostAdDetail.class, "adsPostedCounter"));
        this.adsWriteCounter = metricRegistry.counter(name(PostAdDetail.class, "adsWriteCounter"));
        this.adsReadCounter = metricRegistry.counter(name(PostAdDetail.class, "adsReadCounter"));

        this.chkCounter = metricRegistry.counter(name(CheckoutImpl.class, "chkPostedCounter"));
        this.chkWriteCounter = metricRegistry.counter(name(CheckoutImpl.class, "chkWriteCounter"));
        this.chkReadCounter = metricRegistry.counter(name(CheckoutImpl.class, "chkReadCounter"));
    }

    @Override
    public void setPostAdData(final String editorId, PostAdDetail postAdDetail) {
        final String json = writeToString(mapper, postAdDetail);
        adsWriteCounter.inc();
        syiPersistenceStrategy.writeOperation(handler -> handler.set(editorId, json, timeToLiveSeconds));
    }

    @Override
    public PostAdDetail getPostAdData(String editorId) {
        adsReadCounter.inc();
        String json = syiPersistenceStrategy.readOperation(editorId);
        return readFromString(mapper, json, PostAdDetail.class);
    }

    @Override
    public void removePostAdData(final String editorId) {
        adsCounter.inc();
        syiPersistenceStrategy.writeOperation(handler -> handler.del(editorId));
    }

    @Override
    public void setPostAdDataByEditorId(String editorId, PostAdDetail postAdDetail) {
        final String json = writeToString(mapper, postAdDetail);
        adsWriteCounter.inc();
        syiPersistenceSimpleStrategy.writeOperation(handler -> handler.set(editorId, json, timeToLiveSeconds));
    }

    @Override
    public PostAdDetail getPostAdDataByEditorId(String editorId) {
        adsReadCounter.inc();
        String json = syiPersistenceSimpleStrategy.readOperation(editorId);
        return readFromString(mapper, json, PostAdDetail.class);
    }

    @Override
    public void removePostAdDataByEditorId(String editorId) {
        adsCounter.inc();
        syiPersistenceSimpleStrategy.writeOperation(handler -> handler.del(editorId));
    }

    @Override
    public void setCheckout(final String checkoutId, Checkout checkout) {
        final String json = writeToString(mapper, checkout);
        final String orderJson = convertApiOrderToStorageKey(checkout.getOrder());
        chkWriteCounter.inc();
        checkoutPersistenceStrategy.writeOperation(handler -> {
            handler.set(checkoutId, json, timeToLiveSeconds);
            handler.set(orderJson, json, timeToLiveSeconds);
        });
    }

    @Override
    public void setCheckoutAll(String checkoutId, Checkout checkout) {
        final String json = writeToString(mapper, checkout);
        final String orderJson = convertApiOrderToStorageKey(checkout.getOrder());
        chkWriteCounter.inc();
        checkoutPersistenceStrategy.writeOperation(handler -> {
            handler.set(checkoutId, json, timeToLiveSeconds);
            handler.set(orderJson, json, timeToLiveSeconds);
        });

        checkoutPersistenceSimpleStrategy.writeOperation(handler -> {
            handler.set(checkoutId, json, timeToLiveSeconds);
            handler.set(orderJson, json, timeToLiveSeconds);
        });
    }

    @Override
    public Checkout getCheckout(String checkoutId) {
        String json = checkoutPersistenceStrategy.readOperation(checkoutId);

        if (StringUtils.isEmpty(json)){
            json=checkoutPersistenceSimpleStrategy.readOperation(checkoutId);
        }

        chkReadCounter.inc();
        return readFromString(mapper, json, CheckoutImpl.class);
    }

    @Override
    public Checkout getCheckoutByOrder(ApiOrder apiOrder) {
        String orderJson = convertApiOrderToStorageKey(apiOrder);
        String json = checkoutPersistenceStrategy.readOperation(orderJson);
        chkReadCounter.inc();

        return readFromString(mapper, json, CheckoutImpl.class);
    }

    @Override
    public void setCheckoutComplete(final String checkoutId) {
        Checkout checkout = getCheckout(checkoutId);
        chkCounter.inc();

        if (checkout != null) {
            final String orderJson = convertApiOrderToStorageKey(checkout.getOrder());
            checkoutPersistenceStrategy.writeOperation(handler -> {
                handler.expire(checkoutId, completeCheckoutTimeToLiveSeconds);
                handler.expire(orderJson, completeCheckoutTimeToLiveSeconds);
            });
        }
    }

    @Override
    public UserSessionBean getUserSessionBean(String loginName) {
        String json = userPersistenceStrategy.readOperation(loginName);

        if (json == null) {
            return new UserSessionBeanImpl();
        } else {
            return readFromString(mapper, json, UserSessionBeanImpl.class);
        }
    }

    @Override
    public void persistSessionBean(final String loginName, UserSessionBean userSessionBean) {
        final String json = writeToString(mapper, userSessionBean);
        userPersistenceStrategy.writeOperation(handler -> handler.set(loginName, json, timeToLiveSeconds));
    }

    @Override
    public void removeUserSessionBean(final String loginName) {
        userPersistenceStrategy.writeOperation(handler -> handler.del(loginName));
    }

    @Override
    public void extendUserSessionBean(final String loginName) {
        userPersistenceStrategy.writeOperation(handler -> handler.expire(loginName, timeToLiveSeconds));
    }

    @Override
    public ManageAdsFilterFormBean getManageAdsFilterForm(String filterFormId) {
        String json = madFilterPersistenceStrategy.readOperation(filterFormId);
        return json != null
                ? readFromString(mapper, json, ManageAdsFilterFormBean.class)
                : new ManageAdsFilterFormBean();
    }

    @Override
    public void setManageAdsFilterForm(final String filterFormId, ManageAdsFilterFormBean filterFormBean) {
        final String json = writeToString(mapper, filterFormBean);
        madFilterPersistenceStrategy.writeOperation(handler -> handler.set(filterFormId, json, timeToLiveSeconds));
    }

    @Override
    public void removeManageAdsFilterForm(final String filterFormId) {
        madFilterPersistenceStrategy.writeOperation(handler -> handler.del(filterFormId));
    }

    private String convertApiOrderToStorageKey(ApiOrder apiOrder) {
        StringBuilder builder = new StringBuilder();
        builder.append(apiOrder.getId())
                .append(apiOrder.getAccountId())
                .append(apiOrder.getTotalIncVat());

        return builder.toString();
    }
}
