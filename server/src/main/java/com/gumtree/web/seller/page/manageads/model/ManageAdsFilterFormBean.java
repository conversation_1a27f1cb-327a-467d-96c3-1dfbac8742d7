package com.gumtree.web.seller.page.manageads.model;

import org.codehaus.jackson.annotate.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * Manage ads filter form model
 */
@JsonIgnoreProperties({"status_list", "action" })
public final class ManageAdsFilterFormBean implements Serializable {

    private static final String ACTION = "/manage/ads";

    private ManageAdStatus status = ManageAdStatus.ACTIVE_ADS;

    private String searchTerms = "";

    public String getAction() {
        return ACTION;
    }

    public List<ManageAdStatus> getStatusList() {
        return Arrays.asList(ManageAdStatus.values());
    }

    public ManageAdStatus getStatus() {
        return status;
    }

    public void setStatus(ManageAdStatus v) {
        status = v;
    }

    public String getSearchTerms() {
        return searchTerms;
    }

    public void setSearchTerms(String searchTerms) {
        this.searchTerms = searchTerms;
    }
}
