package com.gumtree.web.seller.page.payment.controller;

import com.gumtree.api.ApiURL;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.ApiOrderItem;
import com.gumtree.seller.domain.order.status.OrderStatus;
import com.gumtree.seller.domain.payment.entity.PaymentMethod;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.domain.order.converter.ApiOrderToOrderEntityConverter;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.PageContextSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutAdvert;
import com.gumtree.web.seller.page.payment.braintree.PaymentService;
import com.gumtree.web.seller.page.payment.model.BillingAddress;
import com.gumtree.web.seller.page.payment.model.PaymentKeys;
import com.gumtree.web.seller.page.payment.reporting.ga.PaymentCheckoutGaConfigurer;
import com.gumtree.web.seller.page.payment.services.PaymentSuccessService;
import com.gumtree.web.seller.page.payment.util.PaymentSuccessUrl;
import com.gumtree.web.seller.page.postad.model.PaymentCheckoutModel;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.util.PaymentFeatureToggle;
import com.gumtree.web.zeno.checkout.CheckoutInput;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.checkout.CheckoutReviewEvent;
import com.gumtree.zeno.core.service.ZenoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.google.common.base.Strings.isNullOrEmpty;

@Controller
@GumtreePage(PageType.OrderReview)
@GoogleAnalytics(configurer = PaymentCheckoutGaConfigurer.class, contextConfigured = true)
public final class PaymentCheckoutController extends PageContextSellerController<Void> {
  public static final String PAGE_PATH = "/checkout/{checkoutKey}";
  public static final String BRAINTREE_TRANSACTION = "/braintree/{checkoutKey}";
  public static final String VIEW_NAME = "checkout";
  public static final String PAYMENT_ERROR_MESSAGE =
      "Please check your payment information and try again " +
          "or try alternative payment methods";
  private final BushfireApi bushfireApi;
  private final ApiOrderToOrderEntityConverter converter;
  private final CheckoutContainer checkoutContainer;
  private final UserSession authenticatedUserSession;
  private final ZenoService zenoService;
  private final String paymentReturnUrlResponsive;
  private final PaymentService paymentService;
  private final CustomMetricRegistry metrics;
  private final PaymentSuccessService paymentSuccessService;
  private final PaymentFeatureToggle paymentFeatureToggle = new PaymentFeatureToggle();

  @Autowired
  public PaymentCheckoutController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                   ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                   UrlScheme urlScheme, BushfireApi bushfireApi,
                                   ApiOrderToOrderEntityConverter converter, CheckoutContainer checkoutContainer,
                                   UserSession authenticatedUserSession, GumtreePageContext<Void> pageContext,
                                   CategoryService categoryService, LocationService locationService,
                                   @Value("${gumtree.url.seller.secure.base_uri}") String baseUri,
                                   ZenoService zenoService,
                                   PaymentService paymentService,
                                   UserSessionService userSessionService,
                                   CustomMetricRegistry metrics,
                                   PaymentSuccessService paymentSuccessService) {
    super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, pageContext, categoryService,
        locationService, userSessionService);
    this.bushfireApi = bushfireApi;
    this.converter = converter;
    this.checkoutContainer = checkoutContainer;
    this.authenticatedUserSession = authenticatedUserSession;
    this.paymentReturnUrlResponsive = isNullOrEmpty(baseUri)
        ? null : baseUri + PaymentConfirmationController.PAGE_PATH + "?return=true";
    this.zenoService = zenoService;
    this.paymentService = paymentService;
    this.metrics = metrics;
    this.paymentSuccessService = paymentSuccessService;
  }

  /**
   * Display of order checkout page
   *
   * @param checkoutKey - current basket's checkout key
   * @return model and view
   */
  @RequestMapping(value = PAGE_PATH, method = RequestMethod.GET)
  public ModelAndView checkoutPage(@PathVariable("checkoutKey") String checkoutKey, HttpServletRequest request) {
    long userId = authenticatedUserSession.getUser().getId();
    boolean isGrayScaleUser = paymentFeatureToggle.isInGrayScale(userId);

    Checkout checkout = checkoutContainer.getCheckout(checkoutKey);

    ApiOrder order = checkout.getOrder();

    CheckoutAdvert advert = checkout.getAdvert();
    if (OrderStatus.PAID.equals(order.getStatus())) {
      String url = paymentSuccessService.getPaymentSuccessUrl(checkout);
      return redirect(url);
    }

    if (advert != null) {
      populatePageContextWithCategoryLocation(advert.getCategoryId(), advert.getLocationId());
    }

    CoreModel.Builder coreBuilder = advert != null && advert.getCategoryId() != null
        ? getCoreModelBuilder(request, advert.getCategoryId())
        : getCoreModelBuilder(request);

    coreBuilder.withUserType(authenticatedUserSession.isProUser());

    PaymentKeys paymentKeys;
    if (isGrayScaleUser) {
      paymentKeys = paymentService.getPaymentKeys(order.getId(), userId);
    } else {
      paymentKeys = paymentService.getPaymentKeys(order.getId());
    }


    PaymentCheckoutModel.Builder builder = PaymentCheckoutModel.builder(coreBuilder)
        .withOrder(converter.convert(order))
        .withCheckoutKey(checkoutKey)
        .withCheckoutFormAction(checkoutFormAction(checkoutKey))
        .withClientBraintreeToken(paymentKeys.getToken())
        .withBraintreeEnvironment(paymentKeys.getEnv())
        .withMerchantId(paymentKeys.getMerchantId())
        .withCheckoutAmount(getCheckoutAmount(checkoutKey))
        .withPaymentFormEnabled(isPaymentFormEnabled(order))
        .withShowDropin(isGrayScaleUser);

    ModelAndView modelAndView = builder.build();

    zenoService.logEvent(
        new CheckoutInput(order, checkout, checkout.isCreateOrEdit() ? "createOrEdit" : "feature"),
        modelAndView.getViewName(), CheckoutReviewEvent.class);

    return modelAndView;
  }

  /**
   * Execute (pay for and action) the order
   *
   * @param checkoutKey the checkout id
   * @return redirect to order checkout display page
   */
  @RequestMapping(value = PAGE_PATH, method = RequestMethod.POST)
  public String paymentStuff(@PathVariable("checkoutKey") String checkoutKey) {
    BushfireApiKey apiKey = authenticatedUserSession.getApiKey();
    Checkout checkout = metrics.checkoutPageTimer("getCheckout")
        .record(() -> checkoutContainer.getCheckout(checkoutKey)); //note you are using the order id lookup

    OrderApi orderApi = bushfireApi.create(OrderApi.class, apiKey);
    ApiOrder order = metrics.checkoutPageTimer("executeTransaction").record(() ->
        orderApi.executeTransaction(checkout.getOrder().getId()));

    if (OrderStatus.UNPAID.equals(order.getStatus())) {

      ApiURL customPaymentUrl = metrics.checkoutPageTimer("getCustomPaymentUrl").record(() ->
          bushfireApi.create(OrderApi.class, apiKey).getCustomPaymentUrl(
              order.getId(),
              paymentReturnUrlResponsive,
              paymentReturnUrlResponsive,
              "responsive-seller-public"));

      zenoService.logEvent(new CheckoutInput(order, checkout,
              checkout.isCreateOrEdit() ? "checkout-click-paypal-createOrEdit"
                  : "checkout-click-paypal-feature"),
          customPaymentUrl.getUrl(), CheckoutReviewEvent.class);
      return createRedirect(customPaymentUrl.getUrl());
    } else {
      zenoService.logEvent(new CheckoutInput(order, checkout, "checkout-click-paypal-paid"),
          PaymentSuccessUrl.MAPPING, CheckoutReviewEvent.class);
      return createRedirect(paymentSuccessService.getPaymentSuccessUrl(checkout));
    }
  }

  /**
   * @param checkoutKey - current basket's checkout key
   * @return - path of this page
   */
  public static String checkoutFormAction(String checkoutKey) {
    return PAGE_PATH.replaceFirst("\\{checkoutKey\\}", checkoutKey);
  }

  private BigDecimal getCheckoutAmount(String checkoutKey) {
    Long amount = checkoutContainer.getCheckout(checkoutKey).getOrder().getTotalIncVat();
    return amount != null
        ? new BigDecimal(amount).divide(new BigDecimal(100), 2, RoundingMode.CEILING)
        : new BigDecimal(0L);
  }

  private boolean isPaymentFormEnabled(ApiOrder order) {
    for (ApiOrderItem item : order.getItems()) {
      boolean hasNonZeroPrice = item.getPriceIncVat() != null && item.getPriceIncVat() > 0;
      boolean notUsingCreditPackage = item.getPaymentDetail() == null ||
          !PaymentMethod.CREDIT_PACKAGE.equals(item.getPaymentDetail().getPaymentMethod());
      if (notUsingCreditPackage && hasNonZeroPrice) {
        return true;
      }
    }
    return false;
  }

  /**
   * Endpoint handling Braintree payments
   *
   * @param checkoutKey        from the checkout
   * @param paymentMethodNonce payment method chosen by the customer
   * @return
   */
  @RequestMapping(value = BRAINTREE_TRANSACTION, method = RequestMethod.POST)
  @ResponseBody
  public String paymentTransaction(@PathVariable("checkoutKey") String checkoutKey,
                                   @RequestParam("payment_method_nonce") String paymentMethodNonce,
                                   @RequestParam(value = "device_data", required = false) String deviceData,
                                   @RequestParam(value = "first_name", required = false) String firstName,
                                   @RequestParam(value = "last_name", required = false) String lastName,
                                   @RequestParam(value = "country", required = false) String country,
                                   @RequestParam(value = "address", required = false) String address,
                                   @RequestParam(value = "town_city", required = false) String townCity,
                                   @RequestParam(value = "postcode", required = false) String postcode) {

    DEVICETYPE deviceType = DEVICETYPE.fromLabel(getPageContext().getDeviceType());

    String platform = "responsive";
    String platformDevice = String.format("%s_%s", platform, deviceType.toString().toLowerCase());
    Checkout checkout = checkoutContainer.getCheckout(checkoutKey);
    String url = paymentService.paymentTransaction(checkout, paymentMethodNonce, getUser(),
        Optional.ofNullable(deviceData).orElse("n/a"), getAccountId(),
        BillingAddress.builder()
            .withAddress(address)
            .withFirstName(firstName)
            .withLastName(lastName)
            .withCountry(country)
            .withPostcode(postcode)
            .withTownCity(townCity)
            .build(),
        platformDevice);

    return url;
  }

  private String buildAdConfirmedAppsUrl(Long adId, String url) {
    try {
      UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
      if (adId != null) {
        builder.queryParam("advertId", adId);
      }
      return builder.build().toUri().toString();
    } catch (Exception e) {
      return url;
    }
  }

  private com.gumtree.api.User getUser() {
    return authenticatedUserSession.getUser();
  }

  private Long getAccountId() {
    return authenticatedUserSession.getSelectedAccountId();
  }

  public enum DEVICETYPE {
    PHONE("desktop-mobile"), TABLET("desktop-tablet"), DESKTOP("desktop-computer"), UNKNOWN("unknown");

    private static List<DEVICETYPE> devicetypes = Arrays.asList(DEVICETYPE.values());

    private String deviceType;

    DEVICETYPE(String deviceType) {
      this.deviceType = deviceType;
    }

    public static DEVICETYPE fromLabel(String label) {
      return DEVICETYPE.devicetypes.stream().filter(t -> t.deviceType.equals(label)).findFirst().orElse(DEVICETYPE.UNKNOWN);
    }
  }
}
