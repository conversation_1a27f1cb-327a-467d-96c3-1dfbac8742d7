package com.gumtree.web.seller.page.registration.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import org.springframework.web.servlet.ModelAndView;

public final class ConfirmationModel extends CommonModel {

    private final String emailAddress;
    private final String resendPath;
    private final boolean resendFlow;

    private ConfirmationModel(CoreModel core, Builder builder) {
        super(core);
        this.emailAddress = builder.emailAddress;
        this.resendPath = builder.resendPath;
        this.resendFlow = builder.resendFlow;
    }

    public String getResendPath() {
        return resendPath;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public boolean isResendFlow() {
        return resendFlow;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private String emailAddress;
        private String resendPath;
        private boolean resendFlow;

        public Builder withEmailAddress(String emailAddress) {
            this.emailAddress = emailAddress;
            return this;
        }

        public Builder withResendPath(String resendPath) {
            this.resendPath = resendPath;
            return this;
        }

        public Builder withResendFlow(boolean resendFlow) {
            this.resendFlow = resendFlow;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.RegistrationConfirmation;
            
            coreModelBuilder.withTitle("Registration Confirmation | My Gumtree - Gumtree");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new ConfirmationModel(coreModelBuilder.build(page), this));
        }

    }
}
