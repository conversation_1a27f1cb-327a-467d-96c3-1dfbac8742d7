package com.gumtree.web.seller.page;

import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.error.ErrorModel;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.Error;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import com.gumtree.zeno.core.util.JsonUtil;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.ModelAndView;

@Controller
public class DefaultSpringController {

    private RequestDetailsService requestDetailsService;

    private CoreModel.BuilderFactory builderFactory;

    @Autowired
    DefaultSpringController(RequestDetailsService requestDetailsService, CoreModel.BuilderFactory builderFactory) {
        this.requestDetailsService = requestDetailsService;
        this.builderFactory = builderFactory;
    }

    @RequestMapping
    @ResponseStatus(value = HttpStatus.NOT_FOUND)
    public ModelAndView pageNotFound(HttpServletRequest request) throws IOException {
        CoreModel.Builder coreBuilder = builderFactory.create(request);
        ModelAndView modelAndView = ErrorModel.builder()
                .coreBuilder(coreBuilder)
                .page(Page.Error404)
                .buildModelAndView();

        // the zeno interceptor is not called because of exception (404) so we need to add the
        // zeno data layer here
        modelAndView.addObject("zenoDataLayer", JsonUtil.toDataLayerJson(getError404Event()));
        return modelAndView;
    }

    private Error getError404Event() {
        PageData pageData = requestDetailsService.getPageData(PageType.Error_404);
        DeviceData deviceData = requestDetailsService.getDeviceData();
        UserData userData = requestDetailsService.getUserData();
        return new Error(pageData, userData, deviceData);
    }

}
