package com.gumtree.web.seller.page.manageads.skill;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.bapi.model.*;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.seller.page.manageads.skill.model.FacadeSkillCreateResponse;
import com.gumtree.web.seller.page.manageads.skill.model.FacadeSkillQueryResponse;
import com.gumtree.web.seller.page.manageads.skill.model.FacadeSkillUpdateResponse;
import com.gumtree.web.seller.service.skill.BapiSkillGateway;
import com.gumtree.web.seller.service.skill.SkillAttributeMetadataService;
import com.gumtree.web.seller.service.skill.model.SkillAttributeMetadata;
import com.gumtree.web.seller.page.manageads.skill.model.SkillAttributeResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.gumtree.web.seller.page.manageads.skill.converter.SkillResponseConverter.*;

@Controller
@RequestMapping("/api/skill")
public class SkillController extends BaseSellerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SkillController.class);

    private final BapiSkillGateway skillGateway;
    private final UserSession userSession;
    private final SkillAttributeMetadataService skillAttributeMetadataService;
    private static final List<Integer> GRAY_RELEASE_CATEGORIES_PHASE_1 = Arrays.asList(538, 4914);
    private static final List<Integer> GRAY_RELEASE_CATEGORIES_PHASE_2 = Arrays.asList(
            11312, 547, 11453, 147, 361, 11284, 1978, 11374, 11342, 11456, 11327, 11417,
            11313, 4911, 11404, 11299, 4917, 4913, 11321, 11414, 11390, 11316, 11422,
            4922, 11426, 4920, 4928, 11408, 11328, 11395, 11441, 11409, 11320, 10746,
            11319, 11443, 11375, 11419, 4927, 11311, 546, 11329, 11455, 11317, 11310,
            11452, 11323, 536, 11352, 135, 11464, 11382, 17, 11240, 11427, 11322,
            11465, 11421, 11458, 11293, 11461, 11378, 11420, 11411, 5186, 11424, 11309,
            11473, 11450, 11413, 11362, 138, 11340, 11454, 11357, 11416, 11314, 11381,
            11410, 11459, 11325, 11407, 11379, 11346, 11412, 4915, 11383,
            145, 4926, 11418, 11446, 11324, 4916, 11406, 11425, 11442, 11405, 11315);


    @Autowired
    public SkillController(BapiSkillGateway skillGateway,
                           CookieResolver cookieResolver,
                           CategoryModel categoryModel,
                           ApiCallExecutor apiCallExecutor,
                           ErrorMessageResolver messageResolver,
                           UrlScheme urlScheme,
                           UserSessionService userSessionService,
                           UserSession userSession,
                           ManageAdsHelper manageAdsHelper,
                           SkillAttributeMetadataService skillAttributeMetadataService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.skillGateway = skillGateway;
        this.userSession = userSession;
        this.skillAttributeMetadataService = skillAttributeMetadataService;
    }

    @RequestMapping(value = "/selected/{categoryId}", method = RequestMethod.GET)
    public ResponseEntity<FacadeSkillQueryResponse> getSelectedSkills(@PathVariable Integer categoryId) {
        try {
            Long accountId = userSession.getSelectedAccountId();
            SkillResponse response = skillGateway.getSelectedSkills(accountId, categoryId);
            FacadeSkillQueryResponse queryResponse = toQueryResponse(response);
            return new ResponseEntity<>(queryResponse, HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("Error getting selected skills", e);
            return new ResponseEntity<>(
                    toQueryErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()),
                    HttpStatus.OK);
        }
    }

    @RequestMapping(value = "/create", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<FacadeSkillCreateResponse> createSkill(@RequestBody SkillRequest skillRequest) {
        try {
            Long accountId = userSession.getSelectedAccountId();
            validateSkillRequest(skillRequest);
            Integer categoryId = skillRequest.getCategoryId();
            List<Integer> selectedSkills = skillRequest.getSelectedSkills();
            Boolean result = skillGateway.createSkill(accountId, skillRequest);
            if (Boolean.TRUE.equals(result)) {
                return new ResponseEntity<>(toCreateResponse(categoryId, selectedSkills), HttpStatus.OK);
            }
            return new ResponseEntity<>(
                    toCreateErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), "skill already existed or other error"),
                    HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            LOGGER.error("Invalid request: {}", e.getMessage());
            return new ResponseEntity<>(
                    toCreateErrorResponse(HttpStatus.BAD_REQUEST.value(), "Invalid request"),
                    HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("Error creating skill", e);
            return new ResponseEntity<>(
                    toCreateErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Something went wrong"),
                    HttpStatus.OK);
        }
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<FacadeSkillUpdateResponse> updateSkill(@RequestBody SkillRequest skillRequest) {
        try {
            Long accountId = userSession.getSelectedAccountId();
            validateSkillRequest(skillRequest);
            List<Integer> selectedSkills = skillRequest.getSelectedSkills();
            Integer categoryId = skillRequest.getCategoryId();
            SkillUpdateResponse response = skillGateway.updateSkill(accountId, skillRequest);
            if (Boolean.TRUE.equals(response.getSuccess())) {
                return new ResponseEntity<>(toUpdateResponse(categoryId, selectedSkills), HttpStatus.OK);
            }
            return new ResponseEntity<>(
                    toUpdateErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getMessage()),
                    HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            LOGGER.error("Invalid request: {}", e.getMessage());
            return new ResponseEntity<>(
                    toUpdateErrorResponse(HttpStatus.BAD_REQUEST.value(), "Invalid request"),
                    HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("Error updating skill", e);
            return new ResponseEntity<>(
                    toUpdateErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Something went wrong"),
                    HttpStatus.OK);
        }
    }

    private void validateSkillRequest(SkillRequest skillRequest) {
        Assert.notNull(skillRequest, "Skill request cannot be null");
        Assert.notNull(skillRequest.getCategoryId(), "Category ID in skill request cannot be null");
        Assert.isTrue(!CollectionUtils.isEmpty(skillRequest.getSelectedSkills()),
                "Selected skills in skill request cannot be empty");
        Assert.isTrue(skillLegalJudge(skillRequest.getCategoryId(), skillRequest.getSelectedSkills()),
                "Selected skills don't match");
    }

    @RequestMapping(value = "/attributes/{categoryId}", method = RequestMethod.GET)
    public ResponseEntity<SkillAttributeResponse> getSkillsByCategoryId(@PathVariable Integer categoryId) {
        try {
            List<SkillAttributeMetadata.SkillAttribute> skills = skillAttributeMetadataService.getSkillsByCategoryId(categoryId);
            SkillAttributeResponse response = new SkillAttributeResponse();
            List<SkillAttributeResponse.CategorySkills> categorySkillsList = new ArrayList<>();

            SkillAttributeResponse.CategorySkills categorySkills = getCategorySkills(categoryId, skills);
            categorySkillsList.add(categorySkills);
            response.setSkillAttributes(categorySkillsList);

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            LOGGER.error("Error getting skills by category", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private static SkillAttributeResponse.CategorySkills getCategorySkills(Integer categoryId, List<SkillAttributeMetadata.SkillAttribute> skills) {
        SkillAttributeResponse.CategorySkills categorySkills = new SkillAttributeResponse.CategorySkills();
        categorySkills.setCategoryId(categoryId.toString());

        List<SkillAttributeResponse.Skill> skillList = new ArrayList<>();
        for (SkillAttributeMetadata.SkillAttribute skill : skills) {
            SkillAttributeResponse.Skill responseSkill = new SkillAttributeResponse.Skill();
            responseSkill.setSkillId(skill.getSkillId());
            responseSkill.setContent(skill.getName());
            skillList.add(responseSkill);
        }

        categorySkills.setSkills(skillList);
        return categorySkills;
    }

    private boolean skillLegalJudge(Integer categoryId, List<Integer> selectedSkills) {
        List<SkillAttributeMetadata.SkillAttribute> skills = skillAttributeMetadataService.getSkillsByCategoryId(categoryId);
        Set<Integer> allSkills = skills.stream()
                .map(SkillAttributeMetadata.SkillAttribute::getSkillId)
                .map(Integer::valueOf)
                .collect(Collectors.toSet());
        return allSkills.containsAll(selectedSkills);
    }
} 
