package com.gumtree.web.seller.service;

import com.gumtree.api.Ad;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.model.CheckoutScene;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serializable;

/**
 *
 */
public interface CheckoutContainer extends Serializable {

    /**
     * Find the associated checkout
     *
     * @param checkoutId assigned id in map
     * @return checkout object
     */
    Checkout getCheckout(String checkoutId);

    /**
     * Remove the checkout object from the map
     *
     * @param checkoutId id for lookup
     */
    void markCheckoutAsComplete(String checkoutId);

    /**
     * Create a new checkout for the given order
     *
     * @param order the order
     * @return a checkout object
     */
    Checkout createCheckout(ApiOrder order);

    /**
     * Create a new checkout for the given order
     *
     * @param order the order
     * @param ad
     * @param createOrEdit true if the checkout is being created in response to an advert create or edit
     * @return a checkout object
     */
    Checkout createCheckout(ApiOrder order, Ad ad, boolean createOrEdit);

    /**
     * Create a new checkout for the given order Distinguish scenarios
     *
     * @param order the order
     * @param ad
     * @param createOrEdit true if the checkout is being created in response to an advert create or edit
     * @return a checkout object
     */
    Checkout createCheckout(ApiOrder order, Ad ad, boolean createOrEdit , CheckoutScene scene);

    /**
     * Exception class for non-found checkouts.
     */
    @ResponseStatus(value = HttpStatus.NOT_FOUND)
    class CheckoutNotFoundException extends RuntimeException {
        private Object checkoutId;

        public CheckoutNotFoundException(Object checkoutId) {
            super(checkoutId + " this checkout Id was not found.");
            if (checkoutId instanceof String) {
                this.checkoutId = checkoutId;
            }
        }

        public Object getCheckoutId() {
            return checkoutId;
        }

    }
}
