package com.gumtree.web.seller.page.postad.model;

import java.util.List;
import java.util.Objects;

public class PriceGuidance {
    private final long totalAds;
    private final List<PriceGuidanceAd> adverts;

    public PriceGuidance(long totalAds, List<PriceGuidanceAd> adverts) {
        this.totalAds = totalAds;
        this.adverts = adverts;
    }

    public long getTotalAds() {
        return totalAds;
    }

    public List<PriceGuidanceAd> getAdverts() {
        return adverts;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PriceGuidance that = (PriceGuidance) o;
        return totalAds == that.totalAds &&
                Objects.equals(adverts, that.adverts);
    }

    @Override
    public int hashCode() {

        return Objects.hash(totalAds, adverts);
    }
}
