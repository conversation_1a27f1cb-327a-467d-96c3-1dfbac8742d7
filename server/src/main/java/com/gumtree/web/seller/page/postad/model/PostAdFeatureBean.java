package com.gumtree.web.seller.page.postad.model;

import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.products.ProductType;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 *
 */
public class PostAdFeatureBean {


    private Map<ProductType, FeatureBean> features = new LinkedHashMap<>();
    private Long advert;

    public PostAdFeatureBean() {

    }

    public PostAdFeatureBean(Long advert) {
        this.advert = advert;
        setDefaultFeatureToSevenDays();
    }

    public Map<ProductType, FeatureBean> getFeatures() {
        return features;
    }

    public void setFeatures(Map<ProductType, FeatureBean> features) {
        this.features = features;
    }

    public Long getAdvert() {
        return advert;
    }

    public void setAdvert(Long advert) {
        this.advert = advert;
    }

    private void setDefaultFeatureToSevenDays() {

            FeatureBean featureBean = new FeatureBean();
            featureBean.setProductName(ProductName.FEATURE_7_DAY.name());
            featureBean.setSelected(false);
            features.put(ProductType.FEATURED, featureBean);

    }
}
