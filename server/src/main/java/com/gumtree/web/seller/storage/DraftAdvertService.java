package com.gumtree.web.seller.storage;

import com.google.common.base.Optional;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;


/**
 * Persists, retrieves and clears {@link PostAdDetail} based on user context
 */
public interface DraftAdvertService {


    /**
     * Retrieve data from storage
     *
     * @return value if found in storage, empty otherwise
     */
    Optional<PostAdDetail> retrieve();


    /**
     * Persists data in storage
     *
     * @param postAdDetail candidate to save
     * @return true if data persisted, false otherwise
     */
    boolean persist(PostAdDetail postAdDetail);

    /**
     * Clears storage for user
     */
    void clear();
}
