package com.gumtree.web.seller.interceptor;


import com.gumtree.analytics.GoogleAnalyticsService;
import com.gumtree.analytics.event.GAEvent;
import com.gumtree.analytics.event.LoginSuccess;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.seller.page.manageads.ManageAdsController;
import com.gumtree.web.seller.page.manageads.mydetails.MyDetailsController;
import com.gumtree.web.seller.page.messagecentre.MessageCentreController;
import com.gumtree.web.seller.page.postad.controller.BasePostAdController;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;
import java.util.UUID;

public class GoogleAnalyticsLoginSuccessInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private GoogleAnalyticsService googleAnalyticsService;

    @Autowired
    private CookieResolver cookieResolver;

//    @Autowired
//    private AbExperimentsService abExperimentsService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Optional isLoginSuccess =  Optional.ofNullable(request.getParameter("loginSuccess"));
        if (isLoginSuccess.isPresent() && isLoginSuccess.get().equals("true")) {
            googleAnalyticsService.sendAsyncEvent(getLoginSuccess(request, (HandlerMethod) handler));
//            GAEvent gaEvent = getLoginSuccess(request, (HandlerMethod) handler);
            if (!(((HandlerMethod)handler).getBean() instanceof BasePostAdController)) {
                response.setStatus(303);
                response.sendRedirect(request.getRequestURL().toString());
            }
        }
        return true;
    }

    // This interceptor is not used. Test participation should be fixed once it is going to be used again.
    private GAEvent getLoginSuccess(HttpServletRequest request, HandlerMethod method) {
        Optional<PermanentCookie> permanentCookie = Optional.ofNullable(cookieResolver.resolve(request, PermanentCookie.class));
//        MyAbExperiments myAbExperiments = abExperimentsService.createMyAbExperiments(request);
        return new LoginSuccess.Builder()
                .withClientId(permanentCookie.isPresent() ? permanentCookie.get().getId() : UUID.randomUUID().toString())
                .withEventCategory(getEventCategoryFor(method))
                //.withCustomDimension(28, myAbExperiments.getMyParticipationsAsString())
                .build();
    }

    private String getEventCategoryFor(HandlerMethod method) {

        if (method.getBean() instanceof ManageAdsController) {
            return PageType.MyAds.name();
        } else if (method.getBean() instanceof MyDetailsController) {
            return PageType.MyAccount.name();
        } else if (method.getBean() instanceof MessageCentreController) {
            return PageType.MyMessages.name();
        } else if (method.getBean() instanceof BasePostAdController) {
            return PageType.PostAd.name();
        }

        //default
        return PageType.MyAds.name();
    }



}
