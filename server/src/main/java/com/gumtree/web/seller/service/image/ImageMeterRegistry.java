package com.gumtree.web.seller.service.image;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ImageMeterRegistry {

    public static final String NAME = "media_upload_total";
    public static final String STATUS = "status";

    private final MeterRegistry meterRegistry;

    @Autowired
    public ImageMeterRegistry(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    public enum Status  {
        IMAGE_TOO_LARGE,
        TOO_MANY_IMAGES,
        UPLOADED,
        BAPI_ERROR,
        STORAGE_FAILURE,
        IMAGE_UNDER_FIVE_KB,
        IMAGE_OVER_FIVE_KB;
        @Override
        public String toString(){
            return name().toLowerCase();
        }
    }

    public void tooMany() {
        incr(Status.TOO_MANY_IMAGES);
    }

    public void tooLarge() {
        incr(Status.IMAGE_TOO_LARGE);
    }

    public void uploaded() {
        incr(Status.UPLOADED);
    }

    public void imageUnderFiveKb() { incr(Status.IMAGE_UNDER_FIVE_KB); }

    public void imageOverFiveKb() { incr(Status.IMAGE_OVER_FIVE_KB); }

    public void storageFailed() {
        incr(Status.STORAGE_FAILURE);
    }

    public void bapiError() {
        incr(Status.BAPI_ERROR);
    }

    private void incr(Status status) {
        Counter.builder(NAME)
                .tag(STATUS, status.name())
                .register(meterRegistry)
                .increment();
    }
}
