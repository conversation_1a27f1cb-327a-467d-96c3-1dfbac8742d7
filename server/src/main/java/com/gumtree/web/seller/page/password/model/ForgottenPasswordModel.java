package com.gumtree.web.seller.page.password.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.password.controller.ResetPasswordFormBean;
import org.springframework.web.servlet.ModelAndView;

public class ForgottenPasswordModel extends CommonModel {

    private ResetPasswordFormBean resetPasswordFormBean;

    public ForgottenPasswordModel() {
        // required by spring mvc to map models
    }

    private ForgottenPasswordModel(CoreModel core, Builder builder) {
        super(core);
        this.resetPasswordFormBean = builder.resetPasswordFormBean;
    }

    public ResetPasswordFormBean getForm() {
        return resetPasswordFormBean;
    }

    public void setForm(ResetPasswordFormBean resetPasswordFormBean) {
        this.resetPasswordFormBean = resetPasswordFormBean;
    }

    public String getFormAction() {
        return "/forgotten-password";
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private ResetPasswordFormBean resetPasswordFormBean;

        public Builder withResetPasswordForm(ResetPasswordFormBean resetPasswordFormBean) {
            this.resetPasswordFormBean = resetPasswordFormBean;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.ForgottenPassword;

            coreModelBuilder.withTitle("Forgotten Password | My Gumtree - Gumtree");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new ForgottenPasswordModel(coreModelBuilder.build(page), this));
        }

    }
}
