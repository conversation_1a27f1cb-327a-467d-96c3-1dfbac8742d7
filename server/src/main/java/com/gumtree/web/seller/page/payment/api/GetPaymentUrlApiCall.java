package com.gumtree.web.seller.page.payment.api;

import com.gumtree.api.ApiURL;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;

/**
 */
public final class GetPaymentUrlApiCall extends AuthenticatedApiCall<ApiURL> {

    private Long orderId;

    /**
     * Constructor
     *
     * @param apiKeyProvider - for the super constructor
     * @param orderId - which order to get for
     */
    public GetPaymentUrlApiCall(ApiKeyProvider apiKeyProvider, Long orderId) {
        super(apiKeyProvider);
        this.orderId = orderId;
    }

    @Override
    public ApiURL execute(BushfireApi api) {
        return api.create(OrderApi.class, getApiKey()).getPaymentUrl(orderId);
    }
}
