package com.gumtree.web.seller.page.openidconnect.service;

import com.google.common.base.Strings;
import com.gumtree.gas.JsonParsers;
import com.gumtree.gas.OIDCUserStore;
import com.gumtree.gas.UserInfo;
import com.gumtree.web.storage.strategy.SessionPersistenceStrategy;
import org.codehaus.jackson.map.ObjectMapper;
import scala.Option;
import scala.collection.JavaConversions;
import scala.collection.immutable.Set;

import static com.gumtree.common.util.json.JsonSerializeUtils.readFromString;
import static com.gumtree.common.util.json.JsonSerializeUtils.writeToString;

/**
 * Exiting Redis cache for seller bases its keys on current user session id
 * This causes problems for services that may connect from several locations
 * like Salesforce authenticating via SSO
 * <p/>
 * To fix that, we provide an alternate implementation that i
 */
public class OIDCRedisBackedUserStore implements OIDCUserStore {

    //keys live for 1 hour in the server
    private static final int TTL_SECONDS_KEYS = 60 * 60;
    private SessionPersistenceStrategy sessionPersistenceStrategy;
    private ObjectMapper mapper;

    public OIDCRedisBackedUserStore(SessionPersistenceStrategy sessionPersistenceStrategy, ObjectMapper mapper) {
        this.sessionPersistenceStrategy = sessionPersistenceStrategy;
        this.mapper = mapper;
    }

    @Override
    public Option<UserInfo> findByAuthorisationToken(String authCode) {
        return loadUserInfoByKey(authCode);
    }

    @Override
    public Option<UserInfo> findByAccessToken(String accessToken) {
        return loadUserInfoByKey(accessToken);
    }

    @Override
    public Option<UserInfo> findByRefreshToken(String refreshToken) {
        return loadUserInfoByKey(refreshToken);
    }

    @Override
    public void saveAuthorisationCodeAndRelatedInfo(final String authCode, final UserInfo userInfo, final Set<String> scopes) {
        saveUserInfoByKey(authCode, userInfo);

        // also save scopes along authorisation code
        final String scopesSJson = writeToString(mapper, JavaConversions.asJavaCollection(scopes));
        sessionPersistenceStrategy.writeOperation(handler -> handler.set(createScopesKey(authCode), scopesSJson, TTL_SECONDS_KEYS));
    }

    @Override
    public void saveAccessTokenWithUserInfo(final String accessToken, final UserInfo userInfo) {
        saveUserInfoByKey(accessToken, userInfo);
    }

    @Override
    public void saveRefreshTokenWithUserInfo(String refreshToken, UserInfo userInfo) {
        saveUserInfoByKey(refreshToken, userInfo);
    }

    @Override
    public void removeAuthCodeRelatedInfo(final String authCode) {
        sessionPersistenceStrategy.writeOperation(handler -> {
            handler.del(authCode);
            handler.del(createScopesKey(authCode));
        });
    }

    @Override
    public Option<Set<String>> getScopesFromAuthCode(String authCode) {
        Option<Set<String>> result = Option.empty();
        String json = sessionPersistenceStrategy.readOperation(createScopesKey(authCode));
        if (!Strings.isNullOrEmpty(json)) {
            java.util.Set<String> set = readFromString(mapper, json, java.util.Set.class);
            result = Option.apply(JavaConversions.asScalaSet(set).<String>toSet());
        }

        return result;
    }

    private String createScopesKey(String key) {
        return "scopes" + key;
    }

    private void saveUserInfoByKey(final String key, final UserInfo userInfo) {
        final String userInfoJson = JsonParsers.userInfoToJson(userInfo);
        sessionPersistenceStrategy.writeOperation(handler -> handler.set(key, userInfoJson, TTL_SECONDS_KEYS));
    }

    private Option<UserInfo> loadUserInfoByKey(String key) {
        Option<UserInfo> result = Option.empty();
        String json = sessionPersistenceStrategy.readOperation(key);
        if (!Strings.isNullOrEmpty(json)) {
            UserInfo userInfo = JsonParsers.jsonToUserInfo(json);
            result = Option.apply(userInfo);
        }

        return result;
    }
}
