package com.gumtree.web.seller.page.postad.service;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableSet;
import com.gumtree.api.category.domain.Category;
import com.gumtree.common.model.location.LocationCountryCounties;
import com.gumtree.common.model.location.impl.DefaultLocationCountryCounties;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.Set;

@Service
public class DefaultPostAdFormDescriptionHintService implements PostAdFormDescriptionHintService {

    private final CategoryService categoryService;
    private final LocationService locationService;
    private final LocationCountryCounties locationCountryCounties;
    private final Set<Long> categoriesWithImageTip;
    private final Set<Long> categoriesWithDescriptionTip;

    @Autowired
    public DefaultPostAdFormDescriptionHintService(
            CategoryService categoryService,
            LocationService locationService,
            LocationCountryCounties locationCountryCounties,
            @Value("#{'${gumtree.postad.hint.image.categories:2488}'.split(',')}") Long[] categoriesWithImageTip,
            @Value("#{'${gumtree.postad.hint.description.categories:2497}'.split(',')}") Long[] categoriesWithDescriptionTip) {
        this.categoryService = categoryService;
        this.locationService = locationService;
        this.locationCountryCounties = locationCountryCounties;
        this.categoriesWithImageTip = ImmutableSet.copyOf(categoriesWithImageTip);
        this.categoriesWithDescriptionTip = ImmutableSet.copyOf(categoriesWithDescriptionTip);
    }

    @Override
    @Nullable
    public String getHint(HintSection section, Long categoryId, Long locationId) {
        if (section == HintSection.DESCRIPTION) {
            return getHintForDescription(categoryId, locationId);
        } else if (section == HintSection.IMAGES) {
            if (isChildOfGivenCategories(categoryId, categoriesWithImageTip)) {
                return "<h3>Tip:</h3><p>Try to upload as many images as you can - having more images gets more views and replies!</p>";
            }
        }
        return null;
    }

    private boolean isChildOfGivenCategories(Long categoryId, Set<Long> categoriesWithTip) {
        if (categoriesWithTip.contains(categoryId)) {
            return true;
        }
        final Category currentCategory = categoryService.getById(categoryId).get();
        Optional<Category> parent = categoryService.getParent(currentCategory);
        while (parent.isPresent()) {
            if (categoriesWithTip.contains(parent.get().getId())) {
                return true;
            }
            parent = categoryService.getParent(parent.get());
        }
        return false;
    }

    private String getHintForDescription(Long categoryId, Long locationId) {
        if (propertyOffered(categoryId)) {
            if (inScotland(locationId)) {
                return "<h3>Remember to:</h3>"
                        + "<ul><li>Include your Landlord Registration Number</li>"
                        + "<li>Include the EPC (Energy Performance Certificate) ratings</li>"
                        + "<li>Mention any other costs that need to be paid, such as a deposit, "
                        + "utility bills, or agency fees.</li></ul>";

            } else {
                return "<h3>Remember to:</h3><ul>"
                        + "<li>Include the EPC (Energy Performance Certificate) ratings</li>"
                        + "<li>Mention any other costs that need to be paid, such as a deposit, "
                        + "utility bills, or agency fees.</li></ul>";
            }
        }
        if (isChildOfGivenCategories(categoryId, categoriesWithDescriptionTip)) {
            return "<h3>Remember:</h3><p>You can enter up to 10,000 characters - try to write as much of this as you can, " +
                    "as longer descriptions get more views and replies!</p>";
        }
        return null;
    }

    private boolean propertyOffered(Long categoryId) {
        return Categories.PROPERTY_TO_RENT.getId().equals(categoryId) || Categories.PROPERTY_TO_SHARE.getId().equals(categoryId);
    }

    private boolean inScotland(Long locationId) {
        Location county = locationService.getCounty(locationService.getById(locationId.intValue()));
        Location country = locationCountryCounties.getCountry(county);
        return country == DefaultLocationCountryCounties.SCOTLAND;
    }

}
