package com.gumtree.web.seller.page.postad.service.legal;

import com.gumtree.common.util.StringUtils;

public class LegalBean {

    private final String label;
    private final String message;
    private final Boolean required;

    public LegalBean(Builder builder) {
        this.required = StringUtils.hasText(builder.message);
        this.label = builder.label;
        this.message = builder.message;
    }

    public String getLabel() {
        return label;
    }

    public String getMessage() {
        return message;
    }

    public Boolean isRequired() {
        return required;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private String label;
        private String message;

        private Builder() {
        }

        public Builder withLabel(String label) {
            this.label = label;
            return this;
        }

        public Builder withMessage(String message) {
            this.message = message;
            return this;
        }

        public LegalBean build() {
            return new LegalBean(Builder.this);
        }
    }
}
