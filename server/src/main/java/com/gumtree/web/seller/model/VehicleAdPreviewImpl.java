package com.gumtree.web.seller.model;

import com.gumtree.domain.newattribute.Attribute;

import java.util.Collection;

/**
 * A Vehicle specific Ad Preview object which allows us to also expose vehicle attributes
 */
public class VehicleAdPreviewImpl extends AdPreviewImpl {

    private Collection<Attribute> vehicleAttributes;

    VehicleAdPreviewImpl() { }

    void setVehicleAttributes(Collection<Attribute> vehicleAttributes) {
        this.vehicleAttributes = vehicleAttributes;
    }

    public Collection<Attribute> getVehicleAttributes() {
        return vehicleAttributes;
    }
}
