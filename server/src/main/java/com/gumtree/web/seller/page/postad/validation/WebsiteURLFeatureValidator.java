package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.features.FeatureBean;
import com.gumtree.web.seller.page.postad.model.products.ProductType;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 */
public class WebsiteURLFeatureValidator implements ConstraintValidator<WebsiteURLFeatureValidation, PostAdFormBean> {

    private String message;

    private String field;

    @Override
    public final void initialize(WebsiteURLFeatureValidation constraintAnnotation) {
        this.message = constraintAnnotation.message();
        this.field = constraintAnnotation.field();
    }

    @Override
    public final boolean isValid(PostAdFormBean value, ConstraintValidatorContext context) {
        FeatureBean bean = value.getFeatures().get(ProductType.WEBSITE_URL);

        if (bean != null && bean.getSelected() != null && bean.getSelected()) {
            if (value.getWebsiteUrl() == null || value.getWebsiteUrl().length() == 0) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(message).addNode(field).addConstraintViolation();
            return false;
            }
        }

        return true;
    }
}
