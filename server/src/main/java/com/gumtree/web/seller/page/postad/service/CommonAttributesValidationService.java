package com.gumtree.web.seller.page.postad.service;

import java.io.Serializable;
import java.util.*;

import com.google.common.base.Optional;
import com.google.common.collect.Maps;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeValueMetadata;
import com.gumtree.api.util.SearchBeanRequestParams;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.stream.Collectors;

@Service
public class CommonAttributesValidationService implements Serializable {

  private static final long serialVersionUID = 1L;


  private static final Logger LOG = LoggerFactory.getLogger(
          CommonAttributesValidationService.class);

  private final CategoryModel categoryModel;

  private static final Map<Long, Set<SearchBeanRequestParams>> CATEGORY_AND_VALIDATION_ATTRIBUTES_MAP
          = Maps.newHashMap();

  static {
    //'DIY Tools & Materials' Category need check 'DIY_TOOLS_MATERIALS_CONDITION' attribue.
    CATEGORY_AND_VALIDATION_ATTRIBUTES_MAP.put(10683L,
            Arrays.asList(SearchBeanRequestParams.DIY_TOOLS_MATERIALS_CONDITION)
                    .stream().collect(Collectors.toSet()));

    // Home&Garden Category need check 'COMMON_FOR_SALE_CONDITION' attribue.
    CATEGORY_AND_VALIDATION_ATTRIBUTES_MAP.put(2514L,
            Arrays.asList(SearchBeanRequestParams.COMMON_FOR_SALE_CONDITION)
                    .stream().collect(Collectors.toSet()));
  }

  @Autowired
  public CommonAttributesValidationService(CategoryModel categoryModel) {
    this.categoryModel = categoryModel;
  }

  private Long matchCategoryWithLoop(Long categoryId) {
    for (Long configId : CATEGORY_AND_VALIDATION_ATTRIBUTES_MAP.keySet()) {
      //categoryId is configId's child or equal to configId
      if (categoryModel.isChildOrEqual(configId, categoryId)) {
        return configId;
      }
    }
    return null;
  }

  /**
   * Validates if the passed  attributes are valid for the given category.
   *
   * @param categoryId The ID of the category to check against.
   * @param attributes A map of attributes containing the category-related properties.
   * @return true if all  attributes are valid for the given category, false otherwise.
   */
  public boolean isValidAttributeValuePassed(Long categoryId, Map<String, String> attributes) {
    Long matchCategoryId = matchCategoryWithLoop(categoryId);
    if (CollectionUtils.isEmpty(CATEGORY_AND_VALIDATION_ATTRIBUTES_MAP)
            || Objects.isNull(matchCategoryId)) {
      return true;
    }
    // find this categoryId need check attributes
    Set<SearchBeanRequestParams> needCheckAttributes = CATEGORY_AND_VALIDATION_ATTRIBUTES_MAP.get(matchCategoryId);
    if (CollectionUtils.isEmpty(needCheckAttributes)
            || CollectionUtils.isEmpty(attributes)) {
      LOG.warn("category {}  not config need checked attributes, or attributes is empty", matchCategoryId);
      return true;
    }
    return validateAttributes(needCheckAttributes, attributes, categoryId);
  }

  /**
   * Helper method: Validates the list of attributes.
   *
   * @param attributeList A set of attribute names to validate.
   * @param attributes    A map of attributes containing the properties to be validated.
   * @param categoryId    The ID of the category to check against.
   * @return true if all attributes are valid, false otherwise.
   */
  private boolean validateAttributes(Set<SearchBeanRequestParams> attributeList,
                                     Map<String, String> attributes, Long categoryId) {
    attributes.replaceAll((key, value) -> StringUtils.lowerCase(value));
    // Iterate through each attribute in the list
    boolean result = true;
    for (SearchBeanRequestParams attributeParam : attributeList) {
      String attribute = attributeParam.getName();
      if (attributes.containsKey(attribute)) {
        Optional<AttributeMetadata> attributeMetadata =
                categoryModel.findAttributesByNameForGivenCategory(attribute, categoryId);

        if (attributeMetadata.isPresent()) {
          List<AttributeValueMetadata> listOfAttributes =
                  attributeMetadata.get().getValues();
          String providedAttributesValue = attributes.get(attribute);
          // Check for null values and validate the provided attribute value
          if (providedAttributesValue == null
                  || !isValidAttributes(providedAttributesValue, listOfAttributes)) {
            result = false;
            break;
          }
        }
      }
    }
    return result;
  }

  private boolean isValidAttributes(String attributeValue, List<AttributeValueMetadata> listOfAttributes) {
    return isMatched(attributeValue, listOfAttributes);
  }

  private boolean isMatched(String attributeValue, List<AttributeValueMetadata> listOfAttributes) {
    return listOfAttributes.stream()
            .anyMatch(attributeValueMetadata -> attributeValueMetadata.getValue()
                    .equalsIgnoreCase(attributeValue));
  }
}
