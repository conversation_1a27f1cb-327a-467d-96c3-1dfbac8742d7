package com.gumtree.web.seller.service.location;

import com.gumtree.domain.location.Location;
import com.gumtree.seller.infrastructure.driven.locations.api.LocationsApi;
import com.gumtree.service.location.LocationService;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;

import static com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState.OUTCODE_RECOGNISED;
import static com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState.POSTCODE_INVALID;
import static com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState.POSTCODE_MISSING;
import static com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState.POSTCODE_NOT_FOUND;
import static com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState.POSTCODE_RECOGNISED;

@Service
public final class DefaultPostAdLocationService implements PostAdLocationService {
    private static final Logger LOG = LoggerFactory.getLogger(DefaultPostAdLocationService.class);
    /**
     * copied from bapi: PostcodeValidatorImpl.UK_POSTCODE_REG_EX
     */
    public static final Pattern UK_POSTCODE_REG_EX = Pattern.compile(
            "^(GIR 0AA|[A-PR-UWYZ]([0-9][0-9A-HJKPS-UW]?|[A-HK-Y][0-9][0-9ABEHMNPRV-Y]?)\\s*"
                    + "[0-9][ABD-HJLNP-UW-Z]{2})$");

    private final LocationService locationService;
    private final LocationsApi locationsApi;


    @Autowired
    public DefaultPostAdLocationService(LocationService locationService, LocationsApi locationsApi) {
        this.locationService = locationService;
        this.locationsApi = locationsApi;
    }

    @Override
    public PostcodeLookupResponse lookupPostcode(String input) {
        if (StringUtils.isBlank(input)) {
            return new PostcodeLookupResponse(POSTCODE_MISSING, input);
        }

        if (!UK_POSTCODE_REG_EX.matcher(input.toUpperCase()).matches()) {
            return new PostcodeLookupResponse(POSTCODE_INVALID, input);
        }

        try {
            Optional<PostcodeLookupResponse> postcode = locationsApi.getPostcodeByName(input)
                    .map(Optional::ofNullable)
                    .map(postcodeOpt ->
                            postcodeOpt.map(p -> new PostcodeLookupResponse(POSTCODE_RECOGNISED, input, p.getLeafLocationId())))
                    .toBlocking()
                    .value();

            if (postcode.isPresent()) {
                return postcode.get();
            }

            String outcodeInput = new PostcodeSplitter(input).getOutcode();
            Optional<PostcodeLookupResponse> outcode = outcodeInput != null
                    ? locationsApi.getOutcodeByName(outcodeInput)
                    .map(Optional::ofNullable)
                    .map(outcodeOpt -> outcodeOpt.map(o -> new PostcodeLookupResponse(OUTCODE_RECOGNISED, input, o.getLeafLocationId())))
                    .toBlocking()
                    .value()
                    : Optional.empty();

            if (outcode.isPresent()) {
                return outcode.get();
            }
        } catch (Exception exc) {
            LOG.warn("locations get postcode/outcode by name failed", exc);
        }

        return new PostcodeLookupResponse(POSTCODE_NOT_FOUND, input);
    }

    @Override
    public Location get(Long id) {
        return id == null ? null : locationService.getById(id.intValue());
    }

    @Override
    public Boolean hasZoomIn(Location location) {
        return locationService.hasZoomIn(location);
    }

    @Override
    public Boolean isCounty(Location location) {
        return locationService.isCounty(location);
    }

    @Override
    public Boolean isInArea(Location location, Integer areaLocId) {
        Map<Integer, Location> hierarchy = locationService.getLocationHierarchy(location);
        return hierarchy.values().stream().map(x->x.getId()).anyMatch(x->x.equals(areaLocId));
    }
}
