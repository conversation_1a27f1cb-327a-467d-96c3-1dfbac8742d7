package com.gumtree.web.seller.page.manageads.mycompany;

import com.google.common.base.Optional;
import com.gumtree.api.CreditPackage;
import com.gumtree.api.PackageUsage;
import com.gumtree.api.PackageUsageSearchResponse;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.util.time.Clock;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.Link;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.SelectableValue;
import com.gumtree.web.seller.page.common.SimpleSelectableValue;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.seller.page.manageads.mycompany.model.DisplayCreditPackageUsage;
import com.gumtree.web.seller.page.manageads.mycompany.model.PackageStatusFilter;
import com.gumtree.web.seller.page.manageads.mycompany.model.PackageUsageModel;
import com.gumtree.web.seller.page.manageads.mycompany.service.PackageUsageService;
import com.gumtree.zeno.core.domain.PageType;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Stream;

import static com.gumtree.web.seller.page.manageads.mycompany.model.DisplayCreditPackageUsage.Builder;
import static java.util.stream.Collectors.toList;

/**
 * Controller that displays package usage details for a specific package
 */
@Controller
@GoogleAnalytics
public final class PackageUsageController extends BaseSellerController {

    public static final String LIGHTBOX_PAGE_PATH = "/manage/company/packages/{id}";
    public static final String FULL_PAGE_PATH = "/manage/company/packages/usage";

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormat.forPattern("dd/MM/yyyy");

    private final PackageUsageService packageUsageService;
    private final ManageAdsHelper manageAdsHelper;
    private final UserSession userSession;
    private final Clock clock;

    @Autowired
    public PackageUsageController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                  ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                  UrlScheme urlScheme, UserSession userSession,
                                  PackageUsageService packageUsageService, Clock clock,
                                  ManageAdsHelper manageAdsHelper, UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.packageUsageService = packageUsageService;
        this.clock = clock;
        this.manageAdsHelper = manageAdsHelper;
        this.userSession = userSession;
    }

    /**
     * Show lightbox package usage page (credit usages for a specific package)
     *
     * @param packageId the package id
     * @param page      the page number
     * @param pageSize  the page size
     * @return view
     */
    @GumtreePage(PageType.AccountPackageUsageLight)
    @RequestMapping(value = LIGHTBOX_PAGE_PATH, method = RequestMethod.GET)
    public ModelAndView showLightboxPage(
            HttpServletRequest request,
            @PathVariable("id") long packageId,
            @RequestParam(value = "page", required = false, defaultValue = "1") int page,
            @RequestParam(value = "pageSize", required = false, defaultValue = "1000") int pageSize) {

        PackageUsageSearchResponse creditUsages = packageUsageService.getCreditUsagesForPackage(
                userSession.getSelectedAccountId(), packageId, page, pageSize);

        return renderPage(creditUsages, page, pageSize, true).build(getCoreModelBuilder(request));
    }

    /**
     * Show full package usage history page (credit usages for an account)
     *
     * @param packageTypeId the package type id filter
     * @param from          the from (applied date) filter
     * @param to            to the to (applied date) filter
     * @param status        the package status filter
     * @param page          the page number
     * @param pageSize      the page size
     * @return full package usage history page template name.
     */
    @GumtreePage(PageType.AccountPackageUsageFull)
    @RequestMapping(value = FULL_PAGE_PATH, method = RequestMethod.GET)
    public ModelAndView showFullPage(
            HttpServletRequest request,
            @RequestParam(value = "package_type", defaultValue = "", required = false) Long packageTypeId,
            @RequestParam(value = "from", defaultValue = "", required = false) String from,
            @RequestParam(value = "to", defaultValue = "", required = false) String to,
            @RequestParam(value = "status", defaultValue = "ACTIVE", required = false) PackageStatusFilter status,
            @RequestParam(value = "page", required = false, defaultValue = "1") int page,
            @RequestParam(value = "pageSize", required = false, defaultValue = "1000") int pageSize) {

        DateTime toDate = processToDate(to);
        DateTime fromDate = processFromDate(from);

        PackageUsageSearchResponse creditUsages = packageUsageService.getCreditUsagesForAccount(
                userSession.getSelectedAccountId(),
                packageTypeId,
                fromDate,
                toDate,
                status,
                page,
                pageSize);

        return renderPage(creditUsages, page, pageSize, false).
                withPackageTypeId(packageTypeId).
                withFrom(DATE_FORMATTER.print(fromDate)).
                withTo(DATE_FORMATTER.print(toDate)).
                withStatus(status.getValue()).
                withPackageTypes(getPackageTypes()).
                build(getCoreModelBuilder(request));
    }

    private DateTime processToDate(String dateString) {
        return (parseDate(dateString)).or(() -> clock.getDateTime().withTimeAtStartOfDay().toDateTime());
    }

    private DateTime processFromDate(String dateString) {
        return parseDate(dateString).or(() -> clock.getDateTime().withTimeAtStartOfDay().minusMonths(1).toDateTime());
    }

    private Link getCurrentPage() {
        return new SimpleLink("Package usage history", getUrlScheme().urlFor(Actions.BUSHFIRE_PACKAGE_USAGE));
    }

    private Optional<DateTime> parseDate(String dateString) {
        try {
            return Optional.of(DATE_FORMATTER.parseDateTime(dateString).withTimeAtStartOfDay());
        } catch (Exception ex) {
            return Optional.absent();
        }
    }

    private PackageUsageModel.Builder renderPage(
            PackageUsageSearchResponse results,
            int page,
            int pageSize,
            boolean isLightBoxView) {

        return new PackageUsageModel.Builder(manageAdsHelper).
                withPackageUsages(convertToDisplayModel(results.getPackageUsages())).
                withPage(page).
                withPageSize(pageSize).
                withLightbox(isLightBoxView).
                withAccountSelectionForm(manageAdsHelper.getAccountSelectionForm(userSession)).
                withAccount(packageUsageService.getAccount(userSession.getSelectedAccountId())).
                withCurrentPage(getCurrentPage());
    }

    private List<SelectableValue> getPackageTypes() {
        List<CreditPackage> creditPackages = packageUsageService.getCreditPackagesForAccount(
                userSession.getSelectedAccountId(), PackageStatusFilter.ALL);

        return Stream.concat(
                Stream.of(new SimpleSelectableValue("", "All products")),
                creditPackages.stream().map(this::convertCreditPackageToSelectableValue)
            ).collect(toList());
    }

    private SimpleSelectableValue convertCreditPackageToSelectableValue(CreditPackage p) {
        return new SimpleSelectableValue(String.valueOf(p.getPackageTypeId()), p.getName());
    }

    private List<DisplayCreditPackageUsage> convertToDisplayModel(List<PackageUsage> usages) {
        return usages.stream().
                map(input -> new Builder().creditPackageUsage(input).build()).
                collect(toList());
    }

}
