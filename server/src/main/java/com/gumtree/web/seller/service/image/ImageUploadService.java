package com.gumtree.web.seller.service.image;

import com.gumtree.api.Image;
import org.springframework.web.multipart.MultipartFile;
import rx.Single;

/**
 * Service for uploading images.
 */
public interface ImageUploadService {

    /**
     * Upload an image.
     *
     * @param file the image file.
     * @return api call response
     */
    Image uploadImage(MultipartFile file);

    Single<Image> uploadImageRX(MultipartFile image);

    Image postImage(MultipartFile multipartFile);

}
