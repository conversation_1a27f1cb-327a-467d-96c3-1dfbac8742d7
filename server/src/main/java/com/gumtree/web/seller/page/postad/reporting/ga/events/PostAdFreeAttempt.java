package com.gumtree.web.seller.page.postad.reporting.ga.events;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsNamedTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsNamedTrackEventName;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEventAction;

public class PostAdFreeAttempt extends GoogleAnalyticsNamedTrackEvent {

    public PostAdFreeAttempt(ThirdPartyRequestContext<?> ctx) {
        super(GoogleAnalyticsNamedTrackEventName.POST_AD_FREE_ATTEMPT, ctx.getPageType(),
                GoogleAnalyticsTrackEventAction.POST_AD_FREE_ATTEMPT);
    }

}
