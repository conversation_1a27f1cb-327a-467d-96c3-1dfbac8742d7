package com.gumtree.web.seller.page.messagecentre;

import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.config.SellerProperty;
import com.gumtree.util.RxUtils;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.domain.messagecentre.Conversations;
import com.gumtree.web.common.domain.messagecentre.PollingFrequencies;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.PageNotFoundException;
import com.gumtree.web.seller.page.UnauthorizedAccessException;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.service.ContactEmailService;
import com.gumtree.zeno.core.domain.MessagesData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.messages.LoadMessagesEvent;
import com.gumtree.zeno.core.service.ZenoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import rx.Single;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;

import static com.gumtree.util.model.Actions.BUSHFIRE_MESSAGE_CENTRE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

@Controller
@GumtreePage(PageType.MyMessages)
public class MessageCentreController extends BaseSellerController {

    private static final String BASE_URL = "/manage/messages";
    private static final String TITLE = "Messages | My Account | Gumtree";

    private final UserSession userSession;
    private final ManageAdsHelper manageAdsHelper;
    private final ZenoService zenoService;
    private final ContactEmailService contactEmailService;
    private final AppBannerCookieHelper appBannerCookieHelper;

    private PropSupplier<Boolean> messageCentreEnabled = GtProps.getDBool(
        SellerProperty.MESSAGE_CENTRE_ENABLED);

    @Autowired
    public MessageCentreController(CookieResolver cookieResolver,
        CategoryModel categoryModel,
        ApiCallExecutor apiCallExecutor,
        ErrorMessageResolver messageResolver,
        UrlScheme urlScheme,
        UserSession userSession,
        ManageAdsHelper manageAdsHelper,
        UserSessionService userSessionService,
        ZenoService zenoService,
        ContactEmailService contactEmailService,
        AppBannerCookieHelper appBannerCookieHelper) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme,
            userSessionService);
        this.userSession = userSession;
        this.manageAdsHelper = manageAdsHelper;
        this.zenoService = zenoService;
        this.contactEmailService = contactEmailService;
        this.appBannerCookieHelper = appBannerCookieHelper;
    }

    @RequestMapping(value = BASE_URL, method = RequestMethod.GET)
    public ModelAndView showMessageCentre(
        @RequestParam(value = "conversationId", required = false) String conversationId,
        @RequestParam(value = "rvw", required = false) Long reviewId,
        @RequestParam(value = "rvw_conversationId", required = false) String reviewConversationId,
        HttpServletRequest request) {

        checkPreconditionsToAccessConversations();

        appBannerCookieHelper.updateAppBannerCookie(AppBannerCookieHelper.Action.MESSAGE_CENTER,
            request);

        MessageCentreModel.Builder modelBuilder = prepareMessageCentreModelBuilder();

        ModelAndView mv = modelBuilder.build(getCoreModel(request), Page.MessageCentre);

        manageAdsHelper.addManageAdsUrls(mv.getModel(),
            new SimpleLink("My messages", urlScheme.urlFor(BUSHFIRE_MESSAGE_CENTRE)));

        zenoService.logEvent(new MessagesData(getContactEmail()), mv, LoadMessagesEvent.class);
        return mv;
    }

    private MessageCentreModel.Builder prepareMessageCentreModelBuilder() {
        String contactEmail = getContactEmail();
        MessageCentreModel.Builder modelBuilder = new MessageCentreModel.Builder();
        modelBuilder.withContactEmail(contactEmail);
        modelBuilder.withMessageCentreErrors(true);
        return modelBuilder;
    }

    @RequestMapping(value = "/conversations", produces = "application/json")
    @ResponseBody
    public ResponseEntity getConversationsBy(
        @RequestParam(value = "page", defaultValue = "0") Integer page,
        @RequestParam(value = "size", defaultValue = "100") Integer size) {
        checkPreconditionsToAccessConversations();

        Conversations conversations = Conversations.builder()
                .setNumUnreadConversations(0)
                .setNumConversations(0)
                .setConversationGroups(Collections.emptyList())
                .setPollingFrequencies(new PollingFrequencies(60, 900))
                .build();

        return RxUtils.singleToResponseEntity(Single.just(conversations), "Unable to get conversations for an user " + getUserId());
    }



    @RequestMapping(value = "/conversations/unread", method = GET,
        headers = "X-Requested-With=XMLHttpRequest",
        produces = "application/json;charset=UTF-8")
    public ResponseEntity numberOfUnreadConversastions() {
        return Single.just(0).to(RxUtils::singleToWebApiResponseEntity);
    }

    private String getContactEmail() {
        return contactEmailService.getPreferred(userSession.getUser());
    }

    private void checkPreconditionsToAccessConversations() {
        if (!messageCentreEnabled()) {
            throw new PageNotFoundException("message center is disabled");
        } else if (!userLoggedIn()) {
            throw new UnauthorizedAccessException();
        }
    }

    private boolean messageCentreEnabled() {
        return messageCentreEnabled.get();
    }

    private boolean userLoggedIn() {
        return userSession.isAuthenticated();
    }

    private Long getUserId() {
        User user = userSession.getUser();
        return user.getId();
    }

    private CoreModel.Builder getCoreModel(HttpServletRequest request) {
        CoreModel.Builder builder = getCoreModelBuilder(request);
        builder.withTitle(TITLE);
        return builder;
    }
}
