package com.gumtree.web.seller.page.common.model;


import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.web.common.domain.converter.Conversions;

public final class SummaryAttribute {
    private final String attributeName;
    private final String attributeDisplayName;
    private final String value;
    private final String valueDisplayName;

    private SummaryAttribute(Builder builder) {
        this.attributeName = builder.attributeName;
        this.attributeDisplayName = builder.attributeDisplayName;
        this.value = builder.value;
        this.valueDisplayName = builder.valueDisplayName;
    }

    public static Builder builder() {
        return new Builder();
    }

    public String getAttributeName() {
        return attributeName;
    }

    public String getAttributeDisplayName() {
        return attributeDisplayName;
    }

    public String getValue() {
        return value;
    }

    public String getValueDisplayName() {
        return valueDisplayName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SummaryAttribute)) return false;

        SummaryAttribute that = (SummaryAttribute) o;

        if (attributeDisplayName != null ? !attributeDisplayName.equals(that.attributeDisplayName) : that.attributeDisplayName != null) {
            return false;
        }
        if (attributeName != null ? !attributeName.equals(that.attributeName) : that.attributeName != null) return false;
        if (value != null ? !value.equals(that.value) : that.value != null) return false;
        if (valueDisplayName != null ? !valueDisplayName.equals(that.valueDisplayName) : that.valueDisplayName != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = attributeName != null ? attributeName.hashCode() : 0;
        result = 31 * result + (attributeDisplayName != null ? attributeDisplayName.hashCode() : 0);
        result = 31 * result + (value != null ? value.hashCode() : 0);
        result = 31 * result + (valueDisplayName != null ? valueDisplayName.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "SummaryAttribute{" +
                "attributeName='" + attributeName + '\'' +
                ", attributeDisplayName='" + attributeDisplayName + '\'' +
                ", value='" + value + '\'' +
                ", valueDisplayName='" + valueDisplayName + '\'' +
                '}';
    }

    public static class Builder implements Conversions.AttributeBuilder<SummaryAttribute> {
        private String attributeName;
        private String attributeDisplayName;
        private String value;
        private String valueDisplayName;

        @Override
        public SummaryAttribute build() {
            return new SummaryAttribute(this);
        }

        @Override
        public Builder setValue(String value) {
            this.value = value != null ? value : null;
            return this;
        }

        @Override
        public Builder setValueDisplayName(String value) {
            this.valueDisplayName = value;
            return this;
        }

        @Override
        public Builder setAttributeMetadata(AttributeMetadata metadata) {
            this.attributeName = metadata.getName();
            this.attributeDisplayName = metadata.getLabel();
            return this;
        }

        public Builder setAttributeName(String attributeName) {
            this.attributeName = attributeName;
            return this;
        }

        public Builder setAttributeDisplayName(String attributeDisplayName) {
            this.attributeDisplayName = attributeDisplayName;
            return this;
        }
    }
}
