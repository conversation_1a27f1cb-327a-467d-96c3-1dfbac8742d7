package com.gumtree.web.seller.security.apiauthentication.controller;

import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.security.apiauthentication.JsonWebTokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Controller
public class ApiAuthenticationController {

    private UserSession userSession;
    private JsonWebTokenService jsonWebTokenService;

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiAuthenticationController.class);

    @Autowired
    public ApiAuthenticationController(UserSession userSession,
                                       JsonWebTokenService jsonWebTokenService) {
        this.userSession = userSession;
        this.jsonWebTokenService = jsonWebTokenService;
    }

    @RequestMapping(method = RequestMethod.POST, value = "/api/authenticate")
    public ResponseEntity<String> authenticateApi() {
        try {
            String token = jsonWebTokenService.generateTokenForUser(userSession.getUser());
            return new ResponseEntity<>(token, HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.warn("An error occurred while creating the Json Web Token", e);
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
    }
}
