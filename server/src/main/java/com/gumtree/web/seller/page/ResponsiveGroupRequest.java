package com.gumtree.web.seller.page;


import com.gumtree.web.security.UserSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Provides current request's responsive status based on 'rg' query parameter.
 * Responsive group 'rg' is set from chase email with potential values
 * 'a', 'b', and 'none'. 'a' denotes responsive.
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Component
public class ResponsiveGroupRequest {

    private final UserSession userSession;

    @Autowired
    public ResponsiveGroupRequest(UserSession userSession) {
        this.userSession = userSession;
    }

    public boolean isResponsiveGroup() {
        return !userSession.isProUser();
    }

    public String getGroup() {
        return !userSession.isProUser() ? "responsive" : "non-responsive";
    }
}
