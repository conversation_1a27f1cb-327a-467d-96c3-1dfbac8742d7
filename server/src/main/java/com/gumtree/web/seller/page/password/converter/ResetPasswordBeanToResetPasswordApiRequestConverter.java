package com.gumtree.web.seller.page.password.converter;

import com.gumtree.api.client.request.EmailRequest;
import com.gumtree.web.seller.page.password.controller.ResetPasswordFormBean;

/**
 * Converter for converting password reset beans.
 */
public interface ResetPasswordBeanToResetPasswordApiRequestConverter {
    /**
     * Convert the supplied bean.
     * @param bean to convert
     * @return EmailRequest for JAPI
     */
    EmailRequest convert(ResetPasswordFormBean bean);
}
