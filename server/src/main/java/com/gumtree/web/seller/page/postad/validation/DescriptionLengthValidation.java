package com.gumtree.web.seller.page.postad.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({TYPE, ANNOTATION_TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = DescriptionLengthValidator.class)
@Documented
public @interface DescriptionLengthValidation {

    /**
     * message
     *
     * @return
     */
    String message() default "{com.moa.podium.util.constraints.matches}";

    /**
     * groups
     *
     * @return
     */
    Class<?>[] groups() default {};

    /**
     * payload
     *
     * @return
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * fieldset
     *
     * @return
     */
    String[] fieldList();
}


