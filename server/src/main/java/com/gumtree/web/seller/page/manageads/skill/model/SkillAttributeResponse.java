package com.gumtree.web.seller.page.manageads.skill.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class SkillAttributeResponse {
    @JsonProperty("skilll-attributes")
    private List<CategorySkills> skillAttributes;

    @Setter
    @Getter
    public static class CategorySkills {
        @JsonProperty("categoryId")
        private String categoryId;

        @JsonProperty("skills")
        private List<Skill> skills;

    }

    @Setter
    @Getter
    public static class Skill {
        @JsonProperty("skillId")
        private String skillId;

        @JsonProperty("content")
        private String content;

    }
}
