package com.gumtree.web.seller.page.password.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import org.springframework.web.servlet.ModelAndView;

public final class ForgottenPasswordConfirmationModel extends CommonModel {

    private final String emailAddress;

    private ForgottenPasswordConfirmationModel(CoreModel core, Builder builder) {
        super(core);
        this.emailAddress = builder.emailAddress;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private String emailAddress;


        public Builder withEmailAddress(String emailAddress) {
            this.emailAddress = emailAddress;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.ForgottenPasswordConfirmation;
            coreModelBuilder.withTitle("Forgotten Password Confirmation | My Gumtree - Gumtree");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new ForgottenPasswordConfirmationModel(coreModelBuilder.build(page), this));
        }

    }
}
