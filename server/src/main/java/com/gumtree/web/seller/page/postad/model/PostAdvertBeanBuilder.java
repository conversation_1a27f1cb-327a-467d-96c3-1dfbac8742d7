package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.Image;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.domain.attribute.ApiAttribute;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Builder for {@link PostAdvertBean}.
 */
public final class PostAdvertBeanBuilder {

    private PostAdvertBean bean;

    private static final String OUT_PATTERN = "yyyyMMdd";
    private static final String IN_PATTERN = "dd/MM/yyyy";

    /**
     * Constructor.
     */
    public PostAdvertBeanBuilder() {
        bean = new PostAdvertBean();
        bean.setAttributes(new ArrayList<ApiAttribute>());
        bean.setVisibleOnMap(false);
    }

    /**
     * Set category id.
     *
     * @param categoryId the category id
     * @return this
     */
    public PostAdvertBeanBuilder categoryId(Long categoryId) {
        bean.setCategoryId(categoryId);
        return this;
    }

    /**
     * Set title
     *
     * @param title the title
     * @return this
     */
    public PostAdvertBeanBuilder title(String title) {
        bean.setTitle(title);
        return this;
    }

    /**
     * Set description
     *
     * @param description the description
     * @return this
     */
    public PostAdvertBeanBuilder description(String description) {
        bean.setDescription(description);
        return this;
    }

    /**
     * Set postcode
     *
     * @param postcode the postcode
     * @return this
     */
    public PostAdvertBeanBuilder postcode(String postcode) {
        bean.setPostcode(postcode != null ? postcode.trim() : "");
        return this;
    }

    /**
     * Set localArea
     *
     * @param localArea the localArea
     * @return this
     */
    public PostAdvertBeanBuilder localArea(String localArea) {
        if (StringUtils.hasLength(localArea)) {
            bean.setArea(localArea);
        }
        return this;
    }

    /**
     * Set visibleOnMap
     *
     * @param visibleOnMap the visibleOnMap flag
     * @return this
     */
    public PostAdvertBeanBuilder visibleOnMap(Boolean visibleOnMap) {
        if (visibleOnMap == null) {
            bean.setVisibleOnMap(false);
        } else {
            bean.setVisibleOnMap(visibleOnMap);
        }
        return this;
    }

    /**
     * Set email
     *
     * @param email the email
     * @return this
     */
    public PostAdvertBeanBuilder contactEmail(String email) {
        bean.setContactEmail(email);
        return this;
    }

    /**
     * Set phone number
     *
     * @param phone the phone number
     * @return this
     */
    public PostAdvertBeanBuilder contactPhone(String phone) {
        bean.setContactTelephone(phone);
        return this;
    }

    /**
     * Set accountId
     *
     * @param accountId the accountId
     * @return this
     */
    public PostAdvertBeanBuilder accountId(Long accountId) {
        bean.setAccountId(accountId);
        return this;
    }

    /**
     * Set extend fields
     *
     * @param key   the attribute name
     * @param value the attribute value
     *
     * @return this builder
     */
    public PostAdvertBeanBuilder addExtendFields(String key, String value) {
        bean.getExtendFields().put(key, value);
        return this;
    }

    public PostAdvertBeanBuilder addAllExtendFields(Map<String,String> map) {
        if (!map.isEmpty()) {
            bean.getExtendFields().putAll(map);
        }
        return this;
    }

    /**
     * Set category id.
     *
     * @param key   the attribute name
     * @param value the attribute value
     * @param attributeType the attribute type
     *
     * @return this builder
     */
    public PostAdvertBeanBuilder attribute(String key, String value, AttributeType attributeType) {

        if (StringUtils.hasLength(key) && StringUtils.hasLength(value)) {
            ApiAttribute attribute = new ApiAttribute();
            attribute.setName(key);
            attribute.setValue(formatAttributeValue(value, attributeType));
            bean.getAttributes().add(attribute);
        }

        return this;
    }

    /**
     * Set images
     *
     * @param images the images
     * @return this
     */
    public PostAdvertBeanBuilder images(List<Image> images) {
        List<Long> imageIds = new ArrayList<>();

        for (Image image : images) {
            imageIds.add(image.getId());
        }

        bean.setImageIds(imageIds);

        return this;
    }

    public PostAdvertBeanBuilder imageIds(List<Long> imageIds) {
        Assert.notNull(imageIds, "imageIds should not be null");
        bean.setImageIds(imageIds);
        return this;
    }

    /**
     * Set main image id
     *
     * @param mainImageId the main image id
     * @return this
     */
    public PostAdvertBeanBuilder mainImageId(Long mainImageId) {
        bean.setMainImageId(mainImageId);
        return this;
    }

    /**
     * Set manual location id.
     *
     * @param locationId the location id
     * @return this
     */
    public PostAdvertBeanBuilder locationId(Long locationId) {
        bean.setLocationId(locationId);
        return this;
    }

    /**
     * @param ipAddress the ip address
     * @return this
     */
    public PostAdvertBeanBuilder ipAddress(String ipAddress) {
        bean.setIp(ipAddress);
        return this;
    }

    /**
     * @param threatmetrixSessionId the threatmetrix session ID
     * @return this
     */
    public PostAdvertBeanBuilder threatmetrixSessionId(String threatmetrixSessionId) {
        bean.setThreatmetrixSessionId(threatmetrixSessionId);
        return this;
    }

    /**
     * @param cookie the permanent cookie id
     * @return this
     */
    public PostAdvertBeanBuilder cookie(String cookie) {
        bean.setCookie(cookie);
        return this;
    }

    /**
     * @param contactUrl the contact url
     * @return this
     */
    public PostAdvertBeanBuilder contactUrl(String contactUrl) {
        bean.setContactUrl(contactUrl);
        return this;
    }

    /**
     * @param contactName the contact name
     * @return this
     */
    public PostAdvertBeanBuilder contactName(String contactName) {
        bean.setContactName(contactName);
        return this;
    }

    /**
     * @param youtubeLink the youtube link
     * @return this
     */
    public PostAdvertBeanBuilder youtubeLink(String youtubeLink) {
        bean.setYoutubeLink(youtubeLink);
        return this;
    }

    /**
     * @param websiteUrl the website link
     * @return this
     */
    public PostAdvertBeanBuilder websiteUrl(String websiteUrl) {
        if (StringUtils.hasLength(websiteUrl)) {
            bean.setWebsiteUrl(websiteUrl);
        }
        return this;
    }

    /**
     * @return the prepared {@link PostAdvertBean}.
     */
    public PostAdvertBean build() {
        bean.setPlatform("seller");
        return bean;
    }

    private String formatAttributeValue(String value, AttributeType attributeType) {

        if (attributeType != null) {
            switch (attributeType) {
                case CURRENCY:
                    try {
                        return new BigDecimal(value.replaceAll(",", "").trim()).multiply(
                                new BigDecimal(100)).toString();
                    } catch (NumberFormatException ex) {
                        return value;
                    }
                case DATETIME:
                    try {
                        Date date = new SimpleDateFormat(IN_PATTERN).parse(value.trim());
                        SimpleDateFormat df = new SimpleDateFormat(OUT_PATTERN);
                        return df.format(date);
                    } catch (ParseException e) {
                        return value;
                    }
                default:
                    return value;
            }
        }

        return value;
    }
}
