package com.gumtree.web.seller.page.payment.reporting.ga;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsCustomVar;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.impl.PostAdBegin;
import com.gumtree.web.reporting.google.ga.impl.AbstractGooogleAnalyticsConfigurer;
import com.gumtree.web.seller.page.ResponsiveGroupRequest;
import com.gumtree.web.seller.reporting.ResponsiveGoogleAnalyticsConfigurer;
import com.gumtree.web.seller.page.payment.reporting.ga.events.OrderCancel;
import com.gumtree.web.seller.page.payment.reporting.ga.events.OrderConfirm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PaymentCheckoutGaConfigurer extends AbstractGooogleAnalyticsConfigurer<Void> {

    private ResponsiveGroupRequest responsiveGroupRequest;

    @Autowired
    public PaymentCheckoutGaConfigurer(ResponsiveGroupRequest responsiveGroupRequest) {
        this.responsiveGroupRequest = responsiveGroupRequest;
    }

    @Override
    public void configure(GoogleAnalyticsReportBuilder reportBuilder, ThirdPartyRequestContext<Void> ctx) {
        reportBuilder
                .pageType(ctx.getPageType())
                .addTrackEvent(new OrderConfirm(ctx))
                .addTrackEvent(new OrderCancel(ctx))
                .addTrackEvent(new PostAdBegin(ctx));

        reportBuilder.addCustomVar(new GoogleAnalyticsCustomVar(ResponsiveGoogleAnalyticsConfigurer.CUSTOM_VAR,
                ResponsiveGoogleAnalyticsConfigurer.GROUP_NAME, responsiveGroupRequest.getGroup(), 3));
    }
}

