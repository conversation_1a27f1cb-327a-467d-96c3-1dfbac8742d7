package com.gumtree.web.seller.service.user.forgotpassword;

import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.ResetPasswordRequest;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.zeno.core.event.user.sellerside.password.PasswordResetEmailSent;
import com.gumtree.zeno.core.service.ZenoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DefaultPasswordResetService implements PasswordResetService {

    private final UserServiceFacade userServiceFacade;
    private final ZenoService zenoService;

    @Autowired
    public DefaultPasswordResetService(UserServiceFacade userServiceFacade, ZenoService zenoService) {
        this.userServiceFacade = userServiceFacade;
        this.zenoService = zenoService;
    }

    @Override
    public void resetPassword(String username) {
        // call reset password action.
        // Note that we ignore the result (error or success) as in all cases we move the user to the next page (success)
        // This is to fix GUMBUG-2490, users with valid data will get the password, malicious users
        // won't be able to see if the email they used is registered with gumtree or not
        ResetPasswordRequest resetPasswordRequest = new ResetPasswordRequest();
        resetPasswordRequest.setUsername(username);
        ApiResponse<Boolean> resetPwdResp = userServiceFacade.resetPassword(resetPasswordRequest);
        if(resetPwdResp.isDefined()) {
            zenoService.logEvent(new String[0], Page.ForgottenPassword.getTemplateName(), PasswordResetEmailSent.class);
        }
    }
}
