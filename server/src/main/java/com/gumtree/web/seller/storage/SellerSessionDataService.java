package com.gumtree.web.seller.storage;

import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.mobile.web.storage.SessionDataService;
import com.gumtree.web.security.UserSessionBean;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.ManageAdsFilterFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.security.UserSessionDataService;

/**
 * Service to handle persisting all 'session' data for a user in a flow on the site. For example post ad, orders.
 */
public interface SellerSessionDataService extends UserSessionDataService, SessionDataService {

    /**
     * Persist a post ad session with all its data
     *
     * @param editorId - identifier for the post ad session
     * @param postAdDetail - detail containing all submitted data for an ad in process of being posted
     */
    void setPostAdData(String editorId, PostAdDetail postAdDetail);

    /**
     * Persist a post ad by editorId
     *
     * @param editorId - identifier for the post ad session
     * @param postAdDetail - detail containing all submitted data for an ad in process of being posted
     */
    void setPostAdDataByEditorId(String editorId, PostAdDetail postAdDetail);

    /**
     * Retrieve a post ad session with all its data
     *
     * @param editorId - identifier for the post ad session
     * @return detail containing all submitted data for an ad in process of being posted
     */
    PostAdDetail getPostAdData(String editorId);
    /**
     * Retrieve a post ad by editorId
     *
     * @param editorId - identifier for the post ad session
     * @return detail containing all submitted data for an ad in process of being posted
     */
    PostAdDetail getPostAdDataByEditorId(String editorId);

    /**
     * Remove a post ad session from persistence
     *
     * @param editorId - identifier for the post ad session
     */
    void removePostAdData(String editorId);

    /**
     * Remove a post ad by editorId
     *
     * @param editorId - identifier for the post ad session
     */
    void removePostAdDataByEditorId(String editorId);

    /**
     * Persists order checkout data. Either new checkout or updated.
     *
     * @param checkoutId - identifier for the checkout session
     * @param checkout - detail containing all data for an order that is being processed
     */
    void setCheckout(String checkoutId, Checkout checkout);

    /**
     * Persists order checkout data. Either new checkout or updated.
     *
     * @param checkoutId - identifier for the checkout session
     * @param checkout - detail containing all data for an order that is being processed
     */
    void setCheckoutAll(String checkoutId, Checkout checkout);

    /**
     * Retrieve a checkout with all its data
     *
     * @param checkoutId - identifier for the checkout session
     * @return detail containing all data for an order that is being processed
     */
    Checkout getCheckout(String checkoutId);

    /**
     * Retrieve a checkout with all its data, identified by the given ApiOrder
     *
     * @param apiOrder - identifier for the order that a checkout should exist for
     * @return detail containing all data for an order that is being processed
     */
    Checkout getCheckoutByOrder(ApiOrder apiOrder);

    /**
     * Sets an existing order checkout data to be in 'completed' state.
     *
     * @param checkoutId - identifier for the checkout session
     */
    void setCheckoutComplete(String checkoutId);

    UserSessionBean getUserSessionBean(String loginName);

    /**
     * Gets a MAD filter object
     *
     * @param filterFormId - identifier for the filter
     * @return detail containing MAD filter
     */
    ManageAdsFilterFormBean getManageAdsFilterForm(String filterFormId);

    /**
     * Persists a MAD filter object
     *
     * @param filterFormId - identifier for the filter
     * @param filterFormBean - detail containing MAD filter
     */
    void setManageAdsFilterForm(String filterFormId, ManageAdsFilterFormBean filterFormBean);

    /**
     * Removes from persistence all MAD filter data for the given identifier
     *
     * @param filterFormId - identifier for the filter
     */
    void removeManageAdsFilterForm(String filterFormId);
}
