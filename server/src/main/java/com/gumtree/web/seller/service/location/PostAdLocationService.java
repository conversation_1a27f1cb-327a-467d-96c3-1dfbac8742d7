package com.gumtree.web.seller.service.location;

import com.gumtree.domain.location.Location;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;

/**
 * Service for post ad specific location business logic.
 */
public interface PostAdLocationService {

    /**
     * Lookup a postcode.
     *
     * @param postcode the postcode to lookup
     * @return the lookup response
     */
    PostcodeLookupResponse lookupPostcode(String postcode);

    Location get(Long id);

    Boolean hasZoomIn(Location location);

    Boolean isCounty(Location location);

    Boolean isInArea(Location location, Integer areaLocId);
}
