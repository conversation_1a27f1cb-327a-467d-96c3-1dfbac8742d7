package com.gumtree.web.seller.service.securetoken;

import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.ModelAndViewDefiningException;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class SecureTokenInterceptor extends HandlerInterceptorAdapter {
    private SecureTokenService secureTokenService;

    /**
     * Constructor.
     *
     * @param secureTokenService - service to store/retrieve tokens
     */
    public SecureTokenInterceptor(SecureTokenService secureTokenService) {
        this.secureTokenService = secureTokenService;
    }

    @Override
    public boolean preHandle(HttpServletRequest req,
                             HttpServletResponse resp,
                             Object handler) throws Exception {
        HandlerMethod method = (HandlerMethod) handler;
        SecureToken secureToken = method.getMethodAnnotation(SecureToken.class);

        RequestMethod requestMethod = RequestMethod.valueOf(req.getMethod());
        if (secureToken != null && (RequestMethod.POST == requestMethod || RequestMethod.PUT == requestMethod)) {
            return checkValidToken(req, resp, secureToken);
        }

        return true;
    }

    private boolean checkValidToken(HttpServletRequest req,
                                    HttpServletResponse resp,
                                    SecureToken secureToken)
            throws IOException, ModelAndViewDefiningException {
        String tokenValue = req.getParameter(SecureToken.TOKEN_PARAMETER_NAME);

        if (StringUtils.isEmpty(tokenValue)) {
            resp.sendError(400);
            // Bad page setup or someone is messing with us
            return false;
        } else if (!secureTokenService.checkToken(secureToken.value(), tokenValue)) {
            ModelAndView mav = new ModelAndView("redirect:/page-expired");
            throw new ModelAndViewDefiningException(mav);
        }

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest req, HttpServletResponse resp,
                           Object handler, ModelAndView modelAndView) throws Exception {
        HandlerMethod method = (HandlerMethod) handler;
        SecureToken secureToken = method.getMethodAnnotation(SecureToken.class);

        if (secureToken != null && modelAndView != null) {
            String tokenValue = secureTokenService.getOrGenerateToken(secureToken.value());
            modelAndView.addObject(SecureToken.TOKEN_PARAMETER_NAME, tokenValue);
        }
    }
}
