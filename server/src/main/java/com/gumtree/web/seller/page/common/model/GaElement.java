package com.gumtree.web.seller.page.common.model;

public class GaElement {
    private String action;
    private String label;

    public GaElement(String action, String label) {
        this.action = action;
        this.label = label;
    }

    public GaElement() {}

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static Builder builder(String action) {
        return new Builder(action);
    }

    public static final class Builder {
        private String action;
        private String label;

        public Builder(String action) {
            this.action = action;
        }
        private Builder(String action, String label) {
            this.action = action;
            this.label = label;
        }

        public Builder withLabel(String label) {
            this.label = label;
            return new Builder(this.action, label);
        }

        public GaElement build() {
            return new GaElement(action, label);

        }
    }

}
