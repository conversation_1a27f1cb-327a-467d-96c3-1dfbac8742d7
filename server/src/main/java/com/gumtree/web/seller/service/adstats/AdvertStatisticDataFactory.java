package com.gumtree.web.seller.service.adstats;

import com.gumtree.api.Ad;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.seller.page.manageads.model.ManageAdStatus;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounters;

import java.util.Map;

class AdvertStatisticDataFactory {

    private Map<Long, AdCounters> advertCounters;
    private UrlScheme urlScheme;

    AdvertStatisticDataFactory(Map<Long, AdCounters> advertCounters, UrlScheme urlScheme) {
        this.advertCounters = advertCounters;
        this.urlScheme = urlScheme;
    }

    public AdvertStatisticData forAdvert(Ad ad) {
        AdvertStatisticData advertStatisticData = new AdvertStatisticData();
        advertStatisticData.setAdvertId(ad.getId().toString());
        advertStatisticData.setAdvertStatus(ManageAdStatus.valueOf(ad.getStatus()));
        advertStatisticData.setAdvertTitle(ad.getTitle());
        advertStatisticData.setContactEmail(ad.getRepliesEmail());
        advertStatisticData.setLastModifiedDate(ad.getLastModifiedDate());
        advertStatisticData.setLastPostedDate(ad.getLiveDate());
        advertStatisticData.setCreationDate(ad.getCreationDate());
        advertStatisticData.setNumberOfTimesReposted(ad.getBumpupCount());
        advertStatisticData.setAdvertVIPUrl(urlScheme.urlFor(ad));
        AdCounters adCounters = advertCounters.getOrDefault(ad.getId(), new AdCounters());
        advertStatisticData.setSRPViews(adCounters.getSearchImpressionCounter());
        advertStatisticData.setVIPViews(adCounters.getViewsCounter());
        advertStatisticData.setNumberOfReplies(adCounters.getRepliesCount());
        return advertStatisticData;
    }
}
