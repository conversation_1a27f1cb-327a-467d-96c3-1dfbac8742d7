package com.gumtree.web.seller.page.common.model;

import com.gumtree.web.seller.page.manageads.mydetails.MyDetailsController;
import com.gumtree.zeno.core.domain.PageType;

public enum Page {
    OpenIdConnectLogin("pages/oidc/login", PageType.Unknown),
    <PERSON>gin("pages/login/login", PageType.Login),
    Login_Post_Ad("pages/login/login-postad", PageType.Login),
    Login_MessageCentreReview("pages/login/login", PageType.Login),
    Logout("pages/logout/logout", PageType.Logout), // uses tiles.xml, redirects to account_logout.jsp
    ForgottenPassword("pages/forgotten-password/forgotten-password", PageType.PasswordForgotten),
    ResetPassword("pages/forgotten-password/update-password", PageType.PasswordReset),
    CreatePassword("pages/forgotten-password/update-password", PageType.Unknown),
    ForgottenPasswordConfirmation("pages/forgotten-password/reset-password-sent", PageType.PasswordResetSent),
    Registration("pages/registration/registration", PageType.UserRegistrationForm),
    RegistrationConfirmation("pages/activation/activation-sent", PageType.UserRegistrationSuccess),
    ActivationFailure("pages/activation/activation-fail", PageType.UserActivationFail),
    EmailVerification("pages/verification/email-verification", PageType.EmailVerification),
    Error404("pages/error/404", PageType.Error_404),
    Error500("pages/error/500", PageType.Error_500),
    ManageAds("pages/manage-ads/manage-ads-responsive", PageType.MyAds),
    ManageAdsPro("pages/manage-ads/manage-ads-pro-responsive", PageType.MyAds),
    ManageAdsEbayMotorsUser("pages/manage-ads/manage-ads-pro-emu", PageType.MyAds),
    PackageUsage("pages/manage-ads/package-usage", PageType.AccountPackageUsageFull),
    PackageUsageLight("pages/manage-ads/package-usage-light", PageType.AccountPackageUsageLight),
    StatsAd("pages/manage-ads/ad-stats", PageType.StatsAd),
    MyDetails(MyDetailsController.VIEW_NAME, PageType.MyAccount),
    MessageCentre("pages/message-centre/message-centre", PageType.MyMessages),
    PostAdForm("pages/syi/syi", PageType.PostAdForm),
    PostAd("pages/syi/syi", PageType.PostAd),
    EditAd("pages/syi/syi", PageType.EditAd),
    OrderError("pages/postad/order-error", PageType.OrderPostAdError),
    PostAdBumpUp("pages/checkout/bump-up", PageType.EditAdBumpUpPromo),
    PaymentCheckoutPage("pages/checkout/checkout", PageType.OrderReview),
    PaymentSuccess("pages/checkout-thankyou/checkout-thankyou-responsive", PageType.OrderSuccess),
    PaymentError("pages/checkout-thankyou/checkout-thankyou-error", PageType.SessionExpired),
    DuplicatedAd("pages/postad/duplicated-ad", PageType.OrderPostAdError),
    PageExpired("pages/page-expired/page-expired", PageType.SessionExpired),
    VehicleVerification("pages/postad/vehicle-verification", PageType.VehicleVerification),
    Deactivate("pages/deactivate/deactivate", PageType.AccountDeactivate);

    private String templateName;
    private PageType pageType;

    Page(String templateName, PageType pageType) {
        this.templateName = templateName;
        this.pageType = pageType;
    }

    public String getTemplateName() {
        return templateName;
    }

    public PageType getPageType() {
        return pageType;
    }
}
