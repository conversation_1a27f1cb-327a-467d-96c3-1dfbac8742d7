package com.gumtree.web.seller.page.postad.model.features;

import com.gumtree.api.Account;
import com.gumtree.api.Ad;
import com.gumtree.api.AdFeature;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.PriceApi;
import com.gumtree.common.util.time.Clock;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.domain.order.OrderItem;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.model.products.DefaultPricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.ProductPrice;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * This class maps the bean for the section of the features you can buy on the post ad confirmation page
 */
@Component
public class FeatureProductBuilder implements Serializable {

    private static final Logger LOGGER = LoggerFactory.getLogger(FeatureProductBuilder.class);

    private BushfireApi bushfireApi;

    private final UserSession userSession;

    private Clock clock;

    private List<ProductName> featureAvailable = new ArrayList<>();

    @Autowired
    public FeatureProductBuilder(BushfireApi bushfireApi, UserSession userSession, Clock clock) {
        this.bushfireApi = bushfireApi;
        this.userSession = userSession;
        this.clock = clock;
        populateFeatureAvailable();
    }

    private void populateFeatureAvailable() {
        featureAvailable.add(ProductName.FEATURE_3_DAY);
        featureAvailable.add(ProductName.FEATURE_7_DAY);
        featureAvailable.add(ProductName.FEATURE_14_DAY);
        featureAvailable.add(ProductName.HOMEPAGE_SPOTLIGHT);
        featureAvailable.add(ProductName.URGENT);
    }

    public PricingMetadata populateFeatureMap(Order order, Ad advert) {
        PricingMetadata pricingMetadata = null;
        Account account = bushfireApi.accountApi().getAccount(order.getAccountId());
        //add condition about order for multiple ad---whatever is, not display it
        if (!account.isPro()) {
            LOGGER.debug("User is not a pro user");
            Set<Long> advertsForOrder = findAdvertFeatured(order);
            LOGGER.debug("Number of Ads in Order is " + advertsForOrder.size());
            boolean areThereFeaturesInTheOrder = featuresInTheOrder(order);
            LOGGER.debug("Are there features in the order: " + areThereFeaturesInTheOrder);
            if (advertsForOrder.size() <= 1 && !areThereFeaturesInTheOrder) {
                LOGGER.debug("Preparing upsell panel");
                //populate the features map with all the option
                PriceApi priceApi = bushfireApi.create(PriceApi.class, userSession.getApiKey());
                Map<ProductType, List<ProductPrice>> pricingMap = ProductType.extract(priceApi
                                .getPricesForCategoryIdLocationId(advert.getCategoryId(), advert.getLocationId()));
                DefaultPricingMetadata.Builder metadataBuilder = new DefaultPricingMetadata.Builder(clock);


                for (ProductType type : pricingMap.keySet()) {

                    List<ProductPrice> priceList = pricingMap.get(type);

                    if (type.equals(ProductType.URGENT) || type.equals(ProductType.FEATURED)
                            || type.equals(ProductType.SPOTLIGHT)) {
                        AdFeature existingFeature = getExistingFeatureByAdAndType(type, advert);
                        if (existingFeature != null) {
                            metadataBuilder.withActiveFeature(type, existingFeature.getEndDate());
                        } else {
                            metadataBuilder.withNonActiveFeature(type, priceList);
                        }
                    }
                }

                pricingMetadata = metadataBuilder.build();

            }
        }
        LOGGER.debug("Returning pricingMetadata " + pricingMetadata);
        return pricingMetadata;
    }

    private AdFeature getExistingFeatureByAdAndType(ProductType type, Ad advert) {
        AdFeature result = null;
        if (advert.getFeatures() != null) {
            for (AdFeature feature : advert.getFeatures()) {
                if (ProductType.getType(
                        feature.getProductName()).equals(type) && feature.getEndDate().isAfter(clock.getDateTime())) {
                    result = feature;
                }
            }
        }
        return result;
    }

    private Set<Long> findAdvertFeatured(Order order) {
        Set<Long> result = new HashSet<>();
        for (OrderItem item : order.getItems()) {
            result.add(item.getAdvert().getId());
        }
        return result;
    }

    public boolean featuresInTheOrder(Order order) {
        boolean result = false;
        if (order.getItems() != null) {
            for (OrderItem item : order.getItems()) {
                if (getFeatureAvailable().contains(item.getProductName())) {
                    result = true;
                }
            }
        }
        return result;
    }

    public List<ProductName> getFeatureAvailable() {
        return featureAvailable;
    }


}
