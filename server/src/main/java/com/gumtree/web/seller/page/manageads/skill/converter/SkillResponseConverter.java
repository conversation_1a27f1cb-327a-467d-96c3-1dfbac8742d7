package com.gumtree.web.seller.page.manageads.skill.converter;

import com.gumtree.bapi.model.SkillResponse;
import com.gumtree.web.seller.page.manageads.skill.model.FacadeSkillCreateResponse;
import com.gumtree.web.seller.page.manageads.skill.model.FacadeSkillQueryResponse;
import com.gumtree.web.seller.page.manageads.skill.model.FacadeSkillUpdateResponse;

import java.util.List;

public final class SkillResponseConverter {
    
    private SkillResponseConverter() {
    }

    public static FacadeSkillQueryResponse toQueryResponse(SkillResponse skill) {
        FacadeSkillQueryResponse response = new FacadeSkillQueryResponse();
        response.setSuccess(true);
        response.setMsg("");
        response.setCode(200);

        if (skill != null) {
            FacadeSkillQueryResponse.SkillData data = new FacadeSkillQueryResponse.SkillData();
            data.setEdited(skill.getIsEdited() != null && skill.getIsEdited());
            data.setSkills(skill.getSkills());
            response.setData(data);
        }

        return response;
    }

    public static FacadeSkillCreateResponse toCreateResponse(Integer categoryId, List<Integer> skills) {
        FacadeSkillCreateResponse response = new FacadeSkillCreateResponse();
        response.setSuccess(true);
        response.setMsg("");
        response.setCode(200);

        FacadeSkillCreateResponse.SkillData data = new FacadeSkillCreateResponse.SkillData();
        data.setCategoryId(String.valueOf(categoryId));
        data.setSkills(skills);
        
        response.setData(data);
        return response;
    }

    public static FacadeSkillUpdateResponse toUpdateResponse(Integer categoryId, List<Integer> skills) {
        FacadeSkillUpdateResponse response = new FacadeSkillUpdateResponse();
        response.setSuccess(true);
        response.setMsg("");
        response.setCode(200);

        FacadeSkillUpdateResponse.SkillData data = new FacadeSkillUpdateResponse.SkillData();
        data.setCategoryId(String.valueOf(categoryId));
        data.setSkills(skills);
        
        response.setData(data);
        return response;
    }

    public static FacadeSkillQueryResponse toQueryErrorResponse(Integer code, String msg) {
        FacadeSkillQueryResponse response = new FacadeSkillQueryResponse();
        response.setSuccess(false);
        response.setMsg(msg);
        response.setCode(code);
        response.setData(null);
        return response;
    }

    public static FacadeSkillCreateResponse toCreateErrorResponse(Integer code, String msg) {
        FacadeSkillCreateResponse response = new FacadeSkillCreateResponse();
        response.setSuccess(false);
        response.setMsg(msg);
        response.setCode(code);
        response.setData(null);
        return response;
    }

    public static FacadeSkillUpdateResponse toUpdateErrorResponse(Integer code, String msg) {
        FacadeSkillUpdateResponse response = new FacadeSkillUpdateResponse();
        response.setSuccess(false);
        response.setMsg(msg);
        response.setCode(code);
        response.setData(null);
        return response;
    }
} 
