package com.gumtree.web.seller.service.presentation.config;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.Category;
import com.gumtree.web.seller.page.postad.model.CategorySpecificPostAdFormPanels;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeGroup;
import com.gumtree.web.seller.page.postad.model.PostAdFormPanel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

public final class MappingBasedAttributePresentationService implements AttributePresentationService {
    private final CategoryModel categoryModel;
    private final Map<String, PresentableAttributeGroups> attributeBySeoName;

    /**
     * Constructor.
     *
     * @param categoryModel the category read API
     * @param groups        the groups config loaded from configuration file
     */
    public MappingBasedAttributePresentationService(CategoryModel categoryModel, List<PresentableAttributeGroups> groups) {
        this.categoryModel = categoryModel;
        this.attributeBySeoName = getAttributeMapBySeoName(groups);
    }

    @Override
    public List<PostAdAttributeGroup> loadAttributeGroups(Long categoryId, Map<String, String> formAttributes) {
        if (formAttributes == null) {
            formAttributes = new HashMap<>();
        }
        PresentableAttributeGroups groups = getPresentableAttributeGroups(categoryId);
        Optional<List<AttributeMetadata>> categoryMetadata = categoryModel.getCategoryAttributes(categoryId);
        if (categoryMetadata.isPresent()) {
            return groups.toPostAdAttributeGroups(categoryMetadata.get(), formAttributes);
        }
        return Lists.newArrayList();
    }

    @Override
    public CategorySpecificPostAdFormPanels loadPrioritisedCategorySpecificFormPanels(Long categoryId, Map<String, String> formAttributes) {
        List<PostAdAttributeGroup> groups = loadAttributeGroups(categoryId, formAttributes);

        Map<Boolean, List<PostAdAttributeGroup>> prioritisedGroups = groups.stream()
                .collect(Collectors.partitioningBy(PostAdAttributeGroup::isHighPriority));

        List<PostAdFormPanel> highPriorityPostAdFormPanels = asPostAdFormPanels(prioritisedGroups.get(true));
        List<PostAdFormPanel> lowPriorityPostAdFormPanels = asPostAdFormPanels(prioritisedGroups.get(false));

        return new CategorySpecificPostAdFormPanels(highPriorityPostAdFormPanels, lowPriorityPostAdFormPanels);
    }

    private List<PostAdFormPanel> asPostAdFormPanels(List<PostAdAttributeGroup> groups) {
        return groups.stream()
                .map(PostAdAttributeGroup::getPanelIdOrDefault)
                .map(this::findProperPanel)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .filter(panel -> !panel.isAddedManually())
                .distinct()
                .collect(toList());
    }

    private Optional<PostAdFormPanel> findProperPanel(String panelId) {
        for (PostAdFormPanel value : PostAdFormPanel.values()) {
            if (value.getId().equals(panelId)) return Optional.of(value);
        }
        return Optional.absent();
    }

    private PresentableAttributeGroups getPresentableAttributeGroups(Long categoryId) {
        Optional<Category> optCategory = categoryModel.getCategory(categoryId);
        PresentableAttributeGroups groups = null;
        if (optCategory.isPresent()) {
            List<Category> hierarchy = categoryModel.getHierarchy(categoryId);
            groups = new PresentableAttributeGroups();
            for (Category category : hierarchy) {
                PresentableAttributeGroups presentableAttributeGroups = attributeBySeoName.get(category.getSeoName());
                if (presentableAttributeGroups != null) {
                    groups = presentableAttributeGroups.merge(groups);
                }
            }
            groups.setCategoryName(optCategory.get().getSeoName());
        }
        return groups;
    }

    private Map<String, PresentableAttributeGroups> getAttributeMapBySeoName(List<PresentableAttributeGroups> groups) {
        return Maps.uniqueIndex(groups, PresentableAttributeGroups::getCategoryName);
    }
}
