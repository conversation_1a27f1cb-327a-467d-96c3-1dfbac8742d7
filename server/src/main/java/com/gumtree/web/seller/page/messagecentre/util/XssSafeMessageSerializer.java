package com.gumtree.web.seller.page.messagecentre.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.gumtree.web.common.domain.messagecentre.Message;
import org.owasp.encoder.Encode;

import java.io.IOException;

/**
 * Custom serializer to encode message body to be used in <SCRIPT> block.
 * See {@link com.gumtree.web.seller.page.messagecentre.MessageCentreModel#getConversationsAsJsonString()}
 * and {@link com.gumtree.web.seller.page.messagecentre.MessageCentreModel#getMessagesAsJsonString()}
 */
public class XssSafeMessageSerializer extends JsonSerializer<Message> {

    @Override
    public void serialize(Message message, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
            throws IOException {
        jsonGenerator.writeStartObject();
        jsonGenerator.writeStringField("id", message.getId());
        jsonGenerator.writeStringField("direction", message.getDirection().toString());
        jsonGenerator.writeStringField("time", message.getTime());
        jsonGenerator.writeFieldName("body");
        jsonGenerator.writeRaw(":\"" + Encode.forJavaScript(message.getBody()) + "\"");
        jsonGenerator.writeEndObject();
    }

}
