package com.gumtree.web.seller.page.payment.event;

import com.gumtree.web.seller.page.manageads.model.Checkout;

public interface PaymentEventsListener {
    void invalidPaymentMethodProblem(String paymentMethod, Checkout checkout);
    void successfulPaymentForTransaction(Long orderId);
    void failedPaymentWithMessage(String message, Checkout checkout);
    void unexpectedPaymentProblemForOrder(Long orderId, Exception e, Checkout checkout);
    void alreadyPaidOrder(Long orderId);
}
