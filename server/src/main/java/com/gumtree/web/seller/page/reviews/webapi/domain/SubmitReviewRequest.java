package com.gumtree.web.seller.page.reviews.webapi.domain;

import java.util.List;
import java.util.Optional;

public class SubmitReviewRequest {
    private String conversationId;
    private String comment;
    private Integer rating;
    private List<String> feedback;

    /**
     * Required by jackson(json unmarshalling)
     */
    public SubmitReviewRequest() {
    }

    public SubmitReviewRequest(String conversationId, String comment, Integer rating) {
        this.conversationId = conversationId;
        this.comment = comment;
        this.rating = rating;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getRating() {
        return rating;
    }

    public void setRating(Integer rating) {
        this.rating = rating;
    }

    public Optional<List<String>> getFeedback() {
        return Optional.ofNullable(feedback);
    }

    public void setFeedback(List<String> feedback) {
        this.feedback = feedback;
    }
}
