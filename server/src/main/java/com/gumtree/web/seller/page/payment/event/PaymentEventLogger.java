package com.gumtree.web.seller.page.payment.event;


import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.zeno.checkout.CheckoutInput;
import com.gumtree.zeno.core.event.user.sellerside.checkout.PaymentFailure;
import com.gumtree.zeno.core.service.ZenoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.codahale.metrics.MetricRegistry.name;

@Service
public class PaymentEventLogger implements PaymentEventsListener {
    public static final String SUCCESSFUL_PAYMENTS_COUNTER_NAME = "successfullPayments";
    public static final String FAILED_PAYMENTS_COUNTER_NAME = "failedPaymentsCounter";
    public static final String ALREADY_PAID_ORDERS_COUNTER_NAME = "alreadyPaidOrdersCounter";
    public static final String UNEXPECTED_PAYMENT_PROBLEMS_COUNTER_NAME = "unexpectedPaymentProblemsCounter";
    public static final String INVALID_PAYMENT_METHOD_PROBLEMS_COUNTER_NAME = "invalidPaymentMethodProblemsCounter";

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentEventLogger.class);
    private final ZenoService zenoService;
    private final MetricRegistry metricRegistry;
    private final Counter successfulPayments;
    private final Counter failedPayments;
    private final Counter alreadyPaidOrders;
    private final Counter unexpectedPaymentProblems;
    private final Counter invalidPaymentMethodProblems;

    @Autowired
    public PaymentEventLogger(ZenoService zenoService, MetricRegistry metricRegistry) {
        this.zenoService = zenoService;
        this.metricRegistry = metricRegistry;
        successfulPayments = getCounterFor(SUCCESSFUL_PAYMENTS_COUNTER_NAME);
        failedPayments = getCounterFor(FAILED_PAYMENTS_COUNTER_NAME);
        alreadyPaidOrders = getCounterFor(ALREADY_PAID_ORDERS_COUNTER_NAME);
        unexpectedPaymentProblems = getCounterFor(UNEXPECTED_PAYMENT_PROBLEMS_COUNTER_NAME);
        invalidPaymentMethodProblems = getCounterFor(INVALID_PAYMENT_METHOD_PROBLEMS_COUNTER_NAME);
    }

    @Override
    public void successfulPaymentForTransaction(Long orderId) {
        LOGGER.info("Successful payment for order ID: " + orderId);
        successfulPayments.inc();
    }

    @Override
    public void failedPaymentWithMessage(String message, Checkout checkout) {
        LOGGER.info("Braintree transaction failed: " + message);
        failedPayments.inc();
        logZenoPaymentFailedEvent(message, checkout);
    }

    @Override
    public void alreadyPaidOrder(Long orderId) {
        LOGGER.warn("Transaction already exists for the orderId {}", orderId);
        alreadyPaidOrders.inc();
    }

    @Override
    public void unexpectedPaymentProblemForOrder(Long orderId, Exception e, Checkout checkout) {
        LOGGER.error("Unexpected problem during Braintree transaction for the order '" + orderId + "' : "
                + e.getMessage(), e);
        unexpectedPaymentProblems.inc();
        logZenoPaymentFailedEvent("Unexpected problem", checkout);
    }

    @Override
    public void invalidPaymentMethodProblem(String paymentMethod, Checkout checkout) {
        LOGGER.error(String.format("Invalid payment method (%s) in Braintree transaction.", paymentMethod));
        invalidPaymentMethodProblems.inc();
        logZenoPaymentFailedEvent("Invalid payment method", checkout);
    }

    private void logZenoPaymentFailedEvent(String msg, Checkout checkout) {
        zenoService.logEvent(
                new CheckoutInput(checkout.getOrder(), checkout, getSource(checkout)), "", PaymentFailure.class);
    }

    private Counter getCounterFor(String counterName) {
        return metricRegistry.counter(name(PaymentEventLogger.class, counterName));
    }

    private String getSource(Checkout checkout) {
        return checkout.isCreateOrEdit() ? "createOrEdit" : "feature";
    }
}
