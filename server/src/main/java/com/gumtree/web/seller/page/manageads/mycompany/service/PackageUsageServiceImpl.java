package com.gumtree.web.seller.page.manageads.mycompany.service;

import com.gumtree.api.Account;
import com.gumtree.api.CreditPackage;
import com.gumtree.api.CreditPackages;
import com.gumtree.api.PackageUsageSearchResponse;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.web.seller.page.manageads.mycompany.command.AccountPackageUsageCommand;
import com.gumtree.web.seller.page.manageads.mycompany.command.CreditPackageUsageCommand;
import com.gumtree.api.client.executor.command.GetAccountCommand;
import com.gumtree.web.seller.page.manageads.mycompany.command.PackageSummaryCommand;
import com.gumtree.web.seller.page.manageads.mycompany.model.PackageStatusFilter;
import com.gumtree.web.security.UserSession;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Default implementation of {@link PackageUsageService}.
 */
@Service
public final class PackageUsageServiceImpl implements PackageUsageService {

    private ApiCallExecutor apiCallExecutor;

    private UserSession userSession;

    /**
     * Constructor.
     *
     * @param apiCallExecutor for fetching data from the BAPI
     * @param userSession     the user session
     */
    @Autowired
    public PackageUsageServiceImpl(ApiCallExecutor apiCallExecutor, UserSession userSession) {
        this.apiCallExecutor = apiCallExecutor;
        this.userSession = userSession;
    }

    @Override
    public Account getAccount(long accountId) {
        return apiCallExecutor.call(
                new GetAccountCommand(userSession.getSelectedAccountId(), userSession))
                .getResponseObject();
    }

    @Override
    public List<CreditPackage> getCreditPackagesForAccount(long accountId, PackageStatusFilter status) {
        PackageSummaryCommand packageSummaryCommand = new PackageSummaryCommand(accountId, userSession);
        ApiCallResponse<CreditPackages> resp = apiCallExecutor.call(packageSummaryCommand);
        return resp.isErrorResponse() ? new ArrayList<CreditPackage>() : resp.getResponseObject().getPackages();
    }

    @Override
    public PackageUsageSearchResponse getCreditUsagesForPackage(
            long accountId,
            long packageId,
            int page,
            int pageSize) {

        ApiCallResponse<PackageUsageSearchResponse> response =
                apiCallExecutor.call(new CreditPackageUsageCommand(
                        userSession.getSelectedAccountId(), packageId, page, pageSize, userSession));

        return response.isErrorResponse() ? new PackageUsageSearchResponse() : response.getResponseObject();
    }

    @Override
    public PackageUsageSearchResponse getCreditUsagesForAccount(
            long accountId,
            Long packageTypeId,
            DateTime from,
            DateTime to,
            PackageStatusFilter status,
            int page,
            int pageSize) {

        ApiCallResponse<PackageUsageSearchResponse> response =
                apiCallExecutor.call(new AccountPackageUsageCommand(
                        userSession.getSelectedAccountId(),
                        packageTypeId,
                        from,
                        to,
                        convertPackageStatusFilter(status),
                        page,
                        pageSize,
                        userSession));

        return response.isErrorResponse() ? new PackageUsageSearchResponse() : response.getResponseObject();
    }

    private Boolean convertPackageStatusFilter(PackageStatusFilter statusFilter) {
        switch (statusFilter) {
            case ACTIVE:
                return true;
            case EXPIRED:
                return false;
            default:
                return null;
        }
    }
}
