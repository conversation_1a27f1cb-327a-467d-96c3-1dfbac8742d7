package com.gumtree.web.seller.page.payment.model;

import java.util.Objects;
import java.util.Optional;

public final class BillingAddress {
    private final Optional<String> firstName;
    private final Optional<String> lastName;
    private final Optional<String> address;
    private final Optional<String> postcode;
    private final Optional<String> townCity;
    private final Optional<String> country;

    private BillingAddress(Builder builder) {
        this.firstName = Optional.ofNullable(builder.firstName);
        this.lastName = Optional.ofNullable(builder.lastName);
        this.address = Optional.ofNullable(builder.address);
        this.postcode = Optional.ofNullable(builder.postcode);
        this.townCity = Optional.ofNullable(builder.townCity);
        this.country = Optional.ofNullable(builder.country);
    }

    public Optional<String> getFirstName() {
        return firstName;
    }

    public Optional<String> getLastName() {
        return lastName;
    }

    public Optional<String> getAddress() {
        return address;
    }

    public Optional<String> getPostcode() {
        return postcode;
    }

    public Optional<String> getTownCity() {
        return townCity;
    }

    public Optional<String> getCountry() {
        return country;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String firstName;
        private String lastName;
        private String address;
        private String postcode;
        private String townCity;
        private String country;

        public Builder withFirstName(String firstName) {
            this.firstName = firstName;
            return this;
        }

        public Builder withLastName(String lastName) {
            this.lastName = lastName;
            return this;
        }

        public Builder withAddress(String address) {
            this.address = address;
            return this;
        }

        public Builder withPostcode(String postcode) {
            this.postcode = postcode;
            return this;
        }

        public Builder withTownCity(String townCity) {
            this.townCity = townCity;
            return this;
        }

        public Builder withCountry(String country) {
            this.country = country;
            return this;
        }

        public BillingAddress build() {
            return new BillingAddress(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BillingAddress that = (BillingAddress) o;
        return Objects.equals(firstName, that.firstName) &&
                Objects.equals(lastName, that.lastName) &&
                Objects.equals(address, that.address) &&
                Objects.equals(postcode, that.postcode) &&
                Objects.equals(townCity, that.townCity) &&
                Objects.equals(country, that.country);
    }

    @Override
    public int hashCode() {
        return Objects.hash(firstName, lastName, address, postcode, townCity, country);
    }

    @Override
    public String toString() {
        return "BillingAddress{" +
                "firstName=" + firstName +
                ", lastName=" + lastName +
                ", address=" + address +
                ", postcode=" + postcode +
                ", townCity=" + townCity +
                ", country=" + country +
                '}';
    }
}
