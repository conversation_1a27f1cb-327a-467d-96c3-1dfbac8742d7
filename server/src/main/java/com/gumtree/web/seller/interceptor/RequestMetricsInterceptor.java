package com.gumtree.web.seller.interceptor;

import com.gumtree.config.filters.RequestTimingFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Interceptor that adds an attributes to the request representing the name of the method & metric
 * that should be used when capturing any metrics <br />.
 * <p>
 * Recording the request is not performed in this interceptor as it cannot give an accurate representation of timings
 * due to the filters being executed prior to the interceptor being invoked.
 */
public class RequestMetricsInterceptor extends HandlerInterceptorAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestMetricsInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        try {
            if (handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                Class<?> beanType = handlerMethod.getBeanType();

                RequestMapping classAnnotation = beanType.getAnnotation(RequestMapping.class);
                RequestMapping methodAnnotation = handlerMethod.getMethod().getAnnotation(RequestMapping.class);

                if (classAnnotation == null && methodAnnotation == null) {
                    return true;
                }

                String classURI = getURI(classAnnotation);
                String methodURI = getURI(methodAnnotation);

                String requestMethod = request.getMethod();
                String requestURI = classURI + methodURI;
                requestURI = !requestURI.isEmpty() ? requestURI : "default";

                request.setAttribute(RequestTimingFilter.METRICS_LOGGING_REQUEST_ATTRIBUTE_URI, requestURI);
                request.setAttribute(RequestTimingFilter.METRICS_LOGGING_REQUEST_ATTRIBUTE_METHOD, requestMethod);
            }
        } catch (Exception exc) {
            LOGGER.warn("Request metric error: {}", request.getRequestURL().toString(), exc);
        }

        return true;
    }

    private String getURI(RequestMapping requestMapping) {
        if (requestMapping == null) {
            return "";
        }
        return String.join("", requestMapping.value());
    }
}
