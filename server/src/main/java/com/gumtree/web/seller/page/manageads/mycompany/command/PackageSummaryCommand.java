package com.gumtree.web.seller.page.manageads.mycompany.command;

import com.gumtree.api.CreditPackages;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;

/**
 * Command object for fetching credit packages for a specified account
 */
public final class PackageSummaryCommand extends AuthenticatedApiCall<CreditPackages> {

    private long accountId;

    /**
     * Constructor.
     *
     * @param accountId      the accountId
     * @param apiKeyProvider the api key provider
     */
    public PackageSummaryCommand(long accountId, ApiKeyProvider apiKeyProvider) {
        super(apiKeyProvider);
        this.accountId = accountId;
    }

    @Override
    public CreditPackages execute(BushfireApi api) {
        return api.create(AccountApi.class, getApiKey()).getPackages(String.valueOf(accountId));
    }
}
