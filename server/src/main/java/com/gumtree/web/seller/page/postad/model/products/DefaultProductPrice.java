package com.gumtree.web.seller.page.postad.model.products;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Default implementation of {@link ProductPrice}
 */
public class DefaultProductPrice implements ProductPrice {

    private ProductType type;

    private String productName;

    private Integer days;

    private BigDecimal price;

    private Integer discountedBy;
    private boolean includedInPackage;

    /**
     * Constructor
     *
     * @param type        the associated {@link ProductType}
     * @param productName the product name
     * @param price       the price within the current context
     */
    public DefaultProductPrice(ProductType type, String productName, long price) {
        this(type, productName, null, price);
    }

    /**
     * Constructor
     *
     * @param type        the associated {@link ProductType}
     * @param productName the product name
     * @param days        the number of days the feature is active for when purchased
     * @param price       the price within the current context
     */
    public DefaultProductPrice(ProductType type, String productName, Integer days, long price) {
        this.type = type;
        this.productName = productName;
        this.days = days;
        this.price = new BigDecimal(price).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_EVEN);
    }

    public DefaultProductPrice(ProductType type, String productName, BigDecimal price) {
        this.type = type;
        this.productName = productName;
        this.days = null;
        this.price = price;
    }

    @Override
    public ProductType getProductType() {
        return type;
    }

    @Override
    public String getProductName() {
        return productName;
    }

    @Override
    public BigDecimal getPrice() {
        return price;
    }

    @Override
    public boolean isFree() {
        return price.compareTo(new BigDecimal("0")) <= 0;
    }

    @Override
    public boolean isIncludedInPackage() {
        return includedInPackage;
    }

    @Override
    public String getDisplayValue() {

        StringBuilder displayValue = new StringBuilder();

        if (days != null) {

            displayValue.append(days).append(" days - ");
        }

        if (null != discountedBy) {
            BigDecimal undiscountedPrice =
                    price.multiply(
                            new BigDecimal((float) 100 / (100 - discountedBy))).setScale(2, RoundingMode.HALF_EVEN);

            //this is really ugly - try not to look
            displayValue.append("<span class='undiscounted-price'>&pound;")
                .append(undiscountedPrice.toString())
                .append("</span><span class='discounted-price'>&pound;")
                .append(price.toString())
                .append("</span>");
        } else {
            displayValue.append("&pound;").append(price.toString());
        }

        return displayValue.toString();
    }

    @Override
    public String getValue() {
        return productName;
    }

    public String getClientValue() {
        return price.toString();
    }

    @Override
    public void setDiscountedBy(Integer percent) {
        this.discountedBy = percent;
    }
}
