package com.gumtree.web.seller.service.adstats.adcounter;

import com.gumtree.adcounters.CountersApi;
import com.gumtree.adcounters.model.SearchRequest;
import com.gumtree.google.authservice.GoogleAuthService;
import com.gumtree.util.UuidProvider;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Single;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.gumtree.web.seller.service.adstats.adcounter.StatsType.AD_COUNTERS;
import static com.gumtree.web.seller.service.adstats.adcounter.StatsType.REPLIES;
import static com.gumtree.web.seller.service.adstats.adcounter.StatsType.SEARCH_IMPRESSIONS;
import static com.gumtree.web.seller.service.adstats.adcounter.StatsType.VIEWS;

public class AdCounterServiceImpl implements AdCounterService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdCounterServiceImpl.class);
    private static final String TOTAL_FORMAT = "%d.%s.total";
    private static final String DAILY_FORMAT = "%d.%s.daily.%s";

    private static final int DEFAULT_NO_DAYS = 10;
    private static final String DATE_FORMAT = "yyyy.MM.dd";

    private final CountersApi countersApi;

    private final UuidProvider uuidProvider;

    private final GoogleAuthService googleAuthService;
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT);

    public AdCounterServiceImpl(CountersApi countersApi, GoogleAuthService googleAuthService, UuidProvider uuidProvider) {
        this.countersApi = countersApi;
        this.googleAuthService = googleAuthService;
        this.uuidProvider = uuidProvider;
    }

    @Override
    public Map<String, AdCounters> getAdvertCounters(Long adId, Integer noOfDays) {
        List<LocalDate> dateList = getDateList(noOfDays);
        SearchRequest request = generateGumtreeDailySearchRequest(adId, dateList);
        String correlationId = generateAdCorrelationId(Collections.singletonList(adId));
        String authToken = googleAuthService.getAuthKey();
        Map<String, Integer> response = countersApi.searchCounters(request, correlationId, null, authToken)
                .onErrorReturn(error -> {
                    LOGGER.error("TSA AdCounters :: Failed to retrieve counters from Gumtree AdCounters  :: ", error);
                    return Collections.emptyMap();
                }).toBlocking().value();

        return generateDailyResponse(response, adId, dateList);
    }

    @Override
    public Map<Long, AdCounters> getAdvertCounters(List<Long> advertIds) {
        return getsCountersFromCDataOrTsaAdCounters(advertIds)
                .map(adCounters -> cdataResponseConverter(adCounters, advertIds))
                .toBlocking()
                .value();
    }

    Single<Map<String, Integer>> getsCountersFromCDataOrTsaAdCounters(List<Long> advertIds) {
        return countersApi.searchCounters(
                                generateTSATotalSearchRequest(advertIds),generateAdCorrelationId(advertIds),null,googleAuthService.getAuthKey())
                .onErrorReturn(error -> {
                    LOGGER.error("TSA AdCounters :: Failed to retrieve counters from Gumtree AdCounters  :: ", error);
                    return Collections.emptyMap();
                });
    }

    private Map<Long, AdCounters> cdataResponseConverter(Map<String, Integer> adCounterResponse, List<Long> advertIds) {
        return advertIds.stream()
                .map(advertId -> convertToSellerFormat(adCounterResponse, advertId))
                .collect(Collectors.toMap(AdCounters::getAdvertId, adCounter -> adCounter));
    }

    private static int getTotal(Map<String, Integer> cDataCounters, Long advertId, String action) {
        Integer total = cDataCounters.get(String.format(TOTAL_FORMAT, advertId, action));
        return total == null ? 0 : total;
    }

    private int getDailyCount(Map<String, Integer> cDataCounters, Long advertId, String action, String date) {
        Integer dailyCount = cDataCounters.get(String.format(DAILY_FORMAT, advertId, action, date));
        return dailyCount == null ? 0 : dailyCount;
    }

    private SearchRequest generateTSATotalSearchRequest(List<Long> advertIds) {
        List<String> includes = advertIds.stream()
                .flatMap(advertId -> AD_COUNTERS.stream()
                        .map(counter -> String.format(TOTAL_FORMAT, advertId, counter)))
                .collect(Collectors.toList());
        return new SearchRequest().include(includes);
    }

    private SearchRequest generateGumtreeDailySearchRequest(Long advertId, List<LocalDate> dateList) {
        List<String> includes = dateList.stream()
                .flatMap(date -> AD_COUNTERS.stream()
                        .map(counter -> String.format(DAILY_FORMAT, advertId, counter, formatter.format(date))))
                .collect(Collectors.toList());
        return new SearchRequest().include(includes);
    }

    private static AdCounters convertToSellerFormat(Map<String, Integer> counter, Long advertId) {
        AdCounters.Builder builder = AdCounters.builder();
        builder.setAdvertId(advertId);
        builder.setViewsCounter(getTotal(counter, advertId, VIEWS.getAdCountersName()));
        builder.setSearchImpressionCounter(getTotal(counter, advertId, SEARCH_IMPRESSIONS.getAdCountersName()));
        builder.setRepliesCount(getTotal(counter, advertId, REPLIES.getAdCountersName()));
        return builder.build();
    }

    private List<LocalDate> getDateList(Integer noOfDays) {
        int numOfDays = noOfDays != null ? noOfDays : DEFAULT_NO_DAYS;
        return Stream.iterate(LocalDate.now(), days -> days.minusDays(1))
                .limit(numOfDays)
                .collect(Collectors.toList());
    }

    private Map<String, AdCounters> generateDailyResponse(Map<String, Integer> adResponse, Long advertId, List<LocalDate> dateList) {
        Map<String, AdCounters> adCountersMap = new HashMap<>();
        dateList.forEach(date -> {
            String formattedDate = formatter.format(date);
            adCountersMap.put(formattedDate, new AdCounters.Builder()
                    .setAdvertId(advertId)
                    .setViewsCounter(getDailyCount(adResponse, advertId, VIEWS.getAdCountersName(), formattedDate))
                    .build()
            );

        });
        return adCountersMap;
    }

    private String generateAdCorrelationId(List<Long> adId) {
        return StringUtils.join(adId,"-")+"-"+"seller"+"-"+ uuidProvider.uuid().toString();
    }
}
