package com.gumtree.web.seller.page.common;

/**
 * Simple POJO implementation of {@link SelectableValue}.
 */
public final class SimpleSelectableValue implements SelectableValue {

    private String value;

    private String displayValue;

    /**
     * Constructor.
     *
     * @param value        value
     * @param displayValue display value
     */
    public SimpleSelectableValue(String value, String displayValue) {
        this.value = value;
        this.displayValue = displayValue;
    }

    @Override
    public String getDisplayValue() {
        return displayValue;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SimpleSelectableValue)) {
            return false;
        }

        SimpleSelectableValue that = (SimpleSelectableValue) o;

        if (!displayValue.equals(that.displayValue)) {
            return false;
        }
        if (!value.equals(that.value)) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = value.hashCode();
        result = 31 * result + displayValue.hashCode();
        return result;
    }
}
