package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.api.domain.order.CreateOrderItemBean;
import com.gumtree.seller.domain.product.entity.ProductName;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Represents a {@link ShoppingCart} for a single advert.
 */
public final class SingleAdvertShoppingCart implements ShoppingCart {

    private Long advertId;

    private Long accountId;

    private Map<ProductName, CreateOrderItemBean> orderItems = new LinkedHashMap<>();

    /**
     * Constructor.
     *
     * @param advertId  the associated advert
     * @param accountId the account for which the order should be made using
     */
    public SingleAdvertShoppingCart(Long advertId, Long accountId) {

        this.advertId = advertId;
        this.accountId = accountId;
    }

    @Override
    public void addProduct(ProductName productName) {

        CreateOrderItemBean orderItemBean = new CreateOrderItemBean();
        orderItemBean.setAdvertId(advertId);
        orderItemBean.setProductName(productName);

        orderItems.put(productName, orderItemBean);
    }

    @Override
    public CreateOrderBean toOrderBean() {

        CreateOrderBean orderBean = new CreateOrderBean();
        orderBean.setAccountId(accountId);
        orderBean.setItems(new ArrayList<>(orderItems.values()));

        return orderBean;
    }
}
