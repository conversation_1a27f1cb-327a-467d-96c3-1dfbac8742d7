package com.gumtree.web.seller.page.manageads.mycompany.service;

import com.gumtree.api.Account;
import com.gumtree.api.CreditPackage;
import com.gumtree.api.PackageUsageSearchResponse;
import com.gumtree.web.seller.page.manageads.mycompany.model.PackageStatusFilter;
import org.joda.time.DateTime;

import java.util.List;

/**
 * Service for retrieving credit package usages.
 */
public interface PackageUsageService {

    /**
     * Get account.
     *
     * @param accountId the account id.
     * @return the account with the given id
     */
    Account getAccount(long accountId);

    /**
     * Fetch all credit packages for an account, filtered by status
     *
     * @param accountId the account id
     * @param status    status to filter on
     * @return all credit packages for an account, filtered by status
     */
    List<CreditPackage> getCreditPackagesForAccount(long accountId, PackageStatusFilter status);

    /**
     * Get credit usages for a specified package.
     *
     * @param accountId the account id
     * @param packageId the package id
     * @param page      the page number
     * @param pageSize  the page size
     * @return credit usages for a specified package.
     */
    PackageUsageSearchResponse getCreditUsagesForPackage(
            long accountId,
            long packageId,
            int page,
            int pageSize);

    /**
     * Get credit usages for a specified account with the given filters applied.
     *
     * @param accountId     the account id
     * @param packageTypeId the type of packages to filter for
     * @param from          the from date range (for applied date)
     * @param to            the to date range (for applied date)
     * @param status        status to filter on
     * @param page          the page number
     * @param pageSize      the page size
     * @return credit usages for a specified account with the given filters applied.
     */
    PackageUsageSearchResponse getCreditUsagesForAccount(
            long accountId,
            Long packageTypeId,
            DateTime from,
            DateTime to,
            PackageStatusFilter status,
            int page,
            int pageSize);
}
