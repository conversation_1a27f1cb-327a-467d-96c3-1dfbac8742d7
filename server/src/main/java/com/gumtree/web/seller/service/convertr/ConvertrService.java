package com.gumtree.web.seller.service.convertr;

import com.gumtree.api.Account;
import com.gumtree.api.Ad;
import com.gumtree.api.Attribute;
import com.gumtree.api.Location;
import com.gumtree.api.User;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.domain.order.OrderItem;
import com.gumtree.web.seller.page.postad.common.AdUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;

import java.net.URISyntaxException;
import java.util.Optional;

public class ConvertrService {
    private static final String CONVERTR_BASE_URL = "https://gumtree.cvtr.io/forms/gumtree-v2";
    private final boolean isProduction;
    private final CategoryService categoryService;

    public ConvertrService(boolean isProduction, CategoryService categoryService) {
        this.isProduction = isProduction;
        this.categoryService = categoryService;
    }

    public Optional<String> buildConvertrUrl(Account account,
                                             User user,
                                             Order order,
                                             Ad ad,
                                             Category category) {
        boolean isPrivateAccount = !account.isPro();

        return isPrivateAccount && isPostingNewAd(order) && isCarsCategory(category)
                ? Optional.of(generateUrl(user, ad))
                : Optional.empty();
    }

    private String generateUrl(User user, Ad ad) {
        Optional<Attribute> vrn = AdUtils.findAttribute(ad, "vrn");
        String location = getLocation(ad);
        try {
            URIBuilder uriBuilder = new URIBuilder(CONVERTR_BASE_URL)
                    .addParameter("firstname", user.getFirstName())
                    .addParameter("lastname", user.getLastName())
                    .addParameter("email", user.getEmail())
                    .addParameter("telephone_number", ad.getPhoneNumber())
                    .addParameter("vehicle_reg", vrn.map(Attribute::getValue).orElse(null))
                    .addParameter("location", location);

            if (!isProduction) {
                uriBuilder.addParameter("qa", "yes");
            }

            return uriBuilder.build().toASCIIString();
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("Unable to build url due to invalid arguments", e);
        }
    }

    private String getLocation(Ad ad) {
        if (StringUtils.isNotEmpty(ad.getPostcode())) {
            return ad.getPostcode();
        }
        Location smallestLocation = ad.getSmallestLocation();
        if (smallestLocation != null) {
            return smallestLocation.getName();
        }
        return "United Kingdom";
    }

    private boolean isCarsCategory(Category category) {
        return categoryService.getCategoriesList(category).stream()
                .anyMatch(Categories.CARS::is);
    }

    private boolean isPostingNewAd(Order order) {
        return order.getItems().stream()
                .map(OrderItem::getProductName)
                .anyMatch(n -> n.equals(ProductName.INSERTION));
    }
}
