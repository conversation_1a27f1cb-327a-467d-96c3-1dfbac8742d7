package com.gumtree.web.seller.page.payment.braintree;

import com.gumtree.api.User;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.model.BillingAddress;
import com.gumtree.web.seller.page.payment.model.PaymentKeys;

public interface PaymentService {

    PaymentKeys getPaymentKeys(Long orderId);

    String paymentTransaction(
            Checkout checkout,
            String paymentMethodNonce,
            User user,
            String deviceData,
            Long accountId,
            BillingAddress billingAddress,
            String platformDevice);

    PaymentKeys getPaymentKeys(Long orderId, Long userid);

}
