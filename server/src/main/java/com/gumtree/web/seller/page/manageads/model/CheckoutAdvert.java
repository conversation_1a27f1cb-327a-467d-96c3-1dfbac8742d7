package com.gumtree.web.seller.page.manageads.model;

import java.io.Serializable;

public class CheckoutAdvert implements Serializable {

    private Long id;
    private String title;
    private Long categoryId;
    private Long locationId;
    private String sellerType;

    public Long getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public String getSellerType() {
        return sellerType;
    }

    public static final class Builder {

        private Builder() {
        }

        public static Builder builder() {
            return new Builder();
        }

        private Long id;
        private String title;
        private Long categoryId;
        private Long locationId;
        private String sellerType;

        public CheckoutAdvert.Builder id(Long id) {
            this.id = id;
            return this;
        }

        public CheckoutAdvert.Builder categoryId(Long categoryId) {
            this.categoryId = categoryId;
            return this;
        }

        public CheckoutAdvert.Builder locationId(Long locationId) {
            this.locationId = locationId;
            return this;
        }

        public CheckoutAdvert.Builder title(String title) {
            this.title = title;
            return this;
        }

        public CheckoutAdvert.Builder sellerType(String sellerType) {
            this.sellerType = sellerType;
            return this;
        }

        public CheckoutAdvert build() {
            CheckoutAdvert checkoutAdvert = new CheckoutAdvert();
            checkoutAdvert.id = this.id;
            checkoutAdvert.title = this.title;
            checkoutAdvert.categoryId = this.categoryId;
            checkoutAdvert.locationId = this.locationId;
            checkoutAdvert.sellerType = this.sellerType;
            return checkoutAdvert;
        }
    }
}
