package com.gumtree.web.seller.page.messagecentre.model;

import java.util.Objects;

public class ReviewMetadata {

    private Long reviewerUserId;
    private Long adId;

    public Long getReviewerUserId() {
        return reviewerUserId;
    }

    public void setReviewerUserId(Long reviewerUserId) {
        this.reviewerUserId = reviewerUserId;
    }

    public Long getAdId() {
        return adId;
    }

    public void setAdId(Long adId) {
        this.adId = adId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReviewMetadata review = (ReviewMetadata) o;
        return Objects.equals(reviewerUserId, review.reviewerUserId) &&
                Objects.equals(adId, review.adId);
    }

    @Override
    public int hashCode() {

        return Objects.hash(reviewerUserId, adId);
    }
}
