package com.gumtree.web.seller.page.motors.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.common.model.SummaryAttribute;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Optional;

public final class VehicleVerificationModel extends CommonModel {
    private List<SummaryAttribute> attributeList;
    private String enteredVrm;
    private Long categoryId;

    private VehicleVerificationModel(CoreModel core, VehicleVerificationModel.Builder builder) {
        super(core);
        this.attributeList = builder.attributeList;
        this.enteredVrm = builder.enteredVrm;
        this.categoryId = builder.categoryId;
    }

    public static Builder builder() {
        return new Builder();
    }

    public String getEnteredVrm() {
        return this.enteredVrm;
    }

    public String getAttributeValueForAttributeName(String attributeName) {
        Optional<SummaryAttribute> maybeAttribute = this.attributeList.stream()
                .filter(x -> x.getAttributeName().equals(attributeName)).findFirst();

        return maybeAttribute.map(x -> (x.getValueDisplayName() != null) ? x.getValueDisplayName().trim() : "").orElse("");
    }

    public Long getCategoryId() {
        return this.categoryId;
    }

    public static final class Builder {
        private Page page = Page.VehicleVerification;
        private List<SummaryAttribute> attributeList;
        private String enteredVrm;
        private Long categoryId;

        public Builder withAttributeList(List<SummaryAttribute> attributeList) {
            this.attributeList = attributeList;
            return this;
        }

        public Builder withEnteredVrm(String enteredVrm) {
            this.enteredVrm = enteredVrm;
            return this;
        }

        public Builder withCategoryId(Long categoryId) {
            this.categoryId = categoryId;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            coreModelBuilder.withTitle(CommonModel.createTitle("VehicleVerification"));
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new VehicleVerificationModel(coreModelBuilder.build(page), this));
        }
    }
}
