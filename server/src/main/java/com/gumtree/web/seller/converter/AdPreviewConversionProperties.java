package com.gumtree.web.seller.converter;

import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Component
public class AdPreviewConversionProperties {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdPreviewConversionProperties.class);
    @Value("${gumtree.srp.showMore.l1Categories:}")
    private String l1CategoriesWithShowTextSnippetsStr = "";
    private List<Long> l1CategoriesWithShowTextSnippets = Lists.newArrayList();

    @PostConstruct
    public void init() {
        l1CategoriesWithShowTextSnippets = splitByComaToListOfLongs(l1CategoriesWithShowTextSnippetsStr);
    }

    public List<Long> getL1CategoriesWithShowTextSnippets() {
        return l1CategoriesWithShowTextSnippets;
    }

    private List<Long> splitByComaToListOfLongs(String text) {
        String[] splitText = splitPropertyText(text);
        List longs = new ArrayList(splitText.length);
        for (String part : splitText) {
            String trimmed = part.trim();
            if (!trimmed.isEmpty()) {
                try {
                    longs.add(Long.parseLong(trimmed));
                } catch (NumberFormatException e) {
                    LOGGER.error("Invalid gumtree.showMore.l1Categories system property value: '" + text + "'");
                    return Lists.newArrayList();
                }
            }
        }
        return longs;
    }

    private String[] splitPropertyText(String text) {
        return (text == null || text.trim().isEmpty())
                ? new String[0]
                : text.split(",");
    }
}
