package com.gumtree.web.seller.service;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.StandardisedVehicleDataResponse;
import com.gumtree.service.category.CategoryService;
import com.gumtree.util.url.CdnImageUrlProvider;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.security.UserSecurityManager;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.ajax.vrm.MotorsApiClient;
import com.gumtree.web.seller.page.common.model.GaElement;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.model.*;
import com.gumtree.web.seller.page.postad.model.exception.UnknownAdvertEditorException;
import com.gumtree.web.seller.page.postad.service.CommonAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.PetAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.PhoneAttributesValidationService;
import com.gumtree.web.seller.page.postad.service.legal.PostAdLegalService;
import com.gumtree.web.seller.service.location.PostAdLocationService;
import com.gumtree.web.seller.service.postad.UserPostcodeLookupService;
import com.gumtree.web.seller.service.presentation.config.AttributePresentationService;
import com.gumtree.web.seller.service.pricing.PricingService;
import com.gumtree.web.seller.storage.DraftAdvertService;
import com.gumtree.web.seller.storage.SellerSessionDataService;
import com.gumtree.web.service.ContactEmailService;
import com.gumtree.zeno.core.service.ZenoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.validation.Validator;
import java.util.UUID;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_MILEAGE;

/**
 * Default implementation of {@link PostAdWorkspace}.
 */
@Component
public final class PostAdWorkspaceImpl implements PostAdWorkspace {

    private final SellerSessionDataService sessionDataService;
    private final UserSession userSession;
    private final UserSecurityManager userSecurityManager;
    private final AttributePresentationService attributePresentationService;
    private final BushfireApi bushfireApi;
    private final PostAdLocationService locationService;
    private final ApiCallExecutor apiCallExecutor;
    private final Validator validator;
    private final CategoryService categoryService;
    private final UserPostcodeLookupService userPostcodeLookupService;
    private final PostAdLegalService legalService;
    private final PricingService pricingService;
    private final CategoryModel categoryModel;
    private final ContactEmailService contactEmailService;
    private final DraftAdvertService draftAdvertService;
    private final MotorsApiClient motorsApiClient;
    private final ZenoService zenoService;
    private final CustomMetricRegistry metrics;

    private final CdnImageUrlProvider cdnImageUrlProvider;
    private final PetAttributesValidationService petAttributesValidationService;
    private final PhoneAttributesValidationService phoneAttributesValidationService;
  private final CommonAttributesValidationService commonAttributesValidationService;

    @Autowired
    public PostAdWorkspaceImpl(
            SellerSessionDataService sessionDataService,
            UserSession userSession,
            UserSecurityManager userSecurityManager,
            AttributePresentationService attributePresentationService,
            BushfireApi bushfireApi,
            PostAdLocationService locationService,
            ApiCallExecutor apiCallExecutor,
            Validator validator,
            CategoryService categoryService,
            UserPostcodeLookupService userPostcodeLookupService,
            PostAdLegalService legalService,
            PricingService pricingService,
            CategoryModel categoryModel,
            ContactEmailService contactEmailService,
            DraftAdvertService draftAdvertService,
            MotorsApiClient motorsApiClient,
            ZenoService zenoService,
            CustomMetricRegistry metrics,
            CdnImageUrlProvider cdnImageUrlProvider,
            PetAttributesValidationService petAttributesValidationService,
            PhoneAttributesValidationService phoneAttributesValidationService,
            CommonAttributesValidationService commonAttributesValidationService) {

        this.sessionDataService = sessionDataService;
        this.userSession = userSession;
        this.userSecurityManager = userSecurityManager;
        this.attributePresentationService = attributePresentationService;
        this.bushfireApi = bushfireApi;
        this.locationService = locationService;
        this.apiCallExecutor = apiCallExecutor;
        this.validator = validator;
        this.categoryService = categoryService;
        this.userPostcodeLookupService = userPostcodeLookupService;
        this.legalService = legalService;
        this.pricingService = pricingService;
        this.categoryModel = categoryModel;
        this.contactEmailService = contactEmailService;
        this.draftAdvertService = draftAdvertService;
        this.motorsApiClient = motorsApiClient;
        this.zenoService = zenoService;
        this.metrics = metrics;
        this.cdnImageUrlProvider = cdnImageUrlProvider;
        this.petAttributesValidationService = petAttributesValidationService;
        this.phoneAttributesValidationService = phoneAttributesValidationService;
      this.commonAttributesValidationService = commonAttributesValidationService;
    }

    @Override
    public AdvertEditor createAndPersistEditor(Long advertId,
                                               Long categoryId,
                                               RemoteIP ip,
                                               PermanentCookie permanentCookie,
                                               ThreatMetrixCookie threatMetrixCookie) {
        return createAndPersistEditor(advertId, categoryId, java.util.Optional.empty(), ip, permanentCookie,
                threatMetrixCookie, true);
    }

    @Override
    public AdvertEditor createAndPersistEditor(Long advertId,
                                               Long categoryId,
                                               java.util.Optional<String> vrm,
                                               RemoteIP ip,
                                               PermanentCookie permanentCookie,
                                               ThreatMetrixCookie threatMetrixCookie,
                                               boolean draftAdEnabled) {
        String newEditorId = UUID.randomUUID().toString().replaceAll("-", "");
        return createAndPersistEditorWithId(newEditorId, advertId, categoryId, vrm, ip, permanentCookie,
                threatMetrixCookie, draftAdEnabled);
    }

    @Override
    public AdvertEditor getEditor(String editorId) {
        return metrics.workspaceTimer("getEditor").record(() -> {
            PostAdDetail detail = sessionDataService.getPostAdData(editorId);

            if (detail == null) {
                throw new UnknownAdvertEditorException();
            }

            return newEditorInstance(editorId, detail);
        });
    }

    @Override
    public void updateEditor(String editorId, AdvertEditor advertEditor) {
        metrics.workspaceTimer("updateEditor").record(() -> {
            metrics.workspaceTimer("sessionDataService.setPostAdData").record(() ->
                    sessionDataService.setPostAdData(editorId, advertEditor.getAdvertDetail()));

            if (advertEditor.getAdvertDetail() != null
                    && advertEditor.getAdvertDetail().getAdvertId() == null
                    && userSession.isLoggedIn()) {
                metrics.workspaceTimer("draftAdvertService.persist").record(() ->
                        draftAdvertService.persist(advertEditor.getAdvertDetail()));
            }
        });
    }

    @Override
    public void removeEditor(String editorId, boolean createMode) {
        metrics.workspaceTimer("removeEditor").record(() -> {

            metrics.workspaceTimer("removePostAdData").record(() -> sessionDataService.removePostAdData(editorId));

            if (createMode) {
                metrics.workspaceTimer("draftAdvertService.clear").record(draftAdvertService::clear);
            }
        });
    }

    @Override
    public AdvertEditor getEditorByEditorId(String editorId) {
        return metrics.workspaceTimer("getEditorByEditorId").record(() -> {
            PostAdDetail detail = sessionDataService.getPostAdDataByEditorId(editorId);

            if (detail == null) {
                throw new UnknownAdvertEditorException();
            }

            return newEditorInstance(editorId, detail);
        });
    }

    @Override
    public void updateEditorByEditorId(String editorId, AdvertEditor advertEditor) {
        metrics.workspaceTimer("updateEditorByEditorId").record(() -> {
            metrics.workspaceTimer("sessionDataService.setPostAdDataByEditorId").record(() ->
                    sessionDataService.setPostAdDataByEditorId(editorId, advertEditor.getAdvertDetail()));

            if (advertEditor.getAdvertDetail() != null
                    && advertEditor.getAdvertDetail().getAdvertId() == null
                    && userSession.isLoggedIn()) {
                metrics.workspaceTimer("draftAdvertService.persist").record(() ->
                        draftAdvertService.persist(advertEditor.getAdvertDetail()));
            }
        });
    }

    @Override
    public void removeAllEditor(String editorId, boolean createMode) {
        metrics.workspaceTimer("removeAllEditor").record(() -> {
            metrics.workspaceTimer("removePostAdDataByEditorId").record(() -> sessionDataService.removePostAdDataByEditorId(editorId));
            metrics.workspaceTimer("removePostAdData").record(() -> sessionDataService.removePostAdData(editorId));

            if (createMode) {
                metrics.workspaceTimer("draftAdvertService.clear").record(draftAdvertService::clear);
            }
        });
    }

    @Override
    public AdvertEditor resetEditor(String editorId, RemoteIP ip, PermanentCookie permanentCookie, ThreatMetrixCookie threatMetrixCookie) {
        return metrics.workspaceTimer("resetEditor").record(() -> {
                    metrics.workspaceTimer("draftAdvertService.clear").record(draftAdvertService::clear);
                    return createAndPersistEditorWithId(editorId, null, null, java.util.Optional.empty(), ip,
                            permanentCookie, threatMetrixCookie, true);
                }
        );
    }

    private AdvertEditor createAndPersistEditorWithId(String editorId,
                                                      Long advertId,
                                                      Long categoryId,
                                                      java.util.Optional<String> vrn,
                                                      RemoteIP ip,
                                                      PermanentCookie cookie,
                                                      ThreatMetrixCookie threatMetrixCookie,
                                                      boolean draftAdEnabled) {
        return metrics.workspaceTimer("createAndPersistEditorWithId").record(() -> {

            Assert.notNull(editorId);
            Assert.notNull(ip);
            Assert.notNull(cookie);

            // Ensure there is a valid user at this point.
            userSession.validate();

            AdvertEditor advertEditor;
            Optional<PostAdDetail> draftAd = metrics.workspaceTimer("getDraftAdIfRequired").record(() -> getDraftAdIfRequired(draftAdEnabled && advertId == null));

            if (advertId != null) {
                advertEditor = newEditorInstance(editorId, new PostAdDetail());
                metrics.workspaceTimer("advertEditor.loadAdvert").record(() -> advertEditor.loadAdvert(advertId));
            } else if (draftAd.isPresent()) {
                advertEditor = newEditorInstance(editorId, draftAd.get());
                advertEditor.getAdvertDetail().setDraft(true);
            } else {
                advertEditor = newEditorInstance(editorId, new PostAdDetail());
                advertEditor.initNew(categoryId);

                metrics.workspaceTimer("fetchVrnDataIfVrnIsPresent").record(() -> fetchVrnDataIfVrnIsPresent(advertEditor, categoryId, vrn));
            }

            advertEditor.getAdvertDetail().setIpAddress(ip.getIpAddress());
            advertEditor.getAdvertDetail().setCookie(cookie.getId());
            metrics.workspaceTimer("refreshPreferredContactDetailsOfNonProUser")
                    .record(advertEditor::refreshPreferredContactDetailsOfNonProUser);
            advertEditor.getAdvertDetail().setThreatmetrixSessionId(threatMetrixCookie.getDefaultValue());

            updateEditor(editorId, advertEditor);
            return advertEditor;
        });
    }

    private AdvertEditor newEditorInstance(String editorId, PostAdDetail detail) {
        return new AdvertEditorImpl(editorId, detail, bushfireApi, userSession, userSecurityManager, userPostcodeLookupService,
                attributePresentationService, locationService, apiCallExecutor, validator, categoryService, legalService, pricingService,
                categoryModel, contactEmailService, motorsApiClient, zenoService, metrics,
                cdnImageUrlProvider, petAttributesValidationService,
                phoneAttributesValidationService, commonAttributesValidationService);
    }

    private Optional<PostAdDetail> getDraftAdIfRequired(boolean tryLoad) {
        boolean loadDraft = tryLoad && userSession.isLoggedIn();
        return loadDraft ? draftAdvertService.retrieve() : Optional.<PostAdDetail>absent();
    }

    private void fetchVrnDataIfVrnIsPresent(AdvertEditor editor, Long categoryId, java.util.Optional<String> vrm) {
        if (vrm.isPresent() && categoryModel.isSupportedAttribute(CategoryConstants.Attribute.VRN.getName(), categoryId)) {
            java.util.Optional<StandardisedVehicleDataResponse> lookupResp = motorsApiClient.lookupVehicleData(vrm.get(), categoryId);
            if (lookupResp.isPresent()) {
                editor.getAdvertDetail().setVrnWidgetFlow(true);

                GaElement.Builder gaElementBuilder = GaElement.builder("VRNFindSuccess");
                gaElementBuilder.withLabel("VrmWidget");
                editor.getAdvertDetail().addGaEvent(gaElementBuilder.build());

                editor.getPostAdFormBean().setVrmStatus(VrmStatus.VRM_VALID);

                lookupResp.get().getAttributes().stream().forEach(attr -> {
                    editor.getPostAdFormBean().getAttributes().put(attr.getName(), attr.getValue());
                    if (CategoryConstants.Attribute.VEHICLE_ESTIMATED_MILEAGE.getName().equals(attr.getName())) {
                        editor.getPostAdFormBean().getAttributes().put(VEHICLE_MILEAGE.getName(), attr.getValue());
                    }
                });
            } else {

                GaElement.Builder gaElementBuilder = GaElement.builder("VRNFindFail");
                gaElementBuilder.withLabel("VrmWidget");
                editor.getAdvertDetail().addGaEvent(gaElementBuilder.build());
                editor.getPostAdFormBean().setVrmStatus(VrmStatus.VRM_INVALID_OR_EMPTY);
            }
        }
    }
}
