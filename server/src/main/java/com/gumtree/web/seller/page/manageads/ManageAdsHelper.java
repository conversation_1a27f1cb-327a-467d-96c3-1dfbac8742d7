package com.gumtree.web.seller.page.manageads;

import com.gumtree.api.Account;
import com.gumtree.api.User;
import com.gumtree.util.model.Link;
import com.gumtree.web.seller.page.manageads.model.ManageAdsAccountSelectionFormBean;
import com.gumtree.web.security.UserSession;
import org.springframework.ui.Model;

import java.util.Map;

/**
 *
 */
public interface ManageAdsHelper {
    void addManageAdsUrls(Map<String, Object> model, Link currentPage);

    void addManageAdsUrls(Model model, Link currentPage);

    ManageAdsAccountSelectionFormBean getAccountSelectionForm(UserSession userSession);

    Account getSelectedAccount(UserSession userSession);

    User getSessionUser(UserSession userSession);

}
