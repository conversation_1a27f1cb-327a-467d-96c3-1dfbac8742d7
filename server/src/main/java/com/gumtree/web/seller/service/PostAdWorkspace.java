package com.gumtree.web.seller.service;

import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;

import java.io.Serializable;

/**
 *
 */
public interface PostAdWorkspace extends Serializable {

    AdvertEditor createAndPersistEditor(Long advertId,
                                        Long categoryId,
                                        RemoteIP remoteIP,
                                        PermanentCookie permanentCookie,
                                        ThreatMetrixCookie threatMetrixCookie);

    AdvertEditor createAndPersistEditor(Long advertId,
                                        Long categoryId,
                                        java.util.Optional<String> vrm,
                                        RemoteIP remoteIP,
                                        PermanentCookie permanentCookie,
                                        ThreatMetrixCookie threatMetrixCookie,
                                        boolean draftAdEnabled);

    AdvertEditor getEditor(String editorId);

    AdvertEditor getEditorByEditorId(String editorId);

    AdvertEditor resetEditor(String editorId, RemoteIP ip, PermanentCookie cookie, ThreatMetrixCookie threatMetrixCookie);

    void updateEditor(String editorId, AdvertEditor advertEditor);

    void updateEditorByEditorId(String editorId, AdvertEditor advertEditor);

    void removeEditor(String editorId, boolean createMode);

    void removeAllEditor(String editorId, boolean createMode);
}
