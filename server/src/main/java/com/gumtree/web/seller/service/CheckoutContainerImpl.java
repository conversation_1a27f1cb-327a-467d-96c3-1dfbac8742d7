package com.gumtree.web.seller.service;

import com.google.common.base.Strings;
import com.gumtree.api.Ad;
import com.gumtree.api.Attribute;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutAdvert;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.payment.model.CheckoutScene;
import com.gumtree.web.seller.storage.SellerSessionDataService;
import org.springframework.beans.factory.annotation.Autowired;
import com.gumtree.web.common.sapi.AdvertAttributeNames;

import java.util.Optional;
import java.util.UUID;

/**
 * TODO: Need to throw exception when checkout doesn't exist - handle in base controller (like for ad editors)
 */
public final class CheckoutContainerImpl implements CheckoutContainer {

    private SellerSessionDataService sessionDataService;

    @Autowired
    public CheckoutContainerImpl(SellerSessionDataService sessionDataService) {
        this.sessionDataService = sessionDataService;
    }

    /**
     * When trying to get a checkout by orderId, in the case your coming back from paypal and our system has crashed
     * create a new checkout and return it.
     *
     * @param order the order
     * @return a new checkout
     */
    @Override
    public Checkout createCheckout(ApiOrder order) {
        Checkout checkout = sessionDataService.getCheckoutByOrder(order);

        // Add checkout if the orderId was not found in the checkout workspace
        if (checkout != null) {
            return checkout;
        } else {
            checkout = addNewCheckout(order);
            sessionDataService.setCheckout(checkout.getKey(), checkout);
            return checkout;
        }
    }

    public Checkout createCheckout(ApiOrder order, Ad ad, boolean createOrEdit) {
        Checkout checkout = findOrCreateCheckoutByOrder(order);
        checkout.setCreateOrEdit(createOrEdit);
        checkout.setAdvert(checkoutAdvert(ad));
        sessionDataService.setCheckout(checkout.getKey(), checkout);
        return checkout;
    }

    @Override
    public Checkout createCheckout(ApiOrder order, Ad ad, boolean createOrEdit, CheckoutScene scene) {
        if (scene==CheckoutScene.PHONE_VERIFY){
            Checkout checkout = findOrCreateCheckoutByOrder(order);
            checkout.setCreateOrEdit(createOrEdit);
            checkout.setAdvert(checkoutAdvert(ad));
            sessionDataService.setCheckoutAll(checkout.getKey(), checkout);
            return checkout;
        }
        return createCheckout(order, ad, createOrEdit);
    }

    private CheckoutAdvert checkoutAdvert(Ad ad) {
        if (ad == null) {
            return null;
        }
        return CheckoutAdvert.Builder.builder()
                .id(ad.getId())
                .title(ad.getTitle())
                .categoryId(ad.getCategoryId())
                .locationId(ad.getLocationId())
                .sellerType(getSellerType(ad).orElse(null))
                .build();
    }

    static Optional<String> getSellerType(Ad advert) {
        return getAttribute(AdvertAttributeNames.SELLER_TYPE.getName(), advert.getAttributes());
    }

    static Optional<String> getAttribute(String name, Attribute[] attributes) {
        if (attributes == null) {
            return Optional.empty();
        }
        for (Attribute attribute : attributes) {
            if (name.equals(attribute.getName())) {
                return Optional.ofNullable(attribute.getValue());
            }
        }
        return Optional.empty();
    }

    @Override
    public Checkout getCheckout(String checkoutId) {
        if (Strings.isNullOrEmpty(checkoutId)) {
            throw new CheckoutNotFoundException(checkoutId);
        }

        Checkout checkout = sessionDataService.getCheckout(checkoutId);

        if (checkout == null) {
            throw new CheckoutNotFoundException(checkoutId);
        }

        return checkout;
    }

    @Override
    public void markCheckoutAsComplete(String checkoutId) {
        sessionDataService.setCheckoutComplete(checkoutId);
    }

    private Checkout findOrCreateCheckoutByOrder(ApiOrder order) {
        Checkout checkout = sessionDataService.getCheckoutByOrder(order);

        return (checkout != null ? checkout : addNewCheckout(order));
    }

    private Checkout addNewCheckout(ApiOrder order) {
        String randomId = UUID.randomUUID().toString().replaceAll("-", "");
        Checkout checkout = new CheckoutImpl();
        checkout.setKey(randomId);
        checkout.setOrder(order);
        return checkout;
    }

}
