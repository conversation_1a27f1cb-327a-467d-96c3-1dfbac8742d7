package com.gumtree.web.seller.page.common.model;

import com.gumtree.web.seller.page.manageads.ManageAdsController;
import org.springframework.web.servlet.ModelAndView;

public final class PageExpiredModel extends CommonModel {

    private String homepageLink;

    private String srcPage;

    private PageExpiredModel(CoreModel core, Builder builder) {
        super(core);
        this.homepageLink = builder.homepageLink;
        this.srcPage = builder.srcPage;
    }

    public final String getManageAdsPageLink() {
        return ManageAdsController.PAGE_PATH;
    }

    public String getSrcPage() {
        return srcPage;
    }

    public String getHomepageLink() {
        return homepageLink;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private String srcPage;

        private String homepageLink;

        public Builder withHomepageLink(String homepageLink) {
            this.homepageLink = homepageLink;
            return this;
        }

        public Builder withSrcPage(String src) {
            this.srcPage = src;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.PageExpired;
            coreModelBuilder.withTitle("Expired");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new PageExpiredModel(coreModelBuilder.build(page), this));
        }

    }
}
