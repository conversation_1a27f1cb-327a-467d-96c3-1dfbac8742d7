package com.gumtree.web.seller.page.postad.model;

import com.gumtree.web.seller.page.common.model.SummaryAttribute;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.gumtree.util.GtCollectors.toLinkedMap;

/**
 * Models a group of attributes.
 */
public final class PostAdAttributeGroup {

    private String label;

    private String panelId;

    private String id;

    private boolean highPriority;

    /**
     * Static attribute data as map where key is attribute name for easy lookup in the views
     */
    private Map<String, PostAdAttribute> attributes;

    /**
     * User entered attribute data (with min required static data)
     */
    private Map<String, SummaryAttribute> formAttributes;

    public Map<String, PostAdAttribute> getAttributes() {
        return attributes;
    }

    public Map<String, SummaryAttribute> getFormAttributes() {
        return formAttributes;
    }

    public String getLabel() {
        return label;
    }

    public String getId() {
        return id;
    }

    public String getPanelId() {
        return panelId;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private PostAdAttributeGroup result = new PostAdAttributeGroup();

        public PostAdAttributeGroup build() {
            if (result.attributes == null) {
                result.attributes = new HashMap<>();
            }
            return result;
        }

        public Builder setLabel(String label) {
            result.label = label;
            return this;
        }

        public Builder setPanelId(String panelId) {
            result.panelId = panelId;
            return this;
        }

        public Builder setId(String id) {
            result.id = id;
            return this;
        }

        public Builder setHighPriority(boolean highPriority) {
            result.highPriority = highPriority;
            return this;
        }

        public Builder setAttributes(@Nullable List<PostAdAttribute> attributes, @Nullable Map<String, String> formAttributes) {
            if (attributes != null) {
                final Map<String, String> notNullFormAttrs = formAttributes == null ? new HashMap<>() : formAttributes;
                // use linked hashmap to preserver order of attributes while we are converting list to map
                result.attributes = attributes.stream().collect(toLinkedMap(PostAdAttribute::getId, m -> m));
                result.formAttributes = attributes.stream()
                        .map(a -> {
                            Optional<PostAdAttributeValue> val = a.getSelectedValue();
                            return SummaryAttribute.builder()
                                    .setAttributeName(a.getId())
                                    .setAttributeDisplayName(a.getLabel())
                                    .setValue(val.isPresent() ? val.get().getValue() : notNullFormAttrs.get(a.getId()))
                                    .setValueDisplayName(val.isPresent() ? val.get().getDisplayValue() : notNullFormAttrs.get(a.getId()))
                                    .build();
                        })
                        .collect(toLinkedMap(SummaryAttribute::getAttributeName, m -> m));
            }
            return this;
        }
    }

    public String getPanelIdOrDefault() {
        return panelId != null ? panelId : PostAdFormPanel.ATTRIBUTE_PANEL.getId();
    }

    public boolean isHighPriority() {
        return highPriority;
    }

    public void setHighPriority(boolean highPriority) {
        this.highPriority = highPriority;
    }
}
