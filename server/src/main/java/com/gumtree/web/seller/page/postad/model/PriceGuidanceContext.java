package com.gumtree.web.seller.page.postad.model;

import java.util.Map;
import java.util.Objects;

public class PriceGuidanceContext {

    private final PriceGuidance priceGuidance;

    private final Map<String, String> vehicleAttributes;

    public PriceGuidanceContext(PriceGuidance priceGuidance, Map<String, String> vehicleAttributes) {
        this.priceGuidance = priceGuidance;
        this.vehicleAttributes = vehicleAttributes;
    }

    public PriceGuidance getPriceGuidance() {
        return priceGuidance;
    }

    public Map<String, String> getVehicleAttributes() {
        return vehicleAttributes;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PriceGuidanceContext that = (PriceGuidanceContext) o;
        return Objects.equals(priceGuidance, that.priceGuidance) &&
                Objects.equals(vehicleAttributes, that.vehicleAttributes);
    }

    @Override
    public int hashCode() {
        return Objects.hash(priceGuidance, vehicleAttributes);
    }

    @Override
    public String toString() {
        return "PriceGuidanceContext{" +
                "priceGuidance=" + priceGuidance +
                ", vehicleAttributes=" + vehicleAttributes +
                '}';
    }

}
