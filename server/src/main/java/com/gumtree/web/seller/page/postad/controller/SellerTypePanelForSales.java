package com.gumtree.web.seller.page.postad.controller;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.gumtree.api.Account;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.common.util.messages.ForSaleMessages;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdAttribute;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeValue;

import java.util.Map;


public class SellerTypePanelForSales implements SellerTypePanel {


    private final AdvertEditor editor;
    private final String tradeValue;
    private final String privateValue;
    private final String tradeLabel;
    private final String privateLabel;
    private final Optional<Account> account;

    public SellerTypePanelForSales(<PERSON><PERSON><PERSON><PERSON> bushfireApi, AdvertEditor editor, AttributeMetadata attributeMetadata) {
        this.editor = editor;

        if (editor.getAccountId() != null) {
            this.account = Optional.fromNullable(bushfireApi.accountApi().getAccount(editor.getAccountId()));
        } else {
            this.account = Optional.absent();
        }

        this.tradeValue = attributeMetadata.getSrp().getFilters().get(0).getValues().get(0).getValue();
        this.privateValue = attributeMetadata.getSrp().getFilters().get(0).getValues().get(1).getValue();
        this.tradeLabel = attributeMetadata.getSrp().getFilters().get(0).getValues().get(0).getLabel();
        this.privateLabel = attributeMetadata.getSrp().getFilters().get(0).getValues().get(1).getLabel();
    }

    @Override
    public String getId() {
        return "seller-type-group";
    }

    @Override
    public String getLabel() {
        return "Seller Type";
    }

    @Override
    public Map<String, String> getPopUp() {
        return null;
    }

    @Override
    public Map<String, String> getText() {
        String sellerType = getSellerType();
        if (tradeValue.equals(sellerType) || (account.isPresent() && account.get().isPro())) {
            return ImmutableMap.of("body", ForSaleMessages.FOR_SALE_REVIEW_BUSINESS);
        }
        return null;
    }

    @Override
    public PostAdAttribute getAttribute() {
        PostAdAttribute postAdAttribute = new PostAdAttribute();
        postAdAttribute.setId(getId());
        postAdAttribute.setMandatory(true);
        postAdAttribute.setPriceSensitive(true);
        postAdAttribute.setValues(getAttributeValues());

        return postAdAttribute;
    }

    private ImmutableList<PostAdAttributeValue> getAttributeValues() {
        String selectedValue = getSellerType();

        if (account.isPresent() && account.get().isPro()) {
            return ImmutableList.of(
                    new PostAdAttributeValue(tradeValue, tradeLabel, true, false),
                    new PostAdAttributeValue(privateValue, privateLabel, false, true));
        }
        if (selectedValue != null) {
            return ImmutableList.of(
                    new PostAdAttributeValue(tradeValue, tradeLabel, tradeValue.equals(selectedValue), false),
                    new PostAdAttributeValue(privateValue, privateLabel, privateValue.equals(selectedValue), false));
        }
        return ImmutableList.of(
                new PostAdAttributeValue(tradeValue, tradeLabel, false, false),
                new PostAdAttributeValue(privateValue, privateLabel, true, false));
    }

    @Override
    public boolean isSellerTypeSelected() {
        return true;
    }

    @Override
    public String getSellerType() {
        return editor.getPostAdFormBean().getAttributes().get(CategoryConstants.Attribute.SELLER_TYPE.getName());
    }
}
