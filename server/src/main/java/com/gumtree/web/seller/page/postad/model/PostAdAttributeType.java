package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;

import java.util.HashMap;
import java.util.Map;

/**
 * The set of post ad specific attribute types.
 */
public enum PostAdAttributeType {

    CHECKBOX, TEXTFIELD, DATE, DROPDOWN, RADIO, CURRENCY, NUMBERFIELD;

    private static final Map<AttributeType, PostAdAttributeType> DEFAULT_TYPE_MAPPINGS = new HashMap<>();

    static {
        DEFAULT_TYPE_MAPPINGS.put(AttributeType.BOOLEAN, PostAdAttributeType.RADIO);
        DEFAULT_TYPE_MAPPINGS.put(AttributeType.STRING, PostAdAttributeType.TEXTFIELD);
        DEFAULT_TYPE_MAPPINGS.put(AttributeType.INTEGER, PostAdAttributeType.TEXTFIELD);
        DEFAULT_TYPE_MAPPINGS.put(AttributeType.ENUM, PostAdAttributeType.DROPDOWN);
        DEFAULT_TYPE_MAPPINGS.put(AttributeType.DATETIME, PostAdAttributeType.DATE);
        DEFAULT_TYPE_MAPPINGS.put(AttributeType.LONG, PostAdAttributeType.NUMBERFIELD);
        DEFAULT_TYPE_MAPPINGS.put(AttributeType.YEAR, PostAdAttributeType.DROPDOWN);
        DEFAULT_TYPE_MAPPINGS.put(AttributeType.CURRENCY, PostAdAttributeType.CURRENCY);
    }

    /**
     * Determine a {@link PostAdAttributeType} for the given {@link AttributeMetadata}.
     *
     * @param attributeMetadata the source metadata
     * @return a {@link PostAdAttributeType} for the given {@link AttributeMetadata}.
     */
    public static PostAdAttributeType typeFor(AttributeMetadata attributeMetadata) {
        // All enum types with only two values become radio buttons.
        AttributeType aType = attributeMetadata.getType();
        if ("operating_system".equals(attributeMetadata.getName())) {
            return PostAdAttributeType.DROPDOWN;
        }

        if (isEnumWithTwoValues(attributeMetadata)) {
            return PostAdAttributeType.RADIO;
        }

        if (attributeMetadata.getSyi().hasValues()) {
            return PostAdAttributeType.DROPDOWN;
        }

        // Nothing found - revert to defaults.
        return DEFAULT_TYPE_MAPPINGS.get(aType);
    }

    private static boolean isEnumWithTwoValues(AttributeMetadata attributeMetadata) {
        return attributeMetadata.getSyi().getValues().size() == 2;
    }
}
