package com.gumtree.web.seller.page.postad.model.location;

import java.math.BigDecimal;

public class MapDetails {

    private BigDecimal latitude;
    private BigDecimal longitude;
    private BigDecimal radius;

    public MapDetails(BigDecimal latitude, BigDecimal longitude, BigDecimal radius) {
        this.latitude = latitude;
        this.longitude = longitude;
        this.radius = radius;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public BigDecimal getRadius() {
        return radius;
    }
}
