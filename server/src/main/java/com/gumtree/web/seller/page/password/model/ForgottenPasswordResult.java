package com.gumtree.web.seller.page.password.model;

import com.gumtree.web.seller.page.password.controller.ResetPasswordFormBean;

public class ForgottenPasswordResult {

    private ResetPasswordFormBean resetPasswordFormBean;
    private boolean withError;

    public ForgottenPasswordResult() {
        // required by spring mvc to map models
    }

    private ForgottenPasswordResult(Builder builder) {
        this.resetPasswordFormBean = builder.resetPasswordFormBean;
        this.withError = builder().withError;
    }

    public boolean isWithError() {
        return withError;
    }

    public ResetPasswordFormBean getResetPasswordFormBean() {
        return resetPasswordFormBean;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        private ResetPasswordFormBean resetPasswordFormBean;
        private boolean withError;

        public Builder resetPasswordFormBean(ResetPasswordFormBean resetPasswordFormBean) {
            this.resetPasswordFormBean = resetPasswordFormBean;
            return this;
        }

        public Builder withError(boolean withError) {
            this.withError = withError;
            return this;
        }

        public ForgottenPasswordResult build() {
            return new ForgottenPasswordResult(this);
        }
    }
}
