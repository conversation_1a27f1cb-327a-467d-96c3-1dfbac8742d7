package com.gumtree.web.seller.page.openidconnect.controller;

import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.gas.OpenIdConnectService;
import com.gumtree.gas.TokenInfo;
import com.gumtree.gas.UserInfo;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.zeno.core.domain.PageType;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import scala.collection.JavaConversions;
import scala.collection.immutable.Map$;
import scala.collection.immutable.Set$;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.gumtree.web.seller.page.openidconnect.controller.OIDCAuthController.PAGE_PATH;

@Controller
@RequestMapping(PAGE_PATH)
@GumtreePage(PageType.Unknown)
public class OIDCAuthController extends BaseSellerController {
    public static final String PAGE_PATH = "/oidc/authorise";

    private OpenIdConnectService oidcService;
    private UserSession userSession;

    @Autowired
    public OIDCAuthController(CookieResolver cookieResolver, CategoryModel categoryModel,
                              ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                              UrlScheme urlScheme, OpenIdConnectService oidcService, UserSession userSession,
                              UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.oidcService = oidcService;
        this.userSession = userSession;
    }

    /**
     * Get the authorisation token
     */
    @RequestMapping(method = RequestMethod.GET)
    public void checkToken(HttpServletRequest request, HttpServletResponse response) throws IOException {
        UserInfo userInfo = getUserInfo(userSession);
        oidcService.getAuthorisationCode(request, response, userInfo);
    }

    private UserInfo getUserInfo(UserSession userSession) {
        User user = userSession.getUser();
        Map<String, Object> data = new HashMap<>();
        data.put("accountsId", user.getAccountIds());
        data.put("privateKey", user.getApiKey().getPrivateKey());
        data.put("bapiKey", user.getApiKey().getAccessKey());
        scala.collection.mutable.Map<String, Object> converted = JavaConversions.mapAsScalaMap(data);

        //we add a dummy token to avoid NPE on serialisation
        TokenInfo dummyToken = new TokenInfo("", "", DateTime.now(), "", Set$.MODULE$.<String>empty());

        return new UserInfo(user.getEmail(), user.getId(), dummyToken, Map$.MODULE$.apply(converted.toSeq()));
    }
}
