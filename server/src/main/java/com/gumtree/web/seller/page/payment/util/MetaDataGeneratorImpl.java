package com.gumtree.web.seller.page.payment.util;

import com.gumtree.api.AdStatus;
import com.gumtree.api.domain.order.ApiOrderItem;
import com.gumtree.seller.domain.order.status.OrderStatus;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.postad.model.meta.PageActionType;
import com.gumtree.web.seller.page.postad.model.meta.PagePaymentType;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by mdivilioglu on 6/15/17.
 */
@Component
public class MetaDataGeneratorImpl implements MetaDataGenerator {
    private static final String INSERTION_PREFIX = "INSERT";

    private static List<ProductType> featuresOfInterest = Arrays.asList(ProductType.BUMP_UP,
            ProductType.URGENT,
            ProductType.FEATURED,
            ProductType.SPOTLIGHT);

    private Function<ApiOrderItem, ProductType> convertFeatureAdToTopAd = new Function<ApiOrderItem, ProductType>() {
        @Override
        public ProductType apply(ApiOrderItem apiOrderItem) {
            String name = apiOrderItem.getProductName().name().trim();
            if (name.startsWith("FEATURE")) {
                return ProductType.FEATURED;
            }
            if (name.startsWith("HOMEPAGE_SPOTLIGHT")) {
                return ProductType.SPOTLIGHT;
            } else {
                return ProductType.valueOf(name);
            }
        }
    };

    public List<PagePaymentType> getPaymentTypes(Checkout checkout) {
        boolean isPaying = !(checkout.getOrder().getStatus() == OrderStatus.PAID);

        if(!isPaying) {
            return new ArrayList<PagePaymentType>();
        }
        Optional<ApiOrderItem> itemMaybe = getFeatureItemsWithPrice(checkout)
                .stream().filter(x -> x.getProductName().name().startsWith(INSERTION_PREFIX)).findFirst();

        List<PagePaymentType> paymentTypes = new ArrayList<PagePaymentType>();
        itemMaybe.ifPresent(x -> {
            paymentTypes.add(PagePaymentType.INSERTION);
        });

        Long numberOfFeatures = getNumberOfFutures(checkout, convertFeatureAdToTopAd);
        boolean hasFeatures = numberOfFeatures > 0;

        if (hasFeatures) {
            paymentTypes.add(PagePaymentType.FEATURE);
        }

        return paymentTypes;
    }

    private Long getNumberOfFutures(Checkout checkout, Function<ApiOrderItem, ProductType> convertFeatureNames) {
        return getFeatureItemsWithPrice(checkout).stream()
                .map(convertFeatureNames)
                .filter(x -> featuresOfInterest.contains(x)).count();
    }

    private List<ApiOrderItem> getFeatureItemsWithPrice(Checkout checkout) {
        List<ApiOrderItem> items = ((checkout != null) && (checkout.getOrder()) != null && (checkout.getOrder().getItems() != null))
                ? (checkout.getOrder().getItems()) : new ArrayList<ApiOrderItem>();

        return items.stream()
                .filter(x -> (x != null) && (x.getPriceIncVat() != null) && (x.getPriceIncVat() > 0))
                .collect(Collectors.toList());

    }

    public PageActionType getPageAction(Checkout checkout, AdStatus originalStatus) {
        if (originalStatus == AdStatus.DELETED_USER) {
            return PageActionType.RELIST;
        } else if (originalStatus == AdStatus.EXPIRED) {
            return PageActionType.RELIST;
        } else {
            boolean isEditorOpen = checkout.isCreateOrEdit();
            List<ApiOrderItem> items = ((checkout != null) && (checkout.getOrder()) != null && (checkout.getOrder().getItems() != null))
                    ? (checkout.getOrder().getItems()) : new ArrayList<ApiOrderItem>();

            boolean isFirstTime = items.stream()
                    .filter(x -> x.getProductName().name().toUpperCase().startsWith(INSERTION_PREFIX))
                    .count() > 0;

            if (isEditorOpen && isFirstTime) {
                return PageActionType.POST;
            } else {
                return PageActionType.UPDATE;
            }
        }
    }

    public List<ProductType> getPageFeatureTypes(Checkout checkout) {
        boolean isPaying = !(checkout.getOrder().getStatus() == OrderStatus.PAID);

        if(!isPaying) {
            return new ArrayList<ProductType>();
        }

        List<ApiOrderItem> features = getFeatureItemsWithPrice(checkout);

        return features.stream()
                .map(convertFeatureAdToTopAd).filter(x -> !x.equals(ProductType.INSERTION)).distinct().collect(Collectors.toList());

    }

}
