package com.gumtree.web.seller.service.presentation.config;

import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.function.Function2;
import com.gumtree.api.category.domain.syi.AttributeSyiMetadata;
import com.gumtree.api.category.domain.syi.SyiAttributeValueMetadata;
import com.gumtree.common.util.StringUtils;
import com.gumtree.web.seller.page.postad.model.PostAdAttribute;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeType;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeValue;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.util.List;
import java.util.Map;

/**
 * Models a presentable attribute.
 */
public final class PresentableAttribute {

    private String id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    /**
     * Convert this {@link PresentableAttribute} to a {@link PostAdAttribute} by merging it together
     * with core attribute metadata from API.
     *
     * @param metadata the core attribute metadata from the API.
     * @return a newly created {@link PostAdAttribute}.
     */
    public PostAdAttribute toPostAdAttribute(@Nonnull AttributeMetadata metadata,
                                             @Nonnull Map<String, String> currentAttributeValues) {
        Assert.notNull(metadata);
        Assert.notNull(currentAttributeValues);

        if (!id.equals(metadata.getName())) {
            throw new IllegalArgumentException("The metadata name does not match the attribute id: " + id
                    + " => " + metadata.getName());
        }
        PostAdAttribute.Builder postAdAttrBuilder = PostAdAttribute.builder();
        postAdAttrBuilder.id(id);
        postAdAttrBuilder.description(metadata.getDescription());
        postAdAttrBuilder.displayLabel(metadata.isDisplayLabel());
        postAdAttrBuilder.mandatory(metadata.isRequired());
        postAdAttrBuilder.type(PostAdAttributeType.typeFor(metadata));
        postAdAttrBuilder.priceSensitive(metadata.isPriceSensitive());
        if (metadata.getSyi().getLabel() != null) {
            postAdAttrBuilder.label(metadata.getSyi().getLabel());
        } else {
            postAdAttrBuilder.label(metadata.getLabel());
        }

        addAttributeValuesFromMetaData(postAdAttrBuilder, metadata, currentAttributeValues);

        return postAdAttrBuilder.build();
    }

    private void addAttributeValuesFromMetaData(PostAdAttribute.Builder builder,
                                                AttributeMetadata metadata,
                                                @Nonnull Map<String, String> currentAttributeValues) {
        if (metadata.getSyi().hasValues()) {
            List<SyiAttributeValueMetadata> metaValues = metadata.getSyi().getValues();

            // For drop-downs, we want to populate values with a "Please Select..." option.
            if (PostAdAttributeType.typeFor(metadata) == PostAdAttributeType.DROPDOWN) {
                if (metaValues.size() > 0) {
                    builder.withValue("", "Please select...");
                }
            }

            String currentValue = currentAttributeValues.get(metadata.getName());
            com.google.common.base.Optional<AttributeSyiMetadata.MatchResult> selectedValue = metadata.getSyi().findValue(currentValue);

            List<PostAdAttributeValue> postAdAttrValues =
                    metadata.getSyi().getValues(new AttributeValue2PostAdAttributeValue(selectedValue, currentValue));

            builder.withValues(postAdAttrValues);
        }
    }

    /**
     * Merge another attribute into this one, creating a brand new attribute representing the
     * merged result.
     * <p/>
     * The logic gives priority to data with this object, i.e. this object will override data in
     * the merge source where it's defined.
     *
     * @param attribute the merge source
     * @return the merged result of this and the merge source
     */
    public PresentableAttribute merge(PresentableAttribute attribute) {

        if (!id.equals(attribute.getId())) {
            throw new IllegalArgumentException("Can't merge an attribute with a different id: "
                    + id
                    + " => " + attribute.getId());
        }

        PresentableAttribute presentationAttribute = new PresentableAttribute();
        presentationAttribute.setId(id);

        return presentationAttribute;
    }

    /**
     * @return create a deep clone of this object.
     */
    @Override
    public PresentableAttribute clone() {
        PresentableAttribute copy = new PresentableAttribute();
        copy.setId(id);

        return copy;
    }

    /**
     * @return a new {@link Builder}.
     */
    public static Builder aPresentableAttribute() {
        return new Builder();
    }

    /**
     * Builder for {@link PresentableAttribute}s.
     */
    public static final class Builder {

        private PresentableAttribute attribute = new PresentableAttribute();

        /**
         * Set an ID
         * @param id - the ID
         * @return this
         */
        public Builder withId(String id) {
            attribute.setId(id);
            return this;
        }

        /**
         * Build the presentable attribute
         * @return the attribute
         */
        public PresentableAttribute build() {
            return attribute.clone();
        }
    }

    public static class AttributeValue2PostAdAttributeValue implements Function2<SyiAttributeValueMetadata, String, PostAdAttributeValue> {
        private final com.google.common.base.Optional<AttributeSyiMetadata.MatchResult> selectedValueMetadata;
        private final String currentValue;

        public AttributeValue2PostAdAttributeValue(com.google.common.base.Optional<AttributeSyiMetadata.MatchResult> selectedValueMetadata,
                                                   String currentValue) {
            this.selectedValueMetadata = selectedValueMetadata;
            this.currentValue = currentValue;
        }

        @Override
        public PostAdAttributeValue apply(SyiAttributeValueMetadata valueMetadata, String convertedValue) {
            boolean selected = selectedValueMetadata.isPresent() && selectedValueMetadata.get().getValueMetadata().equals(valueMetadata);
            String value = selected && currentValue != null ? currentValue : convertedValue;
            String label = StringUtils.hasText(valueMetadata.getLabel()) ? valueMetadata.getLabel() : value;
            return new PostAdAttributeValue(value,  label, selected, false);
        }
    }
}
