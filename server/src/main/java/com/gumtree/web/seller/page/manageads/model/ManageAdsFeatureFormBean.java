package com.gumtree.web.seller.page.manageads.model;

import com.google.common.collect.Lists;
import com.gumtree.web.seller.model.AdPreview;
import com.gumtree.web.common.page.manageads.AdvertFeatureMatrix;
import com.gumtree.web.seller.page.manageads.ManageAdsController;

import java.util.List;

public final class ManageAdsFeatureFormBean {
    private List<AdPreview> previews = Lists.newArrayList();

    private AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

    public String getAction() {
        return ManageAdsController.PAGE_PATH;
    }

    public List<AdPreview> getPreviews() {
        return previews;
    }

    public AdvertFeatureMatrix getAdvertFeatureMatrix() {
        return advertFeatureMatrix;
    }
}
