package com.gumtree.web.seller.page.manageads.api;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.domain.advert.NewStatusBean;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;

/**
 */
public final class NewAdvertStatusApiCall extends AuthenticatedApiCall<Void> {

    private NewStatusBean newStatusBean;

    private String advertId;

    /**
     * Constructor
     *
     * @param advertId       - advert id
     * @param newStatusBean  - status bean
     * @param apiKeyProvider - user's api key
     */
    public NewAdvertStatusApiCall(String advertId, NewStatusBean newStatusBean, ApiKeyProvider apiKeyProvider) {
        super(apiKeyProvider);
        this.advertId = advertId;
        this.newStatusBean = newStatusBean;
    }

    @Override
    public Void execute(BushfireApi api) {
        api.create(AdvertApi.class, getApiKey()).newAdvertStatus(getAdvertId(), getNewStatusBean());
        return null;
    }

    public NewStatusBean getNewStatusBean() {
        return newStatusBean;
    }

    public String getAdvertId() {
        return advertId;
    }
}
