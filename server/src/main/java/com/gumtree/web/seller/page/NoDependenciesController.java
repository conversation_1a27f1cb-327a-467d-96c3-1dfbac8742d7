package com.gumtree.web.seller.page;

import com.google.common.base.Strings;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.web.common.page.util.RedirectViewWithRequiredModelKeys;
import com.gumtree.web.common.path.Path;
import com.gumtree.web.seller.page.common.model.Page;
import org.apache.commons.collections.KeyValue;
import org.apache.commons.collections.keyvalue.DefaultKeyValue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ui.ExtendedModelMap;
import org.springframework.ui.Model;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class NoDependenciesController {

    private static final List<String> GA_TRACKING_KEYS = Arrays.asList("utm_source", "utm_medium", "utm_campaign");

    public static final String ENCRYPTED_PARAMETER_MAP_NAME = "gt_d";

    class ModelException extends RuntimeException {
        ModelException(String s) {
            super(s);
        }
    }

    protected ModelAndView view(Page page) {
        if (page == null) {
            throw new ModelException("Null page parameter");
        }
        ModelAndView mav = new ModelAndView(page.getTemplateName());
        mav.addObject("pageName", page.getTemplateName());
        mav.addObject("pageType", page.getPageType());
        return mav;
    }

    protected ModelAndView redirect(Path path) {
        if (path == null) {
            throw new ModelException("Null path parameter on redirect");
        }
        return redirect(path.getPath());
    }

    protected ModelAndView redirect(String url) {
        if (Strings.isNullOrEmpty(url)) {
            throw new ModelException("Null path parameter on redirect");
        }
        // note we are not http/1.0 compatible due to a bug in spring that looses https when redirecting and being 1.0 compatible
        // given all browsers we support use http/1.1 and only bots may not be 1.1 compatible, and that seller
        // is not indexed by Google... no harm should come from this
        RedirectView redirect = new RedirectView(url, false, false, false);
        return new ModelAndView(redirect);
    }

    /**
     * Build a map containing the Google Analytics tracking parameters
     * @param params - request parameters
     * @return
     */
    private Map<String, Object> allowedQueryParams(Map<String, String> params) {
        HashMap<String, Object> allowedQueryParams = new HashMap<>();
        if(params.keySet().containsAll(GA_TRACKING_KEYS)) {
            GA_TRACKING_KEYS.forEach(key -> allowedQueryParams.put(key, params.get(key)));
        }
        if(params.containsKey(ENCRYPTED_PARAMETER_MAP_NAME)) {
            allowedQueryParams.put(ENCRYPTED_PARAMETER_MAP_NAME, params.get(ENCRYPTED_PARAMETER_MAP_NAME));
        }
        return allowedQueryParams;
    }

    protected final ModelAndView createParametrizedRedirect(String path, Map<String, Object> queryParams) {
        return createParametrizedRedirectPopulatingModel(path, new ExtendedModelMap(), queryParams, false);
    }

    protected final ModelAndView createRedirectWithTracking(String path, Map<String, String> queryParams) {
        return createParametrizedRedirectPopulatingModel(path, new ExtendedModelMap(),
                allowedQueryParams(queryParams), true);
    }

    protected final ModelAndView createParametrizedRedirectPopulatingModel(
            String path, Model model, Map<String, Object> queryParams, boolean appendAttributesToUrl) {
        if (queryParams != null) {
            for (Map.Entry<String, Object> kv : queryParams.entrySet()) {
                if (!model.containsAttribute(kv.getKey())) {
                    model.addAttribute(kv.getKey(), kv.getValue());
                }
            }
        }
        RedirectViewWithRequiredModelKeys view = new RedirectViewWithRequiredModelKeys(path,
                queryParams.keySet(), appendAttributesToUrl);
        return new ModelAndView(view, model.asMap());
    }

    protected String getFullURL(HttpServletRequest request) {
        StringBuilder requestURL = new StringBuilder(GtProps.getStr(SellerProperty.MY_GUMTREE_HOST))
                .append(request.getRequestURI());
        String queryString = request.getQueryString();

        if (!StringUtils.isEmpty(queryString)) {
            requestURL.append('?').append(queryString);
        }
        return requestURL.toString();
    }

    /**
     * Create a {@link org.apache.commons.collections.KeyValue} representing a query param.
     *
     * @param key   the key
     * @param value the value
     * @return a {@link org.apache.commons.collections.KeyValue} representing a query param.
     */
    protected final KeyValue queryParam(String key, String value) {
        return new DefaultKeyValue(key, value);
    }

    /**
     * Create a redirect view name.
     *
     * @param path the path
     * @return a redirect view name.
     */
    protected final String createRedirect(String path) {
        return (createRedirect(path, new KeyValue[]{}));
    }

    /**
     * Create a redirect view name with query string
     *
     * @param path        the path
     * @param queryParams the query params
     * @return a redirect view name with query string
     */
    protected final String createRedirect(String path, KeyValue... queryParams) {
        StringBuilder builder = new StringBuilder("redirect:");
        builder.append(path);
        if (queryParams != null && queryParams.length > 0) {
            builder.append("?");
            int index = 0;
            for (KeyValue kv : queryParams) {
                if (index > 0) {
                    builder.append("&");
                }
                builder.append(kv.getKey()).append("=").append(kv.getValue());
                index++;
            }
        }
        return builder.toString();
    }

    protected final ModelAndView createRedirectTo(String path) {
        return new ModelAndView(new RedirectView(path));
    }
}
