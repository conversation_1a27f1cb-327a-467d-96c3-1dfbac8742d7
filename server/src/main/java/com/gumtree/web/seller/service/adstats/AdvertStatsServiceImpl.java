package com.gumtree.web.seller.service.adstats;

import com.gumtree.api.Ad;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounterService;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * todo test this class
 */
@Service
public class AdvertStatsServiceImpl implements AdvertStatsService {

    public static final int PAGE_SIZE = 100;

    private AdvertApi advertApi;
    private BatchAdStatsProvider batchAdStatsProvider;
    private AdCounterService adCounterService;

    private CustomMetricRegistry metrics;


    /**
     * Constructor.
     *
     * @param bushfireApi          bushfireApi
     * @param batchAdStatsProvider batchAdStatsProvider
     */
    @Autowired
    public AdvertStatsServiceImpl(BushfireApi bushfireApi, BatchAdStatsProvider batchAdStatsProvider, AdCounterService adCounterService,
                                  CustomMetricRegistry metrics) {
        this.batchAdStatsProvider = batchAdStatsProvider;
        this.advertApi = bushfireApi.advertApi();
        this.adCounterService = adCounterService;
        this.metrics = metrics;
    }

    @Override
    public Map<String, AdCounters> getStatisticForAdvert(Long advertId, Integer numOfDays) {
        return adCounterService.getAdvertCounters(advertId, numOfDays);
    }

    @Override
    public final List<AdvertStatisticData> getStatisticForAccount(Long accountId) {
        AccountAdsIterator adsIterator = new AccountAdsIterator(advertApi, accountId, PAGE_SIZE);
        List<AdvertStatisticData> adStatsForTheAccount = new ArrayList<AdvertStatisticData>();
        while (adsIterator.hasNext()) {
            List<Ad> ads = adsIterator.next();
            adStatsForTheAccount.addAll(batchAdStatsProvider.getStatsFor(ads));
        }
        return adStatsForTheAccount;
    }

    @Override
    public final AdvertStatisticData getStatisticForAdvert(Long advertId) {
        Ad advert = advertApi.getAdvert(advertId);
        return batchAdStatsProvider.getStatsFor(Collections.singletonList(advert)).get(0);
    }

    @Override
    public List<AdvertStatisticData> getStatisticForAdvertList(List<Ad> adverts) {
        if (adverts != null && adverts.size() > 0) {
            return metrics.madPageTimer("batchAdStatsProvider.getStatsFor").record(() -> batchAdStatsProvider.getStatsFor(adverts));
        }
        return Collections.emptyList();
    }
}
