package com.gumtree.web.seller.page.openidconnect.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.gas.OpenIdConnectService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.gumtree.web.seller.page.openidconnect.controller.UserInfoController.PAGE_PATH;

/**
 * Created by reweber on 12/23/14.
 */
@Controller
@RequestMapping(PAGE_PATH)
@GumtreePage(PageType.Unknown)
public class UserInfoController extends BaseSellerController {
    public static final String PAGE_PATH = "/oidc/userinfo";

    private OpenIdConnectService oidcService;

    @Autowired
    public UserInfoController(CookieResolver cookieResolver, CategoryModel categoryModel,
                              ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                              UrlScheme urlScheme, OpenIdConnectService oidcService,
                              UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.oidcService = oidcService;
    }

    /**
     * Get user info.
     */
    @RequestMapping(method = RequestMethod.GET)
    public void getUserInfo(HttpServletRequest request, HttpServletResponse response) {
        oidcService.getUserInfo(request, response);
    }
}
