package com.gumtree.web.seller.page.manageads.reporting;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsConfigurer;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.impl.PostAdBegin;
import com.gumtree.web.reporting.google.ga.events.impl.RegisterCvBegin;

public final class ManageAdsGoogleAnalyticsConfigurer implements GoogleAnalyticsConfigurer<Void> {

    @Override
    public void configure(GoogleAnalyticsReportBuilder reportBuilder, ThirdPartyRequestContext<Void> ctx) {
        reportBuilder
                .pageType(ctx.getPageType())
                .addTrackEvent(new PostAdBegin(ctx))
                .addTrackEvent(new RegisterCvBegin(ctx));
    }

}
