package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.Account;
import com.gumtree.api.ApiContactEmail;
import com.gumtree.api.domain.user.beans.UpdateUserBean;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.ManageAdsHelper;
import com.gumtree.web.seller.page.manageads.model.ManageAdsAccountSelectionFormBean;
import com.gumtree.web.seller.page.reviews.model.UserRating;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;
import java.util.Optional;

public final class MyDetailsModel extends CommonModel {
    private Boolean isProUser;
    private Boolean passwordExists;
    private UpdateUserBean updateUserBean;
    private Iterable<ApiContactEmail> contactEmails;
    private String userEmail;
    private ManageAdsAccountSelectionFormBean accountSelectionForm;
    private ReportableErrorsMessageResolvingErrorSource errors;
    private String tabToOpen;
    private UserRating userRating;

    public Boolean getIsProUser() {
        return isProUser;
    }

    public Boolean getPasswordExists() {
        return passwordExists;
    }

    public UpdateUserBean getUpdateUserBean() {
        return updateUserBean;
    }

    public Iterable<ApiContactEmail> getContactEmails() {
        return contactEmails;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public String getTabToOpen() {
        return tabToOpen;
    }

    public ManageAdsAccountSelectionFormBean getAccountSelectionForm() {
        return accountSelectionForm;
    }

    public ReportableErrorsMessageResolvingErrorSource getErrors() {
        return errors;
    }

    private MyDetailsModel(CoreModel core) {
        super(core);
    }

    public static Builder builder(CoreModel.Builder coreBuilder) {
        return new Builder(coreBuilder);
    }

    public UserRating getUserRating() {
        return userRating;
    }

    public static final class Builder {
        private MyDetailsModel model;
        private ManageAdsHelper manageAdsHelper;
        private UrlScheme urlScheme;
        private Account account;

        private Builder(CoreModel.Builder coreBuilder) {
            model = new MyDetailsModel(coreBuilder.build(Page.MyDetails));
        }

        public Builder withIsProUser(Boolean isProUser) {
            model.isProUser = isProUser;
            return this;
        }

        public Builder withPasswordExist(Boolean passwordExists) {
            model.passwordExists = passwordExists;
            return this;
        }

        public Builder withUpdateUserBean(UpdateUserBean updateUserBean) {
            model.updateUserBean = updateUserBean;
            return this;
        }

        public Builder withContactEmails(Iterable<ApiContactEmail> contactEmails) {
            model.contactEmails = contactEmails;
            return this;
        }

        public Builder withUserEmail(String userEmail) {
            model.userEmail = userEmail;
            return this;
        }

        public Builder withAccountSelectionForm(ManageAdsAccountSelectionFormBean accountSelectionForm) {
            model.accountSelectionForm = accountSelectionForm;
            return this;
        }

        public Builder withManageAdsUrls(ManageAdsHelper manageAdsHelper, UrlScheme urlScheme) {
            this.manageAdsHelper = manageAdsHelper;
            this.urlScheme = urlScheme;
            return this;
        }

        public Builder withErrors(ReportableErrorsMessageResolvingErrorSource errors) {
            model.errors = errors;
            return this;
        }

        public Builder withAccount(Account account) {
            this.account = account;
            return this;
        }

        public Builder withTabToOpen(String tabToOpen) {
            model.tabToOpen = tabToOpen;
            return this;
        }

        public Builder withUserReviewRating(Optional<UserRating> userRating)  {
            model.userRating = userRating.orElse(new UserRating());
            return this;
        }

        public ModelAndView build() {
            ModelAndView modelAndView = new ModelAndView(model.getCore().getPage().getTemplateName());
            modelAndView.addObject(CommonModel.MODEL_KEY, model);
            modelAndView.addObject("account", account);

            if (manageAdsHelper != null && urlScheme != null) {
                Map<String, Object> rawModel = modelAndView.getModel();
                manageAdsHelper
                        .addManageAdsUrls(rawModel, new SimpleLink("Manage my ads", urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ACCOUNT)));
            }

            return modelAndView;
        }
    }
}
