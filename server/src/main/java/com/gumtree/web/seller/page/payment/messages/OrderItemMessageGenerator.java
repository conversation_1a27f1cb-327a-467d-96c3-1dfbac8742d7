package com.gumtree.web.seller.page.payment.messages;

import com.google.common.base.Optional;
import com.gumtree.api.Account;
import com.gumtree.api.CreditPackage;
import com.gumtree.api.PaymentInstrument;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.ApiOrderItem;
import com.gumtree.domain.category.Categories;
import com.gumtree.service.category.CategoryService;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class OrderItemMessageGenerator {

    static final String GUMTREE_MEDIA_JOBS_LINK = "http://www.gumtreeforbusiness.co.uk/jobs/";
    static final String GUMTREE_MEDIA_RENTALS_LINK = "http://www.gumtreeforbusiness.co.uk/property/";
    static final String GUMTREE_MEDIA_MOTORS_LINK = "http://www.gumtreeforbusiness.co.uk/motors/";
    static final String GUMTREE_MEDIA_FALLBACK_LINK = "http://www.gumtreeforbusiness.co.uk/";

    private static final String JOBS_NAME = Categories.JOBS.getSeoName();
    private static final String RENTALS_NAME = Categories.PROPERTY_TO_RENT.getSeoName();
    private static final String MOTORS_NAME = Categories.MOTORS.getSeoName();

    private BushfireApi bushfireApi;
    private CategoryService categoryService;
    private MessageProvider messageProvider;

    public OrderItemMessageGenerator(BushfireApi bushfireApi, CategoryService categoryService, MessageProvider messageProvider) {
        this.bushfireApi = bushfireApi;
        this.categoryService = categoryService;
        this.messageProvider = messageProvider;
    }

    @FunctionalInterface
    public interface MessageProvider {
        String resolveMessage(String m, Object... b);
    }

    public List<String> noticesForOrderItems(ApiOrder apiOrder, Account account) {
        return apiOrder.getItems().stream()
                .filter(this::hasCreditPackage)
                .filter(distinctCreditPackages())
                .filter(notCapability(account))
                .map(createMessage(account))
                .collect(Collectors.toList());
    }

    private String createMessageForNonProAccount(ApiOrderItem item) {
        CreditPackage creditPackage = item.getPaymentDetail().getCreditPackage();
        if (creditsRemainingInPackage(creditPackage) > 0) {
            return messageProvider.resolveMessage("postad.package.free.remaining",
                    creditPackage.getUsedCredits(), totalCreditsInPackage(creditPackage),
                    generateGumtreeMediaGetBusinessAccountLink(item.getAdvertId()));
        } else {
            return messageProvider.resolveMessage("postad.package.free.nocredits",
                    generateGumtreeMediaGetBusinessAccountLink(item.getAdvertId()));
        }
    }

    private String createMessageForProAccount(ApiOrderItem item) {
        CreditPackage creditPackage = item.getPaymentDetail().getCreditPackage();
        if (PaymentInstrument.TOKEN.equals(creditPackage.getPaymentInstrument())) {
            return messageProvider.resolveMessage("postad.package.tokens.liveads",
                    creditPackage.getName(), creditPackage.getInitialCredits());
        } else if (creditPackage.getName().startsWith("Unlimited")) {
            return messageProvider.resolveMessage("postad.package.credits.unlimited", creditPackage.getName());
        } else if (creditsRemainingInPackage(creditPackage) >= 0) {
            return messageProvider.resolveMessage("postad.package.credits.remaining",
                    creditsRemainingInPackage(creditPackage), creditPackage.getName());
        } else {
            return messageProvider.resolveMessage("postad.package.credits.overspent",
                    Math.abs(creditsRemainingInPackage(creditPackage)), creditPackage.getName());
        }
    }

    private Function<ApiOrderItem, String> createMessage(Account account) {
        return item -> account.isPro() ? createMessageForProAccount(item) : createMessageForNonProAccount(item);
    }

    private Predicate<ApiOrderItem> notCapability(Account account) {
        return item ->
                !account.isPro() || item.getPaymentDetail().getCreditPackage().getPaymentInstrument() != PaymentInstrument.CAPABILITY;
    }

    private Predicate<ApiOrderItem> distinctCreditPackages() {
        Set<String> seen = new HashSet<>();
        return item -> seen.add(item.getPaymentDetail().getCreditPackage().getName());
    }

    private boolean hasCreditPackage(ApiOrderItem item) {
        return item.getPaymentDetail() != null && item.getPaymentDetail().getCreditPackage() != null;
    }

    private Long creditsRemainingInPackage(CreditPackage creditPackage) {
        return totalCreditsInPackage(creditPackage) - creditPackage.getUsedCredits();
    }

    private Long totalCreditsInPackage(CreditPackage creditPackage) {
        return creditPackage.getInitialCredits() + creditPackage.getAdjustedCredits();
    }

    private String generateGumtreeMediaGetBusinessAccountLink(Long advertId) {
        Long categoryId = bushfireApi.advertApi().getAdvert(advertId).getCategoryId();
        if (categoryIsWithin(categoryId, JOBS_NAME)) {
            return GUMTREE_MEDIA_JOBS_LINK;
        } else if (categoryIsWithin(categoryId, RENTALS_NAME)) {
            return GUMTREE_MEDIA_RENTALS_LINK;
        } else if (categoryIsWithin(categoryId, MOTORS_NAME)) {
            return GUMTREE_MEDIA_MOTORS_LINK;
        }
        return GUMTREE_MEDIA_FALLBACK_LINK;
    }

    private boolean categoryIsWithin(Long id, String possibleParentName) {
        Optional<Category> parentCategory = categoryService.getByUniqueName(possibleParentName);
        Optional<Category> currentCategory = categoryService.getById(id);
        return currentCategory.isPresent()
                && parentCategory.isPresent()
                && categoryService.isChild(currentCategory.get(), parentCategory.get());
    }
}