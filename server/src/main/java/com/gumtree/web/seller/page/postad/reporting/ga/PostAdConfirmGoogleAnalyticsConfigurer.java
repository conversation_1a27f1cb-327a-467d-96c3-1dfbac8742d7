package com.gumtree.web.seller.page.postad.reporting.ga;

import com.gumtree.api.Ad;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsCustomVar;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.impl.PostAdBegin;
import com.gumtree.web.reporting.google.ga.impl.AbstractGooogleAnalyticsConfigurer;
import com.gumtree.web.seller.page.ResponsiveGroupRequest;
import com.gumtree.web.seller.reporting.ResponsiveGoogleAnalyticsConfigurer;
import com.gumtree.web.seller.page.postad.reporting.ga.events.PostAdSuccess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PostAdConfirmGoogleAnalyticsConfigurer extends AbstractGooogleAnalyticsConfigurer<Ad> {

    private ResponsiveGroupRequest responsiveGroupRequest;

    @Autowired
    public PostAdConfirmGoogleAnalyticsConfigurer(ResponsiveGroupRequest responsiveGroupRequest) {
        this.responsiveGroupRequest = responsiveGroupRequest;
    }

    @Override
    public void configure(GoogleAnalyticsReportBuilder reportBuilder, ThirdPartyRequestContext<Ad> ctx) {
        reportBuilder
                .pageType(ctx.getPageType())
                .addTrackEvent(new PostAdSuccess(ctx))
                .addTrackEvent(new PostAdBegin(ctx));

        if (responsiveGroupRequest.isResponsiveGroup()) {
        reportBuilder.addCustomVar(new GoogleAnalyticsCustomVar(ResponsiveGoogleAnalyticsConfigurer.CUSTOM_VAR,
                ResponsiveGoogleAnalyticsConfigurer.GROUP_NAME, responsiveGroupRequest.getGroup(), 3));
        }
    }

}
