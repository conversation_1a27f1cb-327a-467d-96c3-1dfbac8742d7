package com.gumtree.web.seller.page.ajax.vrm.model.attribute;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.gumtree.web.seller.page.common.model.SummaryAttribute;
import org.codehaus.jackson.annotate.JsonProperty;

@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@org.codehaus.jackson.map.annotate.JsonSerialize(include = org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion.NON_NULL)
public class VrmAttributes {
    @JsonProperty("vrn")
    private SummaryAttribute vrm;

    @JsonProperty("vehicle_make")
    private SummaryAttribute make;

    @JsonProperty("motorbike_make")
    private SummaryAttribute motorbikeMake;

    @JsonProperty("vehicle_model")
    private SummaryAttribute model;

    @JsonProperty("vehicle_body_type")
    private SummaryAttribute bodyType;

    @JsonProperty("vehicle_colour")
    private SummaryAttribute colour;

    @JsonProperty("vehicle_registration_year")
    private SummaryAttribute registrationYear;

    @JsonProperty("vehicle_fuel_type")
    private SummaryAttribute fuelType;

    @JsonProperty("vehicle_transmission")
    private SummaryAttribute transmission;

    @JsonProperty("vehicle_engine_size")
    private SummaryAttribute engineSize;

    @JsonProperty("vehicle_doors")
    private SummaryAttribute doors;

    @JsonProperty("vehicle_mileage")
    private SummaryAttribute mileage;

    @JsonProperty("vehicle_estimated_mileage")
    private SummaryAttribute estimatedMileage;

    public VrmAttributes() {
    }

    public SummaryAttribute getVrm() {
        return vrm;
    }

    public SummaryAttribute getMake() {
        return make;
    }

    public SummaryAttribute getModel() {
        return model;
    }

    public SummaryAttribute getBodyType() {
        return bodyType;
    }

    public SummaryAttribute getColour() {
        return colour;
    }

    public SummaryAttribute getRegistrationYear() {
        return registrationYear;
    }

    public SummaryAttribute getFuelType() {
        return fuelType;
    }

    public SummaryAttribute getTransmission() {
        return transmission;
    }

    public SummaryAttribute getEngineSize() {
        return engineSize;
    }

    public SummaryAttribute getMotorbikeMake() {
        return motorbikeMake;
    }

    public SummaryAttribute getDoors() {
        return doors;
    }

    public SummaryAttribute getEstimatedMileage() {
        return estimatedMileage;
    }

    public SummaryAttribute getMileage() {
        return mileage;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private VrmAttributes attrs = new VrmAttributes();

        public VrmAttributes build() {
            VrmAttributes result = attrs;
            // invalidate builder so it can't be used to change instance returned by this builder
            this.attrs = null;
            return result;
        }

        public void setVrm(SummaryAttribute vrm) {
            attrs.vrm = vrm;
        }

        public void setMake(SummaryAttribute make) {
            attrs.make = make;
        }

        public void setModel(SummaryAttribute model) {
            attrs.model = model;
        }

        public void setBodyType(SummaryAttribute bodyType) {
            attrs.bodyType = bodyType;
        }

        public void setColour(SummaryAttribute colour) {
            attrs.colour = colour;
        }

        public void setDoors(SummaryAttribute doors) {
            attrs.doors = doors;
        }

        public void setEstimatedMileage(SummaryAttribute mileage) {
            attrs.estimatedMileage = mileage;
            attrs.mileage = mileage;
        }

        public void setRegistrationYear(SummaryAttribute registrationYear) {
            attrs.registrationYear = registrationYear;
        }

        public void setFuelType(SummaryAttribute fuelType) {
            attrs.fuelType = fuelType;
        }

        public void setTransmission(SummaryAttribute transmission) {
            attrs.transmission = transmission;
        }

        public void setEngineSize(SummaryAttribute engineSize) {
            attrs.engineSize = engineSize;
        }

        public void setMotorbikeMake(SummaryAttribute motorbikeMake) {
            attrs.motorbikeMake = motorbikeMake;
        }

    }
}
