package com.gumtree.web.seller.page.common;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.model.PageExpiredModel;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.http.HttpStatus;

import static com.gumtree.web.seller.page.common.PageExpiredController.PAGE_PATH;

/**
 */
@Controller
@GumtreePage(PageType.SessionExpired)
@RequestMapping(PAGE_PATH)
@GoogleAnalytics
public class PageExpiredController extends BaseSellerController {

    public static final String PAGE_PATH = "/page-expired";

    @Autowired
    public PageExpiredController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                 ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                 UrlScheme urlScheme, UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
    }

    @RequestMapping(method = RequestMethod.GET)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public final ModelAndView viewErrorPage(@RequestParam(required = false) String src, HttpServletRequest request) {
        return PageExpiredModel.builder().
                withHomepageLink(homepageLink()).
                withSrcPage(src).
                build(getCoreModelBuilder(request));
    }

    private String homepageLink() {
        return getUrlScheme().urlFor(Actions.GUMTREE_HOME);
    }
}
