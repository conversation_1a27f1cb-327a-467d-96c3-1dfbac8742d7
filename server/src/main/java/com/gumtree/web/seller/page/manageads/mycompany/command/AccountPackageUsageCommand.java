package com.gumtree.web.seller.page.manageads.mycompany.command;

import com.gumtree.api.PackageUsageSearchResponse;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.PackageApi;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;
import org.joda.time.DateTime;

/**
 * Command object for fetching credit usages for a specified package
 */
public final class AccountPackageUsageCommand extends AuthenticatedApiCall<PackageUsageSearchResponse> {

    private long accountId;

    private Long packageTypeId;

    private DateTime from;

    private DateTime to;

    private Boolean active;

    private int page;

    private int batchSize;

    /**
     * Constructor.
     *
     * @param accountId      the accountId
     * @param packageTypeId  the package type id
     * @param from           the from applied date
     * @param to             the to applied date
     * @param active         whether packages need to be active or not
     * @param page           the pageNumber
     * @param batchSize      the page batch size
     * @param apiKeyProvider the api key provider
     */
    public AccountPackageUsageCommand(
            long accountId,
            Long packageTypeId,
            DateTime from,
            DateTime to,
            Boolean active,
            int page,
            int batchSize,
            ApiKeyProvider apiKeyProvider) {

        super(apiKeyProvider);
        this.accountId = accountId;
        this.packageTypeId = packageTypeId;
        this.from = from;
        this.to = to;
        this.active = active;
        this.page = page;
        this.batchSize = batchSize;
    }

    @Override
    public PackageUsageSearchResponse execute(BushfireApi api) {
        return api.create(PackageApi.class, getApiKey()).getPackageUsages(
                accountId,
                packageTypeId,
                from != null ? from.getMillis() : null,
                to != null ? to.getMillis() : null,
                active,
                page,
                batchSize);
    }
}
