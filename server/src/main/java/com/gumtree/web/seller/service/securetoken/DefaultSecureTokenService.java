package com.gumtree.web.seller.service.securetoken;

import com.gumtree.common.util.StringUtils;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.storage.strategy.SessionPersistenceStrategy;

import java.util.UUID;

/**
 */
public class DefaultSecureTokenService implements SecureTokenService {
    private static final int DEFAULT_TTL = 600;

    private SessionPersistenceStrategy sessionPersistenceStrategy;

    private UserSession userSession;

    /**
     * Constructor.
     *
     * @param sessionPersistenceStrategy - stores & retrieves tokens
     * @param userSession - the logged in user
     */
    public DefaultSecureTokenService(SessionPersistenceStrategy sessionPersistenceStrategy, UserSession userSession) {
        this.sessionPersistenceStrategy = sessionPersistenceStrategy;
        this.userSession = userSession;
    }


    @Override
    public String getOrGenerateToken(String pageType) {
        final String tokenKey = userSession.getUsername() + "_" + pageType;
        String token = sessionPersistenceStrategy.readOperation(tokenKey);

        if (StringUtils.hasText(token)) {
            sessionPersistenceStrategy.writeOperation(handler -> handler.expire(tokenKey, DEFAULT_TTL));
        } else {
            final String newToken = UUID.randomUUID().toString();
            sessionPersistenceStrategy.writeOperation(handler -> handler.set(tokenKey, newToken, DEFAULT_TTL));

            token = newToken;
        }
        return token;
    }

    @Override
    public boolean checkToken(String pageType, String token) {
        String tokenKey = userSession.getUsername() + "_" + pageType;
        String storedToken = sessionPersistenceStrategy.readOperation(tokenKey);

        return StringUtils.hasText(token) && token.equals(storedToken);
    }
}
