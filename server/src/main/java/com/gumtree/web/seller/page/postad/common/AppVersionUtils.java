package com.gumtree.web.seller.page.postad.common;

public class AppVersionUtils {

    public static final String ANDROID_VERSION = "0";
    public static final String IOS_VERSION = "0";

    /**
     * Enum representing different device types.
     */
    public enum DeviceType {
        ANDROID,
        IOS,
        WEB
    }

    /**
     * Determines the device type based on the user-agent string.
     * Mozilla/5.0 (iPhone; CPU iPhone OS 16_0_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 iOS/GumtreeApp AppVersion/18.1.18
     * Mozilla/5.0 (iPhone; CPU iPhone OS 16_0_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 iOS/GumtreeApp/h64x83o7jbz3dvefdpaqetov AppVersion/18.1.18
     * @param userAgent the user-agent string
     * @return the device type
     */
    public static DeviceType getDeviceType(String userAgent) {
        if (userAgent == null) {
            return DeviceType.WEB;
        }
        if (userAgent.contains("Android/GumtreeApp")) {
            return DeviceType.ANDROID;
        } else if (userAgent.contains("iOS/GumtreeApp")) {
            return DeviceType.IOS;
        } else {
            return DeviceType.WEB;
        }
    }

    /**
     * Compares two version numbers.
     *
     * Version numbers are represented as dot-separated strings (e.g., "1.2.3").
     * Each part of the version number is compared sequentially from left to right.
     * If one version number has fewer parts than the other, the missing parts are treated as 0.
     *
     * @param version1 The first version number string to compare.
     * @param version2 The second version number string to compare.
     * @return Returns -1 if version1 is less than version2,
     *         1 if version1 is greater than version2,
     *         or 0 if both versions are equal.
     */
    public static int compareVersions(String version1, String version2) {
        // Split the version strings into arrays by dots
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");

        // Determine the maximum length between the two arrays
        int maxLength = Math.max(v1Parts.length, v2Parts.length);

        for (int i = 0; i < maxLength; i++) {
            // Get the current part of the version number, defaulting to 0 if out of bounds
            int num1 = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int num2 = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;

            // Compare the current parts of the version numbers
            if (num1 < num2) {
                return -1;
            } else if (num1 > num2) {
                return 1;
            }
        }

        // All parts are equal, return 0
        return 0;
    }


    /**
     * Determines whether the client version requires phone number verification.
     * This method evaluates the user agent string to identify the device type and version,
     * and decides if phone number verification is required.
     *
     * @param userAgent The user agent string used to identify the client's device type and version.
     * @return Returns true if the version requires phone number verification; otherwise, returns false.
     */
    public static boolean isPhoneVerificationVersion(String userAgent, String appVersion){
        if(userAgent == null){
            return true;
        }
        // Obtain the device type
        DeviceType deviceType = getDeviceType(userAgent);
        if(DeviceType.ANDROID.equals(deviceType)){
            if (appVersion == null || appVersion.trim().length() == 0){
                return false;
            }
            return compareVersions(appVersion, ANDROID_VERSION) > 0;
        }else if(DeviceType.IOS.equals(deviceType)){
            if (appVersion == null || appVersion.trim().length() == 0){
                return false;
            }
            return compareVersions(appVersion, IOS_VERSION) > 0;
        }else{
            return true;
        }
    }
}
