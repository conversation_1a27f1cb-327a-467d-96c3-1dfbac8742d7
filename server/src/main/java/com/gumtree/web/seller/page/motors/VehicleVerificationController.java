package com.gumtree.web.seller.page.motors;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.motors.webapi.service.MotorsService;
import com.gumtree.motorssyi.client.model.VrmLookupApiResponse;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.GaElement;
import com.gumtree.web.seller.page.common.model.SummaryAttribute;
import com.gumtree.web.seller.page.motors.model.VehicleVerificationModel;
import com.gumtree.zeno.core.domain.PageType;
import javaslang.Tuple2;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.convertToVrmLookupApiResponse;
import static com.gumtree.web.common.domain.converter.Conversions.createAttribute;
import static com.gumtree.web.seller.page.common.model.SummaryAttribute.builder;
import static com.gumtree.web.seller.page.motors.VehicleVerificationController.PAGE_PATH;
import static java.util.Collections.emptyList;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Controller
@RequestMapping(PAGE_PATH)
@GumtreePage(PageType.VehicleVerification)
class VehicleVerificationController extends BaseSellerController {

    public static final String PAGE_PATH = "/vehicle-verification";

    private static final String ATTRIBUTES_VRN_PARAM = "attributes[vrn]";
    private static final Pattern SPECIAL_CHARACTERS_REGEX = Pattern.compile("[^A-Za-z0-9]");

    private final MotorsService motorsService;

    @Autowired
    public VehicleVerificationController(MotorsService motorsService,
                                         CookieResolver cookieResolver,
                                         CategoryModel categoryModel,
                                         ApiCallExecutor apiCallExecutor,
                                         ErrorMessageResolver messageResolver,
                                         UrlScheme urlScheme,
                                         UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.motorsService = motorsService;
    }

    @RequestMapping(
            method = RequestMethod.POST,
            value = "/verification",
            headers = {"content-type=application/x-www-form-urlencoded"},
            params = ATTRIBUTES_VRN_PARAM)
    @ResponseBody()
    public ModelAndView verify(
            @RequestParam(value = "categoryId", required = false) Long categoryId,
            @RequestParam(value = ATTRIBUTES_VRN_PARAM) String vrn,
            HttpServletRequest request) throws Exception {

        String sanitizedVrn = Optional.fromNullable(vrn)
                .transform(value -> Jsoup.clean(vrn, Whitelist.none()))
                .transform(value -> SPECIAL_CHARACTERS_REGEX.matcher(value).replaceAll(""))
                .or("");

        CoreModel.Builder coreModelBuilder = getCoreModelBuilder(request)
                .withIndex();

        List<SummaryAttribute> attributeList = isBlank(sanitizedVrn) ? emptyList() : getAttributeList(vrn, categoryId);

        if (attributeList.isEmpty()) {
            markGAEvent(coreModelBuilder, "VRNFindFail", "VehicleValidationPage");
        } else {
            markGAEvent(coreModelBuilder, "VRNFindSuccess", "VehicleValidationPage");
        }

        return VehicleVerificationModel.builder()
                .withAttributeList(attributeList)
                .withCategoryId(categoryId)
                .withEnteredVrm(sanitizedVrn)
                .build(coreModelBuilder);
    }

    private List<SummaryAttribute> getAttributeList(String vrn, Long categoryId) {
        Tuple2<List<AttributeMetadata>, Map<String, String>> vrmAttributes = motorsService.getVrmAttributes(vrn, categoryId);

        VrmLookupApiResponse vrmLookupApiResponse = convertToVrmLookupApiResponse(vrmAttributes._1, vrmAttributes._2);
        return vrmLookupApiResponse.getAttributes()
                .stream()
                .map(attribute -> createAttribute(attribute.getName(), attribute.getValue(), vrmAttributes._1(), builder()))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    private void markGAEvent(CoreModel.Builder coreModelBuilder, String gaEvent, String gaLabel) {
        GaElement.Builder gaElementBuilder = GaElement.builder(gaEvent);
        gaElementBuilder.withLabel(gaLabel);
        GaElement gaElement = gaElementBuilder.build();
        coreModelBuilder.withGaEventElements(Collections.singletonList(gaElement));
    }
}
