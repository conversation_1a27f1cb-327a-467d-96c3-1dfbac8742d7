package com.gumtree.web.seller.page.redirect.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.view.RedirectView;

/**
 * Controller that handles general URLs in Seller that need to be redirected.
 */
@Controller
public final class SellerRedirectController extends BaseSellerController {

    @Autowired
    public SellerRedirectController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                    ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                    UrlScheme urlScheme, UserSessionService userSessionService) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
    }

    /**
     * Always redirect / to Buyer.
     *
     * @return redirect url
     */
    @RequestMapping(value = "/", method = {RequestMethod.GET, RequestMethod.POST})
    public RedirectView handleSlash() {

        RedirectView redirectView = new RedirectView(getUrlScheme().urlFor(Actions.USER_HOME), false, true, false);
        redirectView.setStatusCode(HttpStatus.MOVED_PERMANENTLY);
        return redirectView;
    }
}
