package com.gumtree.web.seller.service.threatmetrix;

import com.gumtree.common.properties.GtProps;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.CookieUtils;
import com.gumtree.web.cookie.GumtreeCookieProperty;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookieCutter;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrixTracking;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * 统一的ThreatMetrix服务，处理Cookie生成和Tracking数据创建
 * 保持与现有架构的一致性，同时支持API响应场景
 */
@Service
public class ThreatMetrixService {

    private static final Logger log = LoggerFactory.getLogger(ThreatMetrixService.class);

    @Autowired
    private CookieResolver cookieResolver;

    @Autowired
    private ThreatMetrixCookieCutter threatMetrixCookieCutter;

    @Value("${gumtree.threatmetrix.orgId:njrya493}")
    private String organisationId;

    @Value("${gumtree.threatmetrix.webBaseUrl}")
    private String webBaseUrl;

    @Value("${gumtree.threatmetrix.enabled:false}")
    private boolean enabled;

    /**
     * 为API响应处理ThreatMetrix Cookie和Tracking
     * 这个方法遵循现有的Cookie处理机制，确保与其他场景的一致性
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @return ThreatMetrixInfo 包含Cookie和Tracking信息
     */
    public ThreatMetrixInfo processThreatMetrixForApiResponse(HttpServletRequest request, HttpServletResponse response) {
        if (!enabled) {
            return ThreatMetrixInfo.disabled();
        }

        // 使用统一的CookieResolver获取或创建ThreatMetrix Cookie
        // 这确保了与现有架构的一致性
        ThreatMetrixCookie tmCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);
        
        // 创建ThreatMetrix追踪数据
        ThreatMetrixTracking tracking = ThreatMetrixTracking.builder()
                .orgId(organisationId)
                .sessionId(tmCookie.getDefaultValue())
                .webBaseUrl(webBaseUrl)
                .build();

        // 手动设置Cookie到响应中（因为没有ModelAndView，CookieHandlerInterceptor不会自动处理）
        // 使用与CookieHandlerInterceptor完全相同的逻辑，包括环境相关的Cookie命名
        String environmentAwareCookieName = threatMetrixCookieCutter.getName(GtProps.getEnv());
        Cookie cookie = CookieUtils.createHttpCookie(environmentAwareCookieName, tmCookie);
        response.addCookie(cookie);

        return new ThreatMetrixInfo(tmCookie, tracking, true);
    }

    /**
     * 获取现有的ThreatMetrix Cookie（如果存在）
     * 用于其他需要读取ThreatMetrix信息的场景
     *
     * @param request HTTP请求
     * @return ThreatMetrixCookie 或 null（如果不存在）
     */
    public ThreatMetrixCookie getExistingThreatMetrixCookie(HttpServletRequest request) {
        try {
            return cookieResolver.resolve(request, ThreatMetrixCookie.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 使ThreatMetrix Cookie过期
     * 这个方法会正确地将过期的Cookie发送到浏览器，确保Cookie被删除
     *
     * @param request HTTP请求
     * @param response HTTP响应
     */
    public void invalidateThreatMetrixCookie(HttpServletRequest request, HttpServletResponse response) {
        if (!enabled) {
            return;
        }

        // 获取环境相关的Cookie名称
        String environmentAwareCookieName = threatMetrixCookieCutter.getName(GtProps.getEnv());

        // 查找现有的Cookie
        Optional<javax.servlet.http.Cookie> existingCookie =
                CookieUtils.findCookie(request.getCookies(), environmentAwareCookieName);

        if (existingCookie.isPresent()) {
            // 直接修改现有Cookie对象使其过期
            javax.servlet.http.Cookie expiredCookie = existingCookie.get();
            expiredCookie.setMaxAge(0); // 设置为0使Cookie立即过期
            expiredCookie.setPath("/"); // 确保路径匹配
            expiredCookie.setDomain(GtProps.getStr(GumtreeCookieProperty.COOKIES_DOMAIN)); // 设置正确的域
            expiredCookie.setHttpOnly(true); // 保持HttpOnly属性
            expiredCookie.setSecure(GtProps.getBool(GumtreeCookieProperty.COOKIES_SECURE));

            // 发送过期的Cookie到浏览器
            response.addCookie(expiredCookie);

            log.info("ThreatMetrix Cookie invalidated and sent to browser: {}", "aa");
        } else {
            log.info("No ThreatMetrix Cookie found to invalidate");
        }
    }

    /**
     * 检查ThreatMetrix是否启用
     * 
     * @return boolean 是否启用
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 获取组织ID
     * 
     * @return String 组织ID
     */
    public String getOrganisationId() {
        return organisationId;
    }

    /**
     * 获取Web基础URL
     * 
     * @return String Web基础URL
     */
    public String getWebBaseUrl() {
        return webBaseUrl;
    }
}
