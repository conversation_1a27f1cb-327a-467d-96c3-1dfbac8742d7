package com.gumtree.web.seller.model;

import java.util.Comparator;

/**
 * Comparator that sorts {@link AdPreview adverts} by their IDs
 * <p/>
 * Note: this comparator imposes orderings that are inconsistent with equals.
 */
public class AdPreviewIdBaseComparator implements Comparator<AdPreview> {

    /**
     * {@inheritDoc}
     */
    @Override
    public int compare(AdPreview left, AdPreview right) {
        if (left != null) {
            return right != null ? left.getId().compareTo(right.getId()) : 1;
        } else {
            return right != null ? -1 : 0;
        }
    }
}
