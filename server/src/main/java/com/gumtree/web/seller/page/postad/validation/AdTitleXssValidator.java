package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Stack;
import java.util.List;
import java.util.stream.Collectors;

public class AdTitleXssValidator implements ConstraintValidator<AdTitleXssValidation, PostAdDetail> {
    private static final String COMMENT_CODE = "//";

    private final ErrorMessageResolver errorMessageResolver;

    private String[] fieldList;
    private String message;

    private enum TagStatus {
        OPEN('<'),
        CLOSE('>');

        private final Character characterValue;

        TagStatus(char value) {
            this.characterValue = value;
        }

        @Override
        public String toString() {
            return this.characterValue.toString();
        }

        public Character getCharacterValue() {
            return this.characterValue;
        }
    }

    @Autowired
    public AdTitleXssValidator(ErrorMessageResolver errorMessageResolver) {
        this.errorMessageResolver = errorMessageResolver;
    }

    @Override
    public void initialize(AdTitleXssValidation constraintAnnotation) {
        this.fieldList = constraintAnnotation.fieldList();
        this.message = constraintAnnotation.message();
    }

    private boolean includesComment(String text) {
        return StringUtils.isNoneBlank(text) && text.contains(COMMENT_CODE);
    }

    private boolean isTagOpen(String text) {
        if (StringUtils.isBlank(text)){
            return false;
        }

        Stack<Boolean> stack = new Stack<Boolean>();

        List<Character> chars = text.chars().mapToObj(c -> (char) c).collect(Collectors.toList());
        chars.stream().forEach(x -> {
            if (x.equals(TagStatus.OPEN.getCharacterValue())) {
                stack.push(true);
            } else if (x.equals(TagStatus.CLOSE.getCharacterValue())) {
                if (!stack.empty()) {
                    stack.pop();
                }
            }
        });

        return !stack.isEmpty();
    }

    @Override
    public boolean isValid(PostAdDetail value, ConstraintValidatorContext context) {
        final String errorMessage = errorMessageResolver.getMessage(message, "");
        final String title = ObjectUtils.firstNonNull(value.getPostAdFormBean().getTitle(), "");

        if (isTagOpen(title) || includesComment(title)) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(errorMessage)
                    .addPropertyNode(fieldList[0])
                    .addConstraintViolation();
            return false;
        }
        return true;
    }
}
