package com.gumtree.web.seller.page.postad.controller.popups;

import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.zeno.core.domain.PageType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import static com.gumtree.web.seller.page.postad.controller.popups.FeaturedExampleController.PAGE_PATH;

/**
 * Controller for the Featured example
 */
@Controller
@GumtreePage(PageType.PostAdFeatureTopAdExample)
@RequestMapping(PAGE_PATH)
@GoogleAnalytics
public final class FeaturedExampleController {

    public static final String PAGE_PATH = "/featured-example";
    public static final String VIEW_NAME = "featured-example";

    /**
     *
     * @return the View
     */
    @RequestMapping(method = RequestMethod.GET)
    public String showPage() {
        return VIEW_NAME;
    }
}
