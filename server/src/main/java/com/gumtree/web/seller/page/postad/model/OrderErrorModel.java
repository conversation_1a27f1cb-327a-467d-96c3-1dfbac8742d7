package com.gumtree.web.seller.page.postad.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.ManageAdsController;
import org.springframework.web.servlet.ModelAndView;

public final class OrderErrorModel extends CommonModel {

    private OrderErrorModel(CoreModel core) {
        super(core);
    }

    public String getManageAdsLink() {
        return ManageAdsController.PAGE_PATH;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.OrderError;
            coreModelBuilder.withTitle("Order error");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new OrderErrorModel(coreModelBuilder.build(page)));
        }

    }
}
