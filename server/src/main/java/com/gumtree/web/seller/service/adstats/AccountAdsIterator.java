package com.gumtree.web.seller.service.adstats;

import com.gumtree.api.Ad;
import com.gumtree.api.AdSearchResponse;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.web.seller.page.manageads.model.ManageAdStatus;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;

class AccountAdsIterator implements Iterator<List<Ad>> {

    private final AdvertApi advertApi;
    private final Long accountId;
    private final int pageSize;

    private int currentPageNumber;
    private List<Ad> lastBatch;
    private Long totalCount = null;
    private int currentResultProcessed;

    public AccountAdsIterator(AdvertApi advertApi, Long accountId, int pageSize) {
        this.advertApi = advertApi;
        this.accountId = accountId;
        this.pageSize = pageSize;
        this.currentPageNumber = 1;
        this.lastBatch = Collections.emptyList();
    }

    @Override
    public boolean hasNext() {
        if (totalCount != null && totalCount <= currentResultProcessed) {
            lastBatch = null;
            return false;
        }
        AdSearchResponse result = advertApi.search(accountId
                                                    , ManageAdStatus.ACTIVE_ADS.getStatusesNames()
                                                    , currentPageNumber
                                                    , pageSize
                                                   );
        lastBatch = result.getPostings();
        totalCount = result.getTotalCount();
        currentPageNumber++;
        currentResultProcessed += lastBatch.size();
        return !lastBatch.isEmpty();
    }

    @Override
    public List<Ad> next() {
        if (lastBatch == null || lastBatch.isEmpty()) {
            throw new NoSuchElementException();
        }
        return lastBatch;
    }

    @Override
    public void remove() {
        throw new UnsupportedOperationException();
    }
}
