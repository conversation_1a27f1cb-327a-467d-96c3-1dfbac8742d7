package com.gumtree.web.seller.service.location;

import org.springframework.util.StringUtils;

/**
 * Utility class for splitting postcode into outcode and incode.
 *
 * Copied directly from bapi
 */
public final class PostcodeSplitter {

    private String outcode;

    private String incode;

    /**
     * Constructor.
     *
     * @param postcode the postcode to split
     */
    public PostcodeSplitter(String postcode) {
        if (postcode != null) {
            String normalisedPostcode = StringUtils.trimAllWhitespace(postcode.toUpperCase());

            if (normalisedPostcode.length() > 4 && normalisedPostcode.length() < 8) {
                StringBuilder outcode = new StringBuilder();
                StringBuilder incode = new StringBuilder();
                int counted = 0;
                char[] postcodeArray = normalisedPostcode.toCharArray();

                // If length is 6 or 7 characters, then we can treat as a full postcode
                for (int i = normalisedPostcode.length() - 1; i >= 0; i--) {
                    if (counted < 3) {
                        incode.append(postcodeArray[i]);
                    } else {
                        outcode.append(postcodeArray[i]);
                    }
                    counted++;
                }
                this.outcode = outcode.reverse().toString();
                this.incode = incode.reverse().toString();
            } else if (normalisedPostcode.length() < 5 && normalisedPostcode.length() > 2) {
                // If length is 3 or 4 characters, then treat as an out code
                this.outcode = normalisedPostcode;
                this.incode = null;
            }
        }
    }

    public String getOutcode() {
        return outcode;
    }

    public String getIncode() {
        return incode;
    }
}
