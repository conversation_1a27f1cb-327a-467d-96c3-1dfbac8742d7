package com.gumtree.web.seller.page.payment.reporting.ga.events;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsElementTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEventAction;
import com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsTrackEventLabelBuilder;

public class OrderCancel extends GoogleAnalyticsElementTrackEvent {

    public OrderCancel(ThirdPartyRequestContext<Void> ctx) {
        super(ctx.getPageType(), GoogleAnalyticsTrackEventAction.ORDER_CANCEL);
        setBindEvent("click");
        setBindSelector("[ga-event=\\'order-cancel\\']");
        setLabel(eventLabel(ctx));
    }

    private String eventLabel(ThirdPartyRequestContext<Void> ctx) {
        return new GoogleAnalyticsTrackEventLabelBuilder()
                .catId(ctx.getCategory())
                .locId(ctx.getLocation())
                .build();
    }

}
