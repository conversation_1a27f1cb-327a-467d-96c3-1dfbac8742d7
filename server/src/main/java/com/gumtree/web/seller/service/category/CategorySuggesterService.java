package com.gumtree.web.seller.service.category;

import com.gumtree.web.seller.page.ajax.category.SuggestedCategory;
import java.util.List;

public interface CategorySuggesterService {

  List<Long> getSuggestedCategories(String input);

  List<SuggestedCategory> getSuggestedCategoriesAllName(String query, int top);

  List<SuggestedCategory> getSuggestedTextMatchCategories(String query, boolean esMatch, int top);

  List<SuggestedCategory> getCategoryPredictApi(String query, int top);
}
