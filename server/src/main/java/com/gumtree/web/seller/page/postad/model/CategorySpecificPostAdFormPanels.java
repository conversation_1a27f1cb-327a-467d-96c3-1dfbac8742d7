package com.gumtree.web.seller.page.postad.model;

import java.util.List;

public class CategorySpecificPostAdFormPanels {

    /**
     * Panels in this group are display in the SYI form above the advert title panel
     */
    private List<PostAdFormPanel> highPriorityPanels;

    /**
     * Panels in this group are display the SYI form just below the advert description panel
     */
    private List<PostAdFormPanel> lowPriorityPanels;

    public CategorySpecificPostAdFormPanels(List<PostAdFormPanel> highPriorityPanels, List<PostAdFormPanel> lowPriorityPanels) {
        this.highPriorityPanels = highPriorityPanels;
        this.lowPriorityPanels = lowPriorityPanels;
    }

    public List<PostAdFormPanel> getHighPriorityPanels() {
        return highPriorityPanels;
    }

    public List<PostAdFormPanel> getLowPriorityPanels() {
        return lowPriorityPanels;
    }
}
