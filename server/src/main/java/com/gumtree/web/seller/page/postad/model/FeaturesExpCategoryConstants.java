package com.gumtree.web.seller.page.postad.model;

public abstract class FeaturesExpCategoryConstants {
    public static final Long GYM_EQUIPMENT_ID = 10864L;
    public static final Long GUITARS_ID = 11063L;
    public static final Long PRAMS_STROLLERS_ID = 698L;
    public static final Long MOBILITY_DISABILITY_MEDICAL_ID = 10645L;
    public static final Long GAMES_BOARD_GAMES_ID = 4673L;
    public static final Long FILMS_TV_SHOWS_ID = 11000L;
    public static final Long BABY_CHILD_SAFETY_ID = 10579L;
    public static final Long CARPENTRY_SERVICES_ID = 535L;
    public static final Long COMPUTERS_PCS_LAPTOPS_ID = 2487L;
    public static final Long BICYCLES_ID = 86L;
    public static final Long GAME_CONSOLES_ID = 10956L;
    public static final Long OFFICE_FURNITURE_ID = 11091L;
    public static final Long DINING_LIVING_ROOM_FURNITURE_ID = 2486L;

    private FeaturesExpCategoryConstants() {
    }
}
