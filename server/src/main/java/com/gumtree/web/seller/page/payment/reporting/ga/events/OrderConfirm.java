package com.gumtree.web.seller.page.payment.reporting.ga.events;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsElementTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEventAction;
import com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsTrackEventLabelBuilder;

public class OrderConfirm extends GoogleAnalyticsElementTrackEvent {

    public OrderConfirm(ThirdPartyRequestContext<Void> ctx) {
        super(ctx.getPageType(), GoogleAnalyticsTrackEventAction.ORDER_CONFIRM);
        setBindEvent("click");
        setBindSelector("[ga-event=\\'order-confirm\\']");
        setLabel(eventLabel(ctx));
    }

    private String eventLabel(ThirdPartyRequestContext<Void> ctx) {
        return new GoogleAnalyticsTrackEventLabelBuilder()
                .catId(ctx.getCategory())
                .locId(ctx.getLocation())
                .build();
    }

}
