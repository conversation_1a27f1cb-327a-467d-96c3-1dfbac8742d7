package com.gumtree.web.seller.page.manageads.model;

import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.web.seller.page.postad.model.meta.MetaPathInfo;
import com.gumtree.web.seller.page.postad.model.meta.PageActionType;

/**
 * Implementation of {@link Checkout}
 */
public final class CheckoutImpl implements Checkout {
    private PageActionType actionType;
    private ApiOrder order;

    private String uniqueKey;

    private CheckoutAdvert editedAdvert;

    private boolean createOrEdit = false;

    private String userPhone;

    public String getKey() {
        return uniqueKey;
    }

    public void setKey(String editorId) {
        this.uniqueKey = editorId;
    }

    public ApiOrder getOrder() {
        return order;
    }

    public void setOrder(ApiOrder order) {
        this.order = order;
    }

    public void setCreateOrEdit(boolean v) {
        createOrEdit = v;
    }

    public boolean isCreateOrEdit() {
        return createOrEdit;
    }

    @Override
    public void setAdvert(CheckoutAdvert advert) {
        this.editedAdvert = advert;
    }

    @Override
    public CheckoutAdvert getAdvert() {
        return editedAdvert;
    }

    @Override
    public void setUserPhone(String phoneNumber) {
        this.userPhone = phoneNumber;
    }

    @Override
    public String getUserPhone() {
        return userPhone;
    }

    private MetaPathInfo metaPathInfo;

    public void setMetaPathInfo(MetaPathInfo metaPathInfo) {
        this.metaPathInfo = metaPathInfo;
    }

    public MetaPathInfo getMetaPathInfo() {
        return this.metaPathInfo;
    }

}
