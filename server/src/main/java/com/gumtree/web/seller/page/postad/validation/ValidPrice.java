package com.gumtree.web.seller.page.postad.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * For marking attributes field as requiring price attribute validation.
 */
@Target({ FIELD })
@Retention(RUNTIME)
@Constraint(validatedBy = PriceAttributeValidator.class)
@Documented
public @interface ValidPrice {

    /**
     * Get the validation message.
     */
    String message() default "invalid price";

    /**
     * Get the validation groups.
     */
    Class<?>[] groups() default { };

    /**
     * Get the validation payload.
     */
    Class<? extends Payload>[] payload() default { };
}
