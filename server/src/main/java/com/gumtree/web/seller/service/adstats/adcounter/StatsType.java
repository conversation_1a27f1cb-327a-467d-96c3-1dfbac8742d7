package com.gumtree.web.seller.service.adstats.adcounter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum StatsType {
    VIEWS("views"),
    SEARCH_IMPRESSIONS("impressions"),

    REPLIES("replies");

    public static final List<String> AD_COUNTERS = Stream.of(values()).map(StatsType::getAdCountersName).collect(Collectors.toList());

    StatsType(String adCountersName) {
        this.adCountersName = adCountersName;
    }

    private final String adCountersName;

    public String getAdCountersName() {
        return adCountersName;
    }
}
