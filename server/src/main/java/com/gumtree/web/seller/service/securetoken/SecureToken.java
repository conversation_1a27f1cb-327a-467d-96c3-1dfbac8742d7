package com.gumtree.web.seller.service.securetoken;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark a controller handler as requiring secure token checking
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface SecureToken {
    String TOKEN_PARAMETER_NAME = "secureToken";

    String value();
}
