package com.gumtree.web.seller.page.postad.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.gumtree.api.Image;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.abtest.ExperimentsProvider;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.converter.PostAdImageConverter;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdImage;
import com.gumtree.web.seller.page.postad.model.PostAdImageUpload;
import com.gumtree.web.seller.service.PostAdWorkspace;
import com.gumtree.web.seller.service.image.ImageMeterRegistry;
import com.gumtree.web.seller.service.image.ImageUploadService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.gumtree.web.seller.page.postad.model.plupload.PluploadError.aPluploadError;
import static java.util.stream.Collectors.toList;

/**
 *
 */
@Controller
public class PostAdImageController extends BasePostAdController {

    private static final Logger LOG = LoggerFactory.getLogger(PostAdImageController.class);
    public static final String PAGE_PATH = "/postad/{editorId}/images";
    public static final String REMOVE_IMAGE_PATH = "/postad/{editorId}/images/delete";
    public static final String ORDER_IMAGE_PATH = "/postad/{editorId}/images/order";

    public static final int MAX_NUMBER_OF_IMAGES = 20;
    public static final String MAX_IMAGE_SIZE_EXCEEDED = "Max image size exceeded";
    public static final String FAILED_TO_UPLOAD_IMAGE = "Failed to upload image";
    public static final String TOO_MANY_IMAGES = "Too many images";
    public static final String INTERNAL_ERROR = "Internal server error";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String APPLICATION_JSON_CHARSET_UTF_8 = "application/json; charset=UTF-8";
    public static final String MSIE_9_0 = "MSIE 9.0";

    private final ImageUploadService imageUploadService;
    private final PostAdImageConverter postAdImageConverter;
    private final ImageMeterRegistry imageMeterRegistry;
    private final ObjectMapper objectMapper;

    private static final long IMAGE_SIZE_THRESHOLD = 5000;

    @Autowired
    public PostAdImageController(CookieResolver cookieResolver, CategoryModel categoryModel,
                                 ApiCallExecutor apiCallExecutor, ErrorMessageResolver messageResolver,
                                 UrlScheme urlScheme, PostAdWorkspace postAdWorkspace,
                                 UserSession authenticatedUserSession, CategoryService categoryService,
                                 ImageUploadService imageUploadService, PostAdImageConverter postAdImageConverter,
                                 GumtreePageContext pageContext, LocationService locationService,
                                 UserSessionService userSessionService, ImageMeterRegistry imageMeterRegistry) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, postAdWorkspace,
                authenticatedUserSession, categoryService, pageContext, locationService, userSessionService);
        this.imageUploadService = imageUploadService;
        this.postAdImageConverter = postAdImageConverter;
        this.imageMeterRegistry = imageMeterRegistry;
        this.objectMapper = new ObjectMapper();}

    /**
     * IE9 isn't able to process application/json, JS image plugin blows up. Need to send back text/plain in this case.
     *
     * @param imageMultipartFile
     * @param position
     * @param editorId
     * @param userAgent
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA, value = PAGE_PATH)
    public final ResponseEntity<String> uploadImages(
            @RequestParam("image") MultipartFile imageMultipartFile,
            @RequestParam("position") int position,
            @PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId,
            @RequestHeader(value="User-Agent") String userAgent) throws IOException {

        try {
            long imageSize = imageMultipartFile.getSize();
            String imageName = imageMultipartFile.getName();

            if(imageSize >= GtProps.getInt(SellerProperty.MAX_IMAGE_UPLOAD_SIZE)) {
                return handleImageTooLarge(imageMultipartFile, position, imageSize);
            }
            logImageSize(imageSize, imageName, userAgent);
            int imagesUploaded = getPostAdWorkspace().getEditor(editorId).getImages().size();
            if(imagesUploaded + 1 > MAX_NUMBER_OF_IMAGES) {
                return handleTooManyImagesUploaded(imageMultipartFile, position);
            } else {
                return uploadImage(imageMultipartFile, position, editorId, userAgent);
            }
        } catch (Exception e) {
            imageMeterRegistry.storageFailed();
            LOG.error("Unexpected error uploading image", e);
            PostAdImageUpload postAdImageUpload = uploadFailed(imageMultipartFile, INTERNAL_ERROR, position);
            return new ResponseEntity(objectMapper.writeValueAsString(postAdImageUpload), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * IE 9 image upload has problems. Need to send back contentType text/plain. For all other supported browsers,
     * application/json is returned.
     *
     * @param userAgent
     * @return
     */
    private HttpHeaders getImageUploadHttpHeaders(String userAgent) {
        HttpHeaders httpHeaders = new HttpHeaders();
        if(userAgent.contains(MSIE_9_0)) {
            // IE 9 workaround
            httpHeaders.put(CONTENT_TYPE,Lists.newArrayList(org.springframework.http.MediaType.TEXT_PLAIN_VALUE));
        } else {
            httpHeaders.put(CONTENT_TYPE,Lists.newArrayList(APPLICATION_JSON_CHARSET_UTF_8));
        }
        return httpHeaders;
    }

    @RequestMapping(method = RequestMethod.POST, value = PostAdImageController.REMOVE_IMAGE_PATH)
    public final ResponseEntity<DeleteImageResponse> deleteImage(
            @PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId, @RequestBody DeleteImageRequest deleteImageRequest) {
        Long imageId = deleteImageRequest.getImageId();
        try {
            // always get fresh editor to improve race condition issues for parallel uploads
            AdvertEditor editor = getPostAdWorkspace().getEditor(editorId);
            editor.removeImage(imageId);
            getPostAdWorkspace().updateEditor(editor.getEditorId(), editor);
            return new ResponseEntity(new DeleteImageResponse(imageId, null), HttpStatus.OK);
        } catch(Exception e) {
            LOG.error("Failed to delete image {}", imageId, e);
            return new ResponseEntity(new DeleteImageResponse(imageId, "Failed to delete image"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = PostAdImageController.ORDER_IMAGE_PATH)
    public final ResponseEntity<String> orderImages(
            @PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId,
            @RequestBody OrderImagesRequest orderImagesRequest) {
        List<String> imgIds = orderImagesRequest.getImgIds();
        try {
            // always get fresh editor to improve race condition issues for parallel uploads
            AdvertEditor editor = getPostAdWorkspace().getEditor(editorId);
            editor.getPostAdFormBean().setImageIds(imgIds.stream().map(s -> Long.parseLong(s)).collect(Collectors.toList()));
            getPostAdWorkspace().updateEditor(editor.getEditorId(), editor);
            return new ResponseEntity(HttpStatus.OK);
        } catch(Exception e) {
            LOG.error("Failed to order images {}", imgIds, e);
            return new ResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON, value = PAGE_PATH)
    @ResponseStatus(value = HttpStatus.OK)
    @ResponseBody
    public final List<PostAdImage> getSessionImages(@PathVariable(value = EDITOR_ID_PATH_VARIABLE) String editorId) {

        AdvertEditor editor = getPostAdWorkspace().getEditor(editorId);
        return convertImages(editor.getImages());
    }

    private List<PostAdImage> convertImages(List<Image> images) {
        return images.stream().map(postAdImageConverter::convertImageToPostAdImage).collect(toList());
    }

    private ResponseEntity uploadImage(MultipartFile file, int position, String editorId, String userAgent) throws JsonProcessingException {

        PostAdImageUpload postAdImageUpload;

        try {
            Image image = uploadImage(file);

            // always get fresh editor to improve race condition issues for parallel uploads
            AdvertEditor editor = getPostAdWorkspace().getEditor(editorId);
            editor.addImage(image, position);
            getPostAdWorkspace().updateEditor(editor.getEditorId(), editor);
            postAdImageUpload = convertImage(file, image, position);

            if (postAdImageUpload.getId() == null) {
                imageMeterRegistry.storageFailed();
                LOG.warn("Failed to upload image, image ID is null");
                return new ResponseEntity(objectMapper.writeValueAsString(postAdImageUpload), HttpStatus.BAD_GATEWAY);
            }

            String fileSizeAfterConversion = postAdImageUpload.getSize();
            if(NumberUtils.isParsable(fileSizeAfterConversion) && !StringUtils.contains(fileSizeAfterConversion, ".")) {
                if(Long.parseLong(fileSizeAfterConversion) <= IMAGE_SIZE_THRESHOLD) {
                    LOG.info(String.format("Image size after conversion is %s - equal to or below 5000 bytes - " +
                                    "for image id %s and image name %s",
                            fileSizeAfterConversion,
                            image.getId(),
                            postAdImageUpload.getFileName()));
                }
            }

            imageMeterRegistry.uploaded();
            return new ResponseEntity(objectMapper.writeValueAsString(postAdImageUpload),
                    getImageUploadHttpHeaders(userAgent), HttpStatus.CREATED);

        } catch (Exception e) {
            imageMeterRegistry.storageFailed();
            LOG.warn("Image upload failed", e);
            postAdImageUpload = uploadFailed(file, FAILED_TO_UPLOAD_IMAGE, position);
            return new ResponseEntity(objectMapper.writeValueAsString(postAdImageUpload), HttpStatus.BAD_GATEWAY);
        }

    }

    private Image uploadImage(MultipartFile file) {
            return imageUploadService.postImage(file);
    }

    private PostAdImageUpload uploadFailed(MultipartFile file, String message, int position) {
        return PostAdImageUpload.builder()
                .withFileName(file.getOriginalFilename())
                .withError(aPluploadError().withCode(102).message(message))
                .withPosition(position)
                .build();
    }

    private PostAdImageUpload convertImage(MultipartFile file, Image image, int position) {
        PostAdImage postAdImage = postAdImageConverter.convertImageToPostAdImage(image);
        return PostAdImageUpload.builder()
                .withFileName(file.getOriginalFilename())
                .withImage(postAdImage)
                .withPosition(position)
                .build();
    }

    private ResponseEntity<String> handleImageTooLarge(MultipartFile imageMultipartFile, int position, long fileSize)
            throws JsonProcessingException {
        int maxUploadSize = GtProps.getInt(SellerProperty.MAX_IMAGE_UPLOAD_SIZE);
        LOG.info("Image upload failed - too large (fileSize=" + fileSize + ", maxUploadSize=" + maxUploadSize + ")");
        imageMeterRegistry.tooLarge();
        PostAdImageUpload postAdImageUpload = uploadFailed(imageMultipartFile, MAX_IMAGE_SIZE_EXCEEDED, position);
        return new ResponseEntity(objectMapper.writeValueAsString(postAdImageUpload), HttpStatus.REQUEST_ENTITY_TOO_LARGE);
    }

    private ResponseEntity<String> handleTooManyImagesUploaded(MultipartFile imageMultipartFile, int position)
            throws JsonProcessingException {
        imageMeterRegistry.tooMany();
        PostAdImageUpload postAdImageUpload = uploadFailed(imageMultipartFile, TOO_MANY_IMAGES, position);
        return new ResponseEntity(objectMapper.writeValueAsString(postAdImageUpload), HttpStatus.PRECONDITION_FAILED);
    }

    /**
     * This method has been included as part of our effort
     * to understand why images turn up as a plain black canvas.
     * The logs and metrics will help identify the scale of the problem
     * and its origin.
     * @param imageSize
     * @param imageName
     * @param userAgent
     */
    private void logImageSize(long imageSize, String imageName, String userAgent) {

        if(imageSize <= IMAGE_SIZE_THRESHOLD) {
            LOG.info(String.format("Image size is %s - equal to or below 5000 bytes - " +
                    "the user agent is %s and the file name is %s", imageSize, userAgent, imageName));
            imageMeterRegistry.imageUnderFiveKb();
        } else if(imageSize > IMAGE_SIZE_THRESHOLD) {
            imageMeterRegistry.imageOverFiveKb();
        }
    }

    public static class DeleteImageResponse {
        private final Long id;
        private final String message;

        public DeleteImageResponse(Long id, String message) {
            this.id = id;
            this.message = message;
        }

        public Long getId() {
            return id;
        }

        public String getMessage() {
            return message;
        }
    }

    public static class OrderImagesRequest {
        private List<String> imgIds = Lists.newArrayList();

        public List<String> getImgIds() {
            return imgIds;
        }

        public void setImgIds(List<String> imgIds) {
            this.imgIds = imgIds;
        }
    }

    public static class DeleteImageRequest {

        private Long id;

        public Long getImageId() {
            return id;
        }

        public void setId(String id) {
            this.id = Long.parseLong(id);
        }
    }
}
