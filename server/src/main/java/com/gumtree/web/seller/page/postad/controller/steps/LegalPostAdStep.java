package com.gumtree.web.seller.page.postad.controller.steps;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableList;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdFormPanel;
import com.gumtree.web.seller.page.postad.model.PostAdFormStatus;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.seller.page.postad.service.legal.LegalBean;
import org.springframework.stereotype.Component;

@Component
public class LegalPostAdStep implements PostAdStep {
    public static final Integer ORDER = CategorySelectPostAdStep.ORDER + 1;

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public boolean execute(PostAdSubmitModel.Builder model, AdvertEditor editor) {
        model.withStatus(PostAdFormStatus.LEGAL);
        Optional<LegalBean> legalBean = editor.getLegalRequirements();
        if (legalBean.isPresent()) {
            model.withLegal(legalBean.get());
            model.withPanels(ImmutableList.of(
                    PostAdFormPanel.CATEGORY, PostAdFormPanel.LEGAL, PostAdFormPanel.CONTINUE));
            return false;
        }

        model.supportsChangeLocation(editor.supportsChangeLocation());
        model.supportsChangeVisibleOnMap(editor.supportsChangeVisibleOnMap());
        model.supportsPostToAnyLocation(editor.supportsPostToAnyLocation());
        return true;
    }
}
