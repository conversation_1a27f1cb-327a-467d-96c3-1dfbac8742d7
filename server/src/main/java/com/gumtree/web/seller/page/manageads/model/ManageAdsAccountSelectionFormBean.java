package com.gumtree.web.seller.page.manageads.model;

import com.gumtree.api.Account;
import com.gumtree.api.User;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Manage Ads Account Selection Form
 */
public final class ManageAdsAccountSelectionFormBean implements Serializable {

    private static final String ACTION = "/manage/ads";

    private List<ManageAdsAccountSelectionOption> accounts = new ArrayList<>();

    public String getAction() {
        return ACTION;
    }

    public List<ManageAdsAccountSelectionOption> getAccounts() {
        return Collections.unmodifiableList(this.accounts);
    }

    /**
     * Add accounts to model.
     *
     * @param accounts          the accounts
     * @param user              the current user
     * @param selectedAccountId the currently selected account id
     */
    public void addAccounts(List<Account> accounts, User user, long selectedAccountId) {
        for (Account account : accounts) {
            ManageAdsAccountSelectionOption option = new ManageAdsAccountSelectionOption();
            option.setName(renderName(account, user));
            if (account.getId() != null) {
                option.setValue(account.getId().toString());
            }
            this.accounts.add(option);
        }
        Collections.sort(this.accounts, new AccountSelectionFormSort(selectedAccountId));
    }

    private String renderName(Account account, User user) {
        if (account.isPro()) {
            return account.getName() + " (" + account.getId() + ")";
        } else {
            return user.getFirstName() + "'s account";
        }
    }

    /**
     * Constructor.
     */
    private final class AccountSelectionFormSort implements Comparator<ManageAdsAccountSelectionOption> {

        private long selectedAccountId;

        /**
         * Constructor.
         *
         * @param selectedAccountId the currently selected account id
         */
        private AccountSelectionFormSort(long selectedAccountId) {
            this.selectedAccountId = selectedAccountId;
        }

        @Override
        public int compare(ManageAdsAccountSelectionOption o1, ManageAdsAccountSelectionOption o2) {
            if (o1.getValue().equals(String.valueOf(selectedAccountId))) {
                return -1;
            } else if (o2.getValue().equals(String.valueOf(selectedAccountId))) {
                return 1;
            } else {
                return o1.getDisplayName().compareTo(o2.getDisplayName());
            }
        }
    }
}
