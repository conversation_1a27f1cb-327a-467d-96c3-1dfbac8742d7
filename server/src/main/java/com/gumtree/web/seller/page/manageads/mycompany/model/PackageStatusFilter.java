package com.gumtree.web.seller.page.manageads.mycompany.model;

import com.google.common.base.Predicate;
import com.gumtree.api.CreditPackage;
import com.gumtree.common.util.time.Clock;
import com.gumtree.web.common.util.CollectionUtils;
import com.gumtree.web.seller.page.common.SelectableValue;
import org.joda.time.DateMidnight;

import java.util.List;

/**
 * For filtering credit packages
 */
public enum PackageStatusFilter implements SelectableValue {

    ALL("All package statuses") {
        @Override
        public List<CreditPackage> filter(List<CreditPackage> packages, final Clock clock) {
            return packages;
        }
    },
    ACTIVE("Active packages") {
        @Override
        public List<CreditPackage> filter(List<CreditPackage> packages, final Clock clock) {
            return CollectionUtils.filter(packages, new Predicate<CreditPackage>() {
                @Override
                public boolean apply(CreditPackage input) {
                    DateMidnight normalisedNow = clock.getDateTime().toDateMidnight();
                    return input.getEndDate().toDateMidnight().isEqual(normalisedNow)
                            || input.getEndDate().toDateMidnight().isAfter(normalisedNow);
                }
            });
        }
    },
    EXPIRED("Expired packages") {
        @Override
        public List<CreditPackage> filter(List<CreditPackage> packages, final Clock clock) {
            return CollectionUtils.filter(packages, new Predicate<CreditPackage>() {
                @Override
                public boolean apply(CreditPackage input) {
                    DateMidnight normalisedNow = clock.getDateTime().toDateMidnight();
                    return input.getEndDate().toDateMidnight().isBefore(normalisedNow);
                }
            });
        }
    };

    private String displayText;

    /**
     * Constructor.
     *
     * @param displayText the display text for the filter
     */
    PackageStatusFilter(String displayText) {
        this.displayText = displayText;
    }

    /**
     * @return the unique value
     */
    public String getValue() {
        return name();
    }

    /**
     * @return Display Name
     */
    public String getDisplayName() {
        return displayText;
    }

    /**
     * @return Display Value
     */
    public String getDisplayValue() {
        return displayText;
    }

    /**
     * @return the display text for this filter
     */
    public String getDisplayText() {
        return displayText;
    }

    /**
     * Filter the list of packages.
     *
     * @param packages the list of packages to filter
     * @return a filtered list of packages
     */
    public abstract List<CreditPackage> filter(List<CreditPackage> packages, Clock clock);
}