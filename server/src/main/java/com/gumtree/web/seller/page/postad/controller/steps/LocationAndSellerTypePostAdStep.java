package com.gumtree.web.seller.page.postad.controller.steps;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.domain.location.Location;
import com.gumtree.service.location.LocationService;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.seller.page.postad.controller.PanelFactory;
import com.gumtree.web.seller.page.postad.controller.SellerTypePanel;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdFormPanel;
import com.gumtree.web.seller.page.postad.model.PostAdFormStatus;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.seller.page.postad.model.VrmStatus;
import com.gumtree.web.seller.page.postad.model.location.BrowseLocation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.SELLER_TYPE;

@Component
public class LocationAndSellerTypePostAdStep implements PostAdStep {
    public static final Integer ORDER = LegalPostAdStep.ORDER + 1;

    @Autowired
    private CategoryModel categoryModel;

    @Autowired
    private LocationService locationService;

    @Autowired
    protected ErrorMessageResolver messageResolver;

    @Autowired
    private PanelFactory panelFactory;

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public boolean execute(PostAdSubmitModel.Builder model, AdvertEditor editor) {
        //TODO: This is hardcoded, can it be a separate class that checks when seller type panel should be shown?
        // LOCATION && SELLER TYPE REDIRECT
        model.withStatus(PostAdFormStatus.LOCATION);
        Optional<AttributeMetadata> sellerTypeAttribute =
                categoryModel.findAttributesByNameForGivenCategory(SELLER_TYPE.getName(), editor.getCategoryId());

        if (!isSellerTypePriceSensitive(sellerTypeAttribute)) {
            return validateOnlyLocationForm(model, editor);
        }
        return validateLocationAndSellerTypeForm(model, editor, sellerTypeAttribute.get());
    }

    private boolean isSellerTypePriceSensitive(Optional<AttributeMetadata> sellerTypeAttribute) {
        return sellerTypeAttribute.isPresent() && sellerTypeAttribute.get().isPriceSensitive();
    }

    private boolean validateLocationAndSellerTypeForm(PostAdSubmitModel.Builder model,
                                                      AdvertEditor editor,
                                                      AttributeMetadata attributeMetadata) {
        SellerTypePanel sellerTypePanel = panelFactory.createSellerTypePanel(editor, attributeMetadata);
        model.withSellerType(sellerTypePanel);

        preSelectValueForSellerTypeIfPossible(editor.getPostAdFormBean(), sellerTypePanel);
        if (editor.isValidLocationSelected()) {
            Location location = location(editor);
            model.withLocationCrumb(getLocationCrumb(location));
            model.validLocationSelected(true);
            model.withMapDetails(location);
        }

        if (CategoryConstants.CARS_ID.equals(editor.getCategoryId()) && editor.getAdvertDetail().isVrnWidgetFlow()) {
            editor.getPostAdFormBean().populateErrors(editor.getAdvertDetail(), messageResolver);
            if (editor.getPostAdFormBean().getAttributes().get(CategoryConstants.Attribute.VRN.getName()) == null) {
                model.withPanels(Lists.newArrayList(
                        PostAdFormPanel.CATEGORY,
                        PostAdFormPanel.VEHICLE_SPECIFICATIONS));
                return true;
            } else {
                model.withPanels(Lists.newArrayList(
                        PostAdFormPanel.CATEGORY,
                        PostAdFormPanel.VEHICLE_SPECIFICATIONS,
                        PostAdFormPanel.LOCATION,
                        PostAdFormPanel.SELLER_TYPE));
                return true;
            }
        } else if (sellerTypePanel.isSellerTypeSelected() && editor.isValidLocationSelected()) {
            model.withPanels(Lists.newArrayList(PostAdFormPanel.CATEGORY, PostAdFormPanel.LOCATION, PostAdFormPanel.SELLER_TYPE));
            return true;
        } else if (editor.getPostAdFormBean().getVrmStatus().equals(VrmStatus.VRM_INVALID_OR_EMPTY)) {
            editor.getPostAdFormBean().populateErrors(editor.getAdvertDetail(), messageResolver);
            model.withPanels(Lists.newArrayList(
                    PostAdFormPanel.CATEGORY,
                    PostAdFormPanel.VEHICLE_SPECIFICATIONS,
                    PostAdFormPanel.LOCATION,
                    PostAdFormPanel.SELLER_TYPE,
                    PostAdFormPanel.CONTINUE));
            return true;
        } else {
            editor.getPostAdFormBean().populateErrors(editor.getAdvertDetail(), messageResolver);
            model.withPanels(ImmutableList.of(
                    PostAdFormPanel.CATEGORY,
                    PostAdFormPanel.LOCATION,
                    PostAdFormPanel.SELLER_TYPE,
                    PostAdFormPanel.CONTINUE));
            return false;
        }
    }

    private void preSelectValueForSellerTypeIfPossible(PostAdFormBean postAdFormBean, SellerTypePanel sellerTypePanel) {
        String sellerType = sellerTypePanel.getSellerType();
        if (sellerType != null && !postAdFormBean.getAttributes().containsKey(SELLER_TYPE.getName())) {
            postAdFormBean.addAttribute(SELLER_TYPE.getName(), sellerType);
        }
    }

    private boolean validateOnlyLocationForm(PostAdSubmitModel.Builder model, AdvertEditor editor) {

        Boolean validLocationSelected = editor.isValidLocationSelected();

        if (validLocationSelected) {
            Location location = location(editor);
            model.withLocationCrumb(getLocationCrumb(location));
            model.validLocationSelected(true);
            model.withMapDetails(location(editor));
        }

        if (validLocationSelected) {
            model.withPanels(Lists.newArrayList(PostAdFormPanel.CATEGORY, PostAdFormPanel.LOCATION));
            return true;
        } else {
            editor.getPostAdFormBean().populateErrors(editor.getAdvertDetail(), messageResolver);
            model.withPanels(ImmutableList.of(PostAdFormPanel.CATEGORY, PostAdFormPanel.LOCATION, PostAdFormPanel.CONTINUE));
            return false;
        }
    }

    private List<BrowseLocation> getLocationCrumb(Location location) {
        Map<Integer, Location> locationHierarchy = locationService.getLocationHierarchy(location);
        locationHierarchy.remove(1);
        List<BrowseLocation> textCrumb = Lists.newLinkedList();
        for (Location l : locationHierarchy.values()) {
            textCrumb.add(new BrowseLocation(l));
        }
        return textCrumb;
    }

    private Location location(AdvertEditor editor) {
        Long locationId = editor.getLocationId();
        return locationService.getById(locationId.intValue());
    }
}
