package com.gumtree.web.seller.service.image;

import com.google.common.collect.Iterables;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.Image;
import com.gumtree.api.Images;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.api.PostImageApiCall;
import com.gumtree.web.seller.service.image.error.BapiLegacyImageException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import rx.Single;
import rx.functions.Func1;
import com.gumtree.hystrix.ApiKeyHystrixRequestVariable;

import java.util.Optional;

/**
 * Image upload service
 */
@Service
public final class ImageUploadServiceImpl implements ImageUploadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageUploadServiceImpl.class);
    public static final String VALIDATION_ERROR_CODE = "102";

    private final ApiCallExecutor apiCallExecutor;
    private final UserSession authenticatedUserSession;
    private final BapiImageGateway bapiImageGateway;
    private final MediaProcessorGateway mediaProcessorGateway;

    @Autowired
    public ImageUploadServiceImpl(BapiImageGateway bapiImageGateway,
                                  MediaProcessorGateway mediaProcessorGateway,
                                  ApiCallExecutor apiCallExecutor,
                                  UserSession authenticatedUserSession) {
        this.bapiImageGateway = bapiImageGateway;
        this.mediaProcessorGateway = mediaProcessorGateway;
        this.apiCallExecutor = apiCallExecutor;
        this.authenticatedUserSession = authenticatedUserSession;
    }

    @Override
    public Image uploadImage(MultipartFile image) {
        ApiCallResponse<Images> response = apiCallExecutor.call(new PostImageApiCall(authenticatedUserSession, image));

        if (response == null || response.isErrorResponse()) {
            String error = String.format("Image upload failed, BAPI responded with error [%s]", response);
            throw new BapiLegacyImageException(error);
        }

        return Iterables.getOnlyElement(response.getResponseObject().getImages());
    }

    @Override
    public Single<Image> uploadImageRX(MultipartFile image) {
        if (image.getSize() >= GtProps.getInt(SellerProperty.MAX_IMAGE_UPLOAD_SIZE)) {
            return validationError(VALIDATION_ERROR_CODE, "Max image size exceeded");
        }

        return Single.fromCallable(() -> apiCallExecutor.call(new PostImageApiCall(authenticatedUserSession, image)))
                .flatMap(extractImage())
                .onErrorResumeNext(throwable ->
                        internalError(HttpStatus.INTERNAL_SERVER_ERROR.toString(), "Failed to upload image", throwable));
    }

    private Func1<ApiCallResponse<Images>, Single<Image>> extractImage() {
        return response -> {
            if (response.isErrorResponse()) {
                return internalError(response.getErrorCode().toString(), "Failed to upload image");
            }
            Optional<Image> uploadedImage = response.getResponseObject().getImages().stream().findFirst();
            if (uploadedImage.isPresent()) {
                return Single.just(uploadedImage.get());
            } else {
                return internalError(HttpStatus.INTERNAL_SERVER_ERROR.toString(), "Failed to upload image");
            }
        };
    }

    private Single<Image> validationError(String code, String message) {
        return singleError(code, message, HttpStatus.BAD_REQUEST);
    }

    private Single<Image> internalError(String code, String message) {
        return singleError(code, message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private Single<Image> internalError(String code, String message, Throwable throwable) {
        LOGGER.error("Exception thrown while trying to upload an image via Bapi : ", throwable);
        return singleError(code, message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private Single<Image> singleError(String code, String message, HttpStatus internalServerError) {
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(internalServerError,
                code, message);
        WebApiErrorException webApiErrorException = new WebApiErrorException(webApiErrorResponse);
        return Single.error(webApiErrorException);
    }

    @Override
    public Image postImage(MultipartFile multipartFile) {
        BushfireApiKey apiKey = authenticatedUserSession.getApiKey();
        ApiKeyHystrixRequestVariable.set(apiKey.getAccessKey());
        return bapiImageGateway.get(mediaProcessorGateway.post(multipartFile));
    }

}
