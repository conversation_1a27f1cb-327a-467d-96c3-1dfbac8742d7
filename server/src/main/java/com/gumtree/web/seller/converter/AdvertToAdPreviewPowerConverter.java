package com.gumtree.web.seller.converter;

import com.gumtree.domain.advert.Advert;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.web.seller.model.AdPreview;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Utility converter with methods to convert collections
 * and autowired services
 */
@Component
public class AdvertToAdPreviewPowerConverter {

    private AdvertToAdPreviewConverter converter;
    private CategoryService categoryService;
    private LocationService locationService;


    @Autowired
    public AdvertToAdPreviewPowerConverter(AdvertToAdPreviewConverter converter,
                                           CategoryService categoryService, LocationService locationService) {
        this.converter = converter;
        this.categoryService = categoryService;
        this.locationService = locationService;
    }

    public Collection<AdPreview> convert(Collection<Advert> inAdverts) {
        List<AdPreview> results = new ArrayList<AdPreview>();

        for (Advert advert : inAdverts) {
            AdPreview converted = convert(advert);
            results.add(converted);
        }
        return results;
    }

    public AdPreview convert(Advert advert) {
        Category category = categoryService.getById(advert.getCategoryId()).orNull();
        Location location = locationService.getById(advert.getLocationId());
        return converter.convert(advert, location, category);
    }


}
