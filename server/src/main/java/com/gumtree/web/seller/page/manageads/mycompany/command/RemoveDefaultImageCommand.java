package com.gumtree.web.seller.page.manageads.mycompany.command;

import com.gumtree.api.Account;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.api.domain.account.DefaultImageBean;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.impl.AuthenticatedApiCall;

/**
 * Command for removing default image from an account.
 */
public final class RemoveDefaultImageCommand extends AuthenticatedApiCall<Account> {

    private Long accountId;

    /**
     * Constructor.
     *
     * @param accountId      the account id
     * @param apiKeyProvider the api key provider
     */
    public RemoveDefaultImageCommand(Long accountId, ApiKeyProvider apiKeyProvider) {
        super(apiKeyProvider);
        this.accountId = accountId;
    }

    @Override
    public Account execute(BushfireApi api) {
        return api.create(AccountApi.class, getApiKey()).setDefaultImage(accountId, new DefaultImageBean());
    }
}
