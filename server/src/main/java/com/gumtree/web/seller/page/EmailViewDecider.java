package com.gumtree.web.seller.page;

import com.gumtree.web.security.UserSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

/**
 * Decides on a view based on a hash of the user email
 */
public final class EmailViewDecider implements ViewDecider {

    @Value("${gumtree.viewDecider.email.module}")
    private int module;

    @Value("${gumtree.viewDecider.email.threshold}")
    private int threshold;

    @Autowired
    private UserSession userSession;

    @Override
    public String decideView(String originalViewName) {
        String view = originalViewName;
        int value = Math.abs(userSession.getUsername().hashCode() % module);
        if (value <= threshold) {
            view = "pages/" + originalViewName + "/" + originalViewName + "-responsive";
        }
        return view;
    }

    //TODO: remove rg/responsive flags from other parts
}
