package com.gumtree.web.seller.page.postad.controller.steps;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.Category;
import com.gumtree.mobile.web.category.BrowseCategory;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdFormPanel;
import com.gumtree.web.seller.page.postad.model.PostAdFormStatus;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class CategorySelectPostAdStep implements PostAdStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(CategorySelectPostAdStep.class);

    public static final Integer ORDER = 1;

    @Autowired
    private CategoryService categoryService;

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public boolean execute(PostAdSubmitModel.Builder model, AdvertEditor editor) {
        model.withForm(editor.getPostAdFormBean());
        model.supportsChangeCategory(editor.supportsChangeCategory());
        model.withStatus(PostAdFormStatus.CATEGORY);
        if (!editor.isValidCategorySelected()) {
            model.withPanels(ImmutableList.of(PostAdFormPanel.CATEGORY, PostAdFormPanel.CONTINUE));
            return false;
        }
        model.withCategoryCrumb(getCategoryCrumb(editor));
        model.validCategorySelected(true);

        boolean isCreate = editor.isCreateMode() || editor.isDraftMode();
        if(!isCreate){
            model.withPanel(PostAdFormPanel.BUMP);
        }
        model.withPanel(PostAdFormPanel.CATEGORY);
        return true;
    }

    private List<BrowseCategory> getCategoryCrumb(AdvertEditor editor) {
        try {
            Function<Category, BrowseCategory> function = toBrowseCategory(editor.getCategoryId());
            return Lists.newLinkedList(Iterables.transform(getCategoryCrumb(editor.getCategoryId()), function));
        } catch(Exception e) {
            LOGGER.error("Failed to get category crumb, advert category {}", editor.getCategoryId());
            throw e;
        }
    }

    private List<Category> getCategoryCrumb(long categoryId) {
        Optional<Category> category = categoryService.getById(categoryId);
        if (category.isPresent()) {
            Map<Integer, Category> categoryMap = categoryService.getLevelHierarchy(category.get());
            return categoryMap.values().stream().skip(1)
                            .filter(c -> !c.isHidden()).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }

    private Function<Category, BrowseCategory> toBrowseCategory(final Long catId) {
        return new Function<Category, BrowseCategory>() {
            @Nullable
            @Override
            public BrowseCategory apply(@Nullable Category input) {
                return new BrowseCategory(input, Lists.<BrowseCategory>newLinkedList(), input.getId().equals(catId));
            }
        };
    }
}
