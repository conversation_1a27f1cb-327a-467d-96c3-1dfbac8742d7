package com.gumtree.web.seller.service.pricing;

import com.gumtree.api.ApiProductPrice;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.PriceApi;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.domain.attribute.ApiAttribute;
import com.gumtree.common.util.time.Clock;
import com.gumtree.domain.location.Location;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.products.DefaultPricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.DefaultProductPrice;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.ProductPrice;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.SELLER_TYPE;
import static java.util.stream.Collectors.toList;

/**
 * Implementation of {@link PricingService} that retrieves prices via the Bushfire API.
 */
@Service
public final class ApiPricingService implements PricingService {

    private final BushfireApi bushfireApi;
    private final UserSession userSession;
    private final Clock clock;

    @Autowired
    public ApiPricingService(BushfireApi bushfireApi,
                             UserSession userSession,
                             Clock clock) {
        this.bushfireApi = bushfireApi;
        this.userSession = userSession;
        this.clock = clock;
    }

    @Override
    public PricingMetadata getPriceInformation(PricingContext context) {

        Map<ProductType, List<ProductPrice>> pricingMap = ProductType.extract(lookupPrices(context));

        DefaultPricingMetadata.Builder metadataBuilder = new DefaultPricingMetadata.Builder(clock);

        for (ProductType type : pricingMap.keySet()) {

            List<ProductPrice> priceList = pricingMap.get(type);

            if (type.equals(ProductType.INSERTION)) {

                metadataBuilder.withInsertionPrice(getProductInsertionPrice(priceList.get(0)));

            } else if (type.equals(ProductType.BUMP_UP)) {

                metadataBuilder.withBumpUpPrice(priceList.get(0));

            } else {

                if (context.isProductActive(type)) {

                    metadataBuilder.withActiveFeature(type, context.getProductExpiryDate(type));

                } else {

                    metadataBuilder.withNonActiveFeature(type, priceList);
                }
            }
        }

        return metadataBuilder.build();
    }

    private List<ApiProductPrice> lookupPrices(PricingContext context) {
        AdvertEditor advertEditor = context.getAdvertEditor();

        PostAdvertBean bean = new PostAdvertBean();
        bean.setAttributes(attributesFrom(advertEditor));
        bean.setAccountId(context.getAccountId());
        bean.setCategoryId(context.getCategoryId());
        bean.setLocationId(context.getLocationId() != null ? context.getLocationId() : Location.UK_ID);

        if(context.getSellerType().isPresent() && sellerTypeNotSet(bean)) {
            bean.getAttributes().add(new ApiAttribute(SELLER_TYPE.getName(), context.getSellerType().get().name()));
        }

        Long advertId = advertEditor.getAdvertId();
        BushfireApiKey apiKey = userSession.getApiKey();
        PriceApi priceApi = bushfireApi.create(PriceApi.class, apiKey);
        if (advertId != null) {
            return priceApi.getPricesForAdvert(advertId, bean);
        } else {
            return priceApi.getPricesForAdvert(bean);
        }
    }

    private boolean sellerTypeNotSet(PostAdvertBean bean) {
        return bean.getAttributes().stream().noneMatch(e -> Objects.equals(e.getName(), SELLER_TYPE.getName()));
    }

    private List<ApiAttribute> attributesFrom(AdvertEditor advertEditor) {
        return advertEditor
                .getAdvertDetail()
                .getPostAdFormBean()
                .getAttributes()
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue() != null)
                .map(entry -> new ApiAttribute(entry.getKey(), entry.getValue()))
                .collect(toList());
    }

    private ProductPrice getProductInsertionPrice(ProductPrice insertionPrice) {
        return new DefaultProductPrice(insertionPrice.getProductType(), insertionPrice.getProductName(), insertionPrice.getPrice());
    }
}
