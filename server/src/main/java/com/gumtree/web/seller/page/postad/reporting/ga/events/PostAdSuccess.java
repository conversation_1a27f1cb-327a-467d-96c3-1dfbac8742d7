package com.gumtree.web.seller.page.postad.reporting.ga.events;

import com.gumtree.api.Ad;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsElementTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEventAction;
import com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsTrackEventLabelBuilder;

public class PostAdSuccess extends GoogleAnalyticsElementTrackEvent {

    public PostAdSuccess(ThirdPartyRequestContext<Ad> ctx) {
        super(ctx.getPageType(), getEventAction(ctx));
        setLabel(eventLabel(ctx));
    }

    private static GoogleAnalyticsTrackEventAction getEventAction(ThirdPartyRequestContext<Ad> ctx) {
        return ctx.getOrder().isFree()
                ? GoogleAnalyticsTrackEventAction.POST_AD_FREE_SUCCESS
                : GoogleAnalyticsTrackEventAction.POST_AD_PAID_SUCCESS;
    }

    private String eventLabel(ThirdPartyRequestContext<Ad> ctx) {
        return new GoogleAnalyticsTrackEventLabelBuilder()
                .catId(ctx.getCategory())
                .locId(ctx.getLocation())
                .adId(ctx.getPageModel().getId())
                .build();
    }

}
