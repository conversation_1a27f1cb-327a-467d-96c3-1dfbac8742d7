package com.gumtree.web.seller.converter;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.location.Location;
import com.gumtree.web.seller.model.AdPreview;


/**
 * Converts recent ads to {@link AdPreview}.
 */
public interface AdvertToAdPreviewConverter {

    /**
     * Build a AdPreview wrapper for the given Advert.
     *
     * @param advert   the advert to wrap
     * @param location of the advert
     * @param category for category lookups
     * @return a {@link AdPreview} wrapper
     */
    AdPreview convert(Advert advert, Location location, Category category);
}
