package com.gumtree.web.seller.page.payment.model;

import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import org.apache.commons.lang.Validate;
import org.springframework.web.servlet.ModelAndView;

/**
 * Returned when eg ad is deleted during checkout process
 */
public final class PostAdErrorViewModel extends CommonModel {
    private String errorCode;


    public String getErrorCode() {
        return errorCode;
    }


    private PostAdErrorViewModel(CoreModel core) {
        super(core);
    }

    public static final class Builder {
        private final PostAdErrorViewModel model;

        public Builder(CoreModel.Builder coreBuilder){
            model = new PostAdErrorViewModel(coreBuilder.build(Page.PaymentError));
        }

        public Builder withErrorCode(String errorCode) {
            model.errorCode = errorCode;
            return this;
        }

        public ModelAndView build() {
            Validate.notEmpty(model.getErrorCode());

            ModelAndView modelAndView = new ModelAndView(model.getCore().getPage().getTemplateName());
            modelAndView.addObject(CommonModel.MODEL_KEY, model);
            return modelAndView;
        }
    }

}
