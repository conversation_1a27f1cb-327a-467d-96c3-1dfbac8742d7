package com.gumtree.web.seller.service.securetoken;

/**
 * Service for added security on certain pages that may manipulate sensitive data.
 *
 * Use of tokens helps guard against CSRF
 * (http://projects.webappsec.org/w/page/13246919/Cross%20Site%20Request%20Forgery)
 */
public interface SecureTokenService {

    /**
     * Generate a new token for use on a sensitive page
     *
     * @param pageType - what kind of page are we on
     * @return generated Nonce (lol)
     */
    String getOrGenerateToken(String pageType);

    /**
     * Check whether given token is the correct one for the request
     *
     * @param pageType - what kind of page are we on
     * @param token - the nonce to check
     * @return valid token or not
     */
    boolean checkToken(String pageType, String token);
}
