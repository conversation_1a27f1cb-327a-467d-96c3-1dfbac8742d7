package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import org.mvel2.MVEL;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 *
 */
public class AtLeastOneTrueValidator
        implements ConstraintValidator<AtLeastOneTrue, PostAdFormBean> {
    private String[] fieldList;
    private String message;


    @Override
    public final void initialize(AtLeastOneTrue constraintAnnotation) {
        this.fieldList = constraintAnnotation.fieldList();
        this.message = constraintAnnotation.message();
    }

    @Override
    public final boolean isValid(PostAdFormBean value, ConstraintValidatorContext context) {
        boolean valid = false;

        for (String fieldName : fieldList) {
            Object fieldObj = MVEL.getProperty(fieldName, value);

            boolean flagSet = Boolean.parseBoolean(fieldObj.toString());

            if (flagSet) {
                valid = true;
                break;
            }
        }

        if (!valid) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(message)
                    .addNode(fieldList[0])
                    .addConstraintViolation();
        }

        return valid;
    }
}
