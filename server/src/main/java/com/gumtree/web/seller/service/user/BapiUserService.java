package com.gumtree.web.seller.service.user;

import com.google.common.base.Optional;
import com.gumtree.api.User;
import com.gumtree.api.UserResolver;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.command.GetUserApiCallCommand;
import org.springframework.beans.factory.annotation.Autowired;

@SuppressWarnings({"Guava", "SpringJavaAutowiredMembersInspection"})
public class BapiUserService implements UserResolver {

    @Autowired
    private ApiCallExecutor apiCallExecutor;

    @Override
    public Optional<User> resolve(String userEmail) {
        ApiCallResponse<User> response = new GetUserApiCallCommand(userEmail, apiCallExecutor).execute();
        if (response.isErrorResponse()) {
            return Optional.absent();
        } else {
            return Optional.fromNullable(response.getResponseObject());
        }
    }

}
