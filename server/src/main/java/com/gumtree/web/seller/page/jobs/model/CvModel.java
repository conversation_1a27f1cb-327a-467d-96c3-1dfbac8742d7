package com.gumtree.web.seller.page.jobs.model;

import com.gumtree.service.jobs.CvData;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import org.springframework.web.servlet.ModelAndView;

import java.io.Serializable;
import java.util.Optional;

public final class CvModel extends CommonModel implements Serializable {

    private String secureToken;
    private CvData cvData;
    private Boolean isProUser;
    private ReportableErrorsMessageResolvingErrorSource errors;

    private CvModel(CoreModel core) {
        super(core);
    }

    public CvData getCvData() {
        return cvData;
    }

    public String getSecureToken() {
        return secureToken;
    }

    public Boolean getIsProUser() {
        return isProUser;
    }

    public ReportableErrorsMessageResolvingErrorSource getErrors() {
        return errors;
    }

    public static Builder builder(CoreModel.Builder coreModelBuilder, Page page) {
        return new Builder(coreModelBuilder, page);
    }

    public static final class Builder {

        private CvModel cvModel;
        private Page page;

        public Builder(CoreModel.Builder coreModelBuilder, Page page) {
            this.cvModel = new CvModel(coreModelBuilder.build(page));
            this.page = page;
        }

        public Builder withCvData(Optional<CvData> cvData) {
            if(cvData.isPresent()) {
                this.cvModel.cvData = cvData.get();
            } else {
                // don't know if freemaker can handle optional
                this.cvModel.cvData = null;
            }
            return this;
        }

        public Builder withSecureToken(String secureToken) {
            this.cvModel.secureToken = secureToken;
            return this;
        }

        public Builder withIsProUser(boolean isProUser) {
            this.cvModel.isProUser = isProUser;
            return this;
        }

        public Builder withErrors(ReportableErrorsMessageResolvingErrorSource errors) {
            this.cvModel.errors = errors;
            return this;
        }

        public ModelAndView build() {
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY, cvModel);
        }

    }

}
