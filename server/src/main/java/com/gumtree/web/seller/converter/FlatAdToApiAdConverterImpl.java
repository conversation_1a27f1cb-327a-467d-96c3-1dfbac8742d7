package com.gumtree.web.seller.converter;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.LegacyImage;
import com.gumtree.api.Price;
import com.gumtree.api.PriceFrequency;
import com.gumtree.liveadsearch.model.FlatAd;
import com.gumtree.liveadsearch.model.Location;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.AttributeService;
import com.gumtree.service.category.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Currency;
import java.util.List;
import java.util.Map;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.PRICE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.PRICE_FREQUENCY;

/**
 * Convert a FlatAd in a com.gumtree.api.Ad
 */
@Component
public class FlatAdToApiAdConverterImpl implements FlatAdToApiAdConverter {

    private FlatAdConverterUtils utils;

    @Autowired
    public FlatAdToApiAdConverterImpl(AttributeService attributeService, CategoryService categoryService) {
        this.utils = new FlatAdConverterUtils(attributeService, categoryService);
    }

    @Override
    public Ad convert(FlatAd advert) {
        Map<String, Attribute> adAttributeMap = utils.getAdAttributeMap(advert);
        Ad ad = new Ad();
        ad.setId(advert.getId());
        ad.setTitle(advert.getTitle());
        ad.setDescription(advert.getDescription());
        ad.setLocationId(utils.getPrimaryLocation(advert).getId());
        ad.setLocationString(convertLocationString(advert));
        ad.setPostcode(advert.getPostcode());
        ad.setRepliesEmail(advert.getContactEmail());
        ad.setPrice(buildPrice(adAttributeMap));
        ad.setPriceFrequency(buildPriceFrequency(adAttributeMap));
        ad.setCategoryId(utils.getPrimaryCategory(advert).getId());
        ad.setStatus(AdStatus.valueOf(advert.getStatus()));
        convertLocations(advert, ad);
        ad.setImages(getImages(advert));
        ad.setWebsiteUrl(advert.getPublicWebsiteUrl());
        return ad;
    }

    private List<Map<String, LegacyImage>> getImages(FlatAd advert) {
        String primaryImageUrl = advert.getPrimaryImageUrl();
        Map<String, LegacyImage> map = Maps.newHashMap();
        LegacyImage primary = new LegacyImage();
        primary.setUrl(primaryImageUrl);
        map.put(primaryImageUrl, primary);

        List<String> additionalImageUrls = advert.getAdditionalImageUrls();
        if (additionalImageUrls != null) {
            for (String additionalImageUrl : additionalImageUrls) {
                LegacyImage image = new LegacyImage();
                image.setUrl(additionalImageUrl);
                map.put(additionalImageUrl, image);
            }
        }
        return Lists.newArrayList(map);
    }

    private String convertLocationString(FlatAd advert) {
        if (advert.getLocalArea() == null || advert.getLocalArea().isEmpty()) {
            return utils.getPrimaryLocation(advert).getDisplayName();
        }
        return advert.getLocalArea();
    }

    private Price buildPrice(Map<String, Attribute> adAttributeMap) {
        BigDecimal price = getPrice(adAttributeMap);
        if (price != null) {
            Price priceEntity = new Price();
            priceEntity.setAmount(price.longValue());
            priceEntity.setCurrency(Currency.getInstance("GBP").toString());
            return priceEntity;
        }
        return null;
    }

    private PriceFrequency buildPriceFrequency(Map<String, Attribute> adAttributeMap) {
        Attribute priceFrequencyAtr = adAttributeMap.get(PRICE_FREQUENCY.getName());
        if (priceFrequencyAtr != null) {
            return normalizePriceFrequency(priceFrequencyAtr.getValue().getName());
        }
        return null;
    }

    private PriceFrequency normalizePriceFrequency(String name) {
        if (name.equalsIgnoreCase("per_week")) {
            return PriceFrequency.WEEKLY;
        }
        return PriceFrequency.MONTHLY;
    }

    private BigDecimal getPrice(Map<String, Attribute> adAttributeMap) {
        Attribute priceAsStr = adAttributeMap.get(PRICE.getName());
        if (priceAsStr != null) {
            Double priceAsDouble = Double.valueOf(priceAsStr.getValue().as(String.class));
            return BigDecimal.valueOf(priceAsDouble);
        }
        return null;
    }

    private void convertLocations(FlatAd flatAd, Ad advert) {
        if (flatAd.getLocations() != null && flatAd.getLocations().size() > 0) {
            List<com.gumtree.api.Location> locations = new ArrayList<com.gumtree.api.Location>();
            for (Location location : flatAd.getLocations()) {
                com.gumtree.api.Location apiLocation = new com.gumtree.api.Location();
                apiLocation.setId(location.getId());
                apiLocation.setName(location.getDisplayName());
                apiLocation.setSeoName(location.getName());
                locations.add(apiLocation);
            }
            //we are assuming that this list is not ordered from the biggest to the smallest location
            advert.setLocations(locations.toArray(new com.gumtree.api.Location[locations.size()]));
            advert.setLocationId(utils.getPrimaryLocation(flatAd).getId());
        }
    }

}
