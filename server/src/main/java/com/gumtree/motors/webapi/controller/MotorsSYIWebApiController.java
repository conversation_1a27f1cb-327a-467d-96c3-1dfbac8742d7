package com.gumtree.motors.webapi.controller;

import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.motors.webapi.model.MotorsAd;
import com.gumtree.motors.webapi.service.MotorsService;
import com.gumtree.motorssyi.client.model.CreateMotorAdRequest;
import com.gumtree.motorssyi.client.model.InternalError;
import com.gumtree.motorssyi.client.model.MotorUploadImageSuccess;
import com.gumtree.motorssyi.client.model.PostCodeLookUpApiResponse;
import com.gumtree.motorssyi.client.model.VrmLookupApiResponse;
import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.seller.page.payment.controller.PaymentCheckoutController;
import com.gumtree.web.seller.service.image.ImageUploadService;
import com.gumtree.web.seller.service.location.PostAdLocationService;
import javaslang.Tuple2;
import javaslang.control.Try;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import rx.functions.Func1;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.convertToInternalError;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.converterToMotorAdFail;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.convertToMotorAdSuccess;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.converterToMotorUploadImageFail;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.convertToMotorUploadImageSuccess;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.convertToMotorsAd;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.convertToPostCodeLookUpApiResponse;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.convertToVrmLookupApiResponse;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;

@RestController
@RequestMapping("/api/motors/syi")
public class MotorsSYIWebApiController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MotorsSYIWebApiController.class);

    private final PostAdLocationService locationService;

    private final CookieResolver cookieResolver;

    private final MotorsService motorsService;

    private final ImageUploadService imageUploadService;

    @Autowired
    MotorsSYIWebApiController(PostAdLocationService locationService,
                              CookieResolver cookieResolver, MotorsService motorsService, ImageUploadService imageUploadService) {
        this.locationService = locationService;
        this.cookieResolver = cookieResolver;
        this.motorsService = motorsService;
        this.imageUploadService = imageUploadService;
    }

    @RequestMapping(value = "/ajax/postcode/{postcode}", method = RequestMethod.GET)
    public final ResponseEntity validatePostCode(@PathVariable final String postcode) {
        PostCodeLookUpApiResponse postCodeLookUpApiResponse = convertToPostCodeLookUpApiResponse(locationService.lookupPostcode(postcode));
        return new ResponseEntity<>(postCodeLookUpApiResponse, HttpStatus.OK);
    }

    @RequestMapping(value = "/ajax/vrm/{vrm}/category/{categoryId}", method = RequestMethod.GET)
    public final ResponseEntity getVrmAttributes(@PathVariable final String vrm, @PathVariable final Long categoryId) {
        Tuple2<List<AttributeMetadata>, Map<String, String>> vrmAttributes = motorsService.getVrmAttributes(vrm, categoryId);
        VrmLookupApiResponse vrmLookupApiResponse = convertToVrmLookupApiResponse(vrmAttributes._1, vrmAttributes._2);
        return new ResponseEntity<>(vrmLookupApiResponse, HttpStatus.OK);
    }

    @RequestMapping(value = "/ajax/postad", method = RequestMethod.POST)
    public final ResponseEntity postMotorsAd(@RequestBody final CreateMotorAdRequest createMotorAdRequest, RemoteIP remoteIP,
                                             HttpServletRequest request) {

        MotorsAd motorsAd = convertToMotorsAd(createMotorAdRequest);
        PermanentCookie permanentCookie = cookieResolver.resolve(request, PermanentCookie.class);
        ThreatMetrixCookie threatMetrixCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);
        String threatmetrixSessionId = threatMetrixCookie.getDefaultValue();

        return motorsService.createMotorAd(motorsAd, remoteIP.getIpAddress(), permanentCookie, threatmetrixSessionId)
                .map(checkout -> {
                        String redirect = "/" + PaymentCheckoutController.VIEW_NAME + "/" + checkout.getKey();
                        LOGGER.info("Motors ad created successfully with id " +
                                Try.of(() -> checkout.getAdvert().getId()).getOrElse(-1L));
                        return new ResponseEntity(convertToMotorAdSuccess(redirect), HttpStatus.OK);
                    }).onErrorReturn(createApiErrorWith(converterToMotorAdFail()))
                .toBlocking()
                .value();
    }

    @RequestMapping(value = "/ajax/postad/image", method = RequestMethod.POST)
    public final ResponseEntity uploadImage(@RequestParam("image") MultipartFile imageMultipartFile) {

        return imageUploadService.uploadImageRX(imageMultipartFile).map(image -> {
            MotorUploadImageSuccess motorUploadImageSuccess = convertToMotorUploadImageSuccess(imageMultipartFile.getOriginalFilename(),
                    image);
            return new ResponseEntity(motorUploadImageSuccess, HttpStatus.OK);

        }).onErrorReturn(createApiErrorWith(converterToMotorUploadImageFail()))
                .toBlocking()
                .value();
    }

    @ExceptionHandler(Throwable.class)
    private ResponseEntity<InternalError> handleRuntimeException(Throwable throwable) {
        LOGGER.error("Unexpected error in motor SYI:", throwable);
        return new ResponseEntity<>(buildDefaultInternalError(throwable), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private Func1<Throwable, ResponseEntity> createApiErrorWith(Function<WebApiErrorResponse, ?> convertFunction) {
        return error -> {
            if (error instanceof WebApiErrorException) {
                WebApiErrorResponse errorResponse = ((WebApiErrorException) error).getError();
                if (errorResponse.getStatus().is4xxClientError()) {
                    return new ResponseEntity(convertFunction.apply(errorResponse), HttpStatus.BAD_REQUEST);
                } else {
                    LOGGER.error("Error while creating a motors ad", error);
                    return new ResponseEntity(convertToInternalError(errorResponse), HttpStatus.INTERNAL_SERVER_ERROR);
                }
            } else {
                return new ResponseEntity(buildDefaultInternalError(error), HttpStatus.INTERNAL_SERVER_ERROR);
            }
        };
    }

    private InternalError buildDefaultInternalError(Throwable throwable) {
        LOGGER.error("Error while creating a motors ad", throwable);
        InternalError defaultInternalError = new InternalError();
        defaultInternalError.setErrorCode(INTERNAL_SERVER_ERROR.getReasonPhrase());
        defaultInternalError.setErrorDetail("internal-error");
        defaultInternalError.setUserMessage("Something went wrong");
        return defaultInternalError;
    }
}
