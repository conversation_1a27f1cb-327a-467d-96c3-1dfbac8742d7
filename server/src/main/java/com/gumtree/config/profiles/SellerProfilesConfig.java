package com.gumtree.config.profiles;

import com.gumtree.api.util.ObjectMapperFactory;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({
        ElasticSearchProfileConfig.class,
        StubApiProfileConfig.class
})
public class SellerProfilesConfig {

    /**
     * Object mapper bean.
     *
     * @return the bean
     */
    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapperFactory().create();
    }
}
