package com.gumtree.config.servlet;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.MwebProperty;
import com.gumtree.web.common.device.DefaultDeviceArgumentResolver;
import com.gumtree.web.common.device.DeviceResolverHandlerInterceptor;
import com.gumtree.web.common.interceptor.CustomResponseHeadersInterceptor;
import com.gumtree.web.common.interceptor.ModelHandlerInterceptor;
import com.gumtree.web.common.interceptor.RequestAnalyser;
import com.gumtree.web.common.page.context.GumtreePageContextInterceptor;
import com.gumtree.web.cookie.CookieHandlerInterceptor;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.interceptor.LogModelHandlerInterceptor;
import com.gumtree.web.seller.interceptor.ModelExtensionInterceptor;
import com.gumtree.web.seller.interceptor.RequestMetricsInterceptor;
import com.gumtree.web.seller.security.SuperUserAccountOverrideInterceptor;
import com.gumtree.web.seller.service.securetoken.SecureTokenInterceptor;
import com.gumtree.web.seller.service.securetoken.SecureTokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import org.springframework.web.servlet.mvc.WebContentInterceptor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Configuration for MVC interceptors
 */
@Configuration
public class SellerWebsiteInterceptorsConfig extends WebMvcConfigurerAdapter {

    @Value("${gumtree.access.control.allow.origin}")
    private String accessControlAllowOrigin;

    @Autowired
    private UserSession userSession;

    @Autowired
    private SecureTokenService secureTokenService;

    @Autowired
    private HandlerInterceptor zenoInterceptor;

    @Autowired
    private CookieHandlerInterceptor cookieHandlerInterceptor;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        super.addResourceHandlers(registry);
        registry.addResourceHandler("/responsive/**").addResourceLocations("/responsive/");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(requestMetricsInterceptor());
        registry.addInterceptor(cookieHandlerInterceptor);
        registry.addInterceptor(new DeviceResolverHandlerInterceptor());
        registry.addInterceptor(zenoInterceptor);
        registry.addInterceptor(logModelHandlerInterceptor());
        registry.addInterceptor(displayModelHandlerInterceptor());
        registry.addInterceptor(webContentInterceptor());
        registry.addInterceptor(customResponseHeadersInterceptor());
        registry.addInterceptor(requestAnalyser());
        registry.addInterceptor(gumtreePageContextInterceptor());
        registry.addInterceptor(superUserAccountOverrideInterceptor());
        registry.addInterceptor(modelExtensionInterceptor());
        registry.addInterceptor(secureTokenInterceptor());
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new DefaultDeviceArgumentResolver());
    }

    /**
     * Bean for controlling the page context.
     *
     * @return an interceptor
     */
    @Bean
    public HandlerInterceptor gumtreePageContextInterceptor() {
        return new GumtreePageContextInterceptor();
    }

    /**
     * Bean for controlling super user account override
     *
     * @return an interceptor
     */
    @Bean
    public HandlerInterceptor superUserAccountOverrideInterceptor() {
        return new SuperUserAccountOverrideInterceptor();
    }

    /**
     * Bean for adding custom response headers.
     *
     * @return an interceptor
     */
    @Bean
    public HandlerInterceptor customResponseHeadersInterceptor() {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("X-Gumtree-Platform", "Bushfire");
        headers.put("Access-Control-Allow-Origin", accessControlAllowOrigin);
        return new CustomResponseHeadersInterceptor(headers);
    }

    /**
     * Bean for analysing the result.
     *
     * @return an interceptor
     */
    @Bean
    public HandlerInterceptor requestAnalyser() {
        return new RequestAnalyser();
    }

    /**
     * Bean for setting web content settings.
     *
     * @return an interceptor
     */
    @Bean
    public HandlerInterceptor webContentInterceptor() {
        WebContentInterceptor interceptor = new WebContentInterceptor();
        interceptor.setCacheSeconds(0);
        interceptor.setUseExpiresHeader(true);
        interceptor.setUseCacheControlHeader(true);
        interceptor.setUseCacheControlNoStore(true);
        return interceptor;
    }
    @Bean
    public HandlerInterceptor logModelHandlerInterceptor() {
        return new LogModelHandlerInterceptor();
    }

    @Bean
    public HandlerInterceptor requestMetricsInterceptor() {
        return new RequestMetricsInterceptor();
    }

    @Bean
    public HandlerInterceptor displayModelHandlerInterceptor() {
        String headerSecret = GtProps.getStr(MwebProperty.MODEL_JSON_HEADER_SECRET);
        return new ModelHandlerInterceptor(headerSecret);
    }

    @Bean
    public HandlerInterceptor secureTokenInterceptor() {
        return new SecureTokenInterceptor(secureTokenService);
    }

    private HandlerInterceptor modelExtensionInterceptor() {
        return new ModelExtensionInterceptor(userSession);
    }

}
