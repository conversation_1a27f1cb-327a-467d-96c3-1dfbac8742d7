package com.gumtree.config.servlet;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.gumtree.api.OkHttpClientMetricsInterceptor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.MwebProperty;
import com.gumtree.fulladsearch.FullAdsSearchApi;
import com.gumtree.liveadsearch.client.LiveAdsSearchApi;
import com.gumtree.shared.client.CommonApiClient;
import feign.okhttp.OkHttpClient;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

import static java.util.Optional.ofNullable;

@Configuration
public class AdSearchApiConfig {

    public static final String LIVE_AD_HYSTRIX_COMMAND_NAME = "ad_search_live";
    private static final int LIVE_AD_HYSTRIX_COMMAND_CORE_POOL_SIZE = 5;
    public static final String FULL_AD_HYSTRIX_COMMAND_NAME = "ad_search_full";
    private static final int FULL_AD_HYSTRIX_COMMAND_CORE_POOL_SIZE = 5;

    @Autowired
    private MeterRegistry meterRegistry;

    @Bean
    public FullAdsSearchApi fullAdSearchApi() {
        String baseUrl = GtProps.getStr(MwebProperty.AD_SEARCH_FULL_HOST);
        int connectionTimeout = GtProps.getInt(MwebProperty.AD_SEARCH_FULL_CONNECTION_TIMEOUT);
        int readTimeout = GtProps.getInt(MwebProperty.AD_SEARCH_FULL_READ_TIMEOUT);
        Optional<String> username = ofNullable(GtProps.getStr(MwebProperty.AD_SEARCH_FULL_USERNAME)).filter(StringUtils::isNotBlank);
        Optional<String> password = ofNullable(GtProps.getStr(MwebProperty.AD_SEARCH_FULL_PASSWORD)).filter(StringUtils::isNotBlank);

        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        CommonApiClient.Builder<com.gumtree.fulladsearch.FullAdsSearchApi> builder = new CommonApiClient(baseUrl, connectionTimeout,
                readTimeout, FULL_AD_HYSTRIX_COMMAND_NAME, FULL_AD_HYSTRIX_COMMAND_CORE_POOL_SIZE, createObjectMapper(), client)
                .builder(FullAdsSearchApi.class)
                .withCircuitBreaker(false)
                .withDecode404()
                .withRetry(0, 0, 2) // 1 means no retry, 2 means 1 retry
                .withExecutionTimeout(connectionTimeout + readTimeout)
                .withHeader(HttpHeaders.USER_AGENT, "seller")
                .withDefaultGumtreeDecoder();

        username.ifPresent(s -> builder.withBasicAuthentication(s, password.orElse("")));

        return builder.buildClient();
    }

    @Bean
    public LiveAdsSearchApi liveAdSearchApi() {
        String baseUrl = GtProps.getStr(MwebProperty.AD_SEARCH_LIVE_HOST);
        int connectionTimeout = GtProps.getInt(MwebProperty.AD_SEARCH_LIVE_CONNECTION_TIMEOUT);
        int readTimeout = GtProps.getInt(MwebProperty.AD_SEARCH_LIVE_READ_TIMEOUT);
        Optional<String> username = ofNullable(GtProps.getStr(MwebProperty.AD_SEARCH_LIVE_USERNAME)).filter(StringUtils::isNotBlank);
        Optional<String> password = ofNullable(GtProps.getStr(MwebProperty.AD_SEARCH_LIVE_PASSWORD)).filter(StringUtils::isNotBlank);
        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        CommonApiClient.Builder<LiveAdsSearchApi> builder = new CommonApiClient(baseUrl, connectionTimeout, readTimeout,
                LIVE_AD_HYSTRIX_COMMAND_NAME, LIVE_AD_HYSTRIX_COMMAND_CORE_POOL_SIZE, createObjectMapper(), client)
                .builder(LiveAdsSearchApi.class)
                .withCircuitBreaker(false)
                .withDecode404()
                .withRetry(0, 0, 2)
                .withExecutionTimeout(connectionTimeout + readTimeout)
                .withHeader(HttpHeaders.USER_AGENT, "seller")
                .withDefaultGumtreeDecoder();

        username.ifPresent(s -> builder.withBasicAuthentication(s, password.orElse("")));

        return builder.buildClient();
    }

    private ObjectMapper createObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
        objectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.disable(MapperFeature.USE_ANNOTATIONS);
        objectMapper.registerModule(new Jdk8Module());
        objectMapper.registerModule(new JavaTimeModule());
        return objectMapper;
    }
}

