package com.gumtree.config.servlet;

import com.codahale.metrics.MetricRegistry;
import com.google.common.base.Function;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.gumtree.common.properties.Env;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.CommonProperty;
import com.gumtree.config.assets.Assets;
import com.gumtree.config.ftl.HtmlFreeMarkerConfigurer;
import com.gumtree.web.freemarker.ErrorHandlingFreemarkerView;
import freemarker.core.Configurable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;
import org.springframework.web.servlet.view.freemarker.FreeMarkerViewResolver;

import javax.annotation.Nullable;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Properties;

/**
 * Configuration for seller public website freemarker view resolver.
 */
@Configuration
public class SellerWebsiteFreemarkerConfig {

    private static final Logger LOG = LoggerFactory.getLogger(SellerWebsiteFreemarkerConfig.class);

    private static final List<String> TEMPLATE_PATHS = ImmutableList.of(
            "templates/shared/freemarker",
            "templates/seller/freemarker"
    );

    private String staticAssetsUrl = GtProps.getStr("gumtree.static.assets.seller.host");
    private Path templatesPath = Paths.get(GtProps.getStr("gumtree.web.templates.path"));
    private String templatesUpdateDelay = GtProps.getStr("gumtree.web.templates.update_delay");
    private String gumtreehost = GtProps.getStr("gumtree.host");
    private String mygumtreehost = GtProps.getStr("gumtree.host.mygumtree");
    private String recaptchaSiteKey = GtProps.getStr(CommonProperty.RECAPTCHA_SITE_KEY);
    private String exceptionsHandlingStrategy = GtProps.getStr("gumtree.web.templates.freemarker.exceptions_strategy");

    @Bean
    public FreeMarkerConfigurer freeMarkerConfigurer() {
        FreeMarkerConfigurer configurer = new HtmlFreeMarkerConfigurer();

        Properties freeMarkerProps = new Properties();
        freeMarkerProps.setProperty(Configurable.LOCALE_KEY, Locale.UK.toLanguageTag());
        freeMarkerProps.setProperty(Configurable.OUTPUT_ENCODING_KEY, "UTF-8");
        freeMarkerProps.setProperty(freemarker.template.Configuration.TAG_SYNTAX_KEY, "square_bracket");
        freeMarkerProps.setProperty(Configurable.TEMPLATE_EXCEPTION_HANDLER_KEY, exceptionsHandlingStrategy);
        //seconds between template updates
        freeMarkerProps.setProperty(freemarker.template.Configuration.TEMPLATE_UPDATE_DELAY_KEY, templatesUpdateDelay);

        configurer.setFreemarkerSettings(freeMarkerProps);

        List<String> paths = Lists.transform(TEMPLATE_PATHS, toFilePath());
        configurer.setTemplateLoaderPaths(paths.toArray(new String[]{}));
        return configurer;
    }

    private Function<String, String> toFilePath() {
        return new Function<String, String>() {
            @Nullable
            @Override
            public String apply(@Nullable String input) {
                String resolvedPath = "file://" + templatesPath.resolve(input).toAbsolutePath().normalize().toString();
                LOG.info("Adding freemarker templates path {} for {}. Configured base path: {}", resolvedPath, input, templatesPath);
                return resolvedPath;
            }
        };
    }

    @Bean
    public FreeMarkerViewResolver freeMarkerViewResolver(MetricRegistry metricRegistry) {
        FreeMarkerViewResolver resolver = new FreeMarkerViewResolver();
        if (Env.PROD != GtProps.getEnv()) {
            resolver.setViewClass(ErrorHandlingFreemarkerView.class);
        }
        resolver.setExposePathVariables(true);
        resolver.setExposeSpringMacroHelpers(true);
        resolver.setExposeSessionAttributes(true);
        resolver.setSuffix(".ftl");
        resolver.setRedirectHttp10Compatible(false);
        resolver.setContentType("text/html;charset=UTF-8");

        HashMap<String, Object> staticVars = new HashMap<>();
        //TODO : Find the correct way to do this
        staticVars.put("mavenBuildDate", String.valueOf(System.currentTimeMillis()));
        staticVars.put("assets", getAssets(metricRegistry));
        staticVars.put("gumtreehost", gumtreehost);
        staticVars.put("mygumtreehost", mygumtreehost);
        staticVars.put("staticAssetsUrl", staticAssetsUrl);
        staticVars.put("staticAssetsFolder", "/1/resources/assets/");
        staticVars.put("recaptchaSiteKey", recaptchaSiteKey);

        resolver.setAttributesMap(staticVars);

        return resolver;
    }

    private Assets getAssets(MetricRegistry metricRegistry) {
        return new Assets(templatesPath.resolve("seller-assets.json"), Long.valueOf(templatesUpdateDelay), metricRegistry);
    }

}
