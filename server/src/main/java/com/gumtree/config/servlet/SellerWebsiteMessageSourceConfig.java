package com.gumtree.config.servlet;

import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;

/**
 * Configuration for MVC interceptors
 */
@Configuration
public class SellerWebsiteMessageSourceConfig {

    /**
     * Get the message source.
     *
     * @return the message source
     */
    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setUseCodeAsDefaultMessage(true);
        messageSource.setBasenames(
                "messages.registration.registration_error_messages",
                "messages.postad.postad_error_messages",
                "messages.postad.postad_confirm_messages",
                "messages.login.login_error_messages",
                "messages.payment.payment_error_messages",
                "messages.user.user_error_messages",
                "messages.registration.registration_confirm_messages",
                "messages.reply.reply_error_messages",
                "messages.manageads.manage_ads_messages",
                "messages.manageaccount.manage_account_messages",
                "messages.password.password_error_messages",
                "messages.password.password_confirm_messages",
                "messages.jobs.cvupload_messages"
        );
        return messageSource;
    }
}
