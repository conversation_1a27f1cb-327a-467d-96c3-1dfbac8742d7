package com.gumtree.config;

import com.codahale.metrics.MetricRegistry;
import com.gumtree.global.SellerMetrics;
import com.netflix.hystrix.strategy.HystrixPlugins;
import com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisher;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.hystrix.HystrixMetricsBinder;
import io.micrometer.core.instrument.binder.logging.LogbackMetrics;
import io.prometheus.client.CollectorRegistry;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import io.prometheus.client.dropwizard.DropwizardExports;


import static com.gumtree.GumtreeJettyMain.METER_REGISTRY;

@Configuration
@Import(MetricsConfig.class)
public class SellerMetricsConfig implements InitializingBean {

    @Autowired
    private MetricRegistry metricRegistry;

    @Override
    public void afterPropertiesSet() {
        // init global metrics
        SellerMetrics.setMetricRegistry(metricRegistry);

        CollectorRegistry.defaultRegistry.register(new DropwizardExports(metricRegistry));
    }

    @Bean
    public HystrixMetricsPublisher hystrixMetricsPublisher(MeterRegistry meterRegistry) {

        new HystrixMetricsBinder().bindTo(meterRegistry);
        return HystrixPlugins.getInstance().getMetricsPublisher();
    }

    @Bean
    public MeterRegistry prometheusRegistry() {
        MeterRegistry meterRegistry = METER_REGISTRY;

        new LogbackMetrics().bindTo(meterRegistry);

        return meterRegistry;
    }
}
