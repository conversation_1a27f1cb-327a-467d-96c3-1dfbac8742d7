package com.gumtree.config.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.gumtree.api.OkHttpClientMetricsInterceptor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.hystrix.ApiKeyHystrixRequestVariable;
import com.gumtree.shared.client.CommonApiClient;
import com.gumtree.web.seller.service.image.error.BapiErrorDecoder;
import feign.Logger;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.codec.Encoder;
import feign.okhttp.OkHttpClient;
import io.micrometer.core.instrument.MeterRegistry;

import java.util.concurrent.TimeUnit;

public abstract class BapiApiClientFactory {

    private static final String X_AUTHENTICATION_DISABLED = "X-Authentication-Disabled";

    public static <T> T buildClient(Class<T> clientClass, int poolSize, Encoder encoder, MeterRegistry meterRegistry) {
        int connectionTimeOut = GtProps.getInt(SellerProperty.BAPI_CONNECTION_TIMEOUT);
        int readTimeOut = GtProps.getInt(SellerProperty.BAPI_SOCKET_TIMEOUT);

        ObjectMapper bapiObjectMapper = defaultObjectMapper();

        String bapiUri = getBaseUri(GtProps.getStr(SellerProperty.BAPI_HOST));

        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        return new CommonApiClient(bapiUri,
                connectionTimeOut,
                readTimeOut,
                clientClass.getName(),
                poolSize,
                bapiObjectMapper,
                client)
                .builder(clientClass)
                .withEncoder(encoder)
                .withRequestInterceptor(new BapiApiClientFactory.AuthForwardFeignRequestInterceptor())
                .withHeader(X_AUTHENTICATION_DISABLED, GtProps.getStr(SellerProperty.BAPI_SECRET_HEADER))
                .withErrorDecoder(new BapiErrorDecoder(bapiObjectMapper))
                .withCircuitBreaker(false)
                .withExecutionTimeout(connectionTimeOut + readTimeOut)
                .withRetry(100, TimeUnit.SECONDS.toMillis(1L), GtProps.getInt(SellerProperty.BAPI_MAX_ATTEMPTS))
                .buildClient(Logger.Level.FULL);
    }

    private static ObjectMapper defaultObjectMapper() {
        ObjectMapper bapiObjectMapper = new ObjectMapper();
        bapiObjectMapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
        bapiObjectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        bapiObjectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        bapiObjectMapper.disable(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE);
        bapiObjectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        bapiObjectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        bapiObjectMapper.disable(MapperFeature.USE_ANNOTATIONS);
        bapiObjectMapper.registerModule(new JavaTimeModule());
        bapiObjectMapper.registerModule(new Jdk8Module());
        bapiObjectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return bapiObjectMapper;
    }

    /**
     * historically bapi host config had /api appended to end, with bapi contract, it would break, i.e. /api/api
     *
     * @param bapiHost
     * @return
     */
    private static String getBaseUri(String bapiHost) {
        if(bapiHost.endsWith("/api")) {
            return bapiHost.substring(0, bapiHost.indexOf("/api"));
        } else {
            return bapiHost;
        }
    }

    /**
     * https://medium.com/@saurav24081996/java-hystrix-and-threadlocals-95ea9e194e83
     * https://stackoverflow.com/questions/65019801/how-to-get-bearer-token-from-header-of-a-request-in-java-spring-boot
     */
    public static class AuthForwardFeignRequestInterceptor implements RequestInterceptor {

        private static final String API_KEY = "apiKey";

        @Override
        public void apply(RequestTemplate template) {
            template.query(API_KEY, ApiKeyHystrixRequestVariable.get());
        }
    }
}
