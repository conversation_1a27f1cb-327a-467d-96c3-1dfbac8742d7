package com.gumtree.config.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.gumtree.api.OkHttpClientMetricsInterceptor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.hystrix.ApiKeyHystrixRequestVariable;
import com.gumtree.mediaprocessor.api.ImageApi;
import com.gumtree.shared.client.CommonApiClient;
import com.gumtree.web.seller.service.image.error.MediaProcessorErrorDecoder;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.codec.Encoder;
import feign.okhttp.OkHttpClient;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class MediaProcessorApiConfig {

    private static final int IMAGE_UPLOAD_POOL_SIZE = 10;

    @Autowired
    private MeterRegistry meterRegistry;

    public MediaProcessorApiConfig() {
    }

    public MediaProcessorApiConfig(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    @Bean(name = "mediaProcessorImageApi")
    public ImageApi getMediaProcessorImageApi(Encoder feignEncoder) {

        int connectionTimeOut = GtProps.getInt(SellerProperty.MEDIA_PROCESSOR_CONNECTION_TIMEOUT);
        int socketTimeOut = GtProps.getInt(SellerProperty.MEDIA_PROCESSOR_SOCKET_TIMEOUT);
        int maxAttempts = GtProps.getInt(SellerProperty.MEDIA_PROCESSOR_MAX_ATTEMPTS);

        ObjectMapper mediaProcessorObjectMapper = mediaProcessorObjectMapper();

        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        return new CommonApiClient(GtProps.getStr(SellerProperty.MEDIA_PROCESSOR_HOST),
                connectionTimeOut,
                socketTimeOut,
                ImageApi.class.getName(),
                IMAGE_UPLOAD_POOL_SIZE,
                mediaProcessorObjectMapper, client)
                .builder(ImageApi.class)
                .withEncoder(feignEncoder)
                .withRequestInterceptor(new AuthForwardFeignRequestInterceptor())
                .withErrorDecoder(new MediaProcessorErrorDecoder(mediaProcessorObjectMapper))
                .withCircuitBreaker(false)
                .withExecutionTimeout(connectionTimeOut + socketTimeOut)
                .withRetry(1000, TimeUnit.SECONDS.toMillis(10L), maxAttempts)
                .buildClient();
    }


    private static ObjectMapper mediaProcessorObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(new Jdk8Module());
        objectMapper.disable(MapperFeature.USE_ANNOTATIONS);
        return objectMapper;
    }

    /**
     * https://medium.com/@saurav24081996/java-hystrix-and-threadlocals-95ea9e194e83
     * https://stackoverflow.com/questions/65019801/how-to-get-bearer-token-from-header-of-a-request-in-java-spring-boot
     */
    public static class AuthForwardFeignRequestInterceptor implements RequestInterceptor {

        private static final String API_KEY = "apiKey";

        @Override
        public void apply(RequestTemplate template) {
            template.header(API_KEY, ApiKeyHystrixRequestVariable.get());
        }

    }
}
