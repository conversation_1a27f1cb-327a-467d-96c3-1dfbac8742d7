package com.gumtree.config.api;

import com.gumtree.api.CommonApiClient;
import com.gumtree.api.OkHttpClientMetricsInterceptor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.api.PriceGuidanceApi;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.PriceGuidanceResponse;
import feign.okhttp.OkHttpClient;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import rx.Single;

@Configuration
public class MotorsPriceGuidanceServiceConfig {

    @Autowired
    private MeterRegistry meterRegistry;

    @Bean
    public PriceGuidanceApi priceGuidanceApi(PriceGuidanceApiProperties priceGuidanceApiProperties) {

        OkHttpClient client = new OkHttpClient(new okhttp3.OkHttpClient.Builder()
                .addInterceptor(new OkHttpClientMetricsInterceptor(meterRegistry))
                .build());

        return new CommonApiClient(
                priceGuidanceApiProperties.baseUrl,
                "seller",
                priceGuidanceApiProperties.connectionTimeout,
                priceGuidanceApiProperties.readTimeout,
                PriceGuidanceApi.class.getName(), 3, client)
                .builder(PriceGuidanceApi.class)
                .withFallback((vrn, size, categoryId) -> Single.just(new PriceGuidanceResponse()))
                .withExecutionTimeout(priceGuidanceApiProperties.connectionTimeout + priceGuidanceApiProperties.readTimeout)
                .buildClient();
    }

    @Bean
    public PriceGuidanceApiProperties priceGuidanceApiProperties() {
        return new PriceGuidanceApiProperties(
                GtProps.getStr(SellerProperty.MOTORS_PRICE_GUIDANCE_BASE_URL),
                GtProps.getInt(SellerProperty.MOTORS_PRICE_GUIDANCE_CONNECTION_TIMEOUT),
                GtProps.getInt(SellerProperty.MOTORS_PRICE_GUIDANCE_READ_TIMEOUT)
        );
    }

    public static class PriceGuidanceApiProperties {
        private final int connectionTimeout;
        private final int readTimeout;
        private final String baseUrl;


        public PriceGuidanceApiProperties(String baseUrl, int connectionTimeout, int readTimeout) {
            this.connectionTimeout = connectionTimeout;
            this.readTimeout = readTimeout;
            this.baseUrl = baseUrl;
        }
    }

}
