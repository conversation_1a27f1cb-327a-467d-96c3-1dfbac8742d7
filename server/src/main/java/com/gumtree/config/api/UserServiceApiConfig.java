package com.gumtree.config.api;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.seller.infrastructure.driven.user.ApiClient;
import com.gumtree.seller.infrastructure.driven.user.UserServiceApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UserServiceApiConfig {

    public static final String COMMAND_GROUP_NAME = "UAPI";
    public static final String CLIENT_ID = "seller";

    @Bean
    public ApiClient defaultApiClient() {
        return new ApiClient(GtProps.getStr(SellerProperty.BAPI_HOST),
                CLIENT_ID,
                GtProps.getInt(SellerProperty.BAPI_CONNECTION_TIMEOUT),
                GtProps.getInt(SellerProperty.BAPI_SOCKET_TIMEOUT),
                COMMAND_GROUP_NAME,
                5);
    }


    @Bean
    public UserServiceApi userServiceApi(ApiClient apiClient) {
        return apiClient.buildClient(UserServiceApi.class);
    }
}
