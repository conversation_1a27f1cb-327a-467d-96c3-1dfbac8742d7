package com.gumtree.config.api;

import com.google.common.collect.Lists;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.draftsapi.client.config.DraftsApiClientFactory;
import com.gumtree.draftsapi.spec.DraftsApiClient;
import com.gumtree.draftsapi.stub.DraftsApiStub;
import com.gumtree.draftsapi.stub.config.DraftsApiStubServerFactory;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.net.URISyntaxException;

@Configuration
public class DraftsApiConfig {

    @Bean
    public DraftsApiClient draftsApiClient() throws URISyntaxException {
        DraftsApiClientFactory factory = new DraftsApiClientFactory();
        factory.setHost(GtProps.getStr(SellerProperty.DRAFT_ADS_API_HOST));
        factory.setPort(GtProps.getInt(SellerProperty.DRAFT_ADS_API_PORT));
        factory.setVerifySslCertificates(GtProps.getBool(SellerProperty.DRAFT_ADS_API_VERIFY_SSL_CERTS));
        factory.setConnectionTimeout(GtProps.getInt(SellerProperty.DRAFT_ADS_API_CONNECTION_TIMEOUT));
        factory.setSocketTimeout(GtProps.getInt(SellerProperty.DRAFT_ADS_API_SOCKET_TIMEOUT));
        factory.setEnabled(GtProps.getBool(SellerProperty.DRAFT_ADS_API_ENABLED));
        factory.setSupportedClasses(Lists.<Class<?>>newArrayList(PostAdDetail.class));

        return factory.create();
    }

    @Bean
    @Lazy(false)
    public DraftsApiStub.Server draftsApiStub() throws Exception {
        boolean stubEnabled;
        try {
            stubEnabled = GtProps.getBool(SellerProperty.DRAFT_ADS_API_STUB_ENABLED);
        } catch (IllegalStateException ex) {
            // I don't expect this property to be set in production so I have to handle that case
            stubEnabled = false;
        }
        DraftsApiStubServerFactory factory = new DraftsApiStubServerFactory();
        factory.setEnabled(stubEnabled);
        return factory.createAndStartIfEnabled();
    }
}
