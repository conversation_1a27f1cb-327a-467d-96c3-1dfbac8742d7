package com.gumtree.config.api;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.seller.infrastructure.driven.account.AccountServiceApi;
import com.gumtree.seller.infrastructure.driven.account.ApiClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AccountServiceApiConfig {

    public static final String COMMAND_GROUP_NAME = "AAPI";
    public static final String CLIENT_ID = "seller";

    @Bean
    public ApiClient defaultAccountApiClient() {
        return new ApiClient(GtProps.getStr(SellerProperty.BAPI_HOST),
                CLIENT_ID,
                GtProps.getInt(SellerProperty.BAPI_CONNECTION_TIMEOUT),
                GtProps.getInt(SellerProperty.BAPI_SOCKET_TIMEOUT),
                COMMAND_GROUP_NAME,
                5);
    }


    @Bean
    public AccountServiceApi accountServiceApi(ApiClient apiClient) {
        return apiClient.buildClient(AccountServiceApi.class);
    }
}
