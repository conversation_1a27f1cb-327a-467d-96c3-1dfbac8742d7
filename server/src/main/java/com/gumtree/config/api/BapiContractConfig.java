package com.gumtree.config.api;

import com.gumtree.bapi.ImageApi;
import com.gumtree.bapi.UserApi;
import com.gumtree.bapi.SkillControllerApi;
import feign.jackson.JacksonEncoder;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BapiContractConfig {

    private static final int IMAGE_UPLOAD_POOL_SIZE = 3;
    private static final int POOL_SIZE = 10;

    @Autowired
    private MeterRegistry meterRegistry;

    public BapiContractConfig() {
    }

    public BapiContractConfig(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    @Bean(name = "bapiContractImageApi")
    public ImageApi getBapiContractImageApi() {
        return BapiApiClientFactory.buildClient(ImageApi.class, IMAGE_UPLOAD_POOL_SIZE, new JacksonEncoder(), meterRegistry);
    }

    @Bean(name = "bapiContractUserApi")
    public UserApi getUserApi() {
        return BapiApiClientFactory.buildClient(UserApi.class, POOL_SIZE, new JacksonEncoder(), meterRegistry);
    }

    @Bean(name = "bapiContractSkillControllerApi")
    public SkillControllerApi getSkillControllerApi() {
        return BapiApiClientFactory.buildClient(SkillControllerApi.class, POOL_SIZE, new JacksonEncoder(), meterRegistry);
    }
}
