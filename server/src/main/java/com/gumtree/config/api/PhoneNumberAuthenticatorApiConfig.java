package com.gumtree.config.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.config.exception.PhoneNumberAuthenticatorClientException;
import com.gumtree.config.exception.PhoneNumberAuthenticatorServerException;
import com.gumtree.security.phone.number.authenticator.api.PhoneNumberAuthenticatorApi;
import com.gumtree.security.phone.number.authenticator.model.ApiError;
import com.gumtree.shared.client.DefaultObjectMapper;
import feign.Response;
import feign.codec.ErrorDecoder;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

import static com.gumtree.shared.client.DefaultObjectMapper.defaultObjectMapper;

@Configuration
public class PhoneNumberAuthenticatorApiConfig {
    private static final Logger LOG = LoggerFactory.getLogger(PhoneNumberAuthenticatorApiConfig.class);

    public static final String GROUP_COMMAND = "phone-number-authenticator-api";
    public static final int POOL_SIZE = 10;
    @Bean
    public PhoneNumberAuthenticatorApi phoneVerifyApi() {

        String baseUrl = GtProps.getStr(SellerProperty.PHOME_NUMBER_AUTHENTICATOR_BASE_URL);
        int connectionTimeout = GtProps.getInt(SellerProperty.PHOME_NUMBER_AUTHENTICATOR_CONNECTION_TIMEOUT);
        int socketTimeout = GtProps.getInt(SellerProperty.PHOME_NUMBER_AUTHENTICATOR_READ_TIMEOUT);
        return new com.gumtree.shared.client.CommonApiClient(
                baseUrl,
                connectionTimeout,
                socketTimeout,
                GROUP_COMMAND,
                POOL_SIZE,
                defaultObjectMapper())
                .builder(PhoneNumberAuthenticatorApi.class)
                .withEncoder(new JacksonEncoder())
                .withDecoder(new JacksonDecoder(DefaultObjectMapper.defaultObjectMapper()))
                .withErrorDecoder(new PhoneNumberAuthenticatorApiDecoder(defaultObjectMapper()))
                .withExecutionTimeout(connectionTimeout + socketTimeout)
                .buildClient();
    }

    public static class PhoneNumberAuthenticatorApiDecoder implements ErrorDecoder {

        private final ObjectMapper objectMapper;

        public PhoneNumberAuthenticatorApiDecoder(ObjectMapper objectMapper) {
            this.objectMapper = objectMapper;
        }

        @Override
        public Exception decode(String methodKey, Response response) {

            Optional<ApiError> error = parseErrorResponse(response);

            int status = response.status();
            switch (status) {
                case 400:
                case 401:
                case 404:
                    return error.map(PhoneNumberAuthenticatorClientException::new)
                            .orElse(new PhoneNumberAuthenticatorClientException(createErrorMessage(response)));
                case 500:
                default:
                    return error.map(PhoneNumberAuthenticatorServerException::new)
                            .orElse(new PhoneNumberAuthenticatorServerException(createErrorMessage(response)));
            }
        }

        private String createErrorMessage(Response resp) {
            return String.format("Error response received with status HTTP %s", resp.status());
        }

        private Optional<ApiError> parseErrorResponse(Response resp) {
            try {
                ApiError value = objectMapper.readValue(resp.body().asInputStream(), ApiError.class);
                return Optional.of(value);
            } catch (Exception ex) {
                LOG.error("Unable to parse response body to JSON {}, {}", ex.getMessage(), resp);
                return Optional.empty();
            }
        }
    }

}
