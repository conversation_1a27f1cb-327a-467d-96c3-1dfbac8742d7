package com.gumtree.config.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.joda.JodaModule;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.seller.infrastructure.driven.payment.braintree.BraintreeApi;
import com.gumtree.shared.client.CommonApiClient;
import com.gumtree.web.seller.healthcheck.PaymentApiHealthcheck;
import com.gumtree.web.seller.page.payment.api.PaymentApiClient;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.TimeZone;

import static java.lang.String.format;

@Configuration
public class PaymentApiConfig {

    private final String host = GtProps.getStr(SellerProperty.PAYMENT_API_HOST);
    private final Integer port = GtProps.getInt(SellerProperty.PAYMENT_API_PORT);
    private final Integer connectionTimeout = GtProps.getInt(SellerProperty.PAYMENT_API_CONNECTION_TIMEOUT);
    private final Integer readTimeout = GtProps.getInt(SellerProperty.PAYMENT_API_READ_TIMEOUT);
    public static final int POOL_SIZE = 10;
    @Bean
    public PaymentApiClient paymentApiClient(final BraintreeApi braintreeApi) {
        return new PaymentApiClient(braintreeApi);
    }

    @Bean
    public BraintreeApi braintreeApi() {
        ObjectMapper objectMapper = configureObjectMapper();

        return new CommonApiClient(
                format("http://%s:%d", host, port),
                connectionTimeout,
                readTimeout,
                BraintreeApi.class.getName(), POOL_SIZE,
                objectMapper)
                .builder(BraintreeApi.class)
                .withDecode404()
                .withEncoder(new JacksonEncoder(objectMapper))
                .withDecoder(new JacksonDecoder(objectMapper))
                .withExecutionTimeout(connectionTimeout + readTimeout)
                .withCircuitBreaker(false)
                .buildClient();
    }

    private ObjectMapper configureObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
        objectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        objectMapper.registerModule(new JodaModule());
        objectMapper.setDateFormat(buildDefaultDateFormat());
        return objectMapper;
    }

    private static DateFormat buildDefaultDateFormat() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        return dateFormat;
    }
    @Bean
    public PaymentApiHealthcheck paymentApiHealthcheck() {
        return new PaymentApiHealthcheck(host, port, connectionTimeout, readTimeout);
    }
}
