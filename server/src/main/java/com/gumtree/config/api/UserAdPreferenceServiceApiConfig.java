package com.gumtree.config.api;

import com.gumtree.seller.infrastructure.driven.user.ApiClient;
import com.gumtree.seller.infrastructure.driven.user.UserAdPreferenceServiceApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UserAdPreferenceServiceApiConfig {

    @Bean
    public UserAdPreferenceServiceApi userAdPreferenceServiceApi(ApiClient apiClient) {
        return apiClient.buildClient(UserAdPreferenceServiceApi.class);
    }

}
