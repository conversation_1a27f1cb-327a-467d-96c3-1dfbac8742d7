package com.gumtree.config.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.gumtree.category.predictor.client.CategoryPredictApi;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.MwebProperty;
import com.gumtree.shared.client.CommonApiClient;
import org.apache.http.HttpHeaders;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for seller public website freemarker view resolver.
 */
@Configuration
public class CategoryPredictApiConfig {

  private static final String CATEGORY_PREDICT_HYSTRIX_COMMAND_NAME = "category-predictor";
  private static final int CATEGORY_PREDICT_HYSTRIX_COMMAND_CORE_POOL_SIZE = 10;
  public static final String CLIENT_ID = "seller";

  /**
   * categoryPredictApi.
   */
  @Bean
  public CategoryPredictApi categoryPredictApi() {
    String baseUrl = GtProps.getStr(MwebProperty.CATEGORY_PREDICTOR_HOST);
    String port = GtProps.getStr(MwebProperty.CATEGORY_PREDICTOR_PORT);
    int connectionTimeout = GtProps.getInt(MwebProperty.CATEGORY_PREDICTOR_CONNECTION_TIMEOUT);
    int readTimeout = GtProps.getInt(MwebProperty.CATEGORY_PREDICTOR_READ_TIMEOUT);
    int maxAttempts = GtProps.getInt(MwebProperty.CATEGORY_PREDICTOR_RETRIES);
    String basePath = baseUrl + ":" + port;

    CommonApiClient.Builder<CategoryPredictApi> builder = new CommonApiClient(
            basePath,
            connectionTimeout,
            readTimeout,
            CATEGORY_PREDICT_HYSTRIX_COMMAND_NAME,
            CATEGORY_PREDICT_HYSTRIX_COMMAND_CORE_POOL_SIZE,
            createObjectMapper()
    )
            .builder(CategoryPredictApi.class)
            .withCircuitBreaker(false)
            .withDecode404()
            .withRetry(0, 0, maxAttempts)
            .withExecutionTimeout(connectionTimeout + readTimeout)
            .withHeader(HttpHeaders.USER_AGENT, CLIENT_ID)
            .withDefaultGumtreeDecoder();

    return builder.buildClient();
  }

  private ObjectMapper createObjectMapper() {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
    objectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
    objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    objectMapper.disable(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE);
    objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
    objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    objectMapper.disable(MapperFeature.USE_ANNOTATIONS);
    objectMapper.registerModule(new Jdk8Module());
    objectMapper.registerModule(new JavaTimeModule());

    return objectMapper;
  }
}
