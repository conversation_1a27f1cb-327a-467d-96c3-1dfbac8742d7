package com.gumtree.config.session;

import com.codahale.metrics.MetricRegistry;
import com.gumtree.api.UserResolver;
import com.gumtree.gas.OIDCUserStore;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.DefaultUserSessionService;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.openidconnect.service.OIDCRedisBackedUserStore;
import com.gumtree.web.seller.storage.DefaultSellerSessionDataService;
import com.gumtree.web.seller.storage.SellerSessionDataService;
import com.gumtree.web.storage.ratelimit.DefaultRateLimiter;
import com.gumtree.web.storage.ratelimit.RateLimiter;
import com.gumtree.web.storage.ratelimit.RateLimiterPersister;
import com.gumtree.web.storage.strategy.SessionPersistenceStrategy;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.web.context.WebApplicationContext;

import javax.servlet.http.HttpServletRequest;

@Configuration
@Import({SessionPersistenceConfig.class, StubSessionPersistenceConfig.class})
public class SellerSessionStorageConfig {

    @Value("${gumtree.redis.data.ttlseconds}")
    private Integer timeToLiveSeconds;

    @Value("${gumtree.redis.complete.checkout.ttlseconds}")
    private Integer completeCheckoutTimeToLiveSeconds;

    @Value("${gumtree.session.map.expiry.checkms}")
    private Long mapClearExpiredCheckMs;

    @Value("${gumtree.postad.rate.per.interval}")
    private int postAdRateLimit;

    @Value("${gumtree.postad.rate.interval.seconds}")
    private int postAdRateInterval;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private MetricRegistry metricRegistry;

    @Autowired
    private RateLimiterPersister rateLimiterPersister;

    @Autowired
    @Qualifier("syiPersistenceStrategy")
    private SessionPersistenceStrategy syiPersistenceStrategy;

    @Autowired
    @Qualifier("syiPersistenceSimpleStrategy")
    private SessionPersistenceStrategy syiPersistenceSimpleStrategy;

    @Autowired
    @Qualifier("checkoutPersistenceStrategy")
    private SessionPersistenceStrategy checkoutPersistenceStrategy;


    @Autowired
    @Qualifier("checkoutPersistenceSimpleStrategy")
    private SessionPersistenceStrategy checkoutPersistenceSimpleStrategy;

    @Autowired
    @Qualifier("userPersistenceStrategy")
    private SessionPersistenceStrategy userPersistenceStrategy;

    @Autowired
    @Qualifier("madFilterPersistenceStrategy")
    private SessionPersistenceStrategy madFilterPersistenceStrategy;

    @Autowired
    @Qualifier("openIDPersistenceStrategy")
    private SessionPersistenceStrategy openIDPersistenceStrategy;

    @Autowired
    private UserSessionService userSessionService;

    @Bean
    public SellerSessionDataService sessionDataService() {
        return new DefaultSellerSessionDataService(mapper, syiPersistenceStrategy, syiPersistenceSimpleStrategy, checkoutPersistenceStrategy,checkoutPersistenceSimpleStrategy, userPersistenceStrategy,
                madFilterPersistenceStrategy, timeToLiveSeconds, completeCheckoutTimeToLiveSeconds, metricRegistry, userSessionService);
    }

    @Bean
    @Autowired
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.INTERFACES)
    public DefaultUserSessionService cookieIdProvider(HttpServletRequest request, UserResolver userResolver,
                                               CookieResolver cookieResolver) {
        return new DefaultUserSessionService(cookieResolver, request, userResolver);
    }

    @Bean(name = "postAdRateLimiter")
    public RateLimiter postAdRateLimiter() {
        return new DefaultRateLimiter(rateLimiterPersister, postAdRateLimit, postAdRateInterval);
    }

    @Bean
    public OIDCUserStore getOIDCUserStore() throws Exception {
        return new OIDCRedisBackedUserStore(openIDPersistenceStrategy, mapper);
    }

}
