package com.gumtree.config;

import com.gumtree.draftsapi.spec.DraftsApiClient;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.storage.ApiDraftAdvertService;
import com.gumtree.web.seller.storage.DraftAdvertService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.web.context.WebApplicationContext;


@Configuration
public class SellerDraftAdConfig {

    @Bean
    @Autowired
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.INTERFACES)
    public DraftAdvertService draftAdvertService(DraftsApiClient draftsApiClient,
                                                 UserSessionService userSessionService) {
        return new ApiDraftAdvertService(draftsApiClient, userSessionService);
    }
}
