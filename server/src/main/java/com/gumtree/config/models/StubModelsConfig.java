package com.gumtree.config.models;

import com.gumtree.api.Locations;
import com.gumtree.api.Outcodes;
import com.gumtree.api.config.StubCategoryModelConfig;
import com.gumtree.common.model.location.LocationModelManager;
import com.gumtree.common.model.location.impl.StubLocationModelManager;
import com.gumtree.util.stub.api.StubLocationApi;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.ClassPathResource;

/**
 * Configuration class for stub models.
 */
@Configuration
@Profile("stub-models")
@Import(StubCategoryModelConfig.class)
public class StubModelsConfig {

    /**
     * Object mapper the configuration will use.
     */
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Get the location model manager.
     * @return the location model manager
     * @throws Exception if something goes wrong
     */
    @Bean
    public LocationModelManager locationModelManager() throws Exception {

        Locations locations = objectMapper.readValue(new ClassPathResource(
                "stub/location/locations.json").getInputStream(),
                Locations.class);

        Outcodes outcodes = objectMapper.readValue(new ClassPathResource(
                "stub/location/outcodes.json").getInputStream(),
                Outcodes.class);

        return new StubLocationModelManager(new StubLocationApi(locations, outcodes));
    }
}
