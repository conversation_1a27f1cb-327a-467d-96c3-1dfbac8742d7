package com.gumtree.config.models;

import com.gumtree.common.model.category.impl.homepage.mapping.LocationHomepageCategoryConfigurationModel;
import com.gumtree.config.profiles.CommonConfigProfiles;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.ClassPathResource;

/**
 * Configuration for production models.
 */
@Configuration
@ComponentScan(basePackages = {
        "com.gumtree.common.model.category",
        "com.gumtree.common.model.location",
        "com.gumtree.common.model.attribute" })
@Profile(CommonConfigProfiles.PRODUCTION_MODELS)
public class ProductionModelsConfig {

    @Value("${gumtree.homepage.category.version}")
    private String homepageCategoryConfigVersion;

    @Autowired
    private ObjectMapper mapper;

    /**
     * Location homepage category configuration bean.
     * @return the bean
     * @throws Exception if something went wrong
     */
    @Bean(name = "homepageCategoryConfiguration")
    public LocationHomepageCategoryConfigurationModel locationHomepageCategoryConfigurationModel() throws Exception {
        return mapper.readValue(
                new ClassPathResource("homepage-category-configuration-" + homepageCategoryConfigVersion + ".json")
                        .getInputStream(),
                LocationHomepageCategoryConfigurationModel.class);
    }
}
