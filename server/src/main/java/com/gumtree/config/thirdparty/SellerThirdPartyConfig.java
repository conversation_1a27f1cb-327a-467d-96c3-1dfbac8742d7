package com.gumtree.config.thirdparty;

import com.gumtree.config.CommonVWOConfig;
import com.gumtree.web.common.page.model.thirdparty.vwo.DefaultVWOConfigurer;
import com.gumtree.web.common.page.model.thirdparty.vwo.VWOConfigurer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Import;

import java.util.HashMap;
import java.util.Map;

/**
 * This config is used to allow application specific beans to be defined for Third Party content.
 * (i.e Visual Web Optimiser)This particular config is seller application specific.
 * See also PublicThirdPartyConfig.java in public
 */
@Configuration
@ComponentScan("com.gumtree.web.common.page.model.thirdparty")
@Import({CommonVWOConfig.class })
public class SellerThirdPartyConfig {

    @Autowired
    private VWOConfigurer defaultVWOConfigurer;

    /**
     * Returns bean of a Map whose value is an implementation of a {@link VWOConfigurer}, keyed by the class name
     * @return Map of {@link VWOConfigurer}s
     * @throws Exception - Thrown if there was an error creating this bean.
     */
    @Bean
    @DependsOn("genericThirdPartyViewModelAppenderRegistry")
    public Map<String, VWOConfigurer> predefinedVWOConfigurers() throws Exception {
        Map<String, VWOConfigurer> predefinedConfigurers = new HashMap<String, VWOConfigurer>();
        predefinedConfigurers.put(DefaultVWOConfigurer.class.getName(), defaultVWOConfigurer);
        return predefinedConfigurers;
    }
}
