package com.gumtree.config;

import com.gumtree.common.properties.GtProps;

public enum UserApiPropNames implements GtProps.GtProperty {
    BASE_URL("gumtree.user.baseurl", ""),
    CONNECTION_TIMEOUT("gumtree.user.connection_timeout", ""),
    SOCKET_TIMEOUT("gumtree.user.socket_timeout", "");

    private final String name;
    private final String description;

    UserApiPropNames(String name, String description) {
        this.name = name;
        this.description = description;
    }

    @Override
    public String getPropertyName() {
        return name;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
