package com.gumtree.web.seller.service.pricing

import com.gumtree.api.category.domain.CategoryConstants
import com.gumtree.api.config.StubCategoryModelConfig
import CategoryConstants.Attribute.{SELLER_TYPE, VEHICLE_COLOUR, VEHICLE_ENGINE_SIZE, VEH<PERSON>LE_MAKE}
import com.gumtree.web.seller.page.postad.model.{PostAdFormBean, AdvertEditor}
import org.mockito.Mockito
import org.mockito.Mockito.when
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FlatSpec, Matchers}

import scala.collection.JavaConversions._

class PricingContextImplTest extends FlatSpec with Matchers with MockitoSugar {

  val categoryModel = new StubCategoryModelConfig().categoryModel()

  it should "find price sensitive attributes" in {
    // given
    val editor: AdvertEditor = createEditor(Map(SELLER_TYPE.getName -> "trade", VEHICLE_COLOUR.getName -> "red"))
    val context = new PricingContextImpl(editor, categoryModel)

    // then
    context.getPriceSensitiveAttributes.toMap should be(Map(SELLER_TYPE.getName -> "trade"))
  }

  it should "find not find price in-sensitive attributes" in {
    // given
    val editor: AdvertEditor = createEditor(Map(VEHICLE_MAKE.getName -> "trade", VEHICLE_ENGINE_SIZE.getName -> "red"))
    val context = new PricingContextImpl(editor, categoryModel)

    // then
    context.getPriceSensitiveAttributes.toMap should be(Map.empty)
  }

  def createEditor(attrs: Map[String, String]): AdvertEditor = {
    val editor = Mockito.mock(classOf[AdvertEditor], Mockito.RETURNS_DEEP_STUBS)
    val postAdFormBean: PostAdFormBean = new PostAdFormBean()
    postAdFormBean.setAttributes(attrs)
    when(editor.getPostAdFormBean).thenReturn(postAdFormBean)
    when(editor.getCategoryId).thenReturn(CategoryConstants.CARS_ID)
    editor
  }

}
