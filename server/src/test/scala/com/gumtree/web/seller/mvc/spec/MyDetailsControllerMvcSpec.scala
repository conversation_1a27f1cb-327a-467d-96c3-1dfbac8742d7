package com.gumtree.web.seller.mvc.spec

import java.util

import com.gumtree.api.User
import com.gumtree.web.seller.mvc.SellerMvcSpec
import com.gumtree.web.seller.page.Paths
import org.scalatest.DoNotDiscover
import org.springframework.test.web.servlet.{MvcResult, ResultActions}
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders._
import org.springframework.test.web.servlet.result.MockMvcResultMatchers._


@DoNotDiscover
class MyDetailsControllerMvcSpec extends SellerMvcSpec {

  scenario("show my details page if logged in") {
    // given
    val user: User = loginAsDefaultUser

    // when
    val request: MockHttpServletRequestBuilder = get("/manage-account/")
    val result: ResultActions = mockMvc.perform(request)

    // then
    result.andExpect(status.isOk)

    // and
    val model: util.Map[String, AnyRef] = result.andReturn().getModelAndView.getModel
    model.get("userEmail") should be(user.getEmail)
    model.get("secureToken") should not be(null)

    // and
    val response: String = result.andReturn().getResponse.getContentAsString
    response should include(user.getEmail)
    response should include("Update contact details")
    response should include("Change password")
    response should include("Manage contact email")
    response should include("Marketing preferences")
  }

  scenario("not logged in user is redirected to login page if he tries to change a password") {
    val form = Map(
      "currentPassword" -> "password",
      "password" -> "new12345",
      "confirmedPassword" -> "new12345",
      "secureToken" -> "")

    // when
    val result = mvc.perform(POST(Paths.CHANGE_PASSWORD, form))

    // then
    result.andExpect(status.isSeeOther)

    // and
    val mvcResult: MvcResult = result.andReturn()
    mvcResult.getResponse.getRedirectedUrl should endWith("/login")
  }

  scenario("change password is rejected if current password is not correct") {
    // given an user
    val user = createUser()

    // and the user is on manage ads page (we need it to get secure token for our next POST request)
    val secureToken = getSecureToken(user)

    // when
    val form = Map(
      "currentPassword" -> "password",
      "password" -> "new",
      "confirmedPassword" -> "new")

    // /manage-account/change-password
    val result = mvc.perform(ajaxPOST("/manage-account/change-password", form)
      .cookie(createRememberMeCookie(user.getEmail))
      .param("secureToken", secureToken))


    // then
    result.andExpect(status.isSeeOther)

    // and
    val mvcResult: MvcResult = result.andReturn()
    mvcResult.getResponse.getRedirectedUrl should endWith("/manage-account/")
  }

  scenario("logged in user can't change password without supplying valid secureToken") {
    // given an user
    val user = createUser()

    // when
    val form = Map(
      "currentPassword" -> "password",
      "password" -> "new",
      "confirmedPassword" -> "new")

    // /manage-account/change-password
    val result = mvc.perform(ajaxPOST("/manage-account/change-password", form)
      .cookie(createRememberMeCookie(user.getEmail))
      .param("secureToken", "invalid"))


    // then
    result.andExpect(status.isSeeOther)

    // and
    val mvcResult: MvcResult = result.andReturn()
    mvcResult.getResponse.getRedirectedUrl should endWith("/page-expired")
  }

  private def getSecureToken(user: User): String = {
    loginAs(user)
    val resultActions: ResultActions = mockMvc.perform(get("/manage-account/"))
    resultActions.andExpect(status().isOk)
    resultActions.andReturn().getModelAndView.getModel.get("secureToken").asInstanceOf[String]
  }
}
