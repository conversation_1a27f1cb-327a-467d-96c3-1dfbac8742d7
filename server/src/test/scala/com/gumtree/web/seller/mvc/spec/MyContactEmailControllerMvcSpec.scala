package com.gumtree.web.seller.mvc.spec

import java.util

import com.gumtree.api.client.stub.StubUserApi
import com.gumtree.api.{ApiContactEmail, EmailStatus, User}
import com.gumtree.common.util.url.UrlUtils
import com.gumtree.web.seller.mvc.SellerMvcSpec
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.result.MockMvcResultMatchers._

import scala.collection.JavaConversions._

class MyContactEmailControllerMvcSpec extends SellerMvcSpec {
  val ENDPOINT = "/manage-account/contact-email/"

  scenario("should add contact email") {
    // given
    val user: User = loginAsDefaultUser

    // when the user requests to add a new contact email
    val request: MockHttpServletRequestBuilder = ajaxPUT(ENDPOINT,
      "email=" + UrlUtils.enc("<EMAIL>") +
        "&secureToken=" + StubUserApi.VALID_EMAIL_VERIFICATION_KEY)
    val result: ResultActions = mockMvc.perform(request)

    // then
    result.andExpect(status.isOk)
    result.andExpect(content().contentType("application/json;charset=UTF-8"))
    result.andExpect(content().string("{\"successNotice\":\"Success\"}"))

    // and
    val contactEmails: util.List[ApiContactEmail] = bushfireApi.userApi().getContactEmails(user.getId)
    contactEmails.map(_.getEmail).toSet should be(Set("<EMAIL>"))
  }

  ignore("not logged in user should not be able to add new contact email") {
    // when the user requests to add a new contact email
    val request: MockHttpServletRequestBuilder = ajaxPUT(ENDPOINT, "email=" +
      UrlUtils.enc("<EMAIL>") +
      "&secureToken=" + StubUserApi.VALID_EMAIL_VERIFICATION_KEY)
    val result: ResultActions = mockMvc.perform(request)

    // then
    val modelAndView = result.andReturn().getModelAndView
    modelAndView.getViewName should be("redirect:http://localhost/login")
  }

  scenario("should delete contact email") {
    // given a logged-in user
    val user: User = loginAsDefaultUser
    val contactEmail: String = "<EMAIL>"

    // and with a minimum of one contact email
    mockMvc.perform(ajaxPUT(ENDPOINT, "email=" + UrlUtils.enc(contactEmail) +
      "&secureToken=" + StubUserApi.VALID_EMAIL_VERIFICATION_KEY))

    // when user deletes the contact email
    val deleteRequest: MockHttpServletRequestBuilder = ajaxDELETE(ENDPOINT, "email=" + UrlUtils.enc(contactEmail))
    val deleteResult: ResultActions = mockMvc.perform(deleteRequest)

    // then
    deleteResult.andExpect(status().isOk)

    // then
    bushfireApi.userApi().getContactEmails(user.getId).get(0).getStatus should be (EmailStatus.DELETED)
  }

  scenario("should set preferred contact email") {
    // given a logged-in user
    val user: User = loginAsDefaultUser

    // and with at least two contact emails
    val email1 = "<EMAIL>"
    val email2 = "<EMAIL>"
    mockMvc.perform(ajaxPUT(ENDPOINT, "email=" + UrlUtils.enc(email1) +
      "&secureToken=" + StubUserApi.VALID_EMAIL_VERIFICATION_KEY)).andExpect(status.isOk)
    mockMvc.perform(ajaxPUT(ENDPOINT, "email=" + UrlUtils.enc(email2) +
      "&secureToken=" + StubUserApi.VALID_EMAIL_VERIFICATION_KEY)).andExpect(status.isOk)

    // when
    mockMvc.perform(ajaxPUT(ENDPOINT + "primary", "email=" + UrlUtils.enc(email1))).andExpect(status.isOk)

    // then
    val emails: util.List[ApiContactEmail] = bushfireApi.userApi().getContactEmails(user.getId)
    emails.find(_.getEmail.equals(email1)).get.isPreferred.booleanValue() should be(true)
    bushfireApi.userApi().getContactEmails(user.getId).find(_.getEmail.equals(email2)).get.isPreferred.booleanValue() should be(false)
  }

  scenario("should re-send verification email") {
    // given a logged-in user
    val user: User = loginAsDefaultUser

    // and with at least two contact emails
    val email = "<EMAIL>"
    mockMvc.perform(ajaxPUT(ENDPOINT, "email=" + UrlUtils.enc(email))).andExpect(status.isOk)

    // when + then
    mockMvc.perform(POST(ENDPOINT + "resendverify", "email=" + UrlUtils.enc(email))).andExpect(status.isOk)
  }
}
