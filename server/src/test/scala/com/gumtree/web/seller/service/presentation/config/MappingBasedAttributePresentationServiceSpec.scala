package com.gumtree.web.seller.service.presentation.config

import com.gumtree.api.category.domain.{AttributeMetadata, Category}
import com.gumtree.api.category.{CategoryMapperFactory, DefaultCategoryModel}
import com.gumtree.api.config.StubCategoryModelConfig
import com.gumtree.config.servlet.SellerServletConfig
import com.gumtree.web.seller.page.postad.model.{PostAdFormPanel, PostAdAttributeGroup}
import org.scalatest.FlatSpec
import org.scalatest.matchers.ShouldMatchers

import scala.collection.JavaConverters._

class MappingBasedAttributePresentationServiceSpec extends FlatSpec with ShouldMatchers {
  val categoryModel: DefaultCategoryModel = new DefaultCategoryModel(
    "1",
    new CategoryMapperFactory().create.readValue(
      new StubCategoryModelConfig().categoriesResource.getInputStream, classOf[Category]),
    Some(new CategoryMapperFactory().create.readValue(
      new StubCategoryModelConfig().categoriesAttributesResource.getInputStream, classOf[Array[AttributeMetadata]]).toSeq.asJava)
  )

  val fixture = {
    val config = new SellerServletConfig()
    config.attributePresentationService(categoryModel)
  }

  def checkHasAttributes(groupsForCategory: PostAdAttributeGroup, attributes: String*) {
    val actualAttributes = groupsForCategory.getAttributes.asScala
    attributes.foreach(attr => actualAttributes.map(_._2.getId) should contain(attr))
    actualAttributes.size should be(attributes.size)
  }


  it should "return the empty group for the root category" in {
    val rootId = categoryModel.getRootCategory.getId
    fixture.loadAttributeGroups(rootId, null).size() should be(0)
  }

  it should "return the price group for the for-sale category" in {
    val forSaleId = categoryModel.getByName("baby-clothes").get().getId
    val groupsForCategory = fixture.loadAttributeGroups(forSaleId, null)
    groupsForCategory.size() should be(1)
    checkHasAttributes(groupsForCategory.get(0), "price")
  }



  it should "return the cars groups for the audi category" in {
    val carId = categoryModel.getByName("audi").get().getId
    val groupsForCategory = fixture.loadAttributeGroups(carId, null)
    groupsForCategory.size() should be(3)
    checkHasAttributes(groupsForCategory.get(0), "additional_features_available", "alloy_wheels", "radio_with_speakers",
      "power_windows", "air_conditioning", "leather_seats", "navigation_system", "dual_front_airbags_package", "side_airbags",
      "passenger_airbags", "curtain_airbags", "airbag_knee_driver", "anti_lock_braking", "automatic_air_climate_control", "tinted_glass",
      "cruise_control", "central_locking_remote_control", "engine_immobiliser", "safety_belt_pretensioners",
      "alarm_system_remote_anti_theft", "electronic_stability_program", "trip_computer", "power_mirrors", "power_assisted_steering",
      "power_front_seats", "adjustable_steering_wheel", "aux_usb_input_socket", "split_folding_rear_seat", "multi_function_steering_wheel",
      "bluetooth_connectivity", "heated_seats", "heated_door_mirrors", "parking_sensors", "leather_steering_wheel", "daytime_running_lights",
      "android_auto", "apple_car_play", "traction_control", "child_proof_rear_door_locks", "automatic_headlights_with_dusk_sensor", "automatic_stop_start",
      "led_headlights" , "sunroof"
    )
    checkHasAttributes(groupsForCategory.get(1), "price")
    checkHasAttributes(groupsForCategory.get(2), "vehicle_make",
      "vehicle_registration_year", "vehicle_model", "vehicle_fuel_type", "vehicle_body_type",
      "vehicle_transmission", "vehicle_colour", "vehicle_engine_size", "vehicle_mileage", "vehicle_doors"
    )
  }

  it should "return the motorbike groups for the aprilia category" in {
    val catId = categoryModel.getByName("aprilia-motorbikes").get().getId
    val groupsForCategory = fixture.loadAttributeGroups(catId, null)
    groupsForCategory.size() should be(2)
    checkHasAttributes(groupsForCategory.get(0), "price")
    checkHasAttributes(groupsForCategory.get(1), "motorbike_make", "vehicle_registration_year",
      "vehicle_model", "vehicle_colour", "vehicle_engine_size", "vehicle_mileage"
    )
  }

  it should "return the rent groups for the 1-bedroom-rent category" in {
    val catId = categoryModel.getByName("1-bedroom-rent").get().getId
    val groupsForCategory = fixture.loadAttributeGroups(catId, null)
    groupsForCategory.size() should be(1)
    checkHasAttributes(groupsForCategory.get(0), "available_date", "price", "price_frequency", "property_type",
      "property_number_beds")

  }

  it should "return the flatshare  groups for the double-room-flatshare category" in {
    val catId = categoryModel.getByName("double-room-flatshare").get().getId
    val groupsForCategory = fixture.loadAttributeGroups(catId, null)
    groupsForCategory.size() should be(1)
    checkHasAttributes(groupsForCategory.get(0), "available_date", "price", "price_frequency", "property_type",
      "property_room_type", "property_couples")

  }

  it should "return the tickets groups for the tennis-tickets category" in {
    val catId = categoryModel.getByName("tennis-tickets").get().getId
    val groupsForCategory = fixture.loadAttributeGroups(catId, null)
    groupsForCategory.size() should be(3)
    checkHasAttributes(groupsForCategory.get(0), "ticket_date")
    checkHasAttributes(groupsForCategory.get(1), "price")
    checkHasAttributes(groupsForCategory.get(2), "ticket_face_value")

  }

  it should "return the event groups for the events-gigs-nightlife category" in {
    val catId = categoryModel.getByName("events-gigs-nightlife").get().getId
    val groupsForCategory = fixture.loadAttributeGroups(catId, null)
    groupsForCategory.size() should be(1)
    checkHasAttributes(groupsForCategory.get(0), "event_date")
  }

  it should "provide pricing panel with proper content in for-sale category" in {
    val catId = categoryModel.getByName("stereos-audio").get().getId
    val panelsForCategory = fixture.loadPrioritisedCategorySpecificFormPanels(catId, null)
    panelsForCategory.getHighPriorityPanels.size() should be(0)
    panelsForCategory.getLowPriorityPanels should contain only PostAdFormPanel.PRICE
  }

  it should "provide pricing panel with proper content in cars category" in {
    val catId = categoryModel.getByName("cars").get().getId
    val panelsForCategory = fixture.loadPrioritisedCategorySpecificFormPanels(catId, null)
    panelsForCategory.getHighPriorityPanels should contain only PostAdFormPanel.VEHICLE_SPECIFICATIONS
    panelsForCategory.getLowPriorityPanels should contain only PostAdFormPanel.PRICE
  }

  it should "provide pricing and pets birthday panel with proper content in pets-for-sale category" in {
    val catId = categoryModel.getByName("cats").get().getId
    val panelsForCategory = fixture.loadPrioritisedCategorySpecificFormPanels(catId, null)
    panelsForCategory.getHighPriorityPanels.size() should be(0)
    panelsForCategory.getLowPriorityPanels should contain inOrderOnly(PostAdFormPanel.PETS_BIRTHDAY, PostAdFormPanel.PRICE)
  }

  it should "provide pricing and attribute panel with proper content in tickets category" in {
    val catId = categoryModel.getByName("rugby-tickets").get().getId
    val panelsForCategory = fixture.loadPrioritisedCategorySpecificFormPanels(catId, null)
    panelsForCategory.getHighPriorityPanels.size() should be(0)
    panelsForCategory.getLowPriorityPanels should contain inOrderOnly(PostAdFormPanel.ATTRIBUTE_PANEL, PostAdFormPanel.PRICE)
  }

  it should "provide just custom attribute panel with proper content in properties to rent category" in {
    val catId = categoryModel.getByName("flats-and-houses-for-rent-offered").get().getId
    val panelsForCategory = fixture.loadPrioritisedCategorySpecificFormPanels(catId, null)
    panelsForCategory.getHighPriorityPanels.size() should be(0)
    panelsForCategory.getLowPriorityPanels should contain only PostAdFormPanel.ATTRIBUTE_PANEL
  }

  it should "provide just custom attribute panel with proper content in properties for sale category" in {
    val catId = categoryModel.getByName("local-property-for-sale").get().getId
    val panelsForCategory = fixture.loadPrioritisedCategorySpecificFormPanels(catId, null)
    panelsForCategory.getHighPriorityPanels.size() should be(0)
    panelsForCategory.getLowPriorityPanels should contain only PostAdFormPanel.ATTRIBUTE_PANEL
  }
}
