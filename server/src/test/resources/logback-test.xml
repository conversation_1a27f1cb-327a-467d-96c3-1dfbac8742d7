<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</Pattern>
        </layout>
    </appender>

    <!--    <logger name="org.springframework" level="debug"/> -->

    <logger name="wiremock" level="error"/>
    <logger name="com.gargoylesoftware.htmlunit" level="warn"/>
    <logger name="org.springframework.context.support" level="warn"/>
    <logger name="org.elasticsearch" level="warn"/>
    <logger name="com.datastax.driver.core" level="warn"/>
    <logger name="org.apache.camel" level="warn"/>
    <logger name="org.apache.cassandra" level="warn"/>
    <logger name="org.springframework" level="warn"/>
    <logger name="com.gumtree.mobile.spring.mvc" level="warn"/>
    <logger name="com.gumtree.common.properties" level="warn"/>
    <logger name="com.gumtree.draftsapi.stub" level="warn"/>
    <logger name="com.gumtree.es.embedded.EsTestClient" level="warn"/>
    <logger name="testing" level="warn"/>
    <logger name="com.gumtree.config.jobs" level="error"/>

    <root level="${logging.root.level:-INFO}">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>

