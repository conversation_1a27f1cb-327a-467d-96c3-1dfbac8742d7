package com.gumtree.config.servlet;

import com.gumtree.web.common.error.MessageSourceErrorMessageResolver;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

import static org.fest.assertions.api.Assertions.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(
        loader = AnnotationConfigContextLoader.class,
        classes = {SellerWebsiteMessageSourceConfig.class})
public class SellerWebsiteMessageSourceConfigTest {

    @Autowired
    private MessageSource messageSource;
    private MessageSourceErrorMessageResolver messageResolver;

    @Before
    public void beforeEach() {
        messageResolver = new MessageSourceErrorMessageResolver(messageSource);
    }

    @Test
    public void shouldResolveMessage() {
        // when
        String message = messageResolver.getMessage("reply.sendertelephone.length", "too-long");

        // then
        assertThat(message).isEqualTo("Your telephone number cannot exceed x characters");
    }
}