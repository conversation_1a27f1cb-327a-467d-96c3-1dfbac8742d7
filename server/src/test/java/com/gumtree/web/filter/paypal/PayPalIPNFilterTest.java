package com.gumtree.web.filter.paypal;

import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mock.web.DelegatingServletInputStream;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PayPalIPNFilterTest {

    @Mock
    private PayPalIPNRequest payPalIPNRequest;

    @Mock
    private HttpServletRequest request;

    @Mock
    private ServletResponse response;

    @Mock
    private FilterChain chain;

    private PayPalIPNFilter filter;

    private String body = "mybody";
    private String contentType = "mycontenttype";

    @Before
    public void setUp() throws Exception {
        filter = new PayPalIPNFilter();
        ReflectionTestUtils.setField(filter, "payPalIPNRequest", payPalIPNRequest);
        when(request.getContentType()).thenReturn(contentType);
        when(request.getInputStream()).thenReturn(new DelegatingServletInputStream(IOUtils.toInputStream(body)));
    }

    @Test
    public void doFilterIgnoresNonIPNURLs() throws Exception {
        when(request.getRequestURI()).thenReturn("/payment/sdfsdfsdf");

        filter.doFilter(request, response, chain);

        verifyZeroInteractions(payPalIPNRequest);
        verify(chain).doFilter(request, response);
    }

    @Test
    public void doFilterSuccessfullyHandlesIPNURL() throws Exception {
        when(request.getRequestURI()).thenReturn("/payment/ipn");

        filter.doFilter(request, response, chain);

        verify(payPalIPNRequest).setBody(body);
        verify(payPalIPNRequest).setContentType(contentType);
        verify(chain).doFilter(request, response);
    }

    @Test
    public void doFilterSuccessfullyHandlesIPNURLWithSlash() throws Exception {
        when(request.getRequestURI()).thenReturn("/payment/ipn/");

        filter.doFilter(request, response, chain);

        verify(payPalIPNRequest).setBody(body);
        verify(payPalIPNRequest).setContentType(contentType);
        verify(chain).doFilter(request, response);
    }

    @Test(expected = RuntimeException.class)
    public void handleErrorRetrievingIPNData() throws Exception {
        when(request.getRequestURI()).thenReturn("/payment/ipn/");

        doThrow(new RuntimeException("something went wrong..")).when(payPalIPNRequest).setBody(body);

        filter.doFilter(request, response, chain);

        verifyZeroInteractions(chain);
    }
}
