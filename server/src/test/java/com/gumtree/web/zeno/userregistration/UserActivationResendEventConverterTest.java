package com.gumtree.web.zeno.userregistration;

import com.gumtree.api.User;
import com.gumtree.zeno.core.domain.DeviceData;
import com.gumtree.zeno.core.domain.PageData;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.domain.UserData;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserActivationResend;
import com.gumtree.zeno.core.service.ZenoConverterService;
import com.gumtree.zeno.core.service.request.RequestDetailsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserActivationResendEventConverterTest {

    @Mock
    private RequestDetailsService requestDetailsService;

    @Mock
    private ZenoConverterService zenoConverterService;

    @InjectMocks
    private UserActivationResendEventConverter eventConverter;


    @Test
    public void convertUserRegistrationBeginZenoEvent() {

        // given
        Long userId = 1L;
        String emailAddress = "<EMAIL>";
        User user = User.builder().withEmail(emailAddress).withId(userId).build();
        UserData userData = UserData.aUser().withUserId(userId).withUserEmail(emailAddress).build();
        DeviceData deviceData = new DeviceData("", "", "", "");
        PageData pageData = PageData.aPage().withPageType(PageType.UserActivationResend).build();

        when(requestDetailsService.getUserData()).thenReturn(userData);
        when(requestDetailsService.getDeviceData()).thenReturn(deviceData);
        when(requestDetailsService.getPageData(PageType.UserActivationResend)).thenReturn(pageData);

        UserActivationResendZenoEvent userActivationResendZenoEvent = new UserActivationResendZenoEvent(user);

        // when
        UserActivationResend userRegistrationResend = (UserActivationResend) eventConverter.convert(userActivationResendZenoEvent);

        // then
        assertThat(userRegistrationResend.getP().getT()).isEqualTo(PageType.UserActivationResend);
    }


}
