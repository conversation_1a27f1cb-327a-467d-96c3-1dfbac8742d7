package com.gumtree.web.seller.mvctests;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.MwebProperty;
import com.gumtree.web.common.interceptor.ModelHandlerInterceptor;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext;
import org.junit.Before;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
public class SellerMvcJSONModelTest extends SellerMvcTest {

    @Before
    public void beforeEach() {
        super.beforeEach();
        initialiseHystrixRequestContext();
    }

    protected void initialiseHystrixRequestContext() {
        HystrixRequestContext.initializeContext();
    }

    protected MockMvc initialiseWithJSONModelHandler(Object controller) {
        String headerSecret = GtProps.getStr(MwebProperty.MODEL_JSON_HEADER_SECRET);
        return MockMvcBuilders.standaloneSetup(controller).addInterceptors(new ModelHandlerInterceptor(headerSecret)).build();
    }
}
