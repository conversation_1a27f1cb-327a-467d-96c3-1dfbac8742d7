package com.gumtree.web.seller.page.postad.reporting.ga.events;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PostAdPreviewTest {

    @Test
    public void checkValues() {
        ThirdPartyRequestContext ctx = mock(ThirdPartyRequestContext.class);
        when(ctx.getPageType()).thenReturn(PageType.PostAd);

        PostAdPreview event = new PostAdPreview(ctx);

        assertThat(event.getAction(), equalTo("PostAdPreview"));
        assertThat(event.getCategory(), equalTo("Post Ad Form"));
        assertThat(event.getName(), equalTo("PostAdPreview"));
    }

}
