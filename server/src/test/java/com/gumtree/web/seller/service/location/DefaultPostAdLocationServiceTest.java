package com.gumtree.web.seller.service.location;

import com.google.common.collect.Maps;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.location.entity.LocationEntity;
import com.gumtree.seller.infrastructure.driven.locations.api.LocationsApi;
import com.gumtree.seller.infrastructure.driven.model.LocationDataModel;
import com.gumtree.service.location.LocationService;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;
import com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState;
import org.junit.Before;
import org.junit.Test;
import rx.Single;

import java.math.BigDecimal;
import java.util.Map;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class DefaultPostAdLocationServiceTest {
    private LocationsApi locationsApi;
    private LocationService locationService;
    private DefaultPostAdLocationService postAdLocationService;

    private static final Location UK = newLocation(10000392, "UK", null, null);
    private static final Location LONDON = newLocation(10000344, "London", new BigDecimal(51.507248), new BigDecimal(-0.129356));
    private static final Location SOUTH_WEST = newLocation(375, "South West London", new BigDecimal(51.432422), new BigDecimal(-0.224018));
    private static final Location RICHMOND = newLocation(203, "Richmond", new BigDecimal(51.457163), new BigDecimal(-0.307661));
    private static final Location ESSEX = newLocation(10000384, "Essex", new BigDecimal(51.717698), new BigDecimal(0.570999));
    private static Location newLocation(int id, String name, BigDecimal lat, BigDecimal lng) {
        return LocationEntity.Builder.entity()
                .withId(id)
                .withName(name)
                .withLatitude(lat)
                .withLongitude(lng)
                .withDisplayName(name)
                .build();
    }

    @Before
    public void init() {
        locationsApi = mock(LocationsApi.class);
        locationService = mock(LocationService.class);
        postAdLocationService = new DefaultPostAdLocationService(locationService, locationsApi);
    }

    @Test
    public void lookupPostcodeTranslatesFullLocationDataModelCorrectly() {
        // given
        String input = "sw185as";
        long locationId = 125L;

        LocationDataModel postcodeResponse = new LocationDataModel()
                .id(1487558L)
                .type(LocationDataModel.TypeEnum.POSTCODE)
                .locationId(124L)
                .leafLocationId(locationId)
                .latitude(51.4446)
                .longitude(-0.200619)
                .postcode(null);

        when(locationsApi.getPostcodeByName(input)).thenReturn(Single.just(postcodeResponse));

        // when
        PostcodeLookupResponse serviceResponse = postAdLocationService.lookupPostcode(input);

        // then
        assertThat(serviceResponse).isEqualTo(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_RECOGNISED, input, locationId));

        // and
        verify(locationsApi, never()).getOutcodeByName(anyString());
    }

    @Test
    public void lookupPostcodeTranslatesOutcodeOnlyResponseCorrectly() {
        // given
        String input = "sw184ff";
        String outcodeInput = "SW18";
        long locationId = 176L;

        LocationDataModel postcodeResponse = null;
        LocationDataModel outcodeResponse = new LocationDataModel()
                .id(2535L)
                .type(LocationDataModel.TypeEnum.OUTCODE)
                .locationId(2534L)
                .leafLocationId(locationId)
                .latitude(51.45068)
                .longitude(-0.191443)
                .postcode("SW184NL");

        when(locationsApi.getPostcodeByName(input)).thenReturn(Single.just(postcodeResponse));
        when(locationsApi.getOutcodeByName(outcodeInput)).thenReturn(Single.just(outcodeResponse));

        // when
        PostcodeLookupResponse serviceResponse = postAdLocationService.lookupPostcode(input);

        // then
        assertThat(serviceResponse).isEqualTo(new PostcodeLookupResponse(PostcodeSelectionState.OUTCODE_RECOGNISED, input, locationId));

        // and
        verify(locationsApi).getPostcodeByName(input);
        verify(locationsApi).getOutcodeByName(outcodeInput);
    }

    @Test
    public void lookupPostcodeTranslatesInvalidPostcodeErrorCorrectly() {
        // given
        String input = "xyz";

        // when
        PostcodeLookupResponse serviceResponse = postAdLocationService.lookupPostcode(input);

        // then
        assertThat(serviceResponse).isEqualTo(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_INVALID, input, null));

        // and
        verify(locationsApi, never()).getPostcodeByName(anyString());
        verify(locationsApi, never()).getOutcodeByName(anyString());
    }

    @Test
    public void lookupPostcodeTranslatesUnrecognisedPostcodeErrorCorrectly() {
        // given
        String input = "SW185AS";
        String outcodeInput = "SW18";

        LocationDataModel postcodeResponse = null;
        LocationDataModel outcodeResponse = null;

        when(locationsApi.getPostcodeByName(input)).thenReturn(Single.just(postcodeResponse));
        when(locationsApi.getOutcodeByName(outcodeInput)).thenReturn(Single.just(outcodeResponse));

        // when
        PostcodeLookupResponse serviceResponse = postAdLocationService.lookupPostcode(input);

        // then
        assertThat(serviceResponse).isEqualTo(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_NOT_FOUND, input, null));

        // and
        verify(locationsApi).getPostcodeByName(input);
        verify(locationsApi).getOutcodeByName(outcodeInput);
    }

    @Test
    public void lookupPostcodeTranslatesMissingPostcodeErrorCorrectly() {
        // given
        String input = " ";

        // when
        PostcodeLookupResponse serviceResponse = postAdLocationService.lookupPostcode(input);

        // then
        assertThat(serviceResponse).isEqualTo(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_MISSING, input, null));

        // and
        verify(locationsApi, never()).getPostcodeByName(anyString());
        verify(locationsApi, never()).getOutcodeByName(anyString());
    }

    @Test
    public void lookupPostcodeTranslatesToNotRecognizedWhenPostcodeRequestFails() {
        // given
        String input = "sw185as";

        when(locationsApi.getPostcodeByName(input)).thenReturn(Single.error(new RuntimeException("err postcode")));

        // when
        PostcodeLookupResponse serviceResponse = postAdLocationService.lookupPostcode(input);

        // then
        assertThat(serviceResponse).isEqualTo(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_NOT_FOUND, input, null));

        // and
        verify(locationsApi).getPostcodeByName(input);
        verify(locationsApi, never()).getOutcodeByName(anyString());
    }

    @Test
    public void lookupPostcodeTranslatesToNotRecognizedWhenOutcodeRequestFails() {
        // given
        String input = "sw185as";
        String outcodeInput = "SW18";

        when(locationsApi.getPostcodeByName(input)).thenReturn(Single.just(null));
        when(locationsApi.getOutcodeByName(outcodeInput)).thenReturn(Single.error(new RuntimeException("err outcode")));

        // when
        PostcodeLookupResponse serviceResponse = postAdLocationService.lookupPostcode(input);

        // then
        assertThat(serviceResponse).isEqualTo(new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_NOT_FOUND, input, null));

        // and
        verify(locationsApi).getPostcodeByName(input);
        verify(locationsApi).getOutcodeByName(outcodeInput);
    }

    @Test
    public void shouldSpecifyRichmondLocationInAreaLondon() {
        // Given
        Map<Integer, Location> hierarchy = Maps.newHashMap();
        hierarchy.put(1, UK);
        hierarchy.put(3, LONDON);
        hierarchy.put(4, SOUTH_WEST);
        hierarchy.put(5, RICHMOND);
        when(locationService.getLocationHierarchy(any())).thenReturn(hierarchy);

        // When
        boolean inArea = postAdLocationService.isInArea(RICHMOND, LONDON.getId());

        // Then
        assertThat(inArea).isEqualTo(true);
    }

    @Test
    public void shouldSpecifyRichmondLocationNotInAreaSurrey() {
        // Given
        Map<Integer, Location> hierarchy = Maps.newHashMap();
        hierarchy.put(1, UK);
        hierarchy.put(3, LONDON);
        hierarchy.put(4, SOUTH_WEST);
        hierarchy.put(5, RICHMOND);
        when(locationService.getLocationHierarchy(any())).thenReturn(hierarchy);

        // When
        boolean inArea = postAdLocationService.isInArea(RICHMOND, ESSEX.getId());

        assertThat(inArea).isEqualTo(false);
    }
}
