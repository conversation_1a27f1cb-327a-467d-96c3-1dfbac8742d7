package com.gumtree.web.seller.converter;

import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;

public class AdPreviewConversionPropertiesTest {

    @Test
    public void shouldHaveEmptyL1CategoriesList_whenL1CategoriesPropertyIsEmpty() {
        AdPreviewConversionProperties props = newConversionProperties("");

        List<Long> l1Cats = props.getL1CategoriesWithShowTextSnippets();

        assertTrue(l1Cats.isEmpty());
    }

    @Test
    public void shouldHaveEmptyL1CategoriesList_whenL1CategoriesPropertyIsNull() {
        AdPreviewConversionProperties props = newConversionProperties(null);

        List<Long> l1Cats = props.getL1CategoriesWithShowTextSnippets();

        assertTrue(l1Cats.isEmpty());
    }

    @Test
    public void shouldContainL1CategoriesList_whenL1CategoriesPropertyIsValid() {
        AdPreviewConversionProperties props = newConversionProperties("23,45");

        List<Long> l1Cats = props.getL1CategoriesWithShowTextSnippets();

        assertThat(l1Cats, containsInAnyOrder(23L, 45L));
    }

    @Test
    public void shouldContainL1CategoriesList_whenOnlyOneL1CategoryPropertyIsDefined() {
        AdPreviewConversionProperties props = newConversionProperties("111");

        List<Long> l1Cats = props.getL1CategoriesWithShowTextSnippets();

        assertThat(l1Cats, contains(111L));
    }

    @Test
    public void shouldContainL1CategoriesList_whenL1CategoriesPropertyContainsSpaces() {
        AdPreviewConversionProperties props = newConversionProperties(" 67, 99,\t8");

        List<Long> l1Cats = props.getL1CategoriesWithShowTextSnippets();

        assertThat(l1Cats, containsInAnyOrder(67L, 99L, 8L));
    }

    @Test
    public void shouldHaveEmptyL1CategoriesList_whenPropertySeparatorIsInvalid() {
        AdPreviewConversionProperties props = newConversionProperties("1.2");

        List<Long> l1Cats = props.getL1CategoriesWithShowTextSnippets();

        assertTrue(l1Cats.isEmpty());
    }

    @Test
    public void shouldHaveEmptyL1CategoriesList_whenL1CategoriesPropertyIsInvalid() {
        AdPreviewConversionProperties props = newConversionProperties("1,r,2");

        List<Long> l1Cats = props.getL1CategoriesWithShowTextSnippets();

        assertTrue(l1Cats.isEmpty());
    }

    @Test
    public void shouldContainL1CategoriesList_whenPropertySeparatorIsFirstCharacter() {
        AdPreviewConversionProperties props = newConversionProperties(",1,2");

        List<Long> l1Cats = props.getL1CategoriesWithShowTextSnippets();

        assertThat(l1Cats, containsInAnyOrder(1L, 2L));
    }

    @Test
    public void shouldContainL1CategoriesList_whenPropertySeparatorIsLastCharacter() {
        AdPreviewConversionProperties props = newConversionProperties("1,2,");

        List<Long> l1Cats = props.getL1CategoriesWithShowTextSnippets();

        assertThat(l1Cats, containsInAnyOrder(1L, 2L));
    }

    private AdPreviewConversionProperties newConversionProperties(String snippetL1Categories) {
        AdPreviewConversionProperties props = new AdPreviewConversionProperties();
        ReflectionTestUtils.setField(props, "l1CategoriesWithShowTextSnippetsStr", snippetL1Categories);
        props.init();
        return props;
    }
}
