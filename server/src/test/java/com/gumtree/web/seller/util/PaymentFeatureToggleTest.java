package com.gumtree.web.seller.util;


import com.gumtree.util.PaymentFeatureToggle;
import org.junit.Test;

import static org.junit.Assert.*;

public class PaymentFeatureToggleTest {
    
    private final PaymentFeatureToggle toggle = new PaymentFeatureToggle();
    
    @Test
    public void isInWhitelist_shouldReturnTrueForWhitelistedUsers() {
        // Test the hardcoded whitelist users
        assertTrue(toggle.isInWhitelist(9999999999L));
    }
    
    @Test
    public void isInWhitelist_shouldReturnFalseForNonWhitelistedUsers() {
        assertFalse(toggle.isInWhitelist(8888888888L));
    }
    
    @Test
    public void isInGrayScale_shouldReturnTrueForWhitelistedUsers() {
        // Whitelisted users should always be in grayscale
        assertTrue(toggle.isInGrayScale(9999999999L));
    }

    @Test
    public void isInGrayScale_shouldReturnFalseForUsersOutsidePercentageRange() {
        assertFalse(toggle.isInGrayScale(-1000000001L));
    }
}
