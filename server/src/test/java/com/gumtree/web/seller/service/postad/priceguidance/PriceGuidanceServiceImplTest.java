package com.gumtree.web.seller.service.postad.priceguidance;

import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.domain.category.Categories;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.api.PriceGuidanceApi;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.Ad;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.Category;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.PriceGuidanceResponse;
import com.gumtree.web.seller.page.postad.model.PriceGuidance;
import com.gumtree.web.seller.page.postad.model.PriceGuidanceAd;
import com.gumtree.web.seller.page.postad.model.PriceGuidanceContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.core.convert.converter.Converter;
import rx.Single;

import java.util.Collections;
import java.util.Optional;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

@SuppressWarnings("OptionalGetWithoutIsPresent")
@RunWith(MockitoJUnitRunner.class)
public class PriceGuidanceServiceImplTest {

    @InjectMocks
    private PriceGuidanceServiceImpl service;

    @Mock
    private Converter<Ad, PriceGuidanceAd> flatAdConverter;

    @Mock
    private PriceGuidanceApi priceGuidanceApi;

    @Test
    public void shouldGetPriceGuidanceWithOneMatchingAdvert() {
        // given
        Ad ad = createAd();
        when(priceGuidanceApi.getPriceGuidance("bg78bwn", Optional.of(20), Optional.of(Categories.CARS.getId().intValue())))
                .thenReturn(Single.just(new PriceGuidanceResponse()
                        .addAdsItem(ad)
                        .putAttributesItem(CategoryConstants.Attribute.CAP_CODE.getName(), "xyz")
                ));

        // and
        PriceGuidanceAd priceGuidanceAd = priceGuidanceAd();
        when(flatAdConverter.convert(ad)).thenReturn(priceGuidanceAd);

        // when
        Optional<PriceGuidanceContext> guidanceCtx = service.getForCarAd("bg78bwn", Categories.CARS.getId());

        // then
        assertThat(guidanceCtx).isNotEqualTo(Optional.empty());
        assertThat(guidanceCtx.get().getPriceGuidance())
                .isEqualTo(new PriceGuidance(1L, Collections.singletonList(priceGuidanceAd)));

        // and
        verify(priceGuidanceApi).getPriceGuidance(anyString(), any(), any());
        verify(flatAdConverter).convert(ad);
    }


    @Test
    public void shouldReturnEmptyListOfAdsIfNotMatch() {
        // given
        when(priceGuidanceApi.getPriceGuidance("bg78bwn", Optional.of(20), Optional.of(Categories.CARS.getId().intValue())))
                .thenReturn(Single.just(new PriceGuidanceResponse()
                        .ads(Collections.emptyList())
                        .putAttributesItem(CategoryConstants.Attribute.CAP_CODE.getName(), "xyz")
                ));

        // when
        Optional<PriceGuidanceContext> guidanceCtx = service.getForCarAd("bg78bwn", Categories.CARS.getId());

        // then
        assertThat(guidanceCtx).isNotEqualTo(Optional.empty());
        assertThat(guidanceCtx.get().getPriceGuidance()).isEqualTo(new PriceGuidance(0, Collections.emptyList()));

        // and
        verify(priceGuidanceApi).getPriceGuidance(anyString(), any(), any());
        verify(flatAdConverter, never()).convert(any(Ad.class));
    }

    @Test(expected = RuntimeException.class)
    public void shouldReturnEmptyListOfAdsIfApiFails() {
        // given
        when(priceGuidanceApi.getPriceGuidance("bg78bwn", Optional.of(20), Optional.of(Categories.CARS.getId().intValue())))
                .thenReturn(Single.error(new RuntimeException("PriceGuidanceApi mock error!")));

        // when
        service.getForCarAd("bg78bwn", Categories.CARS.getId());

        // then exception
    }

    private PriceGuidanceAd priceGuidanceAd() {
        return new PriceGuidanceAd(654321L, "My title",
                "4,234", "http://image.primary.com/123", "PRIVATE", "60000");
    }

    private Ad createAd() {
        return new Ad()
                .id(654321L)
                .title("My title")
                .primaryImageUrl("http://image.primary.com/123")
                .putAttributeItem("vehicle_mileage", 60000)
                .putAttributeItem("price", 423400)
                .putAttributeItem("seller_type", "PRIVATE")
                .addCategoriesItem(new Category().id(Categories.CARS.getId()).primary(true));
    }
}