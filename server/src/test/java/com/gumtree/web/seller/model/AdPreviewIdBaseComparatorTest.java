package com.gumtree.web.seller.model;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

import java.util.Arrays;
import java.util.Collection;

import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertThat;

@RunWith(Parameterized.class)
public class AdPreviewIdBaseComparatorTest {
    private static final AdPreviewIdBaseComparator COMPARATOR = new AdPreviewIdBaseComparator();
    private AdPreview left;
    private AdPreview right;
    private int expectedResult;

    public AdPreviewIdBaseComparatorTest(long leftAdvertId, long rightAdvertId, int expectedResult) {
        this.left = leftAdvertId == -1 ? null : new AdPreviewImpl.Builder().id(leftAdvertId).build();
        this.right = rightAdvertId == -1 ? null : new AdPreviewImpl.Builder().id(rightAdvertId).build();
        this.expectedResult = expectedResult;
    }

    @Parameterized.Parameters
    public static Collection<Object[]> data() {
        Object[][] data = new Object[][] {
                { 10L, 5L, 1 },
                { 5L, 10L, -1 },
                { 5L, 5L, 0 },
                { -1L, 5L, -1 },
                { 5L, -1L, 1 },
                { -1L, -1L, 0 },
        };
        return Arrays.asList(data);
    }

    @Test
    public void shouldCompareTwoAdPreview() {
        // when
        int result = COMPARATOR.compare(left, right);

        // then
        assertThat(result, is(expectedResult));
    }
}
