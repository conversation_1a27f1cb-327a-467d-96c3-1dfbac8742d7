package com.gumtree.web.seller.page.common.model;

import com.google.common.base.Optional;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import com.gumtree.common.properties.Env;
import com.gumtree.global.AppViewDevice;
import com.gumtree.global.PageTheme;
import com.gumtree.web.abtest.Experiments;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.seller.security.apiauthentication.CSRFTokenService;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CoreModelBuilderTest {

    private static final String EXPERIMENT_KEY = "EXPERIMENT-001";
    private static final String VARIANT_A = "A";
    private static final String VARIANT_B = "B";

    private CoreModel.Builder coreModelBuilder;

    private Experiments experiments;

    private Map<String, String> experimentMap;

    private CSRFTokenService csrfTokenService;

    @Before
    public void setUp() {
        Category rootCategory = mock(Category.class);
        CategoryModel categoryModel = mock(CategoryModel.class);
        when(categoryModel.getRootCategory()).thenReturn(rootCategory);
        coreModelBuilder = CoreModel.builder(Env.DEV, categoryModel, mock(CookieResolver.class));
        experimentMap = new HashMap<>();
        experiments = new Experiments(experimentMap);
        csrfTokenService = mock(CSRFTokenService.class);
    }

    @Test
    public void testWithFeatureForNonParticipationGroup() {
        // Given experiment is not in map
        // When
        CoreModel coreModel = coreModelBuilder.withExperiments(experiments).build(null);

        // Then
        assertThat(coreModel.getExperiments().size(), equalTo(0));
    }

    @Test
    public void testWithFeatureForControlledGroup() {
        // Given
        experimentMap.put(EXPERIMENT_KEY, VARIANT_A);

        //When
        CoreModel coreModel = coreModelBuilder.withExperiments(experiments).build(null);

        // Then
        assertThat(coreModel.getExperiments(), equalTo(experimentMap));
    }

    @Test
    public void testWithFeatureForNonControlledGroup() {
        // Given
        experimentMap.put(EXPERIMENT_KEY, VARIANT_B);

        // When
        CoreModel coreModel = coreModelBuilder.withExperiments(experiments).build(null);

        //Then
        assertThat(coreModel.getExperiments(), equalTo(experimentMap));
    }

    @Test
    public void testWithUserInformation() {
        // Given
        Long userId = 123L;
        String firstName = "testName";
        String email = "<EMAIL>";
        String token = "testToken";
        DateTime creationDate = DateTime.now();

        User user = User.builder().withId(userId).withEmail(email).withFirstName(firstName).build();
        user.setCreationDate(creationDate);

        when(csrfTokenService.csrfTokenForEmail(email)).thenReturn(token);

        // When
        CoreModel coreModel = coreModelBuilder.withUser(Optional.of(user), csrfTokenService).build(null);

        // Then
        assertThat(coreModel.getUser().isUserLoggedIn(), equalTo(Boolean.TRUE));
        assertThat(coreModel.getUser().getId(), equalTo(userId));
        assertThat(coreModel.getUser().getFirstName(), equalTo(firstName));
        assertThat(coreModel.getUser().getCsrfToken(), equalTo(token));
        assertThat(coreModel.getUser().getCreationDateMillis(), equalTo(creationDate.getMillis()));
    }

    @Test
    public void testWithUserInformationEmpty() {
        // Given
        // When
        CoreModel coreModel = coreModelBuilder.withUser(Optional.absent(), csrfTokenService).build(null);

        // Then
        assertThat(coreModel.getUser().isUserLoggedIn(), equalTo(Boolean.FALSE));
    }

    @Test
    public void testWithAppViewDeviceAndPageThemeNoUserAgent() {
        HttpServletRequest mockServletRequest = mock(HttpServletRequest.class);
        CoreModel coreModel = coreModelBuilder.withAppViewDeviceAndPageTheme(mockServletRequest).build(null);

        assertThat(coreModel.getAppViewDevice(), equalTo(null));
        assertThat(coreModel.getPageTheme(), equalTo(PageTheme.WEBVIEW));
    }

    @Test
    public void testWithAppViewDeviceAndPageTheme_IOSUserAgent() {
        HttpServletRequest mockServletRequest = mock(HttpServletRequest.class);
        when(mockServletRequest.getHeader("user-agent")).thenReturn("User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile - iOS/GumtreeApp/h64x83o7jbz3dvefdpaqetov");

        CoreModel coreModel = coreModelBuilder.withAppViewDeviceAndPageTheme(mockServletRequest).build(null);

        assertThat(coreModel.getAppViewDevice(), equalTo(AppViewDevice.IOS));
        assertThat(coreModel.getPageTheme(), equalTo(PageTheme.APPVIEW));
    }

    @Test
    public void testWithAppViewDeviceAndPageTheme_AndroidUserAgent() {
        HttpServletRequest mockServletRequest = mock(HttpServletRequest.class);
        when(mockServletRequest.getHeader("user-agent")).thenReturn("User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile - Android/GumtreeApp/h64x83o7jbz3dvefdpaqetov");

        CoreModel coreModel = coreModelBuilder.withAppViewDeviceAndPageTheme(mockServletRequest).build(null);

        assertThat(coreModel.getAppViewDevice(), equalTo(AppViewDevice.ANDROID));
        assertThat(coreModel.getPageTheme(), equalTo(PageTheme.APPVIEW));
    }

    @Test
    public void testWithAppViewDeviceAndPageTheme_WebUserAgent() {
        HttpServletRequest mockServletRequest = mock(HttpServletRequest.class);
        when(mockServletRequest.getHeader("user-agent")).thenReturn("User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko)");

        CoreModel coreModel = coreModelBuilder.withAppViewDeviceAndPageTheme(mockServletRequest).build(null);

        assertThat(coreModel.getAppViewDevice(), equalTo(null));
        assertThat(coreModel.getPageTheme(), equalTo(PageTheme.WEBVIEW));
    }

    @Test
    public void testWithAppViewDeviceAndPageTheme_UnknownMobileUserAgent() {
        HttpServletRequest mockServletRequest = mock(HttpServletRequest.class);
        when(mockServletRequest.getHeader("user-agent")).thenReturn("User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile - Blackberry/GumtreeApp/h64x83o7jbz3dvefdpaqetov");

        CoreModel coreModel = coreModelBuilder.withAppViewDeviceAndPageTheme(mockServletRequest).build(null);

        assertThat(coreModel.getAppViewDevice(), equalTo(null));
        assertThat(coreModel.getPageTheme(), equalTo(PageTheme.WEBVIEW));
    }
}
