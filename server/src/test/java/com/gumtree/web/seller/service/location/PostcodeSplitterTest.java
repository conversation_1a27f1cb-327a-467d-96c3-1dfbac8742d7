package com.gumtree.web.seller.service.location;

import org.hamcrest.CoreMatchers;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;


/**
 * Copied directly from bapi
 */
public class PostcodeSplitterTest {

    @Test
    public void incodeAndOutcodeNullWhenPostcodeNull() {
        PostcodeSplitter splitter = new PostcodeSplitter(null);
        assertThat(splitter.getOutcode(), CoreMatchers.nullValue());
        assertThat(splitter.getIncode(), CoreMatchers.nullValue());
    }

    @Test
    public void incodeAndOutcodeAsExpectedForPostcodeWithSpaceAnd4CharacterOutcode() {
        PostcodeSplitter splitter = new PostcodeSplitter("EC2V 6DN");
        assertThat(splitter.getOutcode(), equalTo("EC2V"));
        assertThat(splitter.getIncode(), equalTo("6DN"));
    }

    @Test
    public void incodeAndOutcodeAsExpectedForPostcodeWithNoSpaceAnd4CharacterOutcode() {
        PostcodeSplitter splitter = new PostcodeSplitter("EC2V6DN");
        assertThat(splitter.getOutcode(), equalTo("EC2V"));
        assertThat(splitter.getIncode(), equalTo("6DN"));
    }

    @Test
    public void incodeAndOutcodeAsExpectedForPostcodeWithSpaceAnd3CharacterOutcode() {
        PostcodeSplitter splitter = new PostcodeSplitter("EC2 6DN");
        assertThat(splitter.getOutcode(), equalTo("EC2"));
        assertThat(splitter.getIncode(), equalTo("6DN"));
    }

    @Test
    public void incodeAndOutcodeAsExpectedForPostcodeWithNoSpaceAnd3CharacterOutcode() {
        PostcodeSplitter splitter = new PostcodeSplitter("EC26DN");
        assertThat(splitter.getOutcode(), equalTo("EC2"));
        assertThat(splitter.getIncode(), equalTo("6DN"));
    }

    @Test
    public void outcodeAsExpectedFor4CharacterOutcodeOnly() {
        PostcodeSplitter splitter = new PostcodeSplitter("EC2V");
        assertThat(splitter.getOutcode(), equalTo("EC2V"));
        assertThat(splitter.getIncode(), CoreMatchers.nullValue());
    }

    @Test
    public void outcodeAsExpectedFor3CharacterOutcodeOnly() {
        PostcodeSplitter splitter = new PostcodeSplitter("EC2");
        assertThat(splitter.getOutcode(), equalTo("EC2"));
        assertThat(splitter.getIncode(), CoreMatchers.nullValue());
    }

    @Test
    public void outcodeAndIncodeNullWhenLengthLessThan3Characters() {
        PostcodeSplitter splitter = new PostcodeSplitter("EC");
        assertThat(splitter.getOutcode(), CoreMatchers.nullValue());
        assertThat(splitter.getIncode(), CoreMatchers.nullValue());
    }

    @Test
    public void outcodeAndIncodeNullWhenLengthWithSpaceGreaterThan8Characters() {
        PostcodeSplitter splitter = new PostcodeSplitter("EC2Y 8RBR");
        assertThat(splitter.getOutcode(), CoreMatchers.nullValue());
        assertThat(splitter.getIncode(), CoreMatchers.nullValue());
    }

    @Test
    public void outcodeAndIncodeNullWhenLengthWithoutSpaceGreaterThan7Characters() {
        PostcodeSplitter splitter = new PostcodeSplitter("EC2Y8RBR");
        assertThat(splitter.getOutcode(), CoreMatchers.nullValue());
        assertThat(splitter.getIncode(), CoreMatchers.nullValue());
    }

    @Test
    public void outcodeAndIncodeAsExpectedFor5CharacterPostcode() {
        PostcodeSplitter splitter = new PostcodeSplitter("W30NU");
        assertThat(splitter.getOutcode(), equalTo("W3"));
        assertThat(splitter.getIncode(), equalTo("0NU"));
    }

    @Test
    public void outcodeAndIncodeAsExpectedFor5CharacterPostcodeWithSpace() {
        PostcodeSplitter splitter = new PostcodeSplitter("W3 0NU");
        assertThat(splitter.getOutcode(), equalTo("W3"));
        assertThat(splitter.getIncode(), equalTo("0NU"));
    }
}
