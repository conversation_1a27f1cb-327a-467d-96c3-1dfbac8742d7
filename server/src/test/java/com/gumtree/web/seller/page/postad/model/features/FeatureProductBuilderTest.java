package com.gumtree.web.seller.page.postad.model.features;

import com.gumtree.api.Account;
import com.gumtree.api.Ad;
import com.gumtree.api.AdFeature;
import com.gumtree.api.ApiProductPrice;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.api.client.spec.PriceApi;
import com.gumtree.common.util.time.Clock;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.web.common.domain.order.entity.OrderEntity;
import com.gumtree.web.common.domain.order.entity.OrderItemEntity;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Test for the FeatureProductBuilder
 * <p/>
 * For Features
 * "FEATURE_3_DAY"
 * "FEATURE_7_DAY"
 * "FEATURE_14_DAY"
 * "HOMEPAGE_SPOTLIGHT"
 * "URGENT"
 */
public class FeatureProductBuilderTest {

    private Ad advert;
    private Ad advert2;
    private OrderEntity order;
    private BushfireApi bushfireApi;
    private Account account;
    private List<ApiProductPrice> pricingMap;
    private AccountApi accountApi;
    private PriceApi priceApi;
    private Clock clock;
    private UserSession userSession;


    @Before
    public void init() {
        clock = mock(Clock.class);
        userSession = mock(UserSession.class);
        bushfireApi = mock(BushfireApi.class);
        accountApi = mock(AccountApi.class);
        priceApi = mock(PriceApi.class);
        advert = new Ad();
        advert2 = new Ad();
        order = mock(OrderEntity.class);
        account = new Account();
        order.setAccountId(1L);
        advert.setId(1L);
        advert.setCategoryId(1L);
        advert.setLocationId(1L);
        advert2.setId(2L);
        advert2.setCategoryId(1L);
        advert2.setLocationId(2L);
        populatePricingMap();

        BushfireApiKey bushfireApiKey = new BushfireApiKey();
        when(userSession.getApiKey()).thenReturn(bushfireApiKey);
        when(bushfireApi.create(PriceApi.class, bushfireApiKey)).thenReturn(priceApi);
        when(bushfireApi.accountApi()).thenReturn(accountApi);
    }

    /**
     * AC:
     * <p/>
     * pro user
     * performed any action
     * do not display the ad promotion section
     */

    @Test
    public void proUser() {
        //given

        account.setPro(true);

        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, nullValue());
    }

    /**
     * AC:
     * <p/>
     * non-pro user
     * posted/edited/bumpedUp an ad with no features
     * display the ad promotion section
     */
    @Test
    public void whenNonProUserAndAdWithoutFeaturesAdded() {
        //given

        account.setPro(false);

        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);
        order.setItems(new ArrayList<OrderItemEntity>());
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, not(nullValue()));
    }


    /**
     * AC:
     * <p/>
     * non-pro user
     * posted/edit an ad with features
     * do not display the ad promotion section
     */
    @Test
    public void whenNonProUserAndWithFeaturesAdded() {
        //given
        account.setPro(false);
        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);

        OrderItemEntity itemOne = new OrderItemEntity();
        itemOne.setProductName(ProductName.FEATURE_3_DAY);
        itemOne.setAdvert(advert);
        List<OrderItemEntity> items = new ArrayList<OrderItemEntity>();
        items.add(itemOne);
        order.setItems(items);
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, nullValue());
    }


    /**
     * AC:
     * <p/>
     * non-pro user
     * posted/edit an ad with no features but Insertion payed (anything else apart from Features**)
     * display the ad promotion section
     */
    @Test
    public void whenNonProUserAndWithInsertionAndNoFeature() {
        //given
        account.setPro(false);
        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);
        OrderItemEntity itemInsertion = new OrderItemEntity();
        itemInsertion.setProductName(ProductName.INSERTION);
        itemInsertion.setAdvert(advert);
        List<OrderItemEntity> items = new ArrayList<OrderItemEntity>();
        items.add(itemInsertion);
        order.setItems(items);
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, not(nullValue()));
    }


    /**
     * AC:
     * <p/>
     * non-pro user
     * bumped up an ad and added no features
     * display the ad promotion section
     */
    @Test
    public void whenNonProUserAndBumpUpButNoOtherFeatures() {
        //given
        account.setPro(false);
        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);
        OrderItemEntity itemBumpUp = new OrderItemEntity();
        itemBumpUp.setProductName(ProductName.BUMP_UP);
        itemBumpUp.setAdvert(advert);
        List<OrderItemEntity> items = new ArrayList<OrderItemEntity>();
        items.add(itemBumpUp);
        order.setItems(items);
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, not(nullValue()));
    }

    /**
     * AC:
     * <p/>
     * non-pro user
     * bumped up an ad and added features
     * do not display the ad promotion section
     */
    @Test
    public void whenNonProUserAndBumpUpWithOtherFeatures() {
        //given
        account.setPro(false);
        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);
        OrderItemEntity itemBumpUp = new OrderItemEntity();
        itemBumpUp.setProductName(ProductName.BUMP_UP);
        itemBumpUp.setAdvert(advert);
        OrderItemEntity itemSpotLight = new OrderItemEntity();
        itemSpotLight.setProductName(ProductName.HOMEPAGE_SPOTLIGHT);
        itemSpotLight.setAdvert(advert);
        List<OrderItemEntity> items = new ArrayList<OrderItemEntity>();
        items.add(itemBumpUp);
        items.add(itemSpotLight);
        order.setItems(items);
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, nullValue());
    }

    /**
     * AC:
     * non-pro user
     * bumped up multiple ads
     * do not display the ad promotion section
     */
    @Test
    public void whenNonProUserAndBumpUpMultipleAdsWithoutFeatures() {
        //given
        account.setPro(false);
        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);
        OrderItemEntity itemBumpUp = new OrderItemEntity();
        itemBumpUp.setProductName(ProductName.BUMP_UP);
        itemBumpUp.setAdvert(advert);
        OrderItemEntity itemBumpUpDifferentAd = new OrderItemEntity();
        itemBumpUpDifferentAd.setProductName(ProductName.BUMP_UP);
        itemBumpUpDifferentAd.setAdvert(advert2);
        List<OrderItemEntity> items = new ArrayList<OrderItemEntity>();
        items.add(itemBumpUp);
        items.add(itemBumpUpDifferentAd);
        order.setItems(items);
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, nullValue());
    }


    /**
     * AC:
     * <p/>
     * non-pro user
     * just added features to one or more ads
     * do not display the ad promotion section
     */
    @Test
    public void whenNonProUserAndMultipleAdsWithFeatures() {
        //given
        account.setPro(false);
        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);
        OrderItemEntity itemBumpUp = new OrderItemEntity();
        itemBumpUp.setProductName(ProductName.HOMEPAGE_SPOTLIGHT);
        itemBumpUp.setAdvert(advert);
        OrderItemEntity itemBumpUpDifferentAd = new OrderItemEntity();
        itemBumpUpDifferentAd.setProductName(ProductName.FEATURE_7_DAY);
        itemBumpUpDifferentAd.setAdvert(advert2);
        List<OrderItemEntity> items = new ArrayList<OrderItemEntity>();
        items.add(itemBumpUp);
        items.add(itemBumpUpDifferentAd);
        order.setItems(items);
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, nullValue());
    }


    /**
     * AC:
     * non-pro user
     * has already brought a feature for the ad in the past and this active
     * display the pricingMetadata with the specific field selected (not active)
     */
    @Test
    public void whenNonProUserAndAdWithPastFeatureActiveNoNewAdded() {
        //given
        account.setPro(false);
        AdFeature feature14Days = new AdFeature();
        feature14Days.setEndDate(new DateTime().plusDays(14));
        feature14Days.setProductName(ProductName.FEATURE_14_DAY);
        List<AdFeature> existingFeatures = new ArrayList<AdFeature>();
        existingFeatures.add(feature14Days);
        advert.setFeatures(existingFeatures);
        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);
        List<OrderItemEntity> items = new ArrayList<OrderItemEntity>();
        order.setItems(items);
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, not(nullValue()));
        assertTrue(priceMetadata.getFeatureOption(ProductType.FEATURED).isActive());
        assertFalse(priceMetadata.getFeatureOption(ProductType.SPOTLIGHT).isActive());
        assertFalse(priceMetadata.getFeatureOption(ProductType.URGENT).isActive());
    }

    @Test
    public void whenNonProUserAndAdWithPastFeatureExpiredNoNewAdd() {
        //given
        account.setPro(false);
        AdFeature feature14Days = new AdFeature();
        feature14Days.setEndDate(new DateTime().minusDays(100));
        feature14Days.setProductName(ProductName.FEATURE_14_DAY);
        List<AdFeature> existingFeatures = new ArrayList<AdFeature>();
        existingFeatures.add(feature14Days);
        advert.setFeatures(existingFeatures);
        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);
        List<OrderItemEntity> items = new ArrayList<OrderItemEntity>();
        order.setItems(items);
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, not(nullValue()));
        assertFalse(priceMetadata.getFeatureOption(ProductType.FEATURED).isActive());
        assertFalse(priceMetadata.getFeatureOption(ProductType.SPOTLIGHT).isActive());
        assertFalse(priceMetadata.getFeatureOption(ProductType.URGENT).isActive());
    }

    /**
     * Bug GTVR-246
     * <p/>
     * <p/>
     * Steps to recreate:
     * post an ad in QA4
     * include a website URL
     * view the upsell section isn't shown on the confirmation page
     * Actual result:
     * <p/>
     * Posting an ad with URL feature means feature upsell isn't shown on the confirmation page
     * Expected result:
     * <p/>
     * Posting an ad with URL feature means feature upsell is still shown on the confirmation page.
     */
    @Test
    public void whenNonProUserAndAdWithMoreThanOneOrderNoFeatured() {
        //given
        account.setPro(false);
        when(bushfireApi.accountApi().getAccount(1L)).thenReturn(account);
        when(priceApi.getPricesForCategoryIdLocationId(1L, 1L)).thenReturn(pricingMap);
        OrderItemEntity itemBumpUp = new OrderItemEntity();
        itemBumpUp.setProductName(ProductName.INSERTION);
        itemBumpUp.setAdvert(advert);
        OrderItemEntity itemBumpUpDifferentAd = new OrderItemEntity();
        itemBumpUpDifferentAd.setProductName(ProductName.WEBSITE_URL);
        itemBumpUpDifferentAd.setAdvert(advert);
        List<OrderItemEntity> items = new ArrayList<OrderItemEntity>();
        items.add(itemBumpUp);
        items.add(itemBumpUpDifferentAd);
        order.setItems(items);
        //when
        FeatureProductBuilder bean = new FeatureProductBuilder(bushfireApi, userSession, clock);
        PricingMetadata priceMetadata = bean.populateFeatureMap(order, advert);
        //then
        assertThat(priceMetadata, not(nullValue()));
    }

    private void populatePricingMap() {
        this.pricingMap = new ArrayList<ApiProductPrice>();
        this.pricingMap.add(new ApiProductPrice(1L, 2L, 3L, ProductName.HOMEPAGE_SPOTLIGHT, ""));
        this.pricingMap.add(new ApiProductPrice(4L, 5L, 6L, ProductName.BUMP_UP, ""));
        this.pricingMap.add(new ApiProductPrice(7L, 8L, 9L, ProductName.FEATURE_14_DAY, ""));
        this.pricingMap.add(new ApiProductPrice(10L, 11L, 12L, ProductName.FEATURE_3_DAY, ""));
        this.pricingMap.add(new ApiProductPrice(13L, 14L, 15L, ProductName.FEATURE_7_DAY, ""));
        this.pricingMap.add(new ApiProductPrice(16L, 17L, 18L, ProductName.URGENT, ""));
        this.pricingMap.add(new ApiProductPrice(19L, 20L, 21L, ProductName.APPVAULT_RESPONSE_MANAGER, ""));
        this.pricingMap.add(new ApiProductPrice(22L, 23L, 24L, ProductName.WEBSITE_URL, ""));
        this.pricingMap.add(new ApiProductPrice(25L, 26L, 27L, ProductName.INSERTION, ""));

    }


}
