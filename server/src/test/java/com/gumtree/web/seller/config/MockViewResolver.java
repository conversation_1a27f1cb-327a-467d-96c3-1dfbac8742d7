package com.gumtree.web.seller.config;

import org.springframework.core.PriorityOrdered;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.ViewResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;
import java.util.Map;

public class MockViewResolver implements ViewResolver, PriorityOrdered {

    @Override
    public View resolveViewName(String viewName, Locale locale) {
        return new View() {
            @Override
            public String getContentType() {
                return "text/html; charset=UTF-8";
            }

            @Override
            public void render(Map<String, ?> model, HttpServletRequest request, HttpServletResponse response) {
                response.setContentType(getContentType());
                //renders nothing
            }
        };
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE;
    }

}
