package com.gumtree.web.seller.service.user.logout;

import com.gumtree.web.cookie.MadgexCookieHelper;
import com.gumtree.web.cookie.MessageCentreCookieHelper;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.model.ManageAdsWorkspace;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class UserLogoutServiceImplTest {

    @Mock
    private MessageCentreCookieHelper messageCentreCookieHelper;
    @Mock
    private MadgexCookieHelper madgexCookieHelper;
    @Mock
    private ManageAdsWorkspace manageAdsWorkspace;
    @Mock
    private UserSession userSession;
    private UserLogoutService userLogoutService;

    @Before
    public void setUp() {
        userLogoutService = new UserLogoutServiceImpl(
                manageAdsWorkspace,
                userSession,
                messageCentreCookieHelper,
                madgexCookieHelper
        );
    }

    @Test
    public void clearsCookiesAndLogsOutSubjectWhenSubjectIsRemembered() {
        userLogoutService.logoutUser(Mockito.mock(HttpServletRequest.class), Mockito.mock(HttpServletResponse.class));

        verify(messageCentreCookieHelper)
                .removeMessageCentreCookie(any(HttpServletRequest.class), any(HttpServletResponse.class));

        verify(madgexCookieHelper)
                .removeMadgexCookie(any(HttpServletRequest.class), any(HttpServletResponse.class));
    }

    @Test
    public void clearsManageAdsFilterWhenLogsOut() {
        userLogoutService.logoutUser(Mockito.mock(HttpServletRequest.class), Mockito.mock(HttpServletResponse.class));

        verify(manageAdsWorkspace).removeFilterForm();
        verify(messageCentreCookieHelper)
                .removeMessageCentreCookie(any(HttpServletRequest.class), any(HttpServletResponse.class));
    }

    @Test
    public void logsOutUserSession() {
        userLogoutService.logoutUser(Mockito.mock(HttpServletRequest.class), Mockito.mock(HttpServletResponse.class));
        verify(userSession).logout();
    }

}
