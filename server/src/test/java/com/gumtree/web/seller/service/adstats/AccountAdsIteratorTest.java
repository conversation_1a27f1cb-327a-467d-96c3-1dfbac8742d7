package com.gumtree.web.seller.service.adstats;

import com.gumtree.api.Ad;
import com.gumtree.api.AdSearchResponse;
import com.gumtree.api.ApiAdBuilder;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.web.seller.page.manageads.model.ManageAdStatus;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.List;
import java.util.NoSuchElementException;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AccountAdsIteratorTest {

    @Mock
    private AdvertApi advertApi;

    @Test
    public void shouldSayThereIsNoNextIfThereAreNoAdsForTheAccount() {
        //given
        long accountId = 1234L;
        AdSearchResponse adSearchResponse = new AdSearchResponse();
        adSearchResponse.setTotalCount(0);
        int pageSize = 100;
        when(advertApi.search(accountId, ManageAdStatus.ACTIVE_ADS.getStatusesNames(), 1, pageSize)).thenReturn(adSearchResponse);
        //when
        AccountAdsIterator accountAdsIterator = new AccountAdsIterator(advertApi, accountId, pageSize);
        assertThat(accountAdsIterator.hasNext(), is(false));
        assertThatExceptionIsThrownWhenCallingNextOn(accountAdsIterator);
    }

    private void assertThatExceptionIsThrownWhenCallingNextOn(AccountAdsIterator accountAdsIterator) {
        try {
            accountAdsIterator.next();
            fail("NoSuchElementException is expected");
        } catch (NoSuchElementException expected) {
            //expected
        }
    }

    @Test
    public void shouldReturnAllAdsInTheFirstBatchWhenThereAreLessAdsThanPageSize() {
        //given
        long accountId = 1234L;
        AdSearchResponse adSearchResponse = new AdSearchResponse();
        adSearchResponse.setTotalCount(2);
        adSearchResponse.setPostings(new Ad[]{
                                                new ApiAdBuilder().id(1L).build(),
                                                new ApiAdBuilder().id(5L).build()
                                            });
        int pageSize = 100;
        when(advertApi.search(accountId, ManageAdStatus.ACTIVE_ADS.getStatusesNames(), 1, pageSize)).thenReturn(adSearchResponse);
        //when
        AccountAdsIterator accountAdsIterator = new AccountAdsIterator(advertApi, accountId, pageSize);
        assertThat(accountAdsIterator.hasNext(), is(true));
        List<Ad> nextBatch = accountAdsIterator.next();
        assertThat(nextBatch.get(0).getId(), is(1L));
        assertThat(nextBatch.get(1).getId(), is(5L));
        assertThat(accountAdsIterator.hasNext(), is(false));
        assertThatExceptionIsThrownWhenCallingNextOn(accountAdsIterator);
    }

    @Test
    public void shouldIterateOverAllAds() {
        //given
        long accountId = 1234L;
        int pageSize = 2;
        AdSearchResponse adSearchResponseFirstPage = new AdSearchResponse();
        adSearchResponseFirstPage.setTotalCount(5);
        adSearchResponseFirstPage.setPostings(new Ad[]{
                new ApiAdBuilder().id(1L).build(),
                new ApiAdBuilder().id(2L).build()
        });
        when(advertApi.search(accountId, ManageAdStatus.ACTIVE_ADS.getStatusesNames(), 1, pageSize)).thenReturn(adSearchResponseFirstPage);
        AdSearchResponse adSearchResponseSecondPage = new AdSearchResponse();
        adSearchResponseSecondPage.setTotalCount(5);
        adSearchResponseSecondPage.setPostings(new Ad[]{
                new ApiAdBuilder().id(21L).build(),
                new ApiAdBuilder().id(22L).build()
        });
        when(advertApi.search(accountId, ManageAdStatus.ACTIVE_ADS.getStatusesNames(), 2, pageSize)).thenReturn(adSearchResponseSecondPage);
        AdSearchResponse adSearchResponseThirdPage = new AdSearchResponse();
        adSearchResponseThirdPage.setTotalCount(5);
        adSearchResponseThirdPage.setPostings(new Ad[]{
                new ApiAdBuilder().id(31L).build()
        });
        when(advertApi.search(accountId, ManageAdStatus.ACTIVE_ADS.getStatusesNames(), 3, pageSize)).thenReturn(adSearchResponseThirdPage);
        //when
        AccountAdsIterator accountAdsIterator = new AccountAdsIterator(advertApi, accountId, pageSize);
        assertThat(accountAdsIterator.hasNext(), is(true));
        List<Ad> nextBatch = accountAdsIterator.next();
        assertThat(nextBatch.get(0).getId(), is(1L));
        assertThat(nextBatch.get(1).getId(), is(2L));
        assertThat(accountAdsIterator.hasNext(), is(true));
        nextBatch = accountAdsIterator.next();
        assertThat(nextBatch.get(0).getId(), is(21L));
        assertThat(nextBatch.get(1).getId(), is(22L));
        assertThat(accountAdsIterator.hasNext(), is(true));
        nextBatch = accountAdsIterator.next();
        assertThat(nextBatch.get(0).getId(), is(31L));
        assertThat(accountAdsIterator.hasNext(), is(false));
        assertThatExceptionIsThrownWhenCallingNextOn(accountAdsIterator);
    }

    @Test
    @Ignore
    public void shouldIterateWithoutHasNextCall() {
        //given
        long accountId = 1234L;
        int pageSize = 2;
        AdSearchResponse adSearchResponseFirstPage = new AdSearchResponse();
        adSearchResponseFirstPage.setTotalCount(5);
        adSearchResponseFirstPage.setPostings(new Ad[]{
                new ApiAdBuilder().id(1L).build(),
                new ApiAdBuilder().id(2L).build()
        });
        when(advertApi.search(accountId, ManageAdStatus.ACTIVE_ADS.getStatusesNames(), 1, pageSize)).thenReturn(adSearchResponseFirstPage);
        AdSearchResponse adSearchResponseSecondPage = new AdSearchResponse();
        adSearchResponseSecondPage.setTotalCount(5);
        adSearchResponseSecondPage.setPostings(new Ad[]{
                new ApiAdBuilder().id(21L).build(),
                new ApiAdBuilder().id(22L).build()
        });
        when(advertApi.search(accountId, ManageAdStatus.ACTIVE_ADS.getStatusesNames(), 2, pageSize)).thenReturn(adSearchResponseSecondPage);
        AdSearchResponse adSearchResponseThirdPage = new AdSearchResponse();
        adSearchResponseThirdPage.setTotalCount(5);
        adSearchResponseThirdPage.setPostings(new Ad[]{
                new ApiAdBuilder().id(31L).build()
        });
        when(advertApi.search(accountId, ManageAdStatus.ACTIVE_ADS.getStatusesNames(), 3, pageSize)).thenReturn(adSearchResponseThirdPage);
        //when
        AccountAdsIterator accountAdsIterator = new AccountAdsIterator(advertApi, accountId, pageSize);
        List<Ad> nextBatch = accountAdsIterator.next();
        assertThat(nextBatch.get(0).getId(), is(1L));
        assertThat(nextBatch.get(1).getId(), is(2L));
        nextBatch = accountAdsIterator.next();
        assertThat(nextBatch.get(0).getId(), is(21L));
        assertThat(nextBatch.get(1).getId(), is(22L));
        nextBatch = accountAdsIterator.next();
        assertThat(nextBatch.get(0).getId(), is(31L));
        assertThatExceptionIsThrownWhenCallingNextOn(accountAdsIterator);
    }

    @Test
    @Ignore
    public void shouldIterateOverAllAdsWhenAmountOfAdsIncreases() {
    }
}
