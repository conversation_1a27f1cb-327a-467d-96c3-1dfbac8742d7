package com.gumtree.web.seller.page.activation.controller;

import com.gumtree.api.Ad;
import com.gumtree.api.User;
import com.gumtree.api.client.exception.BushfireApiException;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.executor.impl.ExceptionHandlingApiCallExecutor;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.client.util.BushfireApiExceptionTranslator;
import com.gumtree.api.domain.order.OrderInvoiceEmailBean;
import com.gumtree.api.domain.user.UserMessageCodes;
import com.gumtree.config.SellerProperty;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.UserActivationRequest;
import com.gumtree.util.model.Action;
import com.gumtree.web.cookie.cutters.appbanner.AppBannerCookie;
import com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.login.controller.LoginController;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.web.seller.service.user.logout.UserLogoutService;
import com.gumtree.web.zeno.userregistration.UserActivationSuccessZenoEvent;
import com.netflix.config.ConfigurationManager;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.subject.Subject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.RedirectView;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper.Action.ACCOUNT_ACTIVATION;
import static com.gumtree.web.seller.page.NoDependenciesController.ENCRYPTED_PARAMETER_MAP_NAME;
import static com.gumtree.web.seller.page.activation.controller.ActivationPageController.ACTIVATION_KEY_PARAM;
import static com.gumtree.web.seller.page.activation.controller.ActivationPageController.NEW_REGISTRATION_PARAM;
import static com.gumtree.web.seller.page.activation.controller.ActivationPageController.USER_ID_PARAM;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;

@RunWith(MockitoJUnitRunner.class)
public class ActivationPageControllerTest extends BaseSellerControllerTest {
    @Mock
    private ApiCallResponse apiCallResponse;
    @Mock
    private UserApi userApi;
    @Mock
    private AccountApi accountApi;
    @Mock
    private OrderApi orderApi;
    @Mock
    private LoginUtils loginUtils;
    @Mock
    private AppBannerCookieHelper appBannerCookieHelper;
    @Mock
    private AppBannerCookie appBannerCookie;
    @Mock
    private UserLogoutService userLogoutService;
    @Mock
    private ParameterEncryption parameterEncryption;
    @Mock
    private UserServiceFacade userServiceFacade;

    private ActivationPageController controller;

    private User user = new User();

    @Before
    public void init() throws Exception {
        initMocks(this);

        when(urlScheme.urlFor(any(Action.class))).thenReturn("-");
        when(urlScheme.urlFor(any(Ad.class))).thenReturn("-");

        when(bushfireApi.create(UserApi.class)).thenReturn(userApi);
        when(bushfireApi.userApi()).thenReturn(userApi);
        when(bushfireApi.accountApi()).thenReturn(accountApi);
        when(bushfireApi.orderApi()).thenReturn(orderApi);

        apiCallExecutor = new ExceptionHandlingApiCallExecutor(bushfireApi, new BushfireApiExceptionTranslator());

        when(messageResolver.getMessage(anyString(), anyString())).thenReturn("abc");
        when(userApi.getUser("user1")).thenReturn(user);

        controller = new ActivationPageController(cookieResolver, categoryModel, apiCallExecutor, messageResolver,
                urlScheme, bushfireApi, loginUtils, zenoService, userSessionService,
                new SellerProperty.RecaptchaEnabled(false), userLogoutService, userServiceFacade, parameterEncryption);
        when(appBannerCookieHelper.updateAppBannerCookie(ACCOUNT_ACTIVATION, request)).thenReturn(appBannerCookie);
        autowireAbExperimentsService(controller);

        Properties properties = new Properties();
        properties.setProperty("gumtree.host", "http://localhost:8080");
        properties.setProperty("gumtree.host.newgumtree", "http://localhostnew:8080");
        ConfigurationManager.loadProperties(properties);
    }

    @Test
    public void testActivatesWhenEverythingOk() throws IOException {
        // and
        when(userServiceFacade.activate(any(UserActivationRequest.class))).thenReturn(ApiResponse.of(true));
        when(request.getRequestURI()).thenReturn("https://zoidberg.gumtree.io");
        when(request.getParameter(anyString())).thenReturn("true");

        //When wy try to activate the user
        ModelAndView view = requestPageAndFollowRedirect("user1", "key1");

        //Then user has been activated
        verify(userServiceFacade).activate(any(UserActivationRequest.class));

        //And username has been stored in the session
        verify(loginUtils, times(1)).storeNewUserEmailAddressInSession("user1");

        //And login page has been shown
        assertThat(((RedirectView) view.getView()).getUrl(), containsString("login"));
        assertThat(((RedirectView) view.getView()).getUrl(), containsString("cb"));
        assertThat(((RedirectView) view.getView()).getUrl(), containsString("new-registration"));

        //And proper GA event has been sent
        String actualUrl = ((RedirectView) view.getView()).getUrl();
        assertThat(actualUrl, containsString("login"));
        assertThat(actualUrl, containsString("cb"));
        assertThat(actualUrl, containsString("new-registration"));
        verify(zenoService, times(1)).logEvent(new UserActivationSuccessZenoEvent(user));
    }

    @Test
    public void testRedirectsToLoginWhenUserIsAlreadyActivated() throws IOException {
        //Given we have already active user
        User user = new User();
        user.setStatus(UserStatus.ACTIVE);
        when(bushfireApi.userApi().getUser("user1")).thenReturn(user);

        //When we try to activate the user
        ModelAndView view = requestPageAndFollowRedirect("user1", "key1");

        //the user is redirected to the login page
        assertThat(((RedirectView) view.getView()).getUrl(), equalTo(LoginController.PAGE_PATH));
        verify(userServiceFacade, never()).activate(any(UserActivationRequest.class));
    }

    @Test
    public void testRedirectsToHomepageWhenUserIsAlreadyActivatedAndLoggedIn() throws Exception {
        //Given we have already active user
        User user = new User();
        user.setStatus(UserStatus.ACTIVE);
        when(bushfireApi.userApi().getUser("user1")).thenReturn(user);
        //and the user is logged in
        userIsLoggedIn();

        //When we try to activate the user
        ModelAndView view = requestPageAndFollowRedirect("user1", "key1");

        //the user is redirected to the home page
        assertThat(((RedirectView) view.getView()).getUrl(), equalTo("/"));
        verify(userServiceFacade, never()).activate(any(UserActivationRequest.class));
    }

    @Test
    public void testLogsOutCurrentUserAndAShowsActivationSuccessPage() throws Exception {
        when(request.getRequestURI()).thenReturn("https://zoidberg.gumtree.io");
        when(request.getParameter(anyString())).thenReturn("true");
        //Given we have a user awaiting activation
        User user = new User();
        user.setStatus(UserStatus.AWAITING_ACTIVATION);
        when(bushfireApi.userApi().getUser("user1")).thenReturn(user);
        //and the user is logged in
        userIsLoggedIn();
        //and the user activates successfully
        when(userServiceFacade.activate(any(UserActivationRequest.class))).thenReturn(ApiResponse.of(true));

        //When we try to activate the user
        ModelAndView view = requestPageAndFollowRedirect("user1", "key1");

        //Then the current logged in user is logged out
        verify(userLogoutService).logoutUser(request, response);

        //and the new user is activated
        verify(userServiceFacade).activate(any(UserActivationRequest.class));

        //and the user is shown the activation success page with no logged in user
        String actualUrl = ((RedirectView) view.getView()).getUrl();
        assertThat(actualUrl, containsString("login"));
        assertThat(actualUrl, containsString("cb"));
        assertThat(actualUrl, containsString("new-registration"));

    }

    @Test
    public void testAttemptsActivationWhenUserIsNotAlreadyActivated() throws Exception {
        // And
        when(request.getRequestURI()).thenReturn("https://zoidberg.gumtree.io");
        when(request.getParameter(anyString())).thenReturn("true");

        //Given we have an user awaiting activation
        User user = new User();
        user.setStatus(UserStatus.AWAITING_ACTIVATION);
        when(bushfireApi.userApi().getUser("user1")).thenReturn(user);

        //and user service activates user
        when(userServiceFacade.activate(any(UserActivationRequest.class))).thenReturn(ApiResponse.of(true));

        //When we try to activate the user
        requestPageAndFollowRedirect("user1", "key1");

        //the user is redirected to the login page
        verify(userServiceFacade).activate(any(UserActivationRequest.class));
    }

    @Test
    public void testFailurePageWhenActivationFails() throws IOException {
        //Given the service doesn't work
        doThrow(new BushfireApiException(300)).when(userApi).newStatus(any(), any());

        //and a user is awaiting activation
        User user = new User();
        user.setStatus(UserStatus.AWAITING_ACTIVATION);
        when(userApi.getUser("<EMAIL>")).thenReturn(user);

        ApiResponse activationResp = mock(ApiResponse.class);
        when(activationResp.isDefined()).thenReturn(false);
        when(userServiceFacade.activate(any(UserActivationRequest.class))).thenReturn(activationResp);

        //When we try to activate user
        View view = requestPageAndFollowRedirect("<EMAIL>", "key1").getView();

        //Then
        validateRedirectedToFailurePage(view);
    }

    @Test
    public void testExceptionWhenNoUsernameGiven() {
        Map<String, String> params = new HashMap<>();
        params.put(USER_ID_PARAM, null);
        params.put(ACTIVATION_KEY_PARAM, "key1");

        View result = controller.handleRequest(params).getView();

        //Then
        verifyZeroInteractions(userApi);

        //And
        validateRedirectedToFailurePage(result);
    }

    @Test
    public void testExceptionWhenNoActivationKeyGiven() {
        Map<String, String> params = new HashMap<>();
        params.put(USER_ID_PARAM, "user1");
        params.put(ACTIVATION_KEY_PARAM, null);

        View result = controller.handleRequest(params).getView();

        //Then
        verifyZeroInteractions(userApi);

        //And
        validateRedirectedToFailurePage(result);
    }

    @Test
    public void testExceptionWhenNoEncryptedMapParameter() {
        Map<String, String> params = new HashMap<>();
        params.put(USER_ID_PARAM, "user1");
        params.put(ACTIVATION_KEY_PARAM, null);

        View result = controller.activateUser(request, response).getView();

        //Then
        verifyZeroInteractions(userApi);

        //And
        validateRedirectedToFailurePage(result);
    }

    @Test
    public void testGoesToSuccessPageWhenUserHasAdvert() throws IOException {
        // And
        when(request.getRequestURI()).thenReturn("https://zoidberg.gumtree.io");
        when(request.getParameter(anyString())).thenReturn("true");

        //Given
        givenUserHasAnAdvert();

        // and
        when(userServiceFacade.activate(any(UserActivationRequest.class))).thenReturn(ApiResponse.of(true));

        //When
        ModelAndView view = requestPageAndFollowRedirect("user1", "key1");

        //Then we are redirected to the login page
        String actualUrl = ((RedirectView) view.getView()).getUrl();
        assertThat(actualUrl, containsString("login"));
        assertThat(actualUrl, containsString("cb"));
        assertThat(actualUrl, containsString("new-registration"));

    }

    @Test
    public void testDoesNotSendInvoiceEmailWhenAdIsPutLiveButUserAlreadyActive() throws IOException {
        when(request.getRequestURI()).thenReturn("https://zoidberg.gumtree.io");
        when(request.getParameter(anyString())).thenReturn("true");
        //Given
        givenUserHasAnAdvert();

        ApiResponse<Boolean> activationResp = mock(ApiResponse.class);
        when(activationResp.isDefined()).thenReturn(true);
        when(userServiceFacade.activate(any(UserActivationRequest.class))).thenReturn(activationResp);

        //And user is already active
        when(apiCallResponse.isErrorResponse()).thenReturn(true);
        when(apiCallResponse.containsMessageCode(UserMessageCodes.Activation.USER_ALREADY_ACTIVE)).thenReturn(true);

        //When
        requestPageAndFollowRedirect("user1", "key1");

        //Then
        verify(orderApi, never()).sendInvoiceEmail(Matchers.<OrderInvoiceEmailBean>any());
    }

    @Test
    public void testAddsCorrectObjectsToModelWhenAdIsPutLive() throws IOException {
        when(request.getRequestURI()).thenReturn("https://zoidberg.gumtree.io");
        when(request.getParameter(anyString())).thenReturn("true");
        //Given as user has an advert
        givenUserHasAnAdvert();

        // and
        when(userServiceFacade.activate(any(UserActivationRequest.class))).thenReturn(ApiResponse.of(true));

        //When
        ModelAndView view = requestPageAndFollowRedirect("user1", "key1");

        //Then login page contains recently created ad
        String actualUrl = ((RedirectView) view.getView()).getUrl();
        assertThat(actualUrl, containsString("login"));
        assertThat(actualUrl, containsString("cb"));
        assertThat(actualUrl, containsString("new-registration"));
    }

    private void givenUserHasAnAdvert() {
        Ad ad = new Ad();
        ad.setId(1L);
        ad.setTitle("Hello");
        when(accountApi.getAdverts(anyLong())).thenReturn(Arrays.asList(ad));
    }

    private void userIsLoggedIn() {
        SecurityManager securityManager = mock(SecurityManager.class);
        SecurityUtils.setSecurityManager(securityManager);
        Subject subject = mock(Subject.class);
        when(subject.isAuthenticated()).thenReturn(true);
        when(securityManager.createSubject(any())).thenReturn(subject);
    }

    private ModelAndView requestPageAndFollowRedirect(String username, String activationKey) {
        Map<String, String> params = new HashMap<>();
        params.put(USER_ID_PARAM, username);
        params.put(ACTIVATION_KEY_PARAM, activationKey);
        params.put(NEW_REGISTRATION_PARAM, "true");
        params.put("utm_source", "source");
        params.put("utm_medium", "medium");
        params.put("utm_campaign", "campaign");

        Map<String, String> obfuscatedParameters = new HashMap<>();
        obfuscatedParameters.put(USER_ID_PARAM, username);
        obfuscatedParameters.put(ACTIVATION_KEY_PARAM, activationKey);
        obfuscatedParameters.put(NEW_REGISTRATION_PARAM, "true");
        String encrypted = "encrypted..";
        when(parameterEncryption.encryptMapAndUrlEncode(obfuscatedParameters)).thenReturn(encrypted);
        when(parameterEncryption.decryptUrlEncodedParameterMap(encrypted)).thenReturn(obfuscatedParameters);
        when(request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME)).thenReturn(encrypted);

        ModelAndView result = controller.handleRequest(params);

        //and following HTTP redirect
        RedirectView view = (RedirectView) result.getView();
        assertThat(view.getUrl(), equalTo("/activate-user/result"));
        assertThat(result.getModelMap().size(), equalTo(4));
        assertThat(result.getModelMap().get("utm_source"), equalTo("source"));
        assertThat(result.getModelMap().get("utm_medium"), equalTo("medium"));
        assertThat(result.getModelMap().get("utm_campaign"), equalTo("campaign"));
        assertThat(result.getModelMap().get(ENCRYPTED_PARAMETER_MAP_NAME), equalTo("encrypted.."));

        return controller.activateUser(request, response);
    }

    private void validateRedirectedToFailurePage(View view) {
        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo(ActivationFailureController.PAGE_PATH));
    }
}
