package com.gumtree.web.seller.security.apiauthentication.controller;

import com.gumtree.api.User;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.exception.UserNotRecognisedException;
import com.gumtree.web.seller.security.apiauthentication.JsonWebTokenService;
import com.gumtree.web.seller.security.apiauthentication.controller.ApiAuthenticationController;
import com.nimbusds.jose.JOSEException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.when;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class ApiAuthenticationControllerTest {

    @Mock
    private UserSession userSession;
    @Mock
    private JsonWebTokenService tokenService;
    @InjectMocks
    private ApiAuthenticationController controller;

    @Test
    public void returnJWTSuccess() throws Exception {
        //given
        String token = "jwt";
        when(userSession.getUser()).thenReturn(new User());
        when(tokenService.generateTokenForUser(anyObject())).thenReturn(token);

        //when
        ResponseEntity<String> response = controller.authenticateApi();

        //then
        assertEquals(token, response.getBody());
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void return403WhenAnErrorOccurredWhileCreatingTheToken() throws Exception {
        //given
        User user = new User();
        when(userSession.getUser()).thenReturn(user);
        when(tokenService.generateTokenForUser(user)).thenThrow(new RuntimeException("Error"));

        //when
        ResponseEntity<String> response = controller.authenticateApi();

        //then
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
    }
}
