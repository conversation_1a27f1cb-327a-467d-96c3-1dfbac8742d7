package com.gumtree.web.seller.page.ajax.pets;

import com.gumtree.config.DefaultPropertyInitializer;
import com.gumtree.web.seller.mvctests.SellerMvcTest;
import com.jayway.jsonpath.JsonPath;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(initializers = DefaultPropertyInitializer.class)
public class PetsDateControllerTest extends SellerMvcTest {
    private static final String BIRDS_CATEGORY_ID = "9392";

    @Autowired
    private PetsDateController controller;

    @Before
    public void beforeEach() {
        super.beforeEach();
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    public void shouldReturn8ItemsStartingFromNowWhenNoDOBProvided() throws Exception {
        // when
        ResultActions result = mockMvc.perform(get("/api/pets/ready-to-leave-options/"+BIRDS_CATEGORY_ID));

        // then
        result.andExpect(status().isOk());
        result.andExpect(content().contentType("application/json;charset=UTF-8"));

        String contentAsString = result.andReturn().getResponse().getContentAsString();

        JsonPath.read(contentAsString, "$..value");

        // and
        result.andExpect(jsonPath("$..displayValue", hasSize(16)));
        result.andExpect(jsonPath("$..value", hasSize(16)));

        result.andExpect(jsonPath("$..value[0]", is(calculateDateInTheFuture(0))));
        result.andExpect(jsonPath("$..displayValue[0]", is("Now")));

        result.andExpect(jsonPath("$..value[1]", is(calculateDateInTheFuture(1))));
        result.andExpect(jsonPath("$..displayValue[1]", is("in 1 week")));

        result.andExpect(jsonPath("$..value[7]", is(calculateDateInTheFuture(7))));
        result.andExpect(jsonPath("$..displayValue[7]", is("in 7 weeks")));
    }

    @Test
    public void shouldReturn8ItemsStartingIn2WeeksForProvidedDOB6WeeksAgo() throws Exception {
        // when
        ResultActions result = mockMvc.perform(get("/api/pets/ready-to-leave-options/"+BIRDS_CATEGORY_ID+"?dob="+ calculateDateInThePast(6)));

        // then
        result.andExpect(status().isOk());
        result.andExpect(content().contentType("application/json;charset=UTF-8"));

        String contentAsString = result.andReturn().getResponse().getContentAsString();

        JsonPath.read(contentAsString, "$..value");

        // and
        result.andExpect(jsonPath("$..displayValue", hasSize(14)));
        result.andExpect(jsonPath("$..value", hasSize(14)));

        result.andExpect(jsonPath("$..value[0]", is(calculateDateInTheFuture(2))));
        result.andExpect(jsonPath("$..displayValue[0]", is("in 2 weeks")));

        result.andExpect(jsonPath("$..value[1]", is(calculateDateInTheFuture(3))));
        result.andExpect(jsonPath("$..displayValue[1]", is("in 3 weeks")));

        result.andExpect(jsonPath("$..value[7]", is(calculateDateInTheFuture(9))));
        result.andExpect(jsonPath("$..displayValue[7]", is("in 9 weeks")));
    }

    private String calculateDateInTheFuture(int numberOfWeeks){
        return DateTimeFormat.forPattern("dd/MM/yyyy").print(LocalDate.now().plusWeeks(numberOfWeeks));
    }

    private String calculateDateInThePast(int numberOfWeeks){
        return DateTimeFormat.forPattern("dd/MM/yyyy").print(LocalDate.now().minusWeeks(numberOfWeeks));
    }
}