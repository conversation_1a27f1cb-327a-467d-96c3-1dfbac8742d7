package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import org.junit.Before;
import org.junit.Test;

import javax.validation.ConstraintValidatorContext;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

public class DescriptionLengthValidatorTest {

    private static final String ERROR_MESSAGE = "Please use a description longer than {0} characters";

    private static final int MINIMUM_CHARS = 15;

    private DescriptionLengthValidator validator;

    private DescriptionLengthValidation annotation;

    private ConstraintValidatorContext context;

    @Before
    public void setUp() {

        ErrorMessageResolver errorMsgResolver = mock(ErrorMessageResolver.class);
        when(errorMsgResolver.getMessage("gumtree.postad.validation.required_chars", "", MINIMUM_CHARS)).thenReturn(ERROR_MESSAGE.replace("{0}", Integer.toString(MINIMUM_CHARS)));

        annotation = mock(DescriptionLengthValidation.class);
        context = mock(ConstraintValidatorContext.class);
        doNothing().when(context).disableDefaultConstraintViolation();
        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.class);
        ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext nodeContext =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext.class);

        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(builder);
        when(builder.addPropertyNode(anyString())).thenReturn(nodeContext);
        when(builder.addConstraintViolation()).thenReturn(context);

        when(annotation.fieldList()).thenReturn(new String[] {"description"});
        when(annotation.message()).thenReturn("gumtree.postad.validation.required_chars");

        validator = new DescriptionLengthValidator(errorMsgResolver);
        validator.initialize(annotation);
    }


    @Test
    public void testIsValidWithRequiredChars() {
        PostAdDetail detail = createTestObject("This is a valid test description");
        boolean result = validator.isValid(detail, context);
        assertTrue(result);
    }

    @Test
    public void testIsInvalidWithLessThanRequiredChars() {
        PostAdDetail detail = createTestObject("Too short");
        boolean result = validator.isValid(detail, context);
        assertFalse(result);
        verify(context).buildConstraintViolationWithTemplate("Please use a description longer than 15 characters");
    }

    @Test
    public void testIsInvalidSurroundedByfSpaces() {
        PostAdDetail detail = createTestObject("        Fail          ");
        boolean result = validator.isValid(detail, context);
        assertFalse(result);
        verify(context).buildConstraintViolationWithTemplate("Please use a description longer than 15 characters");
    }


    private PostAdDetail createTestObject(String description) {
        PostAdDetail detail = new PostAdDetail();
        PostAdFormBean bean = new PostAdFormBean();
        bean.setDescription(description);
        detail.setPostAdFormBean(bean);
        return detail;
    }
}
