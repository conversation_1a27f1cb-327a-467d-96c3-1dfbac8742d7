package com.gumtree.web.seller.page.motors;

import com.google.common.collect.Lists;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.motors.webapi.service.MotorsService;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.StandardisedVehicleDataResponse;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.VehicleAttribute;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.ajax.vrm.MotorsApiClient;
import com.gumtree.web.seller.page.common.model.GaElement;
import com.gumtree.web.seller.page.motors.model.VehicleVerificationModel;
import com.gumtree.web.seller.service.postad.priceguidance.PriceGuidanceService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;
import java.util.Collections;
import java.util.Optional;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class VehicleVerificationControllerTest extends BaseSellerControllerTest {
    private MotorsApiClient motorsApiClient = null;
    private VehicleVerificationController vehicleVerificationController = null;
    private PriceGuidanceService priceGuidanceService;
    private CategoryModel categoryModelMock;
    private static final String VRN_ATTRIBUTE = "vrn";
    private static final String VRN_VALUE = "VRN123";

    @Before
    public void setup() throws Exception {
        motorsApiClient = mock(MotorsApiClient.class);
        categoryModelMock = mock(CategoryModel.class);

        // Given
        VehicleAttribute vrm = new VehicleAttribute().name(VRN_ATTRIBUTE).value(VRN_VALUE);

        AttributeMetadata vrmMeta = attributeMetadata(VRN_ATTRIBUTE);

        when(categoryModelMock.getCategoryAttributes(9311))
                .thenReturn(com.google.common.base.Optional.of(Lists.newArrayList(vrmMeta)));

        MotorsService motorsService = new MotorsService(motorsApiClient,
                categoryModelMock,
                bushfireApi,
                null,
                null);


        vehicleVerificationController = new VehicleVerificationController(motorsService,
                cookieResolver,
                categoryModelMock,
                apiCallExecutor,
                messageResolver,
                urlScheme,
                userSessionService);

        autowireAbExperimentsService(vehicleVerificationController);
    }

    @Test
    public void shouldReturnTheRightAttributes() throws Exception {
        VehicleAttribute vehicleAttribute = new VehicleAttribute();
        vehicleAttribute.setName(VRN_ATTRIBUTE);
        vehicleAttribute.setValue(VRN_VALUE);

        StandardisedVehicleDataResponse standardisedVehicleDataResponse = new StandardisedVehicleDataResponse();

        standardisedVehicleDataResponse.setAttributes(Collections.singletonList(vehicleAttribute));
        standardisedVehicleDataResponse.setCategoryId(9311);

        when(motorsApiClient.lookupVehicleData(VRN_VALUE, 9311L)).thenReturn(Optional.of(standardisedVehicleDataResponse));

        ModelAndView result = vehicleVerificationController.verify(9311L , VRN_VALUE, request);

        VehicleVerificationModel model = (VehicleVerificationModel)result.getModel().get("model");

        assertThat(model.getAttributeValueForAttributeName("vrn"), equalTo(VRN_VALUE));
    }

    // Since We do NOT mock Motors Service here, we are verifying the invocation of services from within the Motors Service
    @Test
    public void shouldCallMotorsApiFromWithinMotorsServiceForLookUp() throws Exception {
        VehicleAttribute vehicleAttribute = new VehicleAttribute();
        vehicleAttribute.setName(VRN_ATTRIBUTE);
        vehicleAttribute.setValue(VRN_VALUE);

        StandardisedVehicleDataResponse standardisedVehicleDataResponse = new StandardisedVehicleDataResponse();

        standardisedVehicleDataResponse.setAttributes(Collections.singletonList(vehicleAttribute));
        standardisedVehicleDataResponse.setCategoryId(9311);

        when(motorsApiClient.lookupVehicleData(VRN_VALUE, 9311L)).thenReturn(Optional.of(standardisedVehicleDataResponse));

        ModelAndView result = vehicleVerificationController.verify(9311L , VRN_VALUE, request);
        verify(motorsApiClient, times(1)).lookupVehicleData(VRN_VALUE, 9311L);
    }

    // Since We do NOT mock Motors Service here, we are verifying the invocation of services from within the Motors Service
    @Test
    public void shouldCallCategoryModelFromWithinMotorsServiceForLookUp() throws Exception {
        VehicleAttribute vehicleAttribute = new VehicleAttribute();
        vehicleAttribute.setName(VRN_ATTRIBUTE);
        vehicleAttribute.setValue(VRN_VALUE);

        StandardisedVehicleDataResponse standardisedVehicleDataResponse = new StandardisedVehicleDataResponse();

        standardisedVehicleDataResponse.setAttributes(Collections.singletonList(vehicleAttribute));
        standardisedVehicleDataResponse.setCategoryId(9311);

        when(motorsApiClient.lookupVehicleData(VRN_VALUE, 9311L)).thenReturn(Optional.of(standardisedVehicleDataResponse));

        ModelAndView result = vehicleVerificationController.verify(9311L , VRN_VALUE, request);
        verify(categoryModelMock, times(1)).getCategoryAttributes(9311);
    }

    // GaElements
    @Test
    public void shouldHaveRightMembersForGaEventsInTheModelIfVrnFound() throws Exception {
        VehicleAttribute vehicleAttribute = new VehicleAttribute();
        vehicleAttribute.setName(VRN_ATTRIBUTE);
        vehicleAttribute.setValue(VRN_VALUE);

        StandardisedVehicleDataResponse standardisedVehicleDataResponse = new StandardisedVehicleDataResponse();

        standardisedVehicleDataResponse.setAttributes(Collections.singletonList(vehicleAttribute));
        standardisedVehicleDataResponse.setCategoryId(9311);

        when(motorsApiClient.lookupVehicleData(VRN_VALUE, 9311L)).thenReturn(Optional.of(standardisedVehicleDataResponse));

        ModelAndView result = vehicleVerificationController.verify(9311L , VRN_VALUE, request);

        VehicleVerificationModel model = (VehicleVerificationModel)result.getModel().get("model");
        GaElement gaEventElement = model.getCore().getGaEventElements().get(0);
        assertThat(gaEventElement.getAction(), equalTo("VRNFindSuccess"));
        assertThat(gaEventElement.getLabel(), equalTo("VehicleValidationPage"));
    }

    @Test
    public void shouldHaveRightMembersForGaEventsInTheModelIfVrnNotFound() throws Exception {
        VehicleAttribute vehicleAttribute = new VehicleAttribute();
        vehicleAttribute.setName(VRN_ATTRIBUTE);
        vehicleAttribute.setValue(VRN_VALUE);

        StandardisedVehicleDataResponse standardisedVehicleDataResponse = new StandardisedVehicleDataResponse();

        standardisedVehicleDataResponse.setAttributes(Collections.singletonList(vehicleAttribute));
        standardisedVehicleDataResponse.setCategoryId(9311);

        when(motorsApiClient.lookupVehicleData(VRN_VALUE, 9311L)).thenReturn(Optional.empty());

        ModelAndView result = vehicleVerificationController.verify(9311L , VRN_VALUE, request);

        VehicleVerificationModel model = (VehicleVerificationModel)result.getModel().get("model");
        GaElement gaEventElement = model.getCore().getGaEventElements().get(0);
        assertThat(gaEventElement.getAction(), equalTo("VRNFindFail"));
        assertThat(gaEventElement.getLabel(), equalTo("VehicleValidationPage"));
    }

    private AttributeMetadata attributeMetadata(String name) {
        AttributeMetadata vehicleMakeMeta = new AttributeMetadata();
        vehicleMakeMeta.setName(name);
        vehicleMakeMeta.setType(AttributeType.ENUM);
        return vehicleMakeMeta;
    }
}
