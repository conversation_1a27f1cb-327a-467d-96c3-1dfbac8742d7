package com.gumtree.web.seller.service.adstats;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.ApiAdBuilder;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.seller.page.manageads.model.ManageAdStatus;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounters;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.HashMap;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AdvertStatisticDataFactoryTest {

    @Mock
    private UrlScheme urlScheme;

    @Test
    public void shouldConvertAdvertDetails() {
        //given
        Ad advert = new ApiAdBuilder()
                        .with()
                        .id(663388L)
                        .title("expected ad title 89")
                        .repliesEmail("<EMAIL>")
                        .bumpUpCount(73)
                        .creationDate(new DateTime(2938475610L))
                        .lastModifiedDate(new DateTime(1234567892L))
                        .liveDate(new DateTime(5678929968L))
                        .status(AdStatus.NEEDS_EDITING)
                        .build();
        //when
        AdvertStatisticDataFactory builder = new AdvertStatisticDataFactory(new HashMap<Long, AdCounters>(), urlScheme);
        AdvertStatisticData advertStatisticData = builder.forAdvert(advert);
        //then
        assertThat(advertStatisticData.getAdvertId(), is("663388"));
        assertThat(advertStatisticData.getAdvertStatus(), is(ManageAdStatus.NEEDS_EDITING));
        assertThat(advertStatisticData.getAdvertTitle(), is("expected ad title 89"));
        assertThat(advertStatisticData.getContactEmail(), is("<EMAIL>"));
        assertThat(advertStatisticData.getLastModifiedDate(), is(new DateTime(1234567892L)));
        assertThat(advertStatisticData.getLastPostedDate(), is(new DateTime(5678929968L)));
        assertThat(advertStatisticData.getCreationDate(), is(new DateTime(2938475610L)));
        assertThat(advertStatisticData.getNumberOfTimesReposted(), is(73L));
    }

    @Test
    public void shouldGenerateAdvertVIPUrl() {
        //given
        Ad advert = new ApiAdBuilder()
                        .with()
                        .id(663388L)
                        .title("ad title 89")
                        .repliesEmail("<EMAIL>")
                        .bumpUpCount(73)
                        .creationDate(new DateTime(2938475610L))
                        .lastModifiedDate(new DateTime(1234567892L))
                        .liveDate(new DateTime(5678929968L))
                        .status(AdStatus.NEEDS_EDITING)
                        .build();
        when(urlScheme.urlFor(advert)).thenReturn("/expected-url");
        //when
        AdvertStatisticDataFactory builder = new AdvertStatisticDataFactory(new HashMap<Long, AdCounters>(), urlScheme);
        AdvertStatisticData advertStatisticData = builder.forAdvert(advert);
        //then
        assertThat(advertStatisticData.getAdvertVIPUrl(), is("/expected-url"));
    }

    @Test
    public void shouldConvertAwaitingCsReviewAdStatus() {
        //given
        Ad advert = new ApiAdBuilder()
                        .with()
                        .id(663388L)
                        .title("ad title 89")
                        .repliesEmail("<EMAIL>")
                        .bumpUpCount(73)
                        .creationDate(new DateTime(2938475610L))
                        .lastModifiedDate(new DateTime(1234567892L))
                        .liveDate(new DateTime(5678929968L))
                        .status(AdStatus.AWAITING_CS_REVIEW)
                        .build();
        when(urlScheme.urlFor(advert)).thenReturn("/expected-url");
        //when
        AdvertStatisticDataFactory builder = new AdvertStatisticDataFactory(new HashMap<Long, AdCounters>(), urlScheme);
        AdvertStatisticData advertStatisticData = builder.forAdvert(advert);
        //then
        assertThat(advertStatisticData.getAdvertStatus(), is(ManageAdStatus.PROCESSING));
    }

    @Test
    public void shouldConvertAdvertStats() {
        //given
        Ad advert = anAdvertWithId(663388L);
        AdCounters advertStatsCounts = AdCounters.builder()
                .setAdvertId(663388L)
                .setRepliesCount(7261)
                .setSearchImpressionCounter(988)
                .setViewsCounter(123)
                .build();

        HashMap<Long, AdCounters> advertStats = new HashMap<Long, AdCounters>();
        advertStats.put(663388L, advertStatsCounts);
        //when
        AdvertStatisticDataFactory factory = new AdvertStatisticDataFactory(advertStats, urlScheme);
        AdvertStatisticData advertStatisticData = factory.forAdvert(advert);
        //then
        assertThat(advertStatisticData.getSRPViews(), is(988));
        assertThat(advertStatisticData.getVIPViews(), is(123));
        assertThat(advertStatisticData.getNumberOfReplies(), is(7261));
    }

    private Ad anAdvertWithId(long advertId) {
        return new ApiAdBuilder()
                        .with()
                        .id(advertId)
                        .title("expected ad title 89")
                        .status(AdStatus.LIVE)
                        .build();
    }
}
