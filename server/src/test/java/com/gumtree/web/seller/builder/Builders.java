package com.gumtree.web.seller.builder;

/**
 * Builders to use in stubbed tests
 */
public abstract class Builders {

    /**
     * Private constructor
     */
    private Builders() {
    }

    /**
     * account builder
     * @return account builder
     */
    public static AccountBuilder account() {
        return new AccountBuilder();
    }

    /**
     * account builder
     * @param id account id
     * @return account builder
     */
    public static AccountBuilder account(long id) {
        return new AccountBuilder().id(id);
    }

    /**
     * user builder
     * @param email the user emial address
     * @return user builder
     */
    public static UserBuilder user(String email) {
        return new UserBuilder().email(email);
    }
}
