package com.gumtree.web.seller.security.apiauthentication;

import com.gumtree.common.properties.GtPropManager;
import com.gumtree.config.MwebProperty;
import com.gumtree.web.seller.security.ParameterEncryption;
import org.junit.Before;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static org.fest.assertions.api.Assertions.assertThat;

public class CSRFTokenServiceTest {

    private static final String SHARED_SECRET = "Kxj38gTS6crvdRGKbZBkN3dUSrz3Cr8P";
    private static final String ISSUER = "https://seller.dev.gumtree.com";
    private static final String ENCRYPTION_KEY = "pbfO1tAjR1zDyxG2cKDz9g==";

    private ParameterEncryption parameterEncryption;

    private JsonWebTokenProperties jsonWebTokenProperties;


    @Before
    public void setup() throws Exception {
        String privateKey = readStringFromFile();
        jsonWebTokenProperties = new JsonWebTokenProperties(ISSUER, privateKey, 2, 900, SHARED_SECRET);
        parameterEncryption = new ParameterEncryption(ENCRYPTION_KEY);
    }

    @Test
    public void shouldVerifyTokenWhenCompilesWithNotBeforeAndExpiryCriteria() throws Exception {
        // given
        setupValidTimeWindow();

        // and
        CSRFTokenService csrfTokenService = new CSRFTokenService(jsonWebTokenProperties, parameterEncryption);
        Map<String, Object> map = new HashMap<>();
        String key = "email";
        String value = "<EMAIL>";
        map.put(key, value);
        String token = csrfTokenService.generateToken(map);

        // when
        TimeSensitiveResponse<Object> tsr = csrfTokenService.verifyTokenSignatureAndTimeConstraints(token);

        // then
        assertThat(tsr.isValid()).isTrue();
        assertThat(tsr.getClaims().isPresent());
        assertThat(tsr.getClaims().get().get(key)).isEqualTo(value);
    }

    @Test
    public void shouldFailToVerifyTokenWhenCompilesWhenNotBefore() throws Exception {
        // given
        int expireInSeconds = 20;
        int notBeforeDelayInSeconds = 10;
        GtPropManager.setProperty(MwebProperty.TOKEN_VALID_NOT_BEFORE_SECONDS.getPropertyName(), notBeforeDelayInSeconds);
        GtPropManager.setProperty(MwebProperty.TOKEN_VALID_NOT_AFTER_SECONDS.getPropertyName(), expireInSeconds);

        // and
        CSRFTokenService csrfTokenService = new CSRFTokenService(jsonWebTokenProperties, parameterEncryption);
        Map<String, Object> map = new HashMap<>();
        String token = csrfTokenService.generateToken(map);

        // when
        TimeSensitiveResponse<Object> tsr = csrfTokenService.verifyTokenSignatureAndTimeConstraints(token);

        // then
        assertThat(tsr.isValid()).isFalse();
        assertThat(tsr.getData()).isNull();
    }

    @Test
    public void shouldFailToVerifyTokenWhenExpired() throws Exception {
        // given
        int expireInSeconds = 0;
        int notBeforeDelayInSeconds = 0;
        GtPropManager.setProperty(MwebProperty.TOKEN_VALID_NOT_BEFORE_SECONDS.getPropertyName(), notBeforeDelayInSeconds);
        GtPropManager.setProperty(MwebProperty.TOKEN_VALID_NOT_AFTER_SECONDS.getPropertyName(), expireInSeconds);

        // and
        CSRFTokenService csrfTokenService = new CSRFTokenService(jsonWebTokenProperties, parameterEncryption);
        Map<String, Object> map = new HashMap<>();
        String token = csrfTokenService.generateToken(map);

        // when
        TimeSensitiveResponse<Object> tsr = csrfTokenService.verifyTokenSignatureAndTimeConstraints(token);

        // then
        assertThat(tsr.isValid()).isFalse();
        assertThat(tsr.isExpired()).isTrue();

    }

    @Test
    public void shouldFailToVerifyNullToken() throws Exception {
        // given
        int expireInSeconds = 20;
        int notBeforeDelayInSeconds = 10;
        GtPropManager.setProperty(MwebProperty.TOKEN_VALID_NOT_BEFORE_SECONDS.getPropertyName(), notBeforeDelayInSeconds);
        GtPropManager.setProperty(MwebProperty.TOKEN_VALID_NOT_AFTER_SECONDS.getPropertyName(), expireInSeconds);

        // and
        CSRFTokenService csrfTokenService = new CSRFTokenService(jsonWebTokenProperties, parameterEncryption);

        // when
        TimeSensitiveResponse<Object> tsr = csrfTokenService.verifyCsrfTokenForEmail(null, "<EMAIL>");

        // then
        assertThat(tsr.isValid()).isFalse();
    }

    @Test
    public void shouldCreateCsrfTokenForEmail() throws Exception {
        // given
        setupValidTimeWindow();

        CSRFTokenService csrfTokenService = new CSRFTokenService(jsonWebTokenProperties, parameterEncryption);
        String email = "<EMAIL>";
        String csrfToken = URLDecoder.decode(csrfTokenService.csrfTokenForEmail(email), ParameterEncryption.ISO_8859_1);

        // when-then
        assertThat(csrfTokenService.verifyCsrfTokenForEmail(csrfToken, email).isValid()).isTrue();
    }

    @Test
    public void shouldFailToVerifyCsrfTokenMismatchedEmail() throws Exception {
        // given
        setupValidTimeWindow();

        CSRFTokenService csrfTokenService = new CSRFTokenService(jsonWebTokenProperties, parameterEncryption);
        String email = "<EMAIL>";

        // and
        String csrfToken = URLDecoder.decode(csrfTokenService.csrfTokenForEmail("<EMAIL>"), ParameterEncryption.ISO_8859_1);

        // when-then
        assertThat(csrfTokenService.verifyCsrfTokenForEmail(csrfToken, email).isValid()).isFalse();
    }

    private String readStringFromFile() {
        InputStream is = getClass().getClassLoader().getResourceAsStream("testkeys/private.txt");
        assert is != null;
        return new BufferedReader(
                new InputStreamReader(is, StandardCharsets.UTF_8)).lines()
                .collect(Collectors.joining("\n"));
    }

    private void setupValidTimeWindow() {
        int expireInSeconds = 10;
        int notBeforeDelayInSeconds = 0;

        GtPropManager.setProperty(MwebProperty.TOKEN_VALID_NOT_BEFORE_SECONDS.getPropertyName(), notBeforeDelayInSeconds);
        GtPropManager.setProperty(MwebProperty.TOKEN_VALID_NOT_AFTER_SECONDS.getPropertyName(), expireInSeconds);
    }
}
