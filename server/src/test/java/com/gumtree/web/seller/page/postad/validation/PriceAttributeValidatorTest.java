package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.api.category.domain.CategoryConstants;
import org.junit.Test;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 */
public class PriceAttributeValidatorTest {

    @Test
    public void returnsTrueWhenPriceAttributeNotDefinedInMap() {
        assertThat(new PriceAttributeValidator().isValid(Collections.<String, String>emptyMap(), null), equalTo(true));
    }

    @Test
    public void returnsTrueWhenPriceAttributeEmpty() {
        assertThat(new PriceAttributeValidator().isValid(createAttributeMapWithPrice(""), null), equalTo(true));
    }

    @Test
    public void returnsTrueForStandardNumber() {
        assertThat(new PriceAttributeValidator().isValid(createAttributeMapWithPrice("10000000"), null), equalTo(true));
    }

    @Test
    public void returnsTrueForStandardNumberPaddedWithWhitespace() {
        assertThat(new PriceAttributeValidator().isValid(createAttributeMapWithPrice(" 10000000 "), null), equalTo(true));
    }

    @Test
    public void returnsTrueForStandardNumberWithCommas() {
        assertThat(new PriceAttributeValidator().isValid(createAttributeMapWithPrice("100,000,000"), null), equalTo(true));
    }

    @Test
    public void returnsFalseForInvalidValue() {
        assertThat(new PriceAttributeValidator().isValid(createAttributeMapWithPrice("10s0,000,sf"), null), equalTo(false));
    }

    private Map<String, String> createAttributeMapWithPrice(String priceValue) {
        Map<String, String> attributes = new HashMap<String, String>();
        attributes.put(CategoryConstants.Attribute.PRICE.getName(), priceValue);
        return attributes;
    }
}
