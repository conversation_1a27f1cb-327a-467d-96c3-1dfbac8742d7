package com.gumtree.web.seller.page.postad.service;

import com.google.common.base.Optional;
import com.gumtree.api.category.domain.Category;
import com.gumtree.common.model.location.LocationCountryCounties;
import com.gumtree.common.model.location.impl.DefaultLocationCountryCounties;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import org.junit.Before;
import org.junit.Test;

import java.util.Map;

import static com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService.HintSection.DESCRIPTION;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class DefaultPostAdFormDescriptionHintServiceTest {

    private CategoryService categoryService;
    private LocationService locationService;
    private LocationCountryCounties countryCounties;
    private Map levelHierarchy;
    private Category category;
    private Category l3category;
    private Location location;
    private Location country;
    private Location county;
    private DefaultPostAdFormDescriptionHintService service;

    @Before
    public void setUp() {
        categoryService = mock(CategoryService.class);
        locationService = mock(LocationService.class);
        countryCounties = mock(LocationCountryCounties.class);
        levelHierarchy = mock(Map.class);
        category = mock(Category.class);
        l3category = mock(Category.class);
        location = mock(Location.class);
        country = mock(Location.class);
        county = mock(Location.class);

        when(categoryService.getById(anyLong())).thenReturn(Optional.of(category));
        when(categoryService.getLevelHierarchy(23L)).thenReturn(levelHierarchy);
        when(levelHierarchy.size()).thenReturn(2);
        when(locationService.getById(anyInt())).thenReturn(location);
        when(locationService.getCounty(location)).thenReturn(county);
        when(countryCounties.getCountry(county)).thenReturn(country);


        service = new DefaultPostAdFormDescriptionHintService(categoryService, locationService, countryCounties, new Long[]{100L,101L},
                new Long[]{100L,101L});
    }

    @Test
    public void noHint() {
        when(l3category.getSeoName()).thenReturn("some-category");
        when(categoryService.getParent(any(Category.class))).thenReturn(Optional.<Category>absent());

        String hint = service.getHint(DESCRIPTION, 23L, 42L);

        assertThat(hint, nullValue());
    }

    @Test
    public void noScotlandHint() {
        String hint = service.getHint(DESCRIPTION, Categories.PROPERTY_TO_SHARE.getId(), 42L);

        assertThat(hint, containsString("<h3>Remember to:</h3>"));
        assertThat(hint, not(containsString("Landlord Registration Number")));
    }

    @Test
    public void scotlandHint() {
        when(countryCounties.getCountry(county)).thenReturn(DefaultLocationCountryCounties.SCOTLAND);

        String hint = service.getHint(DESCRIPTION, Categories.PROPERTY_TO_SHARE.getId(), 42L);

        assertThat(hint, containsString("<h3>Remember to:</h3>"));
        assertThat(hint, containsString("Landlord Registration Number"));
    }

}
