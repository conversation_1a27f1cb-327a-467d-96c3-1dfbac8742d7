package com.gumtree.web.seller.service;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.ApiOrderItem;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.payment.util.MetaDataGeneratorImpl;
import com.gumtree.web.seller.page.postad.model.meta.PageActionType;
import com.gumtree.web.seller.storage.SellerSessionDataService;
import org.hamcrest.CoreMatchers;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Created by mdivilioglu on 6/24/17.
 */
public class CheckoutMetaInjectorImplTest {

    private CheckoutMetaInjector checkoutMetaInjector;

    private SellerSessionDataService sessionDataService;

    private Checkout checkout;

    @Before
    public void init() {
        sessionDataService = mock(SellerSessionDataService.class);
        checkoutMetaInjector = new CheckoutMetaInjectorImpl(sessionDataService, new MetaDataGeneratorImpl());

        checkout = new CheckoutImpl();

        when(sessionDataService.getCheckout("id1")).thenReturn(checkout);
    }

    private Ad createAd() {
        Ad ad = new Ad();
        ad.setId(1L);
        ad.setCategoryId(11L);
        ad.setLocationId(42L);
        return ad;
    }

    private ApiOrder createOrderWithProductName(ProductName name) {
        ApiOrder order = new ApiOrder();
        ApiOrderItem item = new ApiOrderItem();
        item.setProductName(name);
        order.setItems(new ArrayList<ApiOrderItem>(Arrays.asList(item)));
        return order;
    }

    @Test
    public void createCheckoutWithSetsMetaDataInfoForPostAction() {
        ApiOrder order = createOrderWithProductName(ProductName.INSERTION);
        checkout.setCreateOrEdit(true);
        checkout.setOrder(order);
        when(sessionDataService.getCheckoutByOrder(order)).thenReturn(checkout);
        checkoutMetaInjector.injectTrackingForPost(checkout, createAd());

        assertThat(checkout.getMetaPathInfo().getPageActionType(), equalTo(PageActionType.POST));
        verify(sessionDataService).setCheckout(anyString(), eq(checkout));
    }

    @Test
    public void createCheckoutWithSetsMetaDataInfoForUpdateAction() {
        ApiOrder order = createOrderWithProductName(ProductName.BUMP_UP);
        checkout.setCreateOrEdit(true);
        checkout.setOrder(order);
        when(sessionDataService.getCheckoutByOrder(order)).thenReturn(checkout);
        checkoutMetaInjector.injectTrackingForEditorUpdate(checkout, createAd(), AdStatus.LIVE);

        assertThat(checkout.getMetaPathInfo().getPageActionType(), equalTo(PageActionType.UPDATE));
        verify(sessionDataService).setCheckout(anyString(), eq(checkout));
    }
    @Test
    public void injectMetaDataInformationForExpiredToRelistOnEditorPage() {
        ApiOrder order = createOrderWithProductName(ProductName.BUMP_UP);
        checkout.setCreateOrEdit(true);
        checkout.setOrder(order);
        checkoutMetaInjector.injectTrackingForEditorUpdate(checkout, createAd(), AdStatus.EXPIRED);

        assertThat(checkout.getMetaPathInfo().getPageActionType(), equalTo(PageActionType.RELIST));
        assertThat(checkout.getOrder(), CoreMatchers.sameInstance(order));
        verify(sessionDataService).setCheckout(anyString(), eq(checkout));
    }
    @Test
    public void injectMetaDataInformationForDeletedToRelistOnEditorPage() {
        ApiOrder order = createOrderWithProductName(ProductName.BUMP_UP);
        checkout.setCreateOrEdit(true);
        checkout.setOrder(order);
        checkoutMetaInjector.injectTrackingForEditorUpdate(checkout, createAd(), AdStatus.DELETED_USER);

        assertThat(checkout.getMetaPathInfo().getPageActionType(), equalTo(PageActionType.RELIST));
        assertThat(checkout.getOrder(), CoreMatchers.sameInstance(order));
        verify(sessionDataService).setCheckout(anyString(), eq(checkout));
    }

    @Test
    public void injectMetaDataInformationForExpiredToRelistOnManageAdsPage() {
        ApiOrder order = createOrderWithProductName(ProductName.BUMP_UP);
        checkout.setCreateOrEdit(true);
        checkout.setOrder(order);
        Ad ad = createAd();
        ad.setStatus(AdStatus.EXPIRED);
        checkoutMetaInjector.injectTrackingForManageAdsUpdate(checkout, false, ad);

        assertThat(checkout.getMetaPathInfo().getPageActionType(), equalTo(PageActionType.RELIST));
        assertThat(checkout.getOrder(), CoreMatchers.sameInstance(order));
        verify(sessionDataService).setCheckout(anyString(), eq(checkout));
    }

    @Test
    public void injectMetaDataInformationForDeletedToRelistOnManageAdsPageMultipleFalse() {
        ApiOrder order = createOrderWithProductName(ProductName.BUMP_UP);
        checkout.setCreateOrEdit(false);
        checkout.setOrder(order);
        Ad ad = createAd();
        ad.setStatus(AdStatus.DELETED_USER);
        checkoutMetaInjector.injectTrackingForManageAdsUpdate(checkout, false, ad);

        assertThat(checkout.getMetaPathInfo().getPageActionType(), equalTo(PageActionType.RELIST));
        assertThat(checkout.getOrder(), CoreMatchers.sameInstance(order));
        verify(sessionDataService).setCheckout(anyString(), eq(checkout));
    }

    @Test
    public void injectMetaDataInformationForDeletedToRelistOnManageAdsPageMultipleTrue() {
        ApiOrder order = createOrderWithProductName(ProductName.BUMP_UP);
        checkout.setCreateOrEdit(false);
        checkout.setOrder(order);
        Ad ad = createAd();
        ad.setStatus(AdStatus.DELETED_USER);
        checkoutMetaInjector.injectTrackingForManageAdsUpdate(checkout, true, ad);

        assertThat(checkout.getMetaPathInfo().getPageActionType(), equalTo(PageActionType.RELIST));
        assertThat(checkout.getOrder(), CoreMatchers.sameInstance(order));
        verify(sessionDataService).setCheckout(anyString(), eq(checkout));
    }
}