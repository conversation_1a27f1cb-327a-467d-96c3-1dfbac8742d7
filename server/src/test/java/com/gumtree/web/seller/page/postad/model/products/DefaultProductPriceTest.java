package com.gumtree.web.seller.page.postad.model.products;

import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

/**
 */
public class DefaultProductPriceTest {

    @Test
    public void isFreeReturnsTrueWhenPriceIsZero() {

        assertThat(new DefaultProductPrice(ProductType.URGENT, "product", 0).isFree(), equalTo(true));
    }

    @Test
    public void isFreeReturnsFalseWhenPriceIsGreaterThanZero() {

        assertThat(new DefaultProductPrice(ProductType.URGENT, "product", 1).isFree(), equalTo(false));
        assertThat(new DefaultProductPrice(ProductType.URGENT, "product", 100).isFree(), equalTo(false));
    }

    @Test
    public void isFreeReturnsTrueWhenPriceIsNegative() {

        assertThat(new DefaultProductPrice(ProductType.URGENT, "product", -1).isFree(), equalTo(true));
    }
}
