package com.gumtree.web.seller.resolver;

import com.gumtree.api.Account;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.web.security.UserSession;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AccountArgumentResolverTest {

    @InjectMocks private AccountArgumentResolver argumentResolver;
    @Mock private AccountApi accountApi;
    @Mock private UserSession userSession;

    @Test
    public void shouldResolveAccountArgument() throws Exception {
        Account mockAccount = new Account();

        // given
        when(userSession.getSelectedAccountId()).thenReturn(88L);
        when(accountApi.getAccount(88L)).thenReturn(mockAccount);

        // when
        Object account = argumentResolver.resolveArgument(null, null, null, null);

        assertThat(account.equals(mockAccount), is(true));
    }
}
