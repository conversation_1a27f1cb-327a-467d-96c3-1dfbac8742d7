package com.gumtree.web.seller.page.ajax.category;

import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.config.DefaultPropertyInitializer;
import com.gumtree.web.seller.mvctests.SellerMvcTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.greaterThan;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(initializers = DefaultPropertyInitializer.class)
public class ChildCategoryControllerTest extends SellerMvcTest {

    @Autowired
    private ChildCategoryController controller;

    @Before
    public void beforeEach() {
        super.beforeEach();
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    public void shouldNotReturnChildCategoriesForCars() throws Exception {
        // when
        ResultActions result = mockMvc.perform(get("/ajax/category/children?id=" + CategoryConstants.MOTORS_ID));

        // then
        result.andExpect(status().isOk());
        result.andExpect(content().contentType("application/json;charset=UTF-8"));
        result.andExpect(jsonPath("$.children", is(true))); // all hidden
        result.andExpect(jsonPath("$.childrenItems", hasSize(7)));

        result.andExpect(jsonPath("$.childrenItems[0].id", is(9311)));
        result.andExpect(jsonPath("$.childrenItems[0].seoName", is("cars")));
        result.andExpect(jsonPath("$.childrenItems[0].name", is("Cars")));
        result.andExpect(jsonPath("$.childrenItems[0].children", is(false))); // car make categories hidden

        result.andExpect(jsonPath("$.childrenItems[3].id", is(10442)));
        result.andExpect(jsonPath("$.childrenItems[3].seoName", is("motorbikes-scooters")));
        result.andExpect(jsonPath("$.childrenItems[3].name", is("Motorbikes & Scooters")));
        result.andExpect(jsonPath("$.childrenItems[3].children", is(false))); // car make categories hidden

        result.andExpect(jsonPath("$.childrenItems[5].id", is(9312)));
        result.andExpect(jsonPath("$.childrenItems[5].seoName", is("vans-trucks-plant")));
        result.andExpect(jsonPath("$.childrenItems[5].name", is("Vans, Trucks & Plant")));
        result.andExpect(jsonPath("$.childrenItems[5].children", is(true))); // car make categories hidden

        result.andExpect(jsonPath("$.childrenItems[6].id", is(10301)));
        result.andExpect(jsonPath("$.childrenItems[6].seoName", is("cars-wanted")));
        result.andExpect(jsonPath("$.childrenItems[6].name", is("Wanted")));
        result.andExpect(jsonPath("$.childrenItems[6].children", is(false))); // car make categories hidden

    }

    @Test
    public void shouldReturnForSaleChildCategories() throws Exception {
        // when
        ResultActions result = mockMvc.perform(get("/ajax/category/children?id=" + CategoryConstants.FOR_SALE_ID));

        // then
        result.andExpect(status().isOk());
        result.andExpect(content().contentType("application/json;charset=UTF-8"));
        result.andExpect(jsonPath("$.childrenItems", hasSize(19)));
        result.andExpect(jsonPath("$.childrenItems[0].id", is(2518)));
        result.andExpect(jsonPath("$.childrenItems[0].seoName", is("stereos-audio")));
        result.andExpect(jsonPath("$.childrenItems[0].name", is("Audio & Stereo")));
        result.andExpect(jsonPath("$.childrenItems[0].children", is(true))); // car make categories hidden
    }

    @Test
    public void shouldReturnFreebiesChildCategories() throws Exception {
        // when
        ResultActions result = mockMvc.perform(get("/ajax/category/children?id=" + 120));

        // then
        result.andExpect(status().isOk());
        result.andExpect(content().contentType("application/json;charset=UTF-8"));
        result.andExpect(jsonPath("$.childrenItems", hasSize(0)));
        result.andExpect(jsonPath("$.childrenItems[0]").doesNotExist());
        result.andExpect(jsonPath("$.children", is(false))); // leaf - no children
    }

    @Test
    public void shouldReturnRootForNonExistingCategory() throws Exception {
        // when
        ResultActions result = mockMvc.perform(get("/ajax/category/children?id=" + -1));

        // then
        result.andExpect(status().isOk());
        result.andExpect(content().contentType("application/json;charset=UTF-8"));
        result.andExpect(jsonPath("$.seoName", is("all")));
        result.andExpect(jsonPath("$.childrenItems", hasSize(7)));
    }

    @Test
    public void shouldReturnDropDownModelForSelectedLevel1Category() throws Exception {
        // when get drop down for level 1
        ResultActions result = mockMvc.perform(get("/ajax/category/dropdown?id=2549")); //for-sale

        // then
        verifyResultStatusAndMandatoryCategories(result);

        // then depth
        result.andExpect(jsonPath("$.depth", is(1)));

        // only selected L1 category si marked as selected
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].selected", contains(true)));
        result.andExpect(jsonPath("$.categories[?(@.seoName<>for-sale)].selected", everyItem(is(false))));

        // only selected L1 category is marked as drilled
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].drilled", contains(true)));
        result.andExpect(jsonPath("$.categories[?(@.seoName<>for-sale)].drilled", everyItem(is(false))));

        // all direct children of selected L1 category are in the model
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems", hasSize(greaterThan(15))));

        // none of the children is selected or drilled
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[*].selected", everyItem(is(false))));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[*].drilled", everyItem(is(false))));
    }

    @Test
    public void shouldReturnDropDownModelForSelectedLevel2CategoryWhichHasNoChildren() throws Exception {
        // when get drop down for level 2 category which has no children
        ResultActions result = mockMvc.perform(get("/ajax/category/dropdown?id=120")); //freebies

        // then
        verifyResultStatusAndMandatoryCategories(result);

        // then depth
        result.andExpect(jsonPath("$.depth", is(1)));

        // then none of L1 is selected
        result.andExpect(jsonPath("$.categories[*].selected", everyItem(is(false))));

        // then only L1 which is parent of selected category is drilled
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].drilled", contains(true)));
        result.andExpect(jsonPath("$.categories[?(@.seoName<>for-sale)].drilled", everyItem(is(false))));

        // then all children of the parent L1 category are in the model
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems", hasSize(greaterThan(15))));

        // then but only the selected category is marked as selected but not drilled (as has no children)
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName==freebies)].selected", contains(true)));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName==freebies)].drilled", contains(false)));

        // then other children of the parent L1 are not selected neither drilled
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName<>freebies)]", hasSize(greaterThan(15))));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName<>freebies)].selected", everyItem(is(false))));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName<>freebies)].drilled", everyItem(is(false))));
    }

    @Test
    public void shouldReturnFullDropDownModelForLevel2CategoryWhichHasChildren() throws Exception {
        // when get drop down for level 2 category with children
        ResultActions result = mockMvc.perform(get("/ajax/category/dropdown?id=2521")); // "tickets"

        // then
        verifyResultStatusAndMandatoryCategories(result);

        // then depth
        result.andExpect(jsonPath("$.depth", is(2)));

        // then none of L1 is selected
        result.andExpect(jsonPath("$.categories[*].selected", everyItem(is(false))));

        // then only the parent L1 is drilled
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].drilled", contains(true)));
        result.andExpect(jsonPath("$.categories[?(@.seoName<>for-sale)]", hasSize(greaterThan(5))));
        result.andExpect(jsonPath("$.categories[?(@.seoName<>for-sale)].drilled", everyItem(is(false))));

        // then the selected L2 and it siblings are returned as child of the parent L1 (rest of the children are not returned)
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems", hasSize(greaterThan(15))));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName==tickets)].selected", contains(true)));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName==tickets)].drilled", contains(true)));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName==tickets)].children", contains(true)));

        // then non of the siblings is selected or drilled
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName<>tickets)]", hasSize(greaterThan(15))));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName<>tickets)].selected", everyItem(is(false))));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName<>tickets)].drilled", everyItem(is(false))));

        // then all children of the selected L2 are returned, non of them selected or drilled
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName==tickets)].childrenItems[*]", hasSize(greaterThan(3))));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName==tickets)].childrenItems[*].selected", everyItem(is(false))));
        result.andExpect(jsonPath("$.categories[?(@.seoName==for-sale)].childrenItems[?(@.seoName==tickets)].childrenItems[*].drilled", everyItem(is(false))));
    }

    private void verifyResultStatusAndMandatoryCategories(ResultActions result) throws Exception {
        // then status
        result.andExpect(status().isOk());
        result.andExpect(content().contentType("application/json;charset=UTF-8"));

        // then root and all l1 are returned
        result.andExpect(jsonPath("$.categories", hasSize(7)));
        result.andExpect(jsonPath("$.categories[*].seoName", contains("for-sale", "cars-vans-motorbikes", "jobs", "flats-houses", "business-services", "community", "pets")));
    }
}