package com.gumtree.web.seller.service.manageads;

import com.gumtree.api.Ad;
import com.gumtree.api.AdSearchResponse;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.fulladsearch.FullAdsSearchApi;
import com.gumtree.fulladsearch.model.FullAdFlatAd;
import com.gumtree.fulladsearch.model.FullAdManageAdsRequest;
import com.gumtree.fulladsearch.model.FullAdManageAdsResponse;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.model.ManageAdStatus;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.jboss.resteasy.spi.BadRequestException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Matchers;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import rx.Single;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.anyListOf;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ManageAdsSearchServiceImplTest {

    private static final Long ACCOUNT_ID = 100L;
    private static final Integer PAGE_NUMBER = 1;
    private static final Integer PAGE_SIZE = 10;
    private ManageAdsSearchServiceImpl manageAdsSearchService;
    private FullAdsSearchApi fullAdsSearchApi;
    private BushfireApi bushfireApi;
    private AdvertApi advertApi;

    private CustomMetricRegistry metrics = new CustomMetricRegistry(new SimpleMeterRegistry());

    private List<String> statusList = ManageAdStatus.ACTIVE_ADS.getStatusesNames();

    @Before
    public void init() {
        fullAdsSearchApi = mock(FullAdsSearchApi.class);
        bushfireApi = mock(BushfireApi.class);
        advertApi = mock(AdvertApi.class);
        manageAdsSearchService = new ManageAdsSearchServiceImpl(bushfireApi, fullAdsSearchApi, metrics);
        initManageAdsResponse();
        initBushfireApiMock();
        initAdvertApiMock();
    }

    private void initManageAdsResponse() {
        when(fullAdsSearchApi.manageAds(Mockito.any())).thenReturn(Single.just(getManagedAdsResponse(getDefaultFlatAds())));
    }

    private void initBushfireApiMock() {
        when(bushfireApi.advertApi()).thenReturn(advertApi);
    }

    private void initAdvertApiMock() {
        List<Ad> adsList = getDefaultAdsList();
        Ad[] adsArray = new Ad[adsList.size()];
        AdSearchResponse adSearchResponse = new AdSearchResponse();
        adSearchResponse.setTotalCount(adsArray.length);
        adSearchResponse.setPostings(adsList.toArray(adsArray));
        when(advertApi.search(ACCOUNT_ID, statusList, PAGE_NUMBER, PAGE_SIZE, false)).thenReturn(adSearchResponse);
        when(advertApi.getAdverts(anyListOf(Long.class))).thenReturn(getDefaultAdsList());
    }

    @Test
    public void testNoSearchQueryReturnsDefaultAdSet() {
        String searchTerms = null;
        ManageAdsSearchServiceResponse searchResponse = manageAdsSearchService.search(statusList, searchTerms, ACCOUNT_ID, PAGE_NUMBER, PAGE_SIZE);
        verify(advertApi).search(ACCOUNT_ID, statusList, PAGE_NUMBER, PAGE_SIZE, false);
        assertThat(searchResponse.getEmptySearch(), equalTo(null)); //defaults
        assertThat(searchResponse.getSearchTerms(), equalTo(null));
        assertThat(searchResponse.getTotalResults(), equalTo(2));
        assertThat(searchResponse.getAdverts().size(), equalTo(2));
    }

    @Test
    public void getManagedAdsForSearchTerm() {
        String searchTerms = "TV";
        when(fullAdsSearchApi.manageAds(Matchers.any())).thenReturn(Single.just(
                getManagedAdsResponse(Arrays.asList(new FullAdFlatAd().id(113L).title("TV to be sold"),
                        new FullAdFlatAd().id(114L).title("Old Electronics to be sold")))));
        when(advertApi.getAdverts(anyListOf(Long.class))).thenReturn(getAdsList(Arrays.asList(113L, 114L)));
        ManageAdsSearchServiceResponse searchResponse = manageAdsSearchService.search(statusList, searchTerms, ACCOUNT_ID, PAGE_NUMBER, PAGE_SIZE);
        assertThat(searchResponse.getEmptySearch(), equalTo(false));
        assertThat(searchResponse.getSearchTerms(), equalTo("TV"));
        assertThat(searchResponse.getTotalResults(), equalTo(2));
        assertThat(searchResponse.getAdverts().size(), equalTo(2));
        assertThat(searchResponse.getAdverts().get(0).getTitle(), equalTo("TV Showroom"));
    }

    @Test
    public void returnsEmptyIfNotFoundSearchTerm() {
        String searchTerms = "BMW";
        when(fullAdsSearchApi.manageAds(Matchers.any())).thenReturn(Single.just(new FullAdManageAdsResponse().items(Collections.emptyList()).total(0L)));
        ManageAdsSearchServiceResponse searchResponse = manageAdsSearchService.search(statusList, searchTerms, ACCOUNT_ID, PAGE_NUMBER, PAGE_SIZE);
        assertThat(searchResponse.getEmptySearch(), equalTo(true));
        assertThat(searchResponse.getSearchTerms(), equalTo("BMW"));
        assertThat(searchResponse.getTotalResults(), equalTo(0));
        assertThat(searchResponse.getAdverts().size(), equalTo(0));
    }

    @Test
    public void whenSearchingAValidEmailAddress() {
        String searchTerms = "<EMAIL>";
        ManageAdsSearchServiceResponse searchResponse = manageAdsSearchService.search(statusList, searchTerms, ACCOUNT_ID, PAGE_NUMBER, PAGE_SIZE);
        verify(fullAdsSearchApi).manageAds(new FullAdManageAdsRequest().search(searchTerms)
                .accountId(ACCOUNT_ID).statuses(statusList).offset(PAGE_NUMBER - 1).limit(PAGE_SIZE));
        assertThat(searchResponse.getSearchTerms(), equalTo("<EMAIL>"));
    }

    @Test
    public void getManageAdsReturnsEmptyResultOnApiError() {
        String searchTerms = "Audi";
        when(fullAdsSearchApi.manageAds(Mockito.any())).thenReturn(Single.error(new TimeoutException("API Request Failed")));
        ManageAdsSearchServiceResponse searchResponse = manageAdsSearchService.search(statusList,
                searchTerms, ACCOUNT_ID, PAGE_NUMBER, PAGE_SIZE);
        assertThat(searchResponse.getEmptySearch(), equalTo(true));
        assertThat(searchResponse.getTotalResults(), equalTo(0));
    }

    @Test
    public void getManageAdsReturnsEmptyResultOnBadRequestError() {
        String searchTerms = "Audi";
        when(fullAdsSearchApi.manageAds(Mockito.any())).thenReturn(Single.error(new BadRequestException("[?.offset] is negative. Value: -1")));
        ManageAdsSearchServiceResponse searchResponse = manageAdsSearchService.search(statusList,
                searchTerms, ACCOUNT_ID, -3, PAGE_SIZE);
        assertThat(searchResponse.getEmptySearch(), equalTo(true));
        assertThat(searchResponse.getTotalResults(), equalTo(0));
    }

    @Test
    public void testPageIndex() {
        assertThat(manageAdsSearchService.getFirstIndexOfPage(1, 10), equalTo(0));
        assertThat(manageAdsSearchService.getFirstIndexOfPage(4, 10), equalTo(30));
    }

    private Ad createAd(long id, String title, long accountId) {
        Ad ad = new Ad();
        ad.setId(id);
        ad.setTitle(title);
        ad.setAccountId(accountId);
        return ad;
    }

    private FullAdManageAdsResponse getManagedAdsResponse(List<FullAdFlatAd> flatAds) {
        FullAdManageAdsResponse manageAdsResponse = new FullAdManageAdsResponse();
        manageAdsResponse.setItems(flatAds);
        manageAdsResponse.setTotal((long) flatAds.size());
        return manageAdsResponse;
    }

    private Map<Long, Ad> advertStub() {
        Map<Long, Ad> adsMap = new HashMap<>();
        adsMap.put(111L, createAd(111L, "Audi Car to be sold", 10L));
        adsMap.put(112L, createAd(112L, "Car Dealer", 11L));
        adsMap.put(113L, createAd(113L, "TV Showroom", 10L));
        adsMap.put(114L, createAd(114L, "Good Quality Electronics", 10L));
        return adsMap;
    }

    private List<Ad> getAdsList(List<Long> adIds) {
        return adIds.stream()
                .map(adId -> advertStub().get(adId))
                .collect(Collectors.toList());
    }

    private List<Ad> getDefaultAdsList() {
        List<Ad> ads = new ArrayList<>();
        ads.add(createAd(1L, "Ad1", 10L));
        ads.add(createAd(2L, "Ad2", 10L));
        return ads;
    }

    private List<FullAdFlatAd> getDefaultFlatAds() {
        List<FullAdFlatAd> flatAds = new ArrayList<>();
        flatAds.add(new FullAdFlatAd().id(111L).title("Audi Car to be sold"));
        flatAds.add(new FullAdFlatAd().id(112L).description("Audi Car is in good condition."));
        return flatAds;
    }
}
