package com.gumtree.web.seller.page.manageads;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.Account;
import com.gumtree.api.AccountStatus;
import com.gumtree.api.Ad;
import com.gumtree.api.AdFeature;
import com.gumtree.api.AdSearchResponse;
import com.gumtree.api.AdStatus;
import com.gumtree.api.ApiProductPrice;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.client.executor.ApiCall;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.executor.command.GetAccountCommand;
import com.gumtree.api.client.executor.command.GetAccountsApiCall;
import com.gumtree.api.client.executor.command.GetUserApiCall;
import com.gumtree.api.client.executor.impl.DefaultApiCallResponse;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.client.spec.PriceApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.api.domain.order.CreateOrderItemBean;
import com.gumtree.common.properties.Env;
import com.gumtree.common.util.time.Clock;
import com.gumtree.domain.advert.AdvertStatus;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.web.common.page.manageads.AdvertFeatureMatrix;
import com.gumtree.web.common.page.model.pagination.Pagination;
import com.gumtree.web.common.page.model.pagination.PaginationService;
import com.gumtree.web.common.page.model.pagination.PaginationUrlGenerator;
import com.gumtree.web.common.page.model.pagination.decadicpagination.DecadicPagination;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.converter.AdToAdPreviewConverter;
import com.gumtree.web.seller.dto.ShortAdDTO;
import com.gumtree.web.seller.model.AdPreview;
import com.gumtree.web.seller.model.AdPreviewImpl;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.PageNotFoundException;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.manageads.api.CreateOrderApiCall;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.manageads.model.ManageAdStatus;
import com.gumtree.web.seller.page.manageads.model.ManageAdsAccountSelectionFormBean;
import com.gumtree.web.seller.page.manageads.model.ManageAdsFeatureFormBean;
import com.gumtree.web.seller.page.manageads.model.ManageAdsFilterFormBean;
import com.gumtree.web.seller.page.manageads.model.ManageAdsModel;
import com.gumtree.web.seller.page.manageads.model.ManageAdsWorkspace;
import com.gumtree.web.seller.page.manageads.model.ResponsiveAdModel;
import com.gumtree.web.seller.page.postad.model.products.ProductPrice;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.web.seller.service.CheckoutMetaInjector;
import com.gumtree.web.seller.service.adstats.AdvertStatisticData;
import com.gumtree.web.seller.service.adstats.AdvertStatisticDataBuilder;
import com.gumtree.web.seller.service.adstats.AdvertStatsService;
import com.gumtree.web.seller.service.manageads.ManageAdsSearchService;
import com.gumtree.web.seller.service.manageads.ManageAdsSearchServiceResponse;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.initMocks;

@RunWith(MockitoJUnitRunner.class)
public class ManageAdsControllerTest extends BaseSellerControllerTest {

    @Mock
    private AccountApi accountApi;
    @Mock
    private AdvertApi advertApi;
    @Mock
    private UserApi userApi;
    @Mock
    private PriceApi priceApi;
    @Mock
    private UserSession userSession;
    @Mock
    private AdToAdPreviewConverter converter;
    @Mock
    private PaginationService paginationService;
    @Mock
    private CheckoutContainer checkoutContainer;
    @Mock
    private Clock clock;
    @Mock
    private ManageAdsSearchService manageAdsSearchService;
    @Mock
    private ManageAdsHelper manageAdsHelper;
    @Mock
    private AdvertStatsService advertStatsService;
    @Mock
    private ManageAdsWorkspace manageAdsWorkspace;
    @Mock
    private CheckoutMetaInjector checkoutMetaInjector;
    @Spy
    private CustomMetricRegistry customMetricRegistry = new CustomMetricRegistry(new SimpleMeterRegistry());
    private Account selectedAccount;

    @InjectMocks
    private ManageAdsController controller;

    @Before
    public void init() {
        initMocks(this);
        initApiCallExecutor();
        initBapi();
        initPagination();

        when(advertStatsService.getStatisticForAdvertList(anyList())).thenReturn(createStatistics());

        when(manageAdsSearchService.search(anyList(), anyString(), anyLong(), anyInt(), anyInt())).
                thenReturn(createManageAdsSearchServiceResponse());

        when(manageAdsWorkspace.getFilterForm()).thenReturn(new ManageAdsFilterFormBean());

        CheckoutImpl checkout = new CheckoutImpl();
        checkout.setKey("someid");
        when(checkoutContainer.createCheckout(any(ApiOrder.class))).thenReturn(checkout);

        selectedAccount = new Account();
        selectedAccount.setId(1L);
        selectedAccount.setStatus(AccountStatus.ACTIVE);
        when(userSession.getSelectedAccountId()).thenReturn(1L);
        User user = new User();
        user.setEbayMotorsUser(false);
        when(userSession.getUser()).thenReturn(user);

        when(accountApi.getAccount(1L)).thenReturn(selectedAccount);

        when(manageAdsHelper.getAccountSelectionForm(userSession)).thenReturn(new ManageAdsAccountSelectionFormBean());
        when(manageAdsHelper.getSessionUser(any(UserSession.class))).thenReturn(new com.gumtree.api.User());

        autowireAbExperimentsService(controller);
    }

    @Test
    public void testShowManageResponsiveTemplateName() throws Exception {
        String responsivePage = "pages/manage-ads/manage-ads-responsive";

        ModelAndView mav = controller.showManageAdsPageOne(null, request);
        Object pageTitle = mav.getViewName();
        assertThat(pageTitle.toString(), equalTo(responsivePage));
        verify(manageAdsHelper).getAccountSelectionForm(userSession);
    }

    @Test
    public void testAccountsPopulated() throws Exception {
        List<Account> accounts = new ArrayList<>();
        accounts.add(new Account());
        when(userApi.getAccounts("standard")).thenReturn(accounts);
        List<Account> selectableAccounts = new ArrayList<>();
        when(userSession.getSelectableAccounts()).thenReturn(selectableAccounts);
        ModelAndView mav = controller.showManageAdsPageOne(null, request);
        ManageAdsModel mam = (ManageAdsModel) mav.getModel().get("model");
        assertThat(mam.getAccountSelectionForm(), notNullValue());
    }


    @Test
    public void testAccountSelected() {
        final Long SELECTED_ACCOUNT_ID = 12L;
        when(userSession.getSelectedAccountId()).thenReturn(SELECTED_ACCOUNT_ID);
        Account account = new AccountBuilder().withId(SELECTED_ACCOUNT_ID).isPro().withName("account name").build();
        List<Account> userAccounts = new ArrayList<>();
        userAccounts.add(account);
        when(userSession.getSelectableAccounts()).thenReturn(userAccounts);
        when(accountApi.getAccount(SELECTED_ACCOUNT_ID)).thenReturn(account);
        ModelAndView mav = controller.selectAccount(SELECTED_ACCOUNT_ID, request);
        Account selectedAccount = (Account) mav.getModel().get("account");
        assertThat(selectedAccount.getId(), equalTo(SELECTED_ACCOUNT_ID));
    }

    @Test(expected = PageNotFoundException.class)
    public void testNonSuperUserCantAccessOtherAccounts() {
        final Long SELECTED_ACCOUNT_ID = 12L;
        controller.selectAccount(SELECTED_ACCOUNT_ID, request);
    }

    @Test
    public void testFilterAdverts() {
        ManageAdsFilterFormBean filterFormBean = new ManageAdsFilterFormBean();
        filterFormBean.setStatus(ManageAdStatus.LIVE);
        filterFormBean.setSearchTerms("test-search-terms");

        when(manageAdsWorkspace.getFilterForm()).thenReturn(filterFormBean);

        controller.filterAdverts(ManageAdStatus.LIVE, "test-search-terms", 5, request);

        // Ensure filterFormBean is set correctly
        ArgumentCaptor<ManageAdsFilterFormBean> argument = ArgumentCaptor.forClass(ManageAdsFilterFormBean.class);
        verify(manageAdsWorkspace).setFilterForm(argument.capture());
        assertEquals(ManageAdStatus.LIVE, argument.getValue().getStatus());
        assertEquals("test-search-terms", argument.getValue().getSearchTerms());

        verify(manageAdsWorkspace, times(2)).getFilterForm();
        verify(manageAdsSearchService).search(Lists.<String>newArrayList(ManageAdStatus.LIVE.toString()), "test-search-terms", 1L, 5, 10);
    }

    @Test
    public void testShowManageAdvertList() throws Exception {
        when(advertApi.search(anyLong(), Matchers.<List<String>>any(), anyInt(), anyInt())).thenReturn(
                createAdSearchResponse(createAds()));
        when(accountApi.getAdverts(anyLong())).thenReturn(createAds());
        when(bushfireApi.accountApi()).thenReturn(accountApi);
        ModelAndView mav = controller.showManageAdsPageOne(null, request);
        ManageAdsModel model = (ManageAdsModel) mav.getModel().get("model");
        assertThat(model.getFeatureForm().getPreviews().size(), equalTo(0));
    }

    @Test
    public void testAccountTypeNotSet() {
        Account account = new AccountBuilder().build();
        when(accountApi.getAccount(anyLong())).thenReturn(account);
        ModelAndView mav = controller.showManageAdsPageOne(null, request);
        Account modelAccount = (Account) mav.getModel().get("account");
        assertThat(modelAccount.isPro(), equalTo(false));
    }

    @Test
    public void testAccountTypePro() {
        Account account = new AccountBuilder().isPro().build();
        when(accountApi.getAccount(anyLong())).thenReturn(account);
        ModelAndView mav = controller.showManageAdsPageOne(null, request);
        Account modelAccount = (Account) mav.getModel().get("account");
        assertThat(modelAccount.isPro(), equalTo(true));
    }

    @Test
    public void testAccountLookup() {
        Account account = new AccountBuilder()
                .withName("accountName")
                .withDescription("accountDescription").build();

        when(accountApi.getAccount(anyLong())).thenReturn(account);
        ModelAndView mav = controller.showManageAdsPageOne(null, request);
        Account modelAccount = (Account) mav.getModel().get("account");
        assertThat(modelAccount.getName(), equalTo("accountName"));
        assertThat(modelAccount.getDescription(), equalTo("accountDescription"));
    }

    @Test
    public void testPriceLookup() {
        mockProductPrices();

        when(advertApi.search(anyLong(), Matchers.<List<String>>any(), anyInt(), anyInt())).thenReturn(
                createAdSearchResponse(createAds()));
        when(accountApi.getAdverts(anyLong())).thenReturn(createAds());
        when(bushfireApi.accountApi()).thenReturn(accountApi);

        ModelAndView mav = controller.showManageAdsPageOne(null, request);
        Map<Long, Map<String, ProductPrice>> prices = ((ManageAdsModel) mav.getModel().get("model")).getPrices();
        assertThat(prices, notNullValue());
        assertThat(prices.get(1l), notNullValue());
        assertThat(prices.get(1l).get("INSERTION").getPrice(), equalTo(new BigDecimal("1.50")));
    }

    @Test
    public void featureAdsShouldFailIfAccountIsSuspended() {
        selectedAccount.setStatus(AccountStatus.SUSPENDED);
        ModelAndView modelAndView = controller.featureAdvertsForProUser(new AdvertFeatureMatrix(), request);
        assertThat(modelAndView.getViewName(), equalTo("redirect:" + ManageAdsController.PAGE_PATH));
    }

    @Test
    public void featureAdsShouldFailIfAccountIsClosed() {
        selectedAccount.setStatus(AccountStatus.CLOSED);
        ModelAndView modelAndView = controller.featureAdvertsForProUser(new AdvertFeatureMatrix(), request);
        assertThat(modelAndView.getViewName(), equalTo("redirect:" + ManageAdsController.PAGE_PATH));
    }

    @Test
    public void featureAdsShouldFailIfAccountIsCollectionAgency() {
        selectedAccount.setStatus(AccountStatus.COLLECTION_AGENCY);
        ModelAndView modelAndView = controller.featureAdvertsForProUser(new AdvertFeatureMatrix(), request);
        assertThat(modelAndView.getViewName(), equalTo("redirect:" + ManageAdsController.PAGE_PATH));
    }

    @Test
    public void featureAdsShouldFailIfUserIsEbayMotorsUser() {
        User user = new User();
        user.setEbayMotorsUser(true);
        when(userSession.getUser()).thenReturn(user);
        ModelAndView modelAndView = controller.featureAdvertsForProUser(new AdvertFeatureMatrix(), request);
        assertThat(modelAndView.getViewName(), equalTo("redirect:" + ManageAdsController.PAGE_PATH));
    }

    @Test
    public void shouldFeatureAdsWhenOnlyFewAdsSelected() {
        //given
        selectedAccount.setStatus(AccountStatus.ACTIVE);

        //when
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();
        HashMap<String, Map<String, Boolean>> matrix = new HashMap<>();
        matrix.put("1", new HashMap<>());
        matrix.put("2", new HashMap<>());
        advertFeatureMatrix.setMatrix(matrix);
        ModelAndView modelAndView = controller.featureAdvertsForProUser(advertFeatureMatrix, request);

        //then
        assertThat(getRedirectUrl(modelAndView), equalTo("/checkout/{checkoutKey}"));
    }

    @Test
    public void shouldCreateOrderWithGivenProductForProAccount() {
        //given
        long adId = 1L;
        Ad ad = new Ad();
        ad.setId(adId);
        ad.setAccountId(1L);
        ad.setFeatures(Collections.emptyList());
        when(advertApi.getAdverts(anyListOf(Long.class))).thenReturn(Arrays.asList(ad));
        selectedAccount.setStatus(AccountStatus.ACTIVE);

        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();
        Map<String, Map<String, Boolean>> matrix = ImmutableMap.of(String.valueOf(adId),
                ImmutableMap.of(ProductName.BUMP_UP.name(), true));
        advertFeatureMatrix.setMatrix(matrix);

        //when
        controller.featureAdvertsForProUser(advertFeatureMatrix, request);

        //then
        ArgumentCaptor<ApiCall> argument = ArgumentCaptor.forClass(ApiCall.class);
        verify(apiCallExecutor).call(argument.capture());
        CreateOrderBean createOrderBean = ((CreateOrderApiCall) argument.getValue()).getCreateOrderBean();

        assertThat(createOrderBean.getItems(), hasSize(1));
        CreateOrderItemBean orderItemBean = createOrderBean.getItems().get(0);
        assertThat(orderItemBean.getProductName(), equalTo(ProductName.BUMP_UP));
    }

    @Test
    public void responsiveFeatureAdsShouldFailIfAccountIsSuspended() {
        selectedAccount.setStatus(AccountStatus.SUSPENDED);
        ModelAndView modelAndView = controller.featureAdvertsForStandardUser(mock(HttpServletRequest.class));
        assertThat(modelAndView.getViewName(), equalTo("redirect:" + ManageAdsController.PAGE_PATH));
    }

    @Test
    public void responsiveFeatureAdsShouldFailIfAccountIsClosed() {
        selectedAccount.setStatus(AccountStatus.CLOSED);
        ModelAndView modelAndView = controller.featureAdvertsForStandardUser(mock(HttpServletRequest.class));
        assertThat(modelAndView.getViewName(), equalTo("redirect:" + ManageAdsController.PAGE_PATH));
    }

    @Test
    public void responsiveFeatureAdsShouldFailIfAccountIsCollectionAgency() {
        selectedAccount.setStatus(AccountStatus.COLLECTION_AGENCY);
        ModelAndView modelAndView = controller.featureAdvertsForStandardUser(mock(HttpServletRequest.class));
        assertThat(modelAndView.getViewName(), equalTo("redirect:" + ManageAdsController.PAGE_PATH));
    }

    @Test
    public void responsiveFeatureAdsShouldFailIfUserIsEbayMotorsUser() {
        User user = new User();
        user.setEbayMotorsUser(true);
        when(userSession.getUser()).thenReturn(user);
        ModelAndView modelAndView = controller.featureAdvertsForStandardUser(mock(HttpServletRequest.class));
        assertThat(modelAndView.getViewName(), equalTo("redirect:" + ManageAdsController.PAGE_PATH));
    }

    @Test
    public void responsiveFeatureAdsShouldFeatureAds() {
        //given
        selectedAccount.setStatus(AccountStatus.ACTIVE);

        //when
        //NOTE: due to the way the checkout info is stored, we can't test in here that the data is applied correctly, that's done in other tests
        ModelAndView modelAndView = controller.featureAdvertsForStandardUser(mock(HttpServletRequest.class));
        //then
        assertThat(getRedirectUrl(modelAndView), equalTo("/checkout/{checkoutKey}"));
    }

    @Test
    public void weCantBuyFeatureThatAdAlreadyHas() {
        //Given
        final AdFeature bumpUp = new AdFeature();
        bumpUp.setEndDate(new DateTime().plusMinutes(30));
        bumpUp.setProductName(ProductName.BUMP_UP);

        Ad ad = new Ad();
        ad.setId(1l);
        ad.setFeatures(Lists.newArrayList(bumpUp));
        List<Ad> adverts = Lists.newArrayList(ad);

        Map<String, String[]> params = Maps.newHashMap();
        params.put("1", new String[]{ProductName.BUMP_UP.toString()});

        //When
        CreateOrderBean createOrderBean = controller.buildCreateOrderBean(params, adverts);

        //Then
        assertTrue("We can't have elements in the order bean", createOrderBean.getItems().isEmpty());
    }

    @Test
    public void weCanBuyFeaturesForAd() {
        //Given
        Ad ad = new Ad();
        ad.setId(1l);
        ad.setFeatures(Lists.<AdFeature>newArrayList());
        List<Ad> adverts = Lists.newArrayList(ad);

        Map<String, String[]> params = Maps.newHashMap();
        params.put("1", new String[]{
                ProductName.BUMP_UP.toString(),
                ProductName.HOMEPAGE_SPOTLIGHT.toString(),
                ProductName.URGENT.toString()}
        );

        //When
        CreateOrderBean createOrderBean = controller.buildCreateOrderBean(params, adverts);

        //Then
        assertFalse("We must have order beans", createOrderBean.getItems().isEmpty());
        assertTrue("We must have 3 order beans", createOrderBean.getItems().size() == 3);
        boolean hasBump = false, hasUrgent = false, hasSpotlight = false;
        for (CreateOrderItemBean bean : createOrderBean.getItems()) {
            if (bean.getProductName() == ProductName.BUMP_UP) hasBump = true;
            if (bean.getProductName() == ProductName.URGENT) hasUrgent = true;
            if (bean.getProductName() == ProductName.HOMEPAGE_SPOTLIGHT) hasSpotlight = true;
        }
        assertTrue(hasBump);
        assertTrue(hasUrgent);
        assertTrue(hasSpotlight);
    }

    @Test
    public void weCanBuyFeaturesForManyAdsAtOnce() {
        //Given
        Ad ad = new Ad();
        ad.setId(1l);
        ad.setFeatures(Lists.<AdFeature>newArrayList());
        Ad ad2 = new Ad();
        ad2.setId(2l);
        ad2.setFeatures(Lists.<AdFeature>newArrayList());
        List<Ad> adverts = Lists.newArrayList(ad, ad2);

        Map<String, String[]> params = Maps.newHashMap();
        params.put("1", new String[]{
                ProductName.BUMP_UP.toString(),
                ProductName.URGENT.toString()
        });
        params.put("2", new String[]{
                ProductName.HOMEPAGE_SPOTLIGHT.toString()
        });

        //When
        CreateOrderBean createOrderBean = controller.buildCreateOrderBean(params, adverts);

        //Then
        assertFalse("We must have order beans", createOrderBean.getItems().isEmpty());
        assertTrue("We must have 3 order beans", createOrderBean.getItems().size() == 3);
        boolean hasBump = false, hasUrgent = false, hasSpotlight = false;
        for (CreateOrderItemBean bean : createOrderBean.getItems()) {
            if (bean.getAdvertId() == 1l && bean.getProductName() == ProductName.BUMP_UP) hasBump = true;
            if (bean.getAdvertId() == 1l && bean.getProductName() == ProductName.URGENT) hasUrgent = true;
            if (bean.getAdvertId() == 2l && bean.getProductName() == ProductName.HOMEPAGE_SPOTLIGHT)
                hasSpotlight = true;
        }
        assertTrue(hasBump);
        assertTrue(hasUrgent);
        assertTrue(hasSpotlight);
    }

    @Test
    public void buyTopAdOnlyIfCheckboxSelected() {
        //Given we didn't buy the feature
        Ad ad = new Ad();
        ad.setId(1l);
        ad.setFeatures(Lists.<AdFeature>newArrayList());
        List<Ad> adverts = Lists.newArrayList(ad);

        Map<String, String[]> params = Maps.newHashMap();
        params.put("1", new String[]{
                ProductName.FEATURE_3_DAY.toString(),
        });

        //When
        CreateOrderBean createOrderBean = controller.buildCreateOrderBean(params, adverts);

        //Then
        assertTrue("We didn't buy the feature", createOrderBean.getItems().isEmpty());


        //Given we selected the checkbox to buy the feature
        params.put("1", new String[]{
                ProductName.FEATURE_3_DAY.toString(),
                ManageAdsController.BUY_TOP_AD
        });

        //When
        CreateOrderBean createOrderBean2 = controller.buildCreateOrderBean(params, adverts);

        //Then
        assertFalse("We must have order beans", createOrderBean2.getItems().isEmpty());
        assertTrue("We must have 1 order beans", createOrderBean2.getItems().size() == 1);

        CreateOrderItemBean bean = createOrderBean2.getItems().get(0);
        assertTrue("We bought the feature", bean.getProductName().equals(ProductName.FEATURE_3_DAY));
        assertTrue("We bought for our ad", bean.getAdvertId() == 1l);
    }

    @Test
    public void manageAdsPageModelContainsAccountSuspendedNoticeForSuspendedAccount() {
        Account account = new AccountBuilder()
                .withStatus(AccountStatus.SUSPENDED)
                .build();
        when(accountApi.getAccount(anyLong())).thenReturn(account);
        when(messageResolver.getMessage("account.notice.suspended", "account.notice.suspended")).thenReturn(
                "suspended message");
        when(messageResolver.getMessage("account.title.suspended", "account.title.suspended")).thenReturn(
                "suspended title");
        ModelAndView mav = controller.showManageAdsPageOne(null, request);
        assertThat(mav.getModel().containsKey("accountSuspendedNotice"), equalTo(true));
        assertThat((String) mav.getModelMap().get("accountSuspendedNotice"), equalTo("suspended message"));
        assertThat((String) mav.getModelMap().get("accountSuspendedTitle"), equalTo("suspended title"));
    }

    @Test
    public void exceptionWhenAttemptingToRetrieveAdStatsShouldReturnEmptyMap() {
        when(advertStatsService.getStatisticForAdvert(anyLong())).thenThrow(
                new RuntimeException("Test connect exception"));
        when(advertStatsService.getStatisticForAdvertList(anyList())).thenReturn(new ArrayList<AdvertStatisticData>());
        when(accountApi.getAccount(anyLong())).thenReturn(new AccountBuilder().isPro().build());
        ModelAndView mav = controller.showPagedSearchResult("1", request);
        Map<String, AdvertStatisticData> map = ((ManageAdsModel) mav.getModel().get("model")).getAdViewStats();
        assertThat(map.isEmpty(), equalTo(true));
    }

    @Test
    public void shouldDisplaySingleAdvertIfAdvertIdIsProvided() {
        when(accountApi.getAccount(anyLong())).thenReturn(new AccountBuilder().build());

        // when
        ModelAndView mav = controller.showAdvertById(123456L, request);

        // then
        verify(manageAdsSearchService).search(Lists.<String>newArrayList(), "123456", 1L, 1, 10);
        assertThat(((ManageAdsModel) mav.getModel().get("model")).isShowDeleteAdBalloon(), is(true));
    }

    @Test
    public void testShowAdvertByIdInAllowedStatusList() {
        when(accountApi.getAccount(anyLong())).thenReturn(new AccountBuilder().build());

        // when
        ModelAndView mav = controller.showAdvertByIdInAllowedStatusList(123456L, request);

        List<String> allowedAdTypes = Arrays.asList(
                AdvertStatus.LIVE.toString(),
                AdvertStatus.AWAITING_CS_REVIEW.toString(), AdvertStatus.AWAITING_SCREENING.toString()
        );

        // then
        verify(manageAdsSearchService).search(allowedAdTypes, "123456", 1L, 1, 10);
        assertThat(((ManageAdsModel) mav.getModel().get("model")).isShowDeleteAdBalloon(), is(false));
    }

    @Test
    public void testRecentlyExpiredAdverts_ExpiryDatesWithinRange() {
        LocalDate now = LocalDate.now();

        Ad ad1 = new Ad();
        ad1.setExpiryDate(now.minusDays(1).toDateTimeAtStartOfDay());

        Ad ad2 = new Ad();
        ad2.setExpiryDate(now.minusDays(29).toDateTimeAtStartOfDay());

        ManageAdsSearchServiceResponse searchResponse = new ManageAdsSearchServiceResponse();
        searchResponse.setAdverts(Arrays.asList(ad1, ad2));

        when(accountApi.getAccount(anyLong())).thenReturn(new AccountBuilder().build());

        when(manageAdsSearchService.search(eq(Arrays.asList(AdvertStatus.EXPIRED.toString())), eq(null), anyLong(), eq(1), eq(10))).
                thenReturn(searchResponse);

        ResponseEntity<List<ShortAdDTO>> response = controller.recentlyExpiredAdverts(30, request);

        assertNotNull(response);
        assertEquals(2, response.getBody().size());
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testRecentlyExpiredAdverts_ExpiryDatesOutsideOfRange() {
        LocalDate now = LocalDate.now();

        Ad ad1 = new Ad();
        ad1.setExpiryDate(now.minusDays(30).toDateTimeAtStartOfDay());

        Ad ad2 = new Ad();
        ad2.setExpiryDate(now.minusDays(31).toDateTimeAtStartOfDay());

        ManageAdsSearchServiceResponse searchResponse = new ManageAdsSearchServiceResponse();
        searchResponse.setAdverts(Arrays.asList(ad1, ad2));

        when(accountApi.getAccount(anyLong())).thenReturn(new AccountBuilder().build());

        when(manageAdsSearchService.search(eq(Arrays.asList(AdvertStatus.EXPIRED.toString())), eq(null), anyLong(), eq(1), eq(10))).
                thenReturn(searchResponse);

        ResponseEntity<List<ShortAdDTO>> response = controller.recentlyExpiredAdverts(30, request);

        assertNotNull(response);
        assertEquals(0, response.getBody().size());
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testRecentlyExpiredAdverts_NoExpiryDate() {
        Ad ad1 = new Ad();
        Ad ad2 = new Ad();

        ManageAdsSearchServiceResponse searchResponse = new ManageAdsSearchServiceResponse();
        searchResponse.setAdverts(Arrays.asList(ad1, ad2));

        when(accountApi.getAccount(anyLong())).thenReturn(new AccountBuilder().build());

        when(manageAdsSearchService.search(eq(Arrays.asList(AdvertStatus.EXPIRED.toString())), eq(null), anyLong(), eq(1), eq(10))).
                thenReturn(searchResponse);

        ResponseEntity<List<ShortAdDTO>> response = controller.recentlyExpiredAdverts(30, request);

        assertNotNull(response);
        assertEquals(0, response.getBody().size());
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testRecentlyExpiredAdverts_NoAdverts() {
        ManageAdsSearchServiceResponse searchResponse = new ManageAdsSearchServiceResponse();
        searchResponse.setAdverts(Arrays.asList());

        when(accountApi.getAccount(anyLong())).thenReturn(new AccountBuilder().build());

        when(manageAdsSearchService.search(eq(Arrays.asList(AdvertStatus.EXPIRED.toString())), eq(null), anyLong(), eq(1), eq(10))).
                thenReturn(searchResponse);

        ResponseEntity<List<ShortAdDTO>> response = controller.recentlyExpiredAdverts(30, request);

        assertNotNull(response);
        assertEquals(0, response.getBody().size());
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testRecentlyExpiredAdverts_Exception() {
        ManageAdsSearchServiceResponse searchResponse = new ManageAdsSearchServiceResponse();
        searchResponse.setAdverts(Collections.emptyList());
        when(accountApi.getAccount(anyLong())).thenReturn(new AccountBuilder().build());
        when(manageAdsSearchService.search(eq(Collections.singletonList(AdvertStatus.EXPIRED.toString())), eq(null), anyLong(), eq(1), eq(10))).
                thenThrow(new RuntimeException());

        ResponseEntity<List<ShortAdDTO>> response = controller.recentlyExpiredAdverts(30, request);

        assertNotNull(response);
        assertEquals(0, response.getBody().size());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }

    @Test
    public void testRecentlyExpiredAdverts_Positive() {
        Ad expired = new Ad();
        expired.setExpiryDate(LocalDate.now().minusDays(5).toDateTimeAtStartOfDay());
        ManageAdsSearchServiceResponse searchResponse = new ManageAdsSearchServiceResponse();
        searchResponse.setAdverts(Collections.singletonList(expired));
        when(accountApi.getAccount(anyLong())).thenReturn(new AccountBuilder().build());
        when(manageAdsSearchService.search(eq(Collections.singletonList(AdvertStatus.EXPIRED.toString())), eq(null), anyLong(), eq(1), eq(10))).
                thenReturn(searchResponse);

        ResponseEntity<List<ShortAdDTO>> response = controller.recentlyExpiredAdverts(30, request);

        assertNotNull(response);
        assertEquals(1, response.getBody().size());
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testRecentlyExpiredAdverts_ExpiredMoreThan30DaysAgo() {
        Ad expired = new Ad();
        expired.setExpiryDate(LocalDate.now().minusDays(35).toDateTimeAtStartOfDay());
        ManageAdsSearchServiceResponse searchResponse = new ManageAdsSearchServiceResponse();
        searchResponse.setAdverts(Collections.singletonList(expired));
        when(accountApi.getAccount(anyLong())).thenReturn(new AccountBuilder().build());
        when(manageAdsSearchService.search(eq(Collections.singletonList(AdvertStatus.EXPIRED.toString())), eq(null), anyLong(), eq(1), eq(10))).
                thenReturn(searchResponse);

        ResponseEntity<List<ShortAdDTO>> response = controller.recentlyExpiredAdverts(30, request);

        assertNotNull(response);
        assertEquals(0, response.getBody().size());
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testResponsivePageObjectModel() {
        mockProductPrices();

        List<AdPreview> adPreviewsList = mockAdPreview();
        AdPreview[] adPreviews = (adPreviewsList.subList(1, adPreviewsList.size())).toArray(new AdPreview[]{});
        when(converter.convert(any(Ad.class))).thenReturn(adPreviewsList.get(0), adPreviews);

        ModelAndView mav = controller.showManageAdsPageOne(null, request);
        assertThat(mav.getModel().containsKey("model"), equalTo(true));
        ManageAdsModel model = (ManageAdsModel) mav.getModel().get("model");
        Map<String, ResponsiveAdModel> adverts = model.getAdverts();

        assertNotNull(adverts.get("1"));
        assertEquals("2", adverts.get("1").getListingViews());
        assertEquals("3", adverts.get("1").getReplies());
        assertEquals("4", adverts.get("1").getBumpTimes());
        assertEquals("1.50", adverts.get("1").getBumpPrice());
        assertEquals("1.50", adverts.get("1").getUrgentPrice());
        assertEquals("1.50", adverts.get("1").getTopAd3Price());
        assertEquals("1.50", adverts.get("1").getTopAd7Price());
        assertEquals("1.50", adverts.get("1").getTopAd14Price());
        assertEquals("1.50", adverts.get("1").getSpotlightPrice());
        assertFalse(adverts.get("1").isUrgent());
        assertFalse(adverts.get("1").isFeatured());

        assertTrue(adverts.get("1").isEditable());
        assertTrue(adverts.get("1").isDeletable());
        assertTrue(adverts.get("1").isFeaturable());
        assertTrue(adverts.get("1").isCanBeBumpedUp());
        assertTrue(adverts.get("1").isCanBeHomepageSpotlight());
        assertTrue(adverts.get("1").isCanBeUrgent());
    }

    @Test
    public void testAdSellerType() {
        ManageAdsFeatureFormBean manageAdsFeatureFormBean = mockManageAdsFeatureFormBean();
        CoreModel.Builder coreBuilder = CoreModel.builder(Env.DEV, categoryModel, cookieResolver);
        ManageAdsModel rmadm = (ManageAdsModel) new ManageAdsModel.Builder(coreBuilder, true, false)
                .withManageAdsFeatureFormBean(manageAdsFeatureFormBean)
                .withStatisticForAdvert(new ArrayList<>())
                .withProductPrices(new HashMap<>())
                .withManageAdsUrls(manageAdsHelper, urlScheme)
                .build()
                .getModelMap().get("model");

        Map<String, ResponsiveAdModel> adverts = rmadm.getAdverts();

        assertNotNull(adverts.get("13"));
        assertNotNull(adverts.get("14"));
        assertNotNull(adverts.get("15"));

        ResponsiveAdModel defaultAd = adverts.get("13");
        ResponsiveAdModel privateAd = adverts.get("14");
        ResponsiveAdModel tradeAd = adverts.get("15");

        assertNull(defaultAd.getSellerType());
        assertEquals("private", privateAd.getSellerType());
        assertEquals("trade", tradeAd.getSellerType());

    }

    @Test
    public void testAdStatusType() {
        ManageAdsFeatureFormBean manageAdsFeatureFormBean = mockManageAdsFeatureFormBean();
        CoreModel.Builder coreBuilder = CoreModel.builder(Env.DEV, categoryModel, cookieResolver);
        ManageAdsModel rmadm = (ManageAdsModel) new ManageAdsModel.Builder(coreBuilder, true, false)
                .withManageAdsFeatureFormBean(manageAdsFeatureFormBean)
                .withStatisticForAdvert(new ArrayList<>())
                .withProductPrices(new HashMap<>())
                .withManageAdsUrls(manageAdsHelper, urlScheme)
                .build()
                .getModelMap().get("model");

        Map<String, ResponsiveAdModel> adverts = rmadm.getAdverts();

        assertNotNull(adverts.get("16"));

        ResponsiveAdModel awaitingVerifyAd = adverts.get("16");

        assertEquals("AWAITING_PHONE_VERIFIED", awaitingVerifyAd.getStatus());
        assertTrue(awaitingVerifyAd.isDeletable());
        assertFalse(awaitingVerifyAd.isCanBeBumpedUp());

    }

    @Test
    public void testAdStatusFlags() {
        ManageAdsFeatureFormBean manageAdsFeatureFormBean = mockManageAdsFeatureFormBean();
        CoreModel.Builder coreBuilder = CoreModel.builder(Env.DEV, categoryModel, cookieResolver);
        ManageAdsModel rmadm = (ManageAdsModel) new ManageAdsModel.Builder(coreBuilder, true, false)
                .withManageAdsFeatureFormBean(manageAdsFeatureFormBean)
                .withStatisticForAdvert(createStatistics())
                .withProductPrices(new HashMap<>())
                .withManageAdsUrls(manageAdsHelper, urlScheme)
                .build()
                .getModelMap().get("model");

        Map<String, ResponsiveAdModel> adverts = rmadm.getAdverts();

        assertNotNull(adverts.get("1"));
        assertNotNull(adverts.get("2"));
        assertNotNull(adverts.get("3"));
        assertNotNull(adverts.get("4"));
        assertNotNull(adverts.get("5"));
        assertNotNull(adverts.get("6"));
        assertNotNull(adverts.get("7"));
        assertNotNull(adverts.get("8"));
        assertNotNull(adverts.get("9"));
        assertNotNull(adverts.get("10"));
        assertNotNull(adverts.get("11"));
        assertNotNull(adverts.get("12"));
        assertNotNull(adverts.get("13"));

        ResponsiveAdModel defaultAd = adverts.get("1");
        ResponsiveAdModel expired = adverts.get("2");
        ResponsiveAdModel processing = adverts.get("3");
        ResponsiveAdModel deleted = adverts.get("4");
        ResponsiveAdModel removed = adverts.get("5");
        ResponsiveAdModel draft = adverts.get("6");
        ResponsiveAdModel expires = adverts.get("7");
        ResponsiveAdModel noSpotlight = adverts.get("8");
        ResponsiveAdModel noUrgent = adverts.get("9");
        ResponsiveAdModel noFeature3 = adverts.get("10");
        ResponsiveAdModel noFeature7 = adverts.get("11");
        ResponsiveAdModel noFeature14 = adverts.get("12");
        ResponsiveAdModel noImage = adverts.get("13");

        assertTrue(defaultAd.isEditable());
        assertTrue(defaultAd.isDeletable());
        assertTrue(defaultAd.isFeaturable());
        assertTrue(defaultAd.isCanBeBumpedUp());
        assertTrue(defaultAd.isCanBeHomepageSpotlight());
        assertTrue(defaultAd.isCanBeUrgent());

        assertTrue(expired.isEditable());
        assertFalse(expired.isDeletable());
        assertFalse(expired.isFeaturable());
        assertTrue(expired.isCanBeBumpedUp());
        assertFalse(expired.isCanBeHomepageSpotlight());
        assertFalse(expired.isCanBeUrgent());

        assertFalse(processing.isEditable());
        assertFalse(processing.isDeletable());
        assertFalse(processing.isFeaturable());
        assertFalse(processing.isCanBeBumpedUp());
        assertFalse(processing.isCanBeHomepageSpotlight());
        assertFalse(processing.isCanBeUrgent());

        assertTrue(deleted.isEditable());
        assertFalse(deleted.isDeletable());
        assertFalse(deleted.isFeaturable());
        assertFalse(deleted.isCanBeBumpedUp());
        assertFalse(deleted.isCanBeHomepageSpotlight());
        assertFalse(deleted.isCanBeUrgent());

        assertFalse(removed.isEditable());
        assertFalse(removed.isDeletable());
        assertFalse(removed.isFeaturable());
        assertFalse(removed.isCanBeBumpedUp());
        assertFalse(removed.isCanBeHomepageSpotlight());
        assertFalse(removed.isCanBeUrgent());

        assertTrue(draft.isEditable());
        assertTrue(draft.isDeletable());
        assertTrue(draft.isFeaturable());
        assertFalse(draft.isCanBeBumpedUp());
        assertTrue(draft.isCanBeHomepageSpotlight());
        assertTrue(draft.isCanBeUrgent());

        assertTrue(expires.isEditable());
        assertTrue(expires.isDeletable());
        assertTrue(expires.isFeaturable());
        assertFalse(expires.isCanBeBumpedUp());
        assertTrue(expires.isCanBeHomepageSpotlight());
        assertTrue(expires.isCanBeUrgent());

        assertTrue(noSpotlight.isEditable());
        assertTrue(noSpotlight.isDeletable());
        assertTrue(noSpotlight.isFeaturable());
        assertTrue(noSpotlight.isCanBeBumpedUp());
        assertFalse(noSpotlight.isCanBeHomepageSpotlight());
        assertTrue(noSpotlight.isCanBeUrgent());

        assertTrue(noUrgent.isEditable());
        assertTrue(noUrgent.isDeletable());
        assertTrue(noUrgent.isFeaturable());
        assertTrue(noUrgent.isCanBeBumpedUp());
        assertTrue(noUrgent.isCanBeHomepageSpotlight());
        assertFalse(noUrgent.isCanBeUrgent());

        assertTrue(noFeature3.isEditable());
        assertTrue(noFeature3.isDeletable());
        assertFalse(noFeature3.isFeaturable());
        assertTrue(noFeature3.isCanBeBumpedUp());
        assertTrue(noFeature3.isCanBeHomepageSpotlight());
        assertTrue(noFeature3.isCanBeUrgent());

        assertTrue(noFeature7.isEditable());
        assertTrue(noFeature7.isDeletable());
        assertFalse(noFeature7.isFeaturable());
        assertTrue(noFeature7.isCanBeBumpedUp());
        assertTrue(noFeature7.isCanBeHomepageSpotlight());
        assertTrue(noFeature7.isCanBeUrgent());

        assertTrue(noFeature14.isEditable());
        assertTrue(noFeature14.isDeletable());
        assertFalse(noFeature14.isFeaturable());
        assertTrue(noFeature14.isCanBeBumpedUp());
        assertTrue(noFeature14.isCanBeHomepageSpotlight());
        assertTrue(noFeature14.isCanBeUrgent());

        assertTrue(noImage.isEditable());
        assertTrue(noImage.isDeletable());
        assertTrue(noImage.isFeaturable());
        assertTrue(noImage.isCanBeBumpedUp());
        assertFalse(noImage.isCanBeHomepageSpotlight());
        assertTrue(noImage.isCanBeUrgent());
    }

    @Test
    public void showManageJobsOnMadgexButtonIsTrueForJobUser() {
        // given
        User user = new User();
        user.setJobsUser(true);
        when(userSession.getUser()).thenReturn(user);

        // when
        ManageAdsModel mam = (ManageAdsModel) controller.showManageAdsPageOne(null, request).getModel().get("model");

        // then
        assertThat(mam.isShowManageJobsOnMadgexButton(), equalTo(true));
    }

    @Test
    public void showManageJobsOnMadgexButtonIsFalseForNonJobUser() {
        // given
        User user = new User();
        user.setJobsUser(false);
        when(userSession.getUser()).thenReturn(user);

        // when
        ManageAdsModel mam = (ManageAdsModel) controller.showManageAdsPageOne(null, request).getModel().get("model");

        // then
        assertThat(mam.isShowManageJobsOnMadgexButton(), equalTo(false));
    }

    private ManageAdsFeatureFormBean mockManageAdsFeatureFormBean() {
        final AdFeature bumpup = new AdFeature();
        bumpup.setEndDate(new DateTime().plusMinutes(30));
        bumpup.setProductName(ProductName.BUMP_UP);

        final AdFeature spotlight = new AdFeature();
        spotlight.setEndDate(new DateTime().plusMinutes(30));
        spotlight.setProductName(ProductName.HOMEPAGE_SPOTLIGHT);

        final AdFeature urgent = new AdFeature();
        urgent.setEndDate(new DateTime().plusMinutes(30));
        urgent.setProductName(ProductName.URGENT);

        final AdFeature feature3 = new AdFeature();
        feature3.setEndDate(new DateTime().plusMinutes(30));
        feature3.setProductName(ProductName.FEATURE_3_DAY);

        final AdFeature feature7 = new AdFeature();
        feature7.setEndDate(new DateTime().plusMinutes(30));
        feature7.setProductName(ProductName.FEATURE_7_DAY);

        final AdFeature feature14 = new AdFeature();
        feature14.setEndDate(new DateTime().plusMinutes(30));
        feature14.setProductName(ProductName.FEATURE_14_DAY);

        Ad cantBumpUpAd = new Ad();
        cantBumpUpAd.setId(7l);
        cantBumpUpAd.setFeatures(new ArrayList<AdFeature>() {
            {
                add(bumpup);
            }
        });

        Ad cantSpotlight = new Ad();
        cantSpotlight.setId(8l);
        cantSpotlight.setFeatures(new ArrayList<AdFeature>() {
            {
                add(spotlight);
            }
        });

        Ad cantUrgent = new Ad();
        cantUrgent.setId(9l);
        cantUrgent.setFeatures(new ArrayList<AdFeature>() {
            {
                add(urgent);
            }
        });

        Ad cantFeature3 = new Ad();
        cantFeature3.setId(10l);
        cantFeature3.setFeatures(new ArrayList<AdFeature>() {
            {
                add(feature3);
            }
        });

        Ad cantFeature7 = new Ad();
        cantFeature7.setId(11l);
        cantFeature7.setFeatures(new ArrayList<AdFeature>() {
            {
                add(feature7);
            }
        });

        Ad cantFeature14 = new Ad();
        cantFeature14.setId(12l);
        cantFeature14.setFeatures(new ArrayList<AdFeature>() {
            {
                add(feature14);
            }
        });

        Ad noImage = new Ad();
        noImage.setId(13l);
        noImage.setFeatures(new ArrayList<AdFeature>());

        List<AdPreview> previews = mockAdPreview();
        ManageAdsFeatureFormBean manageAdsFeatureFormBean = new ManageAdsFeatureFormBean();
        manageAdsFeatureFormBean.getPreviews().addAll(previews);
        manageAdsFeatureFormBean.getAdvertFeatureMatrix().add(cantBumpUpAd);
        manageAdsFeatureFormBean.getAdvertFeatureMatrix().add(cantSpotlight);
        manageAdsFeatureFormBean.getAdvertFeatureMatrix().add(cantUrgent);
        manageAdsFeatureFormBean.getAdvertFeatureMatrix().add(cantFeature3);
        manageAdsFeatureFormBean.getAdvertFeatureMatrix().add(cantFeature7);
        manageAdsFeatureFormBean.getAdvertFeatureMatrix().add(cantFeature14);
        manageAdsFeatureFormBean.getAdvertFeatureMatrix().add(noImage);
        return manageAdsFeatureFormBean;
    }

    private void mockProductPrices() {
        Map<Long, List<ApiProductPrice>> apiPrices = new HashMap<>();
        List<ApiProductPrice> list = Arrays.asList(
                new ApiProductPrice(1L, 150L, 120L, ProductName.INSERTION, ""),
                new ApiProductPrice(1L, 150L, 120L, ProductName.BUMP_UP, ""),
                new ApiProductPrice(1L, 150L, 120L, ProductName.URGENT, ""),
                new ApiProductPrice(1L, 150L, 120L, ProductName.FEATURE_3_DAY, ""),
                new ApiProductPrice(1L, 150L, 120L, ProductName.FEATURE_7_DAY, ""),
                new ApiProductPrice(1L, 150L, 120L, ProductName.FEATURE_14_DAY, ""),
                new ApiProductPrice(1L, 150L, 120L, ProductName.HOMEPAGE_SPOTLIGHT, ""));
        List<ApiProductPrice> list2 = Arrays.asList(
                new ApiProductPrice(2L, 250L, 220L, ProductName.INSERTION, ""),
                new ApiProductPrice(2L, 250L, 220L, ProductName.BUMP_UP, ""),
                new ApiProductPrice(2L, 250L, 220L, ProductName.URGENT, ""),
                new ApiProductPrice(2L, 250L, 220L, ProductName.FEATURE_3_DAY, ""),
                new ApiProductPrice(2L, 250L, 220L, ProductName.FEATURE_7_DAY, ""),
                new ApiProductPrice(2L, 250L, 220L, ProductName.FEATURE_14_DAY, ""),
                new ApiProductPrice(2L, 250L, 220L, ProductName.HOMEPAGE_SPOTLIGHT, ""));
        apiPrices.put(1L, list);
        apiPrices.put(2L, list2);
        when(priceApi.getProductPricesForAdverts(anyString())).thenReturn(apiPrices);
    }

    protected void initApiCallExecutor() {
        ApiOrder apiOrder = new ApiOrder();
        ApiCallResponse<User> userApiBushfireResponse;
        ApiCallResponse<Account> accountApiBushfireResponse;
        ApiCallResponse<List<Account>> accountsApiBushfireResponse;
        userApiBushfireResponse = mock(ApiCallResponse.class);
        User user = new User();
        user.setEmail("<EMAIL>");
        when(userApiBushfireResponse.getResponseObject()).thenReturn(user);
        accountApiBushfireResponse = mock(ApiCallResponse.class);
        accountsApiBushfireResponse = mock(ApiCallResponse.class);
        List<Account> accounts = new ArrayList<>();
        accounts.add(new Account());
        when(accountsApiBushfireResponse.getResponseObject()).thenReturn(accounts);
        when(apiCallExecutor.call(Matchers.isA(GetUserApiCall.class))).thenReturn(userApiBushfireResponse);
        when(apiCallExecutor.call(Matchers.isA(GetAccountCommand.class))).thenReturn(accountApiBushfireResponse);
        when(apiCallExecutor.call(Matchers.isA(GetAccountsApiCall.class))).thenReturn(accountsApiBushfireResponse);
        when(apiCallExecutor.call(Matchers.isA(CreateOrderApiCall.class))).thenReturn(new DefaultApiCallResponse<>(apiOrder));
    }

    private void initBapi() {
        when(bushfireApi.accountApi()).thenReturn(accountApi);
        when(bushfireApi.advertApi()).thenReturn(advertApi);
        when(bushfireApi.userApi()).thenReturn(userApi);
        BushfireApiKey bushfireApiKey = new BushfireApiKey();
        when(userSession.getApiKey()).thenReturn(bushfireApiKey);
        when(bushfireApi.create(PriceApi.class, bushfireApiKey)).thenReturn(priceApi);
    }

    private void initPagination() {
        DecadicPagination decadicPagination = new DecadicPagination(new ArrayList<>());
        Pagination pagination = new Pagination(new ArrayList<>(), decadicPagination, 5, 6, true);
        when(paginationService.getPagination(Matchers.<PaginationUrlGenerator>any(), anyInt(), anyInt(), anyInt(), anyInt(),
                anyInt())).thenReturn(pagination);
    }

    private List<Ad> createAds() {
        List<Ad> ads = new ArrayList<>();
        for (long x = 1; x < 5; x++) {
            Ad ad = new Ad();
            ad.setId(x);
            ads.add(ad);
        }
        return ads;
    }

    private AdSearchResponse createAdSearchResponse(List<Ad> adverts) {
        AdSearchResponse adSearchResponse = new AdSearchResponse();
        adSearchResponse.setPostings(adverts.toArray(new Ad[adverts.size()]));
        ReflectionTestUtils.setField(adSearchResponse, "totalCount", adverts.size());
        return adSearchResponse;
    }

    private List<AdvertStatisticData> createStatistics() {
        AdvertStatisticData advertStats = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(1)
                .advertTitle("testAdvert")
                .numberOfReplies(3)
                .numberOfTimesReposted(4l)
                .srpViews(2).build();

        AdvertStatisticData expired = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(2)
                .advertTitle("expired test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.EXPIRED)
                .srpViews(250).build();

        AdvertStatisticData processing = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(3)
                .advertTitle("processing test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.PROCESSING)
                .srpViews(250).build();

        AdvertStatisticData deleted = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(4)
                .advertTitle("deleted test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.DELETED)
                .srpViews(250).build();

        AdvertStatisticData removed = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(5)
                .advertTitle("removed test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.REMOVED)
                .srpViews(250).build();

        AdvertStatisticData draft = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(6)
                .advertTitle("draft test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.DRAFT)
                .srpViews(250).build();

        AdvertStatisticData expires = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(7)
                .advertTitle("expires test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.LIVE)
                .srpViews(250).build();

        AdvertStatisticData spotlight = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(8)
                .advertTitle("spotlight test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.LIVE)
                .srpViews(250).build();

        AdvertStatisticData urgent = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(9)
                .advertTitle("urgent test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.LIVE)
                .srpViews(250).build();

        AdvertStatisticData feature3 = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(10)
                .advertTitle("feature3 test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.LIVE)
                .srpViews(250).build();

        AdvertStatisticData feature7 = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(11)
                .advertTitle("feature7 test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.LIVE)
                .srpViews(250).build();

        AdvertStatisticData feature14 = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(12)
                .advertTitle("feature14 test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.LIVE)
                .srpViews(250).build();

        AdvertStatisticData noImage = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(13)
                .advertTitle("no Image test Advert")
                .numberOfReplies(40)
                .numberOfTimesReposted(0l)
                .advertStatus(ManageAdStatus.LIVE)
                .srpViews(250).build();

        List<AdvertStatisticData> list = new ArrayList<>();
        list.add(advertStats);
        list.add(expired);
        list.add(processing);
        list.add(deleted);
        list.add(removed);
        list.add(draft);
        list.add(expires);
        list.add(spotlight);
        list.add(urgent);
        list.add(feature3);
        list.add(feature7);
        list.add(feature14);
        list.add(noImage);
        return list;
    }

    private List<AdPreview> mockAdPreview() {
        List<AdPreview> list = new ArrayList<>();

        AdPreview defaultAd = new AdPreviewImpl.Builder()
                .id(1l).status(AdStatus.LIVE)
                .previewUrl("not-empty")
                .build();
        AdPreview expired = new AdPreviewImpl.Builder()
                .id(2l).status(AdStatus.EXPIRED)
                .previewUrl("not-empty")
                .build();
        AdPreview processing = new AdPreviewImpl.Builder()
                .id(3l).status(AdStatus.AWAITING_SCREENING)
                .previewUrl("not-empty")
                .build();
        AdPreview deleted = new AdPreviewImpl.Builder()
                .id(4l).status(AdStatus.DELETED_USER)
                .previewUrl("not-empty")
                .build();
        AdPreview removed = new AdPreviewImpl.Builder()
                .id(5l).status(AdStatus.DELETED_CS)
                .previewUrl("not-empty")
                .build();
        AdPreview draft = new AdPreviewImpl.Builder()
                .id(6l).status(AdStatus.DRAFT)
                .previewUrl("not-empty")
                .build();
        //expires set externally, not part of the model in here
        AdPreview expires = new AdPreviewImpl.Builder()
                .id(7l).status(AdStatus.LIVE)
                .previewUrl("not-empty")
                .build();
        AdPreview spotlight = new AdPreviewImpl.Builder()
                .id(8l).status(AdStatus.LIVE)
                .previewUrl("not-empty")
                .build();
        AdPreview urgent = new AdPreviewImpl.Builder()
                .id(9l).status(AdStatus.LIVE)
                .previewUrl("not-empty")
                .build();
        AdPreview feature3 = new AdPreviewImpl.Builder()
                .id(10l).status(AdStatus.LIVE)
                .previewUrl("not-empty")
                .build();
        AdPreview feature7 = new AdPreviewImpl.Builder()
                .id(11l).status(AdStatus.LIVE)
                .previewUrl("not-empty")
                .build();
        AdPreview feature14 = new AdPreviewImpl.Builder()
                .id(12l).status(AdStatus.LIVE)
                .previewUrl("not-empty")
                .build();
        AdPreview noImage = new AdPreviewImpl.Builder()
                .id(13l).status(AdStatus.LIVE)
                .previewUrl(null)
                .build();
        AdPreview withPrivateSellerType = new AdPreviewImpl.Builder()
                .id(14L).status(AdStatus.LIVE)
                .previewUrl("not-empty")
                .sellerType("private")
                .build();
        AdPreview withTradeSellerType = new AdPreviewImpl.Builder()
                .id(15L).status(AdStatus.LIVE)
                .previewUrl("not-empty")
                .sellerType("trade")
                .build();

        AdPreview awaitingPhoneVerify = new AdPreviewImpl.Builder()
                .id(16L).status(AdStatus.AWAITING_PHONE_VERIFIED)
                .build();

        list.add(defaultAd);
        list.add(expired);
        list.add(processing);
        list.add(deleted);
        list.add(removed);
        list.add(draft);
        list.add(expires);
        list.add(spotlight);
        list.add(urgent);
        list.add(feature3);
        list.add(feature7);
        list.add(feature14);
        list.add(noImage);
        list.add(withPrivateSellerType);
        list.add(withTradeSellerType);
        list.add(awaitingPhoneVerify);
        return list;
    }

    private ManageAdsSearchServiceResponse createManageAdsSearchServiceResponse() {
        ManageAdsSearchServiceResponse manageAdsSearchServiceResponse = new ManageAdsSearchServiceResponse();

        AdSearchResponse response = createAdSearchResponse(createAds());
        response.getPostings();
        manageAdsSearchServiceResponse.setAdverts(response.getPostings());
        manageAdsSearchServiceResponse.setTotalResults(response.getPostings().size());

        return manageAdsSearchServiceResponse;
    }

    private String getRedirectUrl(ModelAndView modelAndView) {
        return ((RedirectView) modelAndView.getView()).getUrl();
    }

    private class AccountBuilder {

        private Account account;

        public AccountBuilder() {
            this.account = new Account();
            this.account.setStatus(AccountStatus.ACTIVE);
        }

        public Account build() {
            return account;
        }

        public AccountBuilder withId(Long id) {
            account.setId(id);
            return this;
        }

        public AccountBuilder withName(String name) {
            account.setName(name);
            return this;
        }

        public AccountBuilder withDescription(String name) {
            account.setDescription(name);
            return this;
        }

        public AccountBuilder isPro() {
            account.setPro(true);
            return this;
        }

        public AccountBuilder withStatus(AccountStatus status) {
            account.setStatus(status);
            return this;
        }
    }

    @Test
    public void checkoutMetaInjectorShouldBeCalledWithoutAdParameter() {
        ModelAndView modelAndView = controller.featureAdvertsForStandardUser(mock(HttpServletRequest.class));

        verify(checkoutContainer).createCheckout(anyObject());
    }

    @Test
    public void checkoutMetaInjectorShouldBeCalledWithAdParameter() {
        Ad ad = new Ad();
        ad.setAccountId(selectedAccount.getId());
        ad.setId(1L);
        ad.setFeatures(new ArrayList<AdFeature>(Arrays.asList()));

        HttpServletRequest request = mock(HttpServletRequest.class);
        when(bushfireApi.advertApi().getAdverts(Matchers.anyList())).thenReturn(new ArrayList<Ad>(Arrays.asList(ad)));

        Map<String, String[]> requestMap = new HashMap<String, String[]>();
        requestMap.put("1", new String[]{"BUMP_UP"});
        when(request.getParameterMap()).thenReturn(requestMap);
        ModelAndView modelAndView = controller.featureAdvertsForStandardUser(request);

        verify(checkoutMetaInjector).injectTrackingForManageAdsUpdate(any(Checkout.class), eq(false), eq(ad));

    }

    private Ad createAdWithId(Long id) {
        Ad ad = new Ad();
        ad.setAccountId(selectedAccount.getId());
        ad.setId(1L);
        ad.setFeatures(new ArrayList<AdFeature>(Arrays.asList()));
        return ad;
    }

    @Test
    public void checkoutMetaInjectorShouldBeCalledWithMultipleYes() {
        Ad ad1 = createAdWithId(1L);
        Ad ad2 = createAdWithId(2L);

        HttpServletRequest request = mock(HttpServletRequest.class);
        when(bushfireApi.advertApi().getAdverts(Matchers.anyList())).thenReturn(new ArrayList<Ad>(Arrays.asList(ad1, ad2)));

        Map<String, String[]> requestMap = new HashMap<String, String[]>();
        requestMap.put("1", new String[]{"BUY_TOP_AD", "BUMP_UP"});
        requestMap.put("2", new String[]{"BUY_TOP_AD", "FEATURE_3_DAY"});
        when(request.getParameterMap()).thenReturn(requestMap);
        ModelAndView modelAndView = controller.featureAdvertsForStandardUser(request);

        verify(checkoutMetaInjector).injectTrackingForManageAdsUpdate(any(Checkout.class), eq(true), any(Ad.class));
    }

    @Test
    public void checkoutMetaInjectorShouldBeCalledWithMultipleNo() {
        Ad ad1 = createAdWithId(1L);

        HttpServletRequest request = mock(HttpServletRequest.class);
        when(bushfireApi.advertApi().getAdverts(Matchers.anyList())).thenReturn(new ArrayList<Ad>(Arrays.asList(ad1)));

        Map<String, String[]> requestMap = new HashMap<String, String[]>();
        requestMap.put("1", new String[]{"BUY_TOP_AD", "BUMP_UP", "FEATURE_3_DAY"});

        when(request.getParameterMap()).thenReturn(requestMap);
        ModelAndView modelAndView = controller.featureAdvertsForStandardUser(request);

        verify(checkoutMetaInjector).injectTrackingForManageAdsUpdate(any(Checkout.class), eq(false), any(Ad.class));
        verify(customMetricRegistry, atLeastOnce()).metricCounter(anyString(),anyString());
    }

}
