package com.gumtree.web.seller.page.manageads.reporting;

import com.gumtree.api.category.domain.Category;
import com.gumtree.common.test.hamcrest.Answers;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsElementTrackEvent;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

/**
 */
public class ManageAdsGoogleAnalyticsConfigurerTest {

    private GoogleAnalyticsReportBuilder builder;

    private ThirdPartyRequestContext ctx;

    private ManageAdsGoogleAnalyticsConfigurer configurer;

    @Before
    public void init() {
        builder = mock(GoogleAnalyticsReportBuilder.class, Answers.fluentInterfaceAnswer());
        ctx = mock(ThirdPartyRequestContext.class);
        configurer = new ManageAdsGoogleAnalyticsConfigurer();
        when(ctx.getCategory()).thenReturn(mock(Category.class));
        when(ctx.getLocation()).thenReturn(mock(Location.class));
    }

    @Test
    public void verifyPageTypeSet() {
        when(ctx.getPageType()).thenReturn(PageType.MyAds);
        configurer.configure(builder, ctx);

        verify(builder).pageType(any(PageType.class));
        ArgumentCaptor<GoogleAnalyticsElementTrackEvent> elementCaptor = ArgumentCaptor.forClass(GoogleAnalyticsElementTrackEvent.class);
        verify(builder, times(2)).addTrackEvent(elementCaptor.capture());
        List<GoogleAnalyticsElementTrackEvent> values = elementCaptor.getAllValues();
        assertThat(values.get(0).getAction(), equalTo("PostAdBegin"));
        assertThat(values.get(1).getAction(), equalTo("RegisterCVBegin"));
        verifyNoMoreInteractions(builder);
    }

}
