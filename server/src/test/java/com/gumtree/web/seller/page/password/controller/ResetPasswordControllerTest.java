package com.gumtree.web.seller.page.password.controller;

import com.google.common.collect.ImmutableMap;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.bapi.model.PasswordKey;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.ChangePasswordRequest;
import com.gumtree.user.service.support.builder.UserApiErrorsBuilder;
import com.gumtree.user.service.support.factory.GumtreeAccessTokenFactory;
import com.gumtree.util.model.Action;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.password.api.UserApiService;
import com.gumtree.web.seller.page.password.model.UpdatePasswordModel;
import com.gumtree.web.seller.page.password.model.UpdatePasswordResult;
import org.apache.commons.codec.binary.Base64;
import org.apache.shiro.subject.Subject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Matchers;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.ui.Model;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import java.io.IOException;
import java.util.Map;

import static com.gumtree.web.seller.page.NoDependenciesController.ENCRYPTED_PARAMETER_MAP_NAME;
import static com.gumtree.web.seller.page.password.controller.ResetPasswordController.PASSWORD_RESET_KEY_PARAM;
import static com.gumtree.web.seller.page.password.controller.ResetPasswordController.USER_ID_PARAM;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.core.IsInstanceOf.instanceOf;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ResetPasswordControllerTest extends BaseSellerControllerTest {

    public static final String PASSWORD_KEY = "passwordKey";
    public static final String USERNAME = "username";
    public static final String PASSWORD = "password";
    public static final String PARAMETER_FORM_K = "form.k";

    private ResetPasswordController controller;
    private ApiCallResponse apiCallResponse;
    private LoginUtils loginUtils;
    private UserServiceFacade userServiceFacade;
    private Model model;
    private UserApiService userControllerApiService;

    @Before
    public void init() {
        apiCallResponse = mock(ApiCallResponse.class);
        loginUtils = mock(LoginUtils.class);
        userServiceFacade = mock(UserServiceFacade.class);
        model = mock(Model.class);
        userControllerApiService = mock(UserApiService.class);

        when(apiCallResponse.isErrorResponse()).thenReturn(false);
        when(apiCallExecutor.call(Matchers.any())).thenReturn(apiCallResponse);

        controller = new ResetPasswordController(cookieResolver, categoryModel, apiCallExecutor, messageResolver,
                urlScheme, zenoService, loginUtils, userSessionService, userServiceFacade, metricRegistry,
                parameterEncryption,userControllerApiService);

        when(messageResolver.getMessage(anyString(), anyString())).thenReturn("Your password has been reset.");

        autowireAbExperimentsService(controller);
    }

    @Test
    public void shouldShowPage() {
        // when
        ModelAndView result = controller.showPageWithUrlParams(PASSWORD, request);

        // then
        assertThat(result.getViewName(), equalTo(Page.ResetPassword.getTemplateName()));
        assertThat(getModel(result).getKey(),equalTo(controller.encodeParam(PASSWORD)));
        assertThat(getModel(result).isReload(),equalTo(true));
    }


    @Test
    public void shouldShowResetPageWhenEncryptedParametersMissing() throws IOException {
        // when
        ModelAndView result = controller.showPageWithUrlParams(null, request);

        // then
        RedirectView view = (RedirectView) result.getView();
        assertThat(view.getUrl(), equalTo(ForgottenPasswordController.PAGE_PATH));
    }

    @Test
    public void shouldShowPageApiResponseFail() throws IOException {
        // given
        String encrypted = "encrypted..";

        when(request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME)).thenReturn(encrypted);
        when(apiCallResponse.isErrorResponse()).thenReturn(true);

        Map<String, Object> modelMap = ImmutableMap.of(USER_ID_PARAM, USERNAME, PASSWORD_RESET_KEY_PARAM, PASSWORD);
        when(model.asMap()).thenReturn(modelMap);

        // when
        ModelAndView result = controller.showPage(request);

        // then
        UpdatePasswordModel model = getModel(result);
        assertThat(result.getViewName(), equalTo(Page.ResetPassword.getTemplateName()));
        assertTrue(model.isErrorPage());
    }

    @Test
    public void shouldShowPageApiResponseSuccess() throws IOException {
        // given
        String key = "valid-reset-password-key";
        String encrypted = "encrypted..";

        when(request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME)).thenReturn(encrypted);
        when(request.getParameter("form.k")).thenReturn(encrypted);
        when(apiCallResponse.isErrorResponse()).thenReturn(true);

        when(userControllerApiService.getPasswordKey(anyString()))
                .thenReturn( new PasswordKey().key(key).username("valid-user"));

        Map<String, Object> modelMap = ImmutableMap.of(USER_ID_PARAM, USERNAME, PASSWORD_RESET_KEY_PARAM, PASSWORD);
        when(model.asMap()).thenReturn(modelMap);

        // when
        ModelAndView result = controller.showPage(request);

        // then
        UpdatePasswordModel model = getModel(result);
        assertThat(result.getViewName(), equalTo(Page.ResetPassword.getTemplateName()));
        assertThat(model.getActionName(), equalTo("Reset"));

        assertThat(model.getCore().getGaEvents().get(0) , equalTo("PasswordResetAttempt"));
    }

    @Test
    public void resetPasswordLoginExperimentShouldShowPageApiResponseFail() throws IOException {
        // given
        String encrypted = "";

        when(request.getParameter(PARAMETER_FORM_K)).thenReturn(encrypted);

        Map<String, Object> modelMap = ImmutableMap.of(USER_ID_PARAM, USERNAME, PASSWORD_RESET_KEY_PARAM, PASSWORD);
        when(model.asMap()).thenReturn(modelMap);

        // when
        ResponseEntity<UpdatePasswordResult> result = controller.showResetPasswordPage(request);

        // then
        assertThat(result.getStatusCode(), equalTo(HttpStatus.BAD_REQUEST));
        assertThat(result.getBody().isErrorPage(), equalTo(true));
    }

    @Test
    public void resetPasswordLoginExperimentShouldShowPageApiResponseFailWhenNoPassordKey() throws IOException {
        // given
        String encrypted = "encrypted..";

        when(request.getParameter(PARAMETER_FORM_K)).thenReturn(encrypted);

        when(userControllerApiService.getPasswordKey(any())).thenReturn(null);

        Map<String, Object> modelMap = ImmutableMap.of(USER_ID_PARAM, USERNAME, PASSWORD_RESET_KEY_PARAM, PASSWORD);
        when(model.asMap()).thenReturn(modelMap);

        // when
        ResponseEntity<UpdatePasswordResult> result = controller.showResetPasswordPage(request);

        // then
        assertThat(result.getStatusCode(), equalTo(HttpStatus.BAD_REQUEST));
        assertThat(result.getBody().isErrorPage(), equalTo(true));
    }


    @Test
    public void resetPasswordLoginExperimentShouldShowPageApiResponseSuccess() throws IOException {
        // given
        String key = "valid-reset-password-key";
        String encrypted = "encrypted..";

        when(request.getParameter(ENCRYPTED_PARAMETER_MAP_NAME)).thenReturn(encrypted);
        when(request.getParameter("form.k")).thenReturn(encrypted);
        when(apiCallResponse.isErrorResponse()).thenReturn(true);

        when(userControllerApiService.getPasswordKey(anyString()))
                .thenReturn( new PasswordKey().key(key).username("valid-user"));

        Map<String, Object> modelMap = ImmutableMap.of(USER_ID_PARAM, USERNAME, PASSWORD_RESET_KEY_PARAM, PASSWORD);
        when(model.asMap()).thenReturn(modelMap);

        // when
        ResponseEntity<UpdatePasswordResult> result = controller.showResetPasswordPage(request);

        // then
        assertThat(result.getStatusCode(), equalTo(HttpStatus.OK));
        assertThat(result.getBody().isErrorPage(), equalTo(false));
    }

    @Test
    public void shouldResetPassword() throws IOException {
        // given
        String redirectUrl = "http://example.com";
        when(urlScheme.urlFor(any(Action.class))).thenReturn(redirectUrl);

        Subject subject = mock(Subject.class);
        RedirectAttributes redirectAttributes = mock(RedirectAttributes.class);

        String passwordKeyEncoded = Base64.encodeBase64URLSafeString(PASSWORD_KEY.getBytes());
        String usernameEncoded = Base64.encodeBase64URLSafeString(USERNAME.getBytes());
        when(userServiceFacade.changePassword(any(ChangePasswordRequest.class)))
                .thenReturn(ApiResponse.of(GumtreeAccessTokenFactory.create(1L)));

        // when
        final ModelAndView result = controller.resetPassword(subject, redirectAttributes, request,
                passwordKeyEncoded, usernameEncoded, PASSWORD, PASSWORD);

        // then
        verify(loginUtils).login(subject, USERNAME, PASSWORD);
        assertThat(result.getView(), instanceOf(RedirectView.class));
        String url = ((RedirectView) result.getView()).getUrl();
        assertThat(url, equalTo(redirectUrl));
    }

    @Test
    public void shouldFailResetPassword() throws IOException {
        // given
        String redirectUrl = "http://example.com";
        when(urlScheme.urlFor(any(Action.class))).thenReturn(redirectUrl);

        Subject subject = mock(Subject.class);
        RedirectAttributes redirectAttributes = mock(RedirectAttributes.class);

        String passwordKeyEncoded = Base64.encodeBase64URLSafeString(PASSWORD_KEY.getBytes());
        String usernameEncoded = Base64.encodeBase64URLSafeString(USERNAME.getBytes());

        when(userServiceFacade.changePassword(any(ChangePasswordRequest.class)))
                .thenReturn(ApiResponse.error(UserApiErrorsBuilder.builder().withErrorCode("code").build()));

        // when
        final ModelAndView result = controller.resetPassword(subject, redirectAttributes, request,
                passwordKeyEncoded, usernameEncoded, PASSWORD, PASSWORD);

        // then
        verifyNoMoreInteractions(loginUtils);
        UpdatePasswordModel model = getModel(result);
        assertThat(result.getViewName(), equalTo(Page.ResetPassword.getTemplateName()));
        assertThat(model.getActionName(), equalTo("Reset"));
        assertThat(model.getCore().getGaEvents().get(0) , equalTo("PasswordResetFail"));
    }

    @Test
    public void shouldSubmitResetPassword() throws IOException {
        // given
        String redirectUrl = "http://example.com";
        when(urlScheme.urlFor(any(Action.class))).thenReturn(redirectUrl);

        Subject subject = mock(Subject.class);
        RedirectAttributes redirectAttributes = mock(RedirectAttributes.class);

        String passwordKeyEncoded = Base64.encodeBase64URLSafeString(PASSWORD_KEY.getBytes());
        String usernameEncoded = Base64.encodeBase64URLSafeString(USERNAME.getBytes());
        when(userServiceFacade.changePassword(any(ChangePasswordRequest.class)))
                .thenReturn(ApiResponse.of(GumtreeAccessTokenFactory.create(1L)));

        // when
        final ResponseEntity<UpdatePasswordResult> result = controller.submitResetPassword(subject,
                request, passwordKeyEncoded, usernameEncoded, PASSWORD, PASSWORD);

        // then
        verify(loginUtils).login(subject, USERNAME, PASSWORD);
        assertThat(result.getStatusCode(), equalTo(HttpStatus.OK));
    }

    @Test
    public void shouldFailSubmitResetPassword() throws IOException {
        // given
        String redirectUrl = "http://example.com";
        when(urlScheme.urlFor(any(Action.class))).thenReturn(redirectUrl);

        Subject subject = mock(Subject.class);
        RedirectAttributes redirectAttributes = mock(RedirectAttributes.class);

        String passwordKeyEncoded = Base64.encodeBase64URLSafeString(PASSWORD_KEY.getBytes());
        String usernameEncoded = Base64.encodeBase64URLSafeString(USERNAME.getBytes());

        when(userServiceFacade.changePassword(any(ChangePasswordRequest.class)))
                .thenReturn(ApiResponse.error(UserApiErrorsBuilder.builder().withErrorCode("code").build()));

        // when
        final ResponseEntity<UpdatePasswordResult> result = controller.submitResetPassword(subject,
                request, passwordKeyEncoded, usernameEncoded, PASSWORD, PASSWORD);

        // then
        verifyNoMoreInteractions(loginUtils);
        assertThat(result.getStatusCode(), equalTo(HttpStatus.BAD_REQUEST));
        assertThat(result.getBody().getActionName(), equalTo("Reset"));
    }

    @Test
    public void shouldFailSubmitResetPasswordWhenInvalidUsername() throws IOException {
        // given
        String redirectUrl = "http://example.com";
        when(urlScheme.urlFor(any(Action.class))).thenReturn(redirectUrl);

        Subject subject = mock(Subject.class);
        RedirectAttributes redirectAttributes = mock(RedirectAttributes.class);

        String passwordKeyEncoded = "";
        String usernameEncoded = Base64.encodeBase64URLSafeString(USERNAME.getBytes());

        when(userServiceFacade.changePassword(any(ChangePasswordRequest.class)))
                .thenReturn(ApiResponse.error(UserApiErrorsBuilder.builder().withErrorCode("code").build()));

        // when
        final ResponseEntity<UpdatePasswordResult> result = controller.submitResetPassword(subject,
                request, passwordKeyEncoded, usernameEncoded, PASSWORD, PASSWORD);

        // then
        verifyNoMoreInteractions(userServiceFacade);
        verifyNoMoreInteractions(loginUtils);
        assertThat(result.getStatusCode(), equalTo(HttpStatus.BAD_REQUEST));
        assertThat(result.getBody().getActionName(), equalTo("Reset"));
    }

    private UpdatePasswordModel getModel(ModelAndView result) {
        return (UpdatePasswordModel) result.getModel().get(CommonModel.MODEL_KEY);
    }
}
