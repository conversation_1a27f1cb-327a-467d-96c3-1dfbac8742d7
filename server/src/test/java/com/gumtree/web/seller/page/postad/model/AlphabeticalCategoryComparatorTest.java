package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.category.domain.Category;
import org.junit.Assert;
import org.junit.Test;

/**
 * User: <PERSON>
 * Date: 21/08/13
 * Time: 16:39
 */
public class AlphabeticalCategoryComparatorTest {


    @Test
    public void testCompareOther() throws Exception {

        AlphabeticalCategoryComparator comparator = new AlphabeticalCategoryComparator();

        Category categoryWithOther = postAdCategory(1, "another-shoes", "Other Shoes", 123);
        Category postAdCategory = postAdCategory(2, "nice-shoes", "Nice Shoes", 123);

        Assert.assertTrue(comparator.compare(categoryWithOther, postAdCategory) > 0);
        Assert.assertTrue(comparator.compare(postAdCategory, categoryWithOther) < 0);
    }

    private static Category postAdCategory(int id, String name, String seoDisplayName, int parentId) {
        Category category = new Category();
        category.setId((long)id);
        category.setSeoName(name);
        category.setSeoDisplayName(seoDisplayName);
        category.setParentId((long) parentId);
        return category;
    }
}
