package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.seller.domain.product.entity.ProductName;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

/**
 */
public class SingleAdvertShoppingCartTest {

    @Test
    public void orderItemsEmptyWhenNoProductsAdded() {
        SingleAdvertShoppingCart cart = new SingleAdvertShoppingCart(1L, 2L);
        CreateOrderBean orderBean = cart.toOrderBean();
        assertThat(orderBean.getAccountId(), equalTo(2L));
        assertThat(orderBean.getItems().size(), equalTo(0));
    }

    @Test
    public void sameProductNotAddedMoreThanOnce() {
        SingleAdvertShoppingCart cart = new SingleAdvertShoppingCart(1L, 2L);
        cart.addProduct(ProductName.INSERTION);
        cart.addProduct(ProductName.INSERTION);
        CreateOrderBean orderBean = cart.toOrderBean();
        assertThat(orderBean.getItems().size(), equalTo(1));
    }

    @Test
    public void orderBeanCreatedCorrectlyAfterProductsAdded() {
        SingleAdvertShoppingCart cart = new SingleAdvertShoppingCart(1L, 2L);
        cart.addProduct(ProductName.INSERTION);
        cart.addProduct(ProductName.BUMP_UP);
        cart.addProduct(ProductName.FEATURE_3_DAY);
        CreateOrderBean orderBean = cart.toOrderBean();
        assertThat(orderBean.getAccountId(), equalTo(2L));
        assertThat(orderBean.getItems().size(), equalTo(3));
        assertThat(orderBean.getItems().get(0).getAdvertId(), equalTo(1L));
        assertThat(orderBean.getItems().get(0).getProductName(), equalTo(ProductName.INSERTION));
        assertThat(orderBean.getItems().get(1).getAdvertId(), equalTo(1L));
        assertThat(orderBean.getItems().get(1).getProductName(), equalTo(ProductName.BUMP_UP));
        assertThat(orderBean.getItems().get(2).getAdvertId(), equalTo(1L));
        assertThat(orderBean.getItems().get(2).getProductName(), equalTo(ProductName.FEATURE_3_DAY));
    }
}
