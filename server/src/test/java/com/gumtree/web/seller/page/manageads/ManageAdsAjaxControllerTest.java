package com.gumtree.web.seller.page.manageads;

import com.gumtree.api.client.BushfireApi;
import com.gumtree.web.security.UserSession;
import org.junit.Test;
import org.springframework.web.servlet.ModelAndView;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;

/**
 */
public class ManageAdsAjaxControllerTest {

    private BushfireApi bushfireApi;
    private UserSession userSession;


    @Test
    public void testShowBumpUp() throws Exception {
        ModelAndView view = controller().showBumpUp(1l);

        assertThat(view.getViewName(), equalTo(ManageAdsAjaxController.BUMPUP_VIEW));
    }

    @Test
    public void testShowBumpUpResponsive() throws Exception {
        ModelAndView view = controller().showBumpUp(1l);

        assertThat(view.getViewName(), equalTo(ManageAdsAjaxController.BUMPUP_VIEW));
    }

    @Test
    public void testShowUrgent() throws Exception {
        ModelAndView view= controller().showUrgent(1l);

        assertThat(view.getViewName(), equalTo(ManageAdsAjaxController.URGENT_VIEW));
    }

    @Test
    public void testShowUrgentResponsive() throws Exception {
        ModelAndView view = controller().showUrgent(1l);

        assertThat(view.getViewName(), equalTo(ManageAdsAjaxController.URGENT_VIEW));
    }

    @Test
    public void testShowSpotlight() throws Exception {
        ModelAndView view= controller().showSpotlight(1l);

        assertThat(view.getViewName(), equalTo(ManageAdsAjaxController.SPOTLIGHT_VIEW));
    }
    @Test
    public void testShowSpotlightResponsive() throws Exception {
        ModelAndView view = controller().showSpotlight(1l);

        assertThat(view.getViewName(), equalTo(ManageAdsAjaxController.SPOTLIGHT_VIEW));
    }

    @Test
    public void testShowTopAd() throws Exception {
        ModelAndView view = controller().showTopAd(1l);

        assertThat(view.getViewName(), equalTo(ManageAdsAjaxController.TOPAD_VIEW));
    }

    @Test
    public void testShowTopAdResponsive() throws Exception {
        ModelAndView view = controller().showTopAd(1l);

        assertThat(view.getViewName(), equalTo(ManageAdsAjaxController.TOPAD_VIEW));
    }

    private ManageAdsAjaxController controller() {

        userSession = mock(UserSession.class);

        bushfireApi = mock(BushfireApi.class);

        ManageAdsAjaxController controller = new ManageAdsAjaxController(bushfireApi, userSession);

        return controller;
    }
}
