package com.gumtree.web.seller.security.capi.controller;

import com.gumtree.common.properties.GtPropertiesInitializer;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.GumtreeAccessToken;
import com.gumtree.user.service.model.UserApiErrors;
import com.gumtree.user.service.model.VerificationDetails;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.cutters.capi.CapiAuthenticationCookie;
import com.gumtree.web.security.shiro.UserAuthenticationInfo;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.util.ThreadContext;
import org.apache.shiro.web.mgt.RemainingMaxAgeRememberMeManager;
import org.apache.shiro.web.subject.support.WebDelegatingSubject;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.OffsetDateTime;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CapiAuthenticationControllerTest {
    @Mock
    protected HttpServletRequest request;
    @Mock
    protected HttpServletResponse response;
    @Mock
    protected WebDelegatingSubject subject;
    @Mock
    protected SecurityManager securityManager;
    @Mock
    private CookieResolver cookieResolver;
    @Mock
    private UserServiceFacade userServiceFacade;
    @Mock
    private RemainingMaxAgeRememberMeManager rememberMeManager;

    @InjectMocks
    private CapiAuthenticationController controller;

    @BeforeClass
    public static void initProperties() throws ConfigurationException {
        GtPropertiesInitializer.init("seller-server");
    }


    @Before
    public void setup() {
        when(securityManager.createSubject(any())).thenReturn(subject);
        when(subject.getServletRequest()).thenReturn(request);
        when(subject.getServletResponse()).thenReturn(response);
        ThreadContext.bind(subject);
    }

    @Test
    public void redirectsToLoginWhenCookieNotPresent() throws Exception {
        //given
        when(cookieResolver.resolve(request, CapiAuthenticationCookie.class)).thenReturn(new CapiAuthenticationCookie("www.gumtree.com", 1, "/"));

        //when
        controller.authenticateCapi(request, response);

        //then
        verifyZeroInteractions(rememberMeManager);
        verifyZeroInteractions(subject);
        verifyRedirectedToUrl("/login");
    }

    @Test
    public void redirectsToLoginWhenUserServiceReturnsForbidden() throws Exception {
        //given
        when(cookieResolver.resolve(request, CapiAuthenticationCookie.class)).thenReturn(new CapiAuthenticationCookie("www.gumtree.com", 1, "/", new Cookie("auth", new String(Base64.encodeBase64("auth-header".getBytes())))));
        when(userServiceFacade.verifyCapiAccessToken("auth-header")).thenReturn(ApiResponse.error(new UserApiErrors()));

        //when
        controller.authenticateCapi(request, response);

        //then
        verifyZeroInteractions(rememberMeManager);
        verifyZeroInteractions(subject);
        verifyRedirectedToUrl("/login");
    }

    @Test
    public void redirectsToSYIWhenAllOKAndCallbackNotDefined() throws Exception {
        //given
        when(cookieResolver.resolve(request, CapiAuthenticationCookie.class)).thenReturn(new CapiAuthenticationCookie("www.gumtree.com", 1, "/", new Cookie("auth", new String(Base64.encodeBase64("auth-header".getBytes())))));
        when(userServiceFacade.verifyCapiAccessToken("auth-header")).thenReturn(ApiResponse.of(new VerificationDetails().username("<EMAIL>").token(new GumtreeAccessToken().value("token")).createdDateTime(OffsetDateTime.now())));

        // and
        when(subject.isRemembered()).thenReturn(true);

        //when
        controller.authenticateCapi(request, response);

        //then
        verify(rememberMeManager).onSuccessfulLogin(any(WebDelegatingSubject.class), any(UserAuthenticationInfo.class), any(OffsetDateTime.class));
        verify(subject).logout();
        verifyRedirectedToUrl("http://dev.gumtree.com:8080/postad/category");
    }

    @Test
    public void redirectsToCallbackWhenAllOKAndCallbackDefined() throws Exception {
        //given
        when(cookieResolver.resolve(request, CapiAuthenticationCookie.class)).thenReturn(new CapiAuthenticationCookie("www.gumtree.com", 1, "/", new Cookie("auth", new String(Base64.encodeBase64("auth-header".getBytes())))));
        when(userServiceFacade.verifyCapiAccessToken("auth-header")).thenReturn(ApiResponse.of(new VerificationDetails().username("<EMAIL>").token(new GumtreeAccessToken().value("token")).createdDateTime(OffsetDateTime.now())));

        // and
        when(subject.isRemembered()).thenReturn(true);

        // and
        when(request.getParameter("cb")).thenReturn("https://my.gumtree.com/manage-ads");

        //when
        controller.authenticateCapi(request, response);

        //then
        verify(rememberMeManager).onSuccessfulLogin(any(WebDelegatingSubject.class), any(UserAuthenticationInfo.class), any(OffsetDateTime.class));
        verify(subject).logout();
        verifyRedirectedToUrl("https://my.gumtree.com/manage-ads");
    }

    private void verifyRedirectedToUrl(String value) {
        verify(response).setStatus(303);
        verify(response).setHeader("Location", response.encodeRedirectURL(value));
    }

}
