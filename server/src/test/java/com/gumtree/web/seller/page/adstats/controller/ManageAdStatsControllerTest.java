package com.gumtree.web.seller.page.adstats.controller;

import com.gumtree.api.Ad;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.common.util.time.StoppedClock;
import com.gumtree.web.seller.model.AdPreviewImpl;
import com.gumtree.web.seller.converter.AdToAdPreviewConverter;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.PageNotFoundException;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.service.adstats.AdvertStatisticData;
import com.gumtree.web.seller.service.adstats.AdvertStatisticDataBuilder;
import com.gumtree.web.seller.service.adstats.AdvertStatsService;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounters;
import org.jboss.resteasy.client.ClientResponseFailure;
import org.junit.Before;
import org.junit.Test;
import org.springframework.web.servlet.ModelAndView;
import rx.Single;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ManageAdStatsControllerTest extends BaseSellerControllerTest {

    private AdvertStatsService adStatisticsService;

    private AdvertApi advertApi;

    private UserSession userSession;

    private ManageAdStatsController controller;

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");

    @Before
    public void init() {
        adStatisticsService=mock(AdvertStatsService.class);
        BushfireApi bushfireApi = mock(BushfireApi.class);
        advertApi = mock(AdvertApi.class);
        AdToAdPreviewConverter converter = ad -> new AdPreviewImpl.Builder().id(ad.getId()).title(ad.getTitle()).build();

        userSession = mock(UserSession.class);
        controller = new ManageAdStatsController(cookieResolver, categoryModel, apiCallExecutor, messageResolver,
                urlScheme, userSessionService,
                adStatisticsService, bushfireApi, new StoppedClock(), converter, userSession);

        when(bushfireApi.create(AdvertApi.class)).thenReturn(advertApi);

        AdvertStatisticData stats = new AdvertStatisticData();
        when(adStatisticsService.getStatisticForAdvert(anyLong())).thenReturn(stats);

        autowireAbExperimentsService(controller);
    }

    @Test(expected = PageNotFoundException.class)
    public void shouldNotBeAbleToViewStatsIfSelectedAccountDoesNotMatchAdvertAccount() {
        //given
        when(userSession.getSelectedAccountId()).thenReturn(9L);
        when(advertApi.getAdvert(1L)).thenReturn(createSampleAd());

        //when
        controller.showAdStatistics(1L, request);
    }

    @Test
    public void shouldBeAbleToViewStatsIfSelectedAccountDoesMatchAdvertAccount() {
        //given
        mockAccessibleAd();

        //when
        ModelAndView mav = controller.showAdStatistics(1L, request);

        //then
        ManageAdStatsModel model = extractModel(mav, ManageAdStatsModel.class);

        assertThat(model.getAdvert().getTitle(), equalTo("Test ad"));
        assertThat(model.getAdvert().getId(), equalTo(1L));
        assertThat(model.getAdstats().getAdvertId(), equalTo("1"));
        assertThat(mav.getViewName(), equalTo(Page.StatsAd.getTemplateName()));
    }

    @Test
    public void shouldBeAbleToViewStatsAsJSON() {
        //given
        mockAccessibleAd();

        //when
        AdvertStatisticData stats = controller.showAdStatisticsAsJSON(1L);

        //then
        assertThat(stats.getAdvertId(), equalTo("1"));
        assertThat(stats.getAdvertTitle(), equalTo("Test ad"));
    }

    @Test(expected = PageNotFoundException.class)
    public void shouldThrowExceptionIfAdDoesntExistWhenCallingAsJSON() {
       controller.showAdStatisticsAsJSON(-1L);
    }

    @Test
    public void shouldBeNullWhenServiceFailsOnViewStatsAsJSON() {
        //given
        mockAccessibleAd();
        when(adStatisticsService.getStatisticForAdvert(anyLong())).thenThrow(new RuntimeException("Test connect exception"));

        //when
        AdvertStatisticData stats = controller.showAdStatisticsAsJSON(1L);

        //then
        assertThat(stats, nullValue());
    }

    @Test(expected = PageNotFoundException.class)
    public void shouldHandleErrorCallingApi() {
        //given
        ClientResponseFailure failure = mock(ClientResponseFailure.class);
        when(userSession.getSelectedAccountId()).thenReturn(9L);
        when(advertApi.getAdvert(1L)).thenThrow(failure);

        //when
        controller.showAdStatistics(1L, request);

        //then
    }

    @Test
    public void failureOnStatsServiceShouldReturnNullAdStats() {
        //given
        when(userSession.getSelectedAccountId()).thenReturn(10L);
        when(adStatisticsService.getStatisticForAdvert(anyLong())).thenThrow(new RuntimeException("Test connect exception"));

        Ad ad = createSampleAd();
        when(advertApi.getAdvert(1L)).thenReturn(ad);

        //when
        ModelAndView mav = controller.showAdStatistics(1L, request);

        //then
        ManageAdStatsModel model = extractModel(mav, ManageAdStatsModel.class);

        assertThat(model.getAdvert().getTitle(), equalTo(ad.getTitle()));
        assertThat(model.getAdvert().getId(), equalTo(ad.getId()));
        assertThat(model.getAdstats(), nullValue());

    }

    @Test
    public void shoudReturnDefaultDailyStatsForOnlyId() {
        LocalDate now = LocalDate.now();
        Map<String, AdCounters> adCounterResponse = new HashMap<>();

        for (int i=1; i<=10; i++){
            adCounterResponse.put(formatter.format(now.minusDays(i)),
                    new AdCounters.Builder()
                    .setAdvertId(1L)
                    .setViewsCounter(i)
                    .build());
        }
        when(userSession.getSelectedAccountId()).thenReturn(10L);
        when(advertApi.getAdvert(1L)).thenReturn(createSampleAd());
        when(adStatisticsService.getStatisticForAdvert(1L,null))
                .thenReturn(adCounterResponse);
        //when
        Map<String, AdCounters> adCountersHistory = controller.getAdCountersHistory(1L, null);

        //then
        assertThat(adCountersHistory.size(), equalTo(10));
        assertThat(adCountersHistory.get(formatter.format(now.minusDays(5))).getViewsCounter(),
                equalTo(5));

    }

    @Test
    public void shoudReturnOnlyCertainNumberOfRecordsIfDaysMentioned() {
        int days =3;
        LocalDate now = LocalDate.now();
        Map<String, AdCounters> adCounterResponse = new HashMap<>();

        for (int i=1; i<=days; i++){
            adCounterResponse.put(formatter.format(now.minusDays(i)),
                    new AdCounters.Builder()
                            .setAdvertId(1L)
                            .setViewsCounter(i)
                            .build());
        }
        when(userSession.getSelectedAccountId()).thenReturn(10L);
        when(advertApi.getAdvert(1L)).thenReturn(createSampleAd());
        when(adStatisticsService.getStatisticForAdvert(1L,days))
                .thenReturn(adCounterResponse);
        //when number of days are mentioned
        Map<String, AdCounters> adCountersHistory = controller.getAdCountersHistory(1L, days);

        //then
        assertThat(adCountersHistory.size(), equalTo(3));
        assertThat(adCountersHistory.get(formatter.format(now.minusDays(2))).getViewsCounter(),
                equalTo(2));

    }

    @Test
    public void shoudReturnEmptyMapIfErrorOccurs() {

        LocalDate now = LocalDate.now();
        Map<String, AdCounters> adCounterResponse = new HashMap<>();
        when(userSession.getSelectedAccountId()).thenReturn(10L);
        when(advertApi.getAdvert(1L)).thenReturn(createSampleAd());

        for (int i=1; i<=10; i++){
            adCounterResponse.put(formatter.format(now.minusDays(i)),
                    new AdCounters.Builder()
                            .setAdvertId(1L)
                            .setViewsCounter(i)
                            .build());
        }
        when(adStatisticsService.getStatisticForAdvert(1L,null))
                .thenThrow(new RuntimeException("Test connect exception"));

        //when date is incorrect
        Map<String, AdCounters> adCountersHistory = controller.getAdCountersHistory(1L, null);

        //then
        assertThat(adCountersHistory.size(), equalTo(0));
        assertThat(adCountersHistory.isEmpty(), equalTo(true));
    }

    @Test(expected = PageNotFoundException.class)
    public void shouldReturnErrorIfAccountIdDoesntNotMatch() {
        LocalDate now = LocalDate.now();
        Map<String, AdCounters> adCounterResponse = new HashMap<>();
        for (int i=1; i<=10; i++){
            adCounterResponse.put(formatter.format(now.minusDays(i)),
                    new AdCounters.Builder()
                            .setAdvertId(1L)
                            .setViewsCounter(i)
                            .build());
        }
        when(userSession.getSelectedAccountId()).thenReturn(20L); //User With different account tries to access stats
        when(advertApi.getAdvert(1L)).thenReturn(createSampleAd());
        when(adStatisticsService.getStatisticForAdvert(1L,null))
                .thenReturn(adCounterResponse);
        //when
        Map<String, AdCounters> adCountersHistory = controller.getAdCountersHistory(1L,null);
        //then
        assertThat(adCountersHistory.size(), equalTo(0));
        assertThat(adCountersHistory.isEmpty(), equalTo(true));
    }

    @Test(expected = PageNotFoundException.class)
    public void shouldReturnErrorIfAccountIdNotFound() {
        LocalDate now = LocalDate.now();
        Map<String, AdCounters> adCounterResponse = new HashMap<>();
        for (int i=1; i<=10; i++){
            adCounterResponse.put(formatter.format(now.minusDays(i)),
                    new AdCounters.Builder()
                            .setAdvertId(1L)
                            .setViewsCounter(i)
                            .build());
        }
        when(userSession.getSelectedAccountId()).thenReturn(10L);
        when(advertApi.getAdvert(20L)).thenReturn(createSampleAd());//Account Id not present
        when(adStatisticsService.getStatisticForAdvert(1L,null))
                .thenReturn(adCounterResponse);
        //when
        Map<String, AdCounters> adCountersHistory = controller.getAdCountersHistory(1L, null);
        //then
        assertThat(adCountersHistory.size(), equalTo(0));
        assertThat(adCountersHistory.isEmpty(), equalTo(true));
    }

    private void mockAccessibleAd() {
        when(userSession.getSelectedAccountId()).thenReturn(10L);
        Ad ad = createSampleAd();
        when(advertApi.getAdvert(1L)).thenReturn(ad);

        AdvertStatisticData stats = AdvertStatisticDataBuilder.advertStatisticData()
                .advertId(ad.getId())
                .numberOfTimesReposted(ad.getBumpupCount())
                .advertTitle(ad.getTitle())
                .build();
        when(adStatisticsService.getStatisticForAdvert(anyLong())).thenReturn(stats);

    }

    private Ad createSampleAd() {
        Ad ad = new Ad();
        ad.setId(1L);
        ad.setTitle("Test ad");
        ad.setAccountId(10L);
        ad.setBumpupCount(0L);
        return ad;
    }
}
