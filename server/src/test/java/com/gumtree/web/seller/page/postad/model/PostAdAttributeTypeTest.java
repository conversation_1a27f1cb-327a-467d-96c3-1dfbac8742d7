package com.gumtree.web.seller.page.postad.model;

import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.category.domain.syi.AttributeSyiMetadata;
import com.gumtree.api.category.domain.syi.SyiAttributeValueMetadata;
import org.junit.Test;

import java.util.ArrayList;

import static org.fest.assertions.api.Assertions.assertThat;

public class PostAdAttributeTypeTest {

    @Test
    public void shouldGetTypeForEnumWith2Values() {
        // given
        AttributeMetadata attributeMetadata = new AttributeMetadata();
        attributeMetadata.setType(AttributeType.ENUM);

        ArrayList<SyiAttributeValueMetadata> syiValues = Lists.newArrayList(
                new SyiAttributeValueMetadata("val1", "lab1"),
                new SyiAttributeValueMetadata("val2", "lab2"));
        attributeMetadata.setSyi(AttributeSyiMetadata.builder().setValues(syiValues).build());

        // when
        PostAdAttributeType postAdAttributeType = PostAdAttributeType.typeFor(attributeMetadata);

        // then
        assertThat(postAdAttributeType).isEqualTo(PostAdAttributeType.RADIO);
    }

    @Test
    public void shouldGetTypeForEnumWith3Values() {
        // given
        AttributeMetadata attributeMetadata = new AttributeMetadata();
        attributeMetadata.setType(AttributeType.ENUM);

        ArrayList<SyiAttributeValueMetadata> syiValues = Lists.newArrayList(
                new SyiAttributeValueMetadata("val1", "lab1"),
                new SyiAttributeValueMetadata("val2", "lab2"),
                new SyiAttributeValueMetadata("val3", "lab3"));
        attributeMetadata.setSyi(AttributeSyiMetadata.builder().setValues(syiValues).build());

        // when
        PostAdAttributeType postAdAttributeType = PostAdAttributeType.typeFor(attributeMetadata);

        // then
        assertThat(postAdAttributeType).isEqualTo(PostAdAttributeType.DROPDOWN);
    }

    @Test
    public void shouldGetTypeForAnyTypeWith2SyiValues() {
        // given
        AttributeMetadata attributeMetadata = new AttributeMetadata();

        ArrayList<SyiAttributeValueMetadata> syiValues = Lists.newArrayList(
                new SyiAttributeValueMetadata("val1", "lab1"),
                new SyiAttributeValueMetadata("val2", "lab2"));
        attributeMetadata.setSyi(AttributeSyiMetadata.builder().setValues(syiValues).build());

        // when
        PostAdAttributeType postAdAttributeType = PostAdAttributeType.typeFor(attributeMetadata);

        // then
        assertThat(postAdAttributeType).isEqualTo(PostAdAttributeType.RADIO);
    }

    @Test
    public void shouldGetTypeForAnyTypeWith3SyiValues() {
        // given
        AttributeMetadata attributeMetadata = new AttributeMetadata();

        ArrayList<SyiAttributeValueMetadata> syiValues = Lists.newArrayList(
                new SyiAttributeValueMetadata("val1", "lab1"),
                new SyiAttributeValueMetadata("val2", "lab2"),
                new SyiAttributeValueMetadata("val3", "lab3"));
        attributeMetadata.setSyi(AttributeSyiMetadata.builder().setValues(syiValues).build());

        // when
        PostAdAttributeType postAdAttributeType = PostAdAttributeType.typeFor(attributeMetadata);

        // then
        assertThat(postAdAttributeType).isEqualTo(PostAdAttributeType.DROPDOWN);
    }

}