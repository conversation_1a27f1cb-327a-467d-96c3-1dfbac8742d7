package com.gumtree.web.seller.page.payment.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.api.ApiException;
import com.gumtree.api.User;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.seller.infrastructure.driven.payment.ApiClient;
import com.gumtree.seller.infrastructure.driven.payment.braintree.BraintreeApi;
import com.gumtree.seller.infrastructure.driven.payment.braintree.model.*;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.model.PaymentKeys;
import com.gumtree.web.seller.service.payment.OrderAlreadyPaidException;
import com.gumtree.web.seller.service.payment.PaymentDeclinedException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import rx.Single;

import java.io.IOException;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PaymentApiClientTest {

    private static final Long ORDER_ID = 1234L;
    private static final String CLIENT_ID = "seller";
    private static final String MERCHANT_ID = "merchantId";
    private static final String TOKEN_VALUE = "tokenValue";
    private static final String ENV = "sandbox";
    private static final String PAYMENT_REFERENCE = "paymentReference";
    private static final String PAYMENT_METHOD = "paymentMethod";
    private static final Long ACCOUNT_ID = 123L;
    private static final Long USER_ID = 333L;
    private static final String EMAIL = "<EMAIL>";
    private static final String FIRST_NAME = "firstName";
    private static final String LAST_NAME = "lastName";
    private static final String STREET = "street";
    private static final String TOWN = "town";
    private static final String COUNTRY = "country";
    private static final String POSTCODE = "postcode";
    private static final Long TOTAL_INCLUDING_VAT = 120L;
    private static final Long TOTAL_VAT = 20L;
    private static final String PAYMENT_METHOD_NONCE = "paymentMethodNonce";
    private static final String DEVICE_DATA = "deviceData";
    private static final String ERROR_RESPONSE_BODY = "errorResponseBody";

    @Mock
    private BraintreeApi braintreeApiMock;

    @Mock
    private ApiClient apiClientMock;

    @Mock
    private ObjectMapper objectMapperMock;

    @InjectMocks
    private PaymentApiClient paymentApiClient;

    private Checkout checkout;
    private User user;
    private com.gumtree.web.seller.page.payment.model.BillingAddress billingAddress;
    private ExecuteTransactionRequest executeTransactionRequest;
    private ApiException apiException;

    @Before
    public void setUp() throws Exception {
        when(apiClientMock.getObjectMapper()).thenReturn(objectMapperMock);

        apiException = mock(ApiException.class);

        checkout = mock(Checkout.class);
        ApiOrder order = new ApiOrder();
        order.setId(ORDER_ID);
        order.setTotalIncVat(TOTAL_INCLUDING_VAT);
        order.setTotalVat(TOTAL_VAT);
        when(checkout.getOrder()).thenReturn(order);

        user = new User();
        user.setId(USER_ID);
        user.setEmail(EMAIL);

        billingAddress = com.gumtree.web.seller.page.payment.model.BillingAddress
                .builder()
                .withFirstName(FIRST_NAME)
                .withLastName(LAST_NAME)
                .withAddress(STREET)
                .withTownCity(TOWN)
                .withCountry(COUNTRY)
                .withPostcode(POSTCODE)
                .build();

        executeTransactionRequest = new ExecuteTransactionRequest()
                .accountId(ACCOUNT_ID)
                .userId(USER_ID)
                .email(EMAIL)
                .billingAddress(new BillingAddress().firstName(FIRST_NAME).lastName(LAST_NAME)
                        .street(STREET).town(TOWN).country(COUNTRY).postcode(POSTCODE))
                .paymentDetails(new PaymentDetails()
                        .totalIncludingVat(TOTAL_INCLUDING_VAT)
                        .totalVat(TOTAL_VAT)
                        .paymentMethodNonce(PAYMENT_METHOD_NONCE)
                        .deviceData(DEVICE_DATA))
                .platformDevice("platformDevice");
    }

    @Test
    public void initialisePaymentShouldReturnPaymentKeysWhenPaymentApiRequestIsSuccessful() throws Exception {

        TransactionInitialisedResponse initialisePaymentResponse = new TransactionInitialisedResponse().merchantId(MERCHANT_ID).token(TOKEN_VALUE).environmentName(ENV);

        when(braintreeApiMock.initialiseTransaction(ORDER_ID, CLIENT_ID)).thenReturn((Single.just(initialisePaymentResponse)));

        PaymentKeys paymentKeys = paymentApiClient.intialisePayment(ORDER_ID);
        assertThat(paymentKeys, equalTo(new PaymentKeys(MERCHANT_ID, TOKEN_VALUE, ENV)));

    }

    @Test
    public void generateTokenShouldReturnPaymentKeysWhenPaymentApiRequestIsSuccessful() throws Exception {

        TransactionInitialisedResponse initialisePaymentResponse = new TransactionInitialisedResponse().merchantId(MERCHANT_ID).token(TOKEN_VALUE).environmentName(ENV);

        GenerateTokenRequest generateTokenRequest = new GenerateTokenRequest();
        generateTokenRequest.setOrderId(ORDER_ID);
        generateTokenRequest.setCustomerId(USER_ID);

        when(braintreeApiMock.generateToken(generateTokenRequest, CLIENT_ID)).thenReturn((Single.just(initialisePaymentResponse)));

        PaymentKeys paymentKeys = paymentApiClient.generateToken(ORDER_ID,USER_ID);
        assertThat(paymentKeys, equalTo(new PaymentKeys(MERCHANT_ID, TOKEN_VALUE, ENV)));

    }

    @Test(expected = PaymentApiClient.PaymentApiException.class)
    public void initialisePaymentShouldPaymentApiExceptionWhenPaymentApiRequestFails() throws Exception {

        when(braintreeApiMock.initialiseTransaction(ORDER_ID, CLIENT_ID)).thenThrow(ApiException.class);

        paymentApiClient.intialisePayment(ORDER_ID);

    }

    @Test
    public void executePaymentShouldReturnPaymentReferenceWhenPaymentApiRequestIsSuccessful() throws Exception {

        paymentApiClient.executePayment(checkout, user, ACCOUNT_ID, billingAddress, PAYMENT_METHOD_NONCE, DEVICE_DATA, "platformDevice");

        verify(braintreeApiMock).executeTransaction(ORDER_ID, executeTransactionRequest, CLIENT_ID);

    }

    @Test(expected = PaymentApiClient.PaymentApiException.class)
    public void executePaymentShouldThrowPaymentApiExceptionWhenPaymentApiRequestFails() throws Exception {

        doThrow(apiException).when(braintreeApiMock).executeTransaction(ORDER_ID, executeTransactionRequest, CLIENT_ID);
        when(objectMapperMock.readValue(ERROR_RESPONSE_BODY, ErrorResponse.class)).thenThrow(IOException.class);

        paymentApiClient.executePayment(checkout, user, ACCOUNT_ID, billingAddress, PAYMENT_METHOD_NONCE, DEVICE_DATA, "platformDevice");

    }

    private ErrorResponse errorResponse(String errorCode) {
        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setErrorCode(errorCode);
        return errorResponse;
    }
}