package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.Image;
import com.gumtree.web.seller.page.postad.model.plupload.PluploadError;
import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class PostAdImageUploadTest {

    @Test
    public void shouldConstructOkWithImage() {

        Long id = 1L;
        String size = "2";
        String thumbnailUrl = "http://i.sandbox.ebayimg.com/t.jpg";
        String url = "http://i.sandbox.ebayimg.com/u.jpg";
        int position = 5;

        Image image = new Image();
        image.setId(id);
        image.setSize(size);
        image.setThumbnailUrl(thumbnailUrl);
        image.setUrl(url);

        PostAdImage.Builder postAdImageBuilder = new PostAdImage.Builder()
                .id(image.getId())
                .size(image.getSize())
                .thumbnailUrl(thumbnailUrl)
                .url(image.getUrl());

        PostAdImageUpload.Builder builder = PostAdImageUpload.builder();
        builder.withImage(postAdImageBuilder.build());
        PostAdImageUpload postAdImageUpload = new PostAdImageUpload(builder);

        assertThat(postAdImageUpload.getId()).isEqualTo(id);
        assertThat(postAdImageUpload.getThumbnailUrl()).isEqualTo(thumbnailUrl);
        assertThat(postAdImageUpload.getUrl()).isEqualTo("//i.sandbox.ebayimg.com/u.jpg");
        assertThat(postAdImageUpload.getPosition()).isNull();
    }

    @Test
    public void shouldConstructOkWithout() {

        String filename = "u.jpg";
        int position = 5;
        String message = "oops";
        int code = 102;

        PluploadError.Builder error = PluploadError.aPluploadError().message(message).withCode(code);
        PostAdImageUpload postAdImageUpload = PostAdImageUpload.builder()
                .withPosition(position).withFileName(filename).withError(error).build();

        assertThat(postAdImageUpload.getId()).isNull();
        assertThat(postAdImageUpload.getThumbnailUrl()).isNull();
        assertThat(postAdImageUpload.getUrl()).isNull();
        assertThat(postAdImageUpload.getPosition()).isEqualTo(position);
        assertThat(postAdImageUpload.getError()).isEqualTo(error.build());
        assertThat(postAdImageUpload.getFileName()).isEqualTo(filename);
    }



}
