package com.gumtree.web.seller.page.jobs.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Optional;
import com.gumtree.api.User;
import com.gumtree.service.jobs.CvData;
import com.gumtree.service.jobs.StubCvService;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.jobs.model.CvJsonModel;
import com.gumtree.web.seller.page.jobs.model.CvUploadBean;
import com.gumtree.web.seller.page.manageads.mydetails.MyDetailsController;
import com.gumtree.web.seller.service.securetoken.SecureTokenService;
import com.gumtree.web.util.ResourceNotFoundException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.validation.BindingResult;
import org.springframework.validation.MapBindingResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashMap;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.Matchers.containsString;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MyCVControllerTest  extends BaseSellerControllerTest {

    private StubCvService cvService = new StubCvService();

    @Mock
    private SecureTokenService secureTokenService;

    private ObjectMapper mapper = new ObjectMapper();

    private User user;

    private MyCvController controller;

    @Before
    public void setUp() {

        user = User.builder().withId(1L).build();

        StubCvService.clearAllData();

        when(secureTokenService.getOrGenerateToken(MyDetailsController.VIEW_NAME)).thenReturn("theToken");
        messageResolver = (code, defaultMessage, args) -> "message_" + code;

        controller = new MyCvController(userSessionService, categoryModel, apiCallExecutor, messageResolver, urlScheme,
                cookieResolver, cvService, secureTokenService);
    }

    @Test
    public void shouldProduceEmptyCvDataIfNoCVUploaded() throws Exception {
        //given
        when(userSessionService.getUser()).thenReturn(Optional.of(user));

        //when
        CvJsonModel cvJsonModel = controller.get();

        //then
        assertThat(cvJsonModel.getCvData(), nullValue());
        assertThat(cvJsonModel.getSecureToken(), equalTo("theToken"));

    }

    @Test
    public void shouldProduceValidCvDataIfCvPreviouslyStored() throws Exception {
        //given
        when(userSessionService.getUser()).thenReturn(Optional.of(user));

        CvData newData = CvData.builder().
                withUserId(1L).
                withContentType("application/pdf").
                withOriginalFilename("file.pdf").
                withBytes("Test".getBytes()).
                build();
        cvService.upload(newData);

        //when
        CvJsonModel cvJsonModel = controller.get();

        //then
        assertThat(cvJsonModel.getCvData(), notNullValue());
        assertThat(cvJsonModel.getSecureToken(), equalTo("theToken"));
        assertThat(cvJsonModel.getCvData().getContentType(), equalTo("application/pdf"));
        assertThat(cvJsonModel.getCvData().getOriginalFilename(), equalTo("file.pdf"));
        assertThat(cvJsonModel.getCvData().getPeriodSinceUploaded(), equalTo("today"));
    }

    @Test
    public void shouldUploadCV() throws Exception {
        //given
        when(userSessionService.getUser()).thenReturn(Optional.of(user));

        MultipartFile multipartFile = fileToMultipartFile("src/test/resources/com/gumtree/mime/real.pdf", "application/pdf");
        CvUploadBean bean = new CvUploadBean();
        bean.setFile(multipartFile);

        BindingResult bindingResult = new MapBindingResult(new HashMap<>(), "cv");

        //when
        String response = controller.upload(bean, bindingResult);

        //then
        assertThat(cvService.getMetadata(user.getId()).isPresent(), equalTo(true));
        CvData data = cvService.getMetadata(user.getId()).get();
        assertThat(data.getContentType(), equalTo("application/pdf"));
        assertThat(data.getOriginalFilename(), equalTo("real.pdf"));
        assertThat(cvService.getContent(user.getId()).get().getBytes(), equalTo(multipartFile.getBytes()));

        JsonNode node = mapper.reader().readTree(response);
        assertThat(node.get("successNotice"), notNullValue());

    }

    @Test
    public void shouldNotUseContentTypeProvidedByUserWhenUploadingCV() throws Exception {
        //given
        when(userSessionService.getUser()).thenReturn(Optional.of(user));

        MultipartFile multipartFile = fileToMultipartFile("src/test/resources/com/gumtree/mime/real.pdf", "application/somethingWrong");
        CvUploadBean bean = new CvUploadBean();
        bean.setFile(multipartFile);

        BindingResult bindingResult = new MapBindingResult(new HashMap<>(), "cv");

        //when
        String response = controller.upload(bean, bindingResult);

        //then
        CvData data = cvService.getMetadata(user.getId()).get();
        assertThat(data.getContentType(), equalTo("application/pdf"));

        JsonNode node = mapper.reader().readTree(response);
        assertThat(node.get("successNotice"), notNullValue());
    }

    @Test
    public void shouldFailToUploadIfThereAreValidationErrors() throws Exception {
        //given
        when(userSessionService.getUser()).thenReturn(Optional.of(user));

        String fileContents = "hello file";
        MockMultipartFile mockMultipartFile = new MockMultipartFile("file", "plain.pdf", "application/somethingWrong",
                fileContents.getBytes());
        CvUploadBean bean = new CvUploadBean();
        bean.setFile(mockMultipartFile);

        MapBindingResult bindingResult = new MapBindingResult(new HashMap<>(), "cv");
        bindingResult.rejectValue("file", "file.too.big");

        //when
        String response = controller.upload(bean, bindingResult);

        //then
        assertThat(cvService.getMetadata(user.getId()), equalTo(java.util.Optional.empty()));

        JsonNode node = mapper.reader().readTree(response);
        assertThat(node.get("errors"), notNullValue());
        assertThat(node.get("errors").get("allResolvedFieldErrorMessages"), notNullValue());
        assertThat(node.get("errors").get("allResolvedFieldErrorMessages").get("file"), notNullValue());
        JsonNode fileErrors = node.get("errors").get("allResolvedFieldErrorMessages").get("file");
        assertThat(fileErrors.size(), equalTo(1));
        assertThat(fileErrors.get(0).asText(), equalTo("message_file.too.big"));
    }


    @Test
    public void shouldDeleteCV() throws IOException, ResourceNotFoundException {
        //given
        when(userSessionService.getUser()).thenReturn(Optional.of(user));

        CvData newData = CvData.builder().
                withUserId(1L).
                withContentType("application/pdf").
                withOriginalFilename("file.pdf").
                withBytes("Test".getBytes()).
                build();
        cvService.upload(newData);

        //when
        String response = controller.remove();

        //then
        JsonNode node = mapper.reader().readTree(response);
        assertThat(node.get("successNotice"), notNullValue());

        assertThat(cvService.getMetadata(user.getId()), equalTo(java.util.Optional.empty()));
    }


    @Test
    public void shouldDownloadCVProperly() throws ResourceNotFoundException, IOException {
        //given
        when(userSessionService.getUser()).thenReturn(Optional.of(user));

        CvData newData = CvData.builder().
                withUserId(1L).
                withContentType("application/pdf").
                withOriginalFilename("file.pdf").
                withBytes("Test".getBytes()).
                build();
        cvService.upload(newData);

        //when
        ResponseEntity<ByteArrayResource> result = controller.download();

        //then
        assertThat(result.getStatusCode(), equalTo(HttpStatus.OK));
        assertTrue(Arrays.equals(result.getBody().getByteArray(), newData.getBytes()));
        assertThat(result.getHeaders().getContentType().toString(), equalTo("application/pdf"));
        assertThat(result.getHeaders().getFirst("Content-disposition"), containsString("file.pdf"));
        assertThat(result.getHeaders().getContentLength(), equalTo((long)newData.getBytes().length));
    }

    @Test
    public void shouldReturn404WhenTryingToDownloadNonExistentCv() throws IOException, ResourceNotFoundException {
        //given
        when(userSessionService.getUser()).thenReturn(Optional.of(user));

        //when
        ResponseEntity<ByteArrayResource> result;
        result = controller.download();

        //then
        assertThat(result.getStatusCode(), equalTo(HttpStatus.NOT_FOUND));
    }

    private MultipartFile fileToMultipartFile(String filePath, String contentType){
        Path path = Paths.get(filePath);
        String[] splits = filePath.split("/");
        String name = splits[splits.length - 1];
        String originalFileName = splits[splits.length - 1];
        byte[] content = null;
        try {
            content = Files.readAllBytes(path);
        } catch (final IOException e) {
        }
        return  new MockMultipartFile(name, originalFileName, contentType, content);
    }
}
