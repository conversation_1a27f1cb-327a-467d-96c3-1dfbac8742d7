package com.gumtree.web.seller.page.payment.reporting.ga;

import com.gumtree.common.test.hamcrest.Answers;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsCustomVar;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsElementTrackEvent;
import com.gumtree.web.seller.page.ResponsiveGroupRequest;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PaymentCheckoutGaConfigurerTest {

    private GoogleAnalyticsReportBuilder reportBuilder;
    private ThirdPartyRequestContext ctx;
    private PaymentCheckoutGaConfigurer configurer;
    private ResponsiveGroupRequest switcher;

    @Before
    public void setUp() {
        reportBuilder = mock(GoogleAnalyticsReportBuilder.class, Answers.fluentInterfaceAnswer());
        ctx = mock(ThirdPartyRequestContext.class);
        when(ctx.getCategory()).thenReturn(mock(Category.class));
        when(ctx.getLocation()).thenReturn(mock(Location.class));
        when(ctx.getPageType()).thenReturn(PageType.OrderReview);
        switcher = mock(ResponsiveGroupRequest.class);
        configurer = new PaymentCheckoutGaConfigurer(switcher);
    }

    @Test
    public void trackEvents() {

        // given
        when(ctx.getCategory().getId()).thenReturn(23L);
        when(ctx.getLocation().getId()).thenReturn(42);
        when(ctx.hasContent()).thenReturn(true);
        when(switcher.getGroup()).thenReturn("none");

        // when
        configurer.configure(reportBuilder, ctx);

        // then
        ArgumentCaptor<GoogleAnalyticsElementTrackEvent> captor = ArgumentCaptor.forClass(GoogleAnalyticsElementTrackEvent.class);
        verify(reportBuilder, times(3)).addTrackEvent(captor.capture());
        List<GoogleAnalyticsElementTrackEvent> values = captor.getAllValues();
        assertThat(values.get(0).getAction(), equalTo("PostAdPaidAttempt"));
        assertThat(values.get(1).getAction(), equalTo("PostAdPaidCancel"));
        assertThat(values.get(2).getAction(), equalTo("PostAdBegin"));

        ArgumentCaptor<GoogleAnalyticsCustomVar> captorCustom = ArgumentCaptor.forClass(GoogleAnalyticsCustomVar.class);
        verify(reportBuilder, times(1)).addCustomVar(captorCustom.capture());
        //todo finish asserting captured);

    }

}
