package com.gumtree.web.seller.builder;

import com.gumtree.api.Account;

/**
 * Account builder to used in stubbed tests
 */
public class AccountBuilder {
    public static final long DEFAULT_ACC_ID = 1L;
    private long accountId = DEFAULT_ACC_ID;
    private String name = "default name";
    private Boolean pro = Boolean.FALSE;

    /**
     * set account id
     * @param accountId acc id
     * @return this builder
     */
    public AccountBuilder id(long accountId) {
        this.accountId = accountId;
        return this;
    }

    /**
     * set account name
     * @param name acc name
     * @return this builder
     */
    public AccountBuilder name(String name) {
        this.name = name;
        return this;
    }

    /**
     * set pro account
     * @return this builder
     */
    public AccountBuilder proAccount() {
        this.pro = Boolean.TRUE;
        return this;
    }

    /**
     * builds account
     * @return the account
     */
    public Account build() {
        Account account = new Account();
        account.setId(accountId);
        account.setName(name);
        account.setPro(pro);
        account.setDescription("stubbed test account");
        return account;
    }
}
