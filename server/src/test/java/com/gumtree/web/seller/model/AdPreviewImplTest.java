package com.gumtree.web.seller.model;

import com.google.common.base.Optional;
import com.gumtree.liveadsearch.model.Category;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.internal.DefaultAttribute;
import com.gumtree.domain.newattribute.internal.InvalidAttributeValueException;
import com.gumtree.domain.newattribute.internal.value.LongValue;
import com.gumtree.domain.newattribute.internal.value.TextValue;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static com.google.common.collect.Lists.newArrayList;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_ENGINE_SIZE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_FUEL_TYPE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_MILEAGE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_REGISTRATION_YEAR;
import static org.hamcrest.CoreMatchers.endsWith;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.springframework.util.Assert.isNull;

/**
 *
 */
public class AdPreviewImplTest {
    private final String _136_CHARACTERS = generateStringOfLenght(136, WithSpaces.EVERY_10TH_CHARACTER);
    private final String _356_CHARACTERS_STARTING_WITH_SPACE = " " + generateStringOfLenght(355, WithSpaces.EVERY_10TH_CHARACTER);

    @Test
    public void extendedDescriptionShouldNotBeAddedForNonConfiguredL1Category() {
        String initialDescription = generateStringOfLenght(130, WithSpaces.EVERY_10TH_CHARACTER) + ("bleblablelaaaa");

        AdPreview adPreview = new AdPreviewImpl.Builder()
                .l1CategoryId(11L)
                .extendedDescriptionL1Categories(newArrayList(23L, 24L))
                .description(initialDescription).build();

        isNull(adPreview.getDescriptionExtensionOrNull());
    }

    @Test
    public void extendedDescriptionShouldBeAddedForConfiguredL1Category() {
        String initialDescription = generateStringOfLenght(130, WithSpaces.EVERY_10TH_CHARACTER) + "bleblablelaaaa";

        AdPreview adPreview = new AdPreviewImpl.Builder()
                .categories(newArrayList(categoryWithId(23L), categoryWithId(13L)))
                .extendedDescriptionL1Categories(newArrayList(23L, 24L))
                .description(initialDescription).build();

        assertThat(adPreview.getDescriptionExtensionOrNull(), is(" bleblablelaaaa"));
    }

    @Test
    public void extendedDescriptionShouldNotBeAddedWhenThereIsNoL1CategoryProvided() {
        String initialDescription = generateStringOfLenght(130, WithSpaces.EVERY_10TH_CHARACTER) + ("bleblablelaaaa");

        AdPreview adPreview = new AdPreviewImpl.Builder()
                .extendedDescriptionL1Categories(newArrayList(23L, 24L))
                .description(initialDescription).build();

        isNull(adPreview.getDescriptionExtensionOrNull());
    }

    @Test
    public void testBasicSetDescription() {
        AdPreview adPreview = getAdPreviewWithoutExtendedDescription("Basic description");

        assertThat(adPreview.getDescription(), equalTo("Basic description"));
    }

    @Test
    public void shouldTrancateLongDescriptionOnWhiteSpace() {
        AdPreview adPreview = getAdPreviewWithoutExtendedDescription(
                "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz " +
                        "abcdefghijklmnopqrstuvwxyz ");

        assertThat(adPreview.getDescription(), equalTo("abcdefghijklmnopqrstuvwxyz " +
                "abcdefghijklmnopqrstuvwxyz " +
                "abcdefghijklmnopqrstuvwxyz " +
                "abcdefghijklmnopqrstuvwxyz " +
                "abcdefghijklmnopqrstuvwxyz " +
                "abcdefghijklmnopqrstuvwxyz" +
                "..."));
    }

    @Test
    public void shouldTrancateLongDescriptionWithoutWhiteSpaces() {
        AdPreview adPreview = getAdPreviewWithoutExtendedDescription("abcdefghijklmnopqrstuvwxyz" +
                "abcdefghijklmnopqrstuvwxyz" +
                "abcdefghijklmnopqrstuvwxyz" +
                "abcdefghijklmnopqrstuvwxyz" +
                "abcdefghijklmnopqrstuvwxyz" +
                "abcdefghijklmnopqrstuvwxyz" +
                "abcdefghijklmnopqrstuvwxyz" +
                "abcdefghijklmnopqrstuvwxyz" +
                "abcdefghijklmnopqrstuvwxyz" +
                "abcdefghijklmnopqrstuvwxyz ");

        String descriptionOfLength190exactEndingWithThreeDots =
                "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefg...";
        assertThat(adPreview.getDescription(), equalTo(descriptionOfLength190exactEndingWithThreeDots));
    }

    @Test
    public void testLineBreaksRemovedFromDescription() {
        AdPreview adPreview = getAdPreviewWithoutExtendedDescription("I am\t on\n" +
                "multiple\n\r" +
                "lines");

        assertThat(adPreview.getDescription(), equalTo("I am on multiple lines"));
    }

    @Test
    public void testDoubleSpacesRemovedFromDescription() {
        AdPreview adPreview = getAdPreviewWithoutExtendedDescription("I have   double  spaces");

        assertThat(adPreview.getDescription(), equalTo("I have double spaces"));
    }

    @Test
    public void getCustomDateTextShouldReturnPostedTime() {
        AdPreview adPreview = new AdPreviewImpl.Builder().postedTime("recently").build();

        assertThat(adPreview.getCustomDateText(), equalTo("recently"));
    }

    @Test
    public void getCustomDateTextShouldReturnEventDateIfCustomDateIsSet() {
        AdPreview adPreview = new AdPreviewImpl.Builder().postedTime("today").customDateText(Optional.fromNullable("tomorrow!")).build();

        assertThat(adPreview.getCustomDateText(), equalTo("tomorrow!"));
    }

    @Test
    public void shouldGetDistanceInMilesForDistanceSmallerThan1Mile() {
        // given
        AdPreview adPreview = new AdPreviewImpl.Builder().distance(0.0075).build();

        // when
        String distance = adPreview.getDistance();

        // then
        assertThat(distance, is("0 miles"));
    }

    @Test
    public void shouldGetDistanceInMilesForDistanceSmallerThan025Mile() {
        // given
        AdPreview adPreview = new AdPreviewImpl.Builder().distance(0.2).build();

        // when
        String distance = adPreview.getDistance();

        // then
        assertThat(distance, is("1/4 mile"));
    }

    @Test
    public void shouldGetDistanceInMilesForDistanceSmallerThan05Mile() {
        // given
        AdPreview adPreview = new AdPreviewImpl.Builder().distance(0.445).build();

        // when
        String distance = adPreview.getDistance();

        // then
        assertThat(distance, is("1/2 mile"));
    }

    @Test
    public void shouldGetDistanceInMilesForDistanceGreaterThan1Mile() {
        // given
        AdPreview adPreview = new AdPreviewImpl.Builder().distance(4.25).build();

        // when
        String distance = adPreview.getDistance();

        // then
        assertThat(distance, is("5 miles"));
    }

    @Test
    public void shouldReturnBlankDistanceIfDistanceIsSetToNull() {
        // given
        AdPreview adPreview = new AdPreviewImpl.Builder().distance(null).build();

        // when
        String distance = adPreview.getDistance();

        // then
        assertThat(distance, is(""));
    }

    @Test
    public void shouldReturnVehicleAdPreviewIfInCarsCategory() {
        // given
        List<Category> categories = new ArrayList<Category>();
        categories.add(new Category().primary(true).id(9311L).name("cars"));
        categories.add(new Category().primary(true).id(2551L).name("cars-vans-motorbikes"));
        categories.add(new Category().primary(true).id(10303L).name("bmw"));

        // when
        AdPreview adPreview = new AdPreviewImpl.Builder().categories(categories).build();

        // then
        assertThat(adPreview instanceof VehicleAdPreviewImpl, equalTo(true));
    }

    @Test
    public void shouldReturnVehicleAdPreviewAttributesIfInCarsCategory() throws InvalidAttributeValueException {
        // given
        List<Category> categories = new ArrayList<Category>();
        categories.add(new Category().primary(true).id(9311L).name("cars"));
        categories.add(new Category().primary(true).id(2551L).name("cars-vans-motorbikes"));
        categories.add(new Category().primary(true).id(10303L).name("bmw"));

        List<Attribute> attributes = new ArrayList<Attribute>();

        attributes.add(new DefaultAttribute(VEHICLE_ENGINE_SIZE.getName(), LongValue.create(new Long(300L), "cc")));
        attributes.add(new DefaultAttribute(VEHICLE_MILEAGE.getName(), LongValue.create(new Long(10000L), "")));
        attributes.add(new DefaultAttribute(VEHICLE_REGISTRATION_YEAR.getName(), LongValue.create(new Long(2000L), "")));
        attributes.add(new DefaultAttribute(VEHICLE_FUEL_TYPE.getName(), TextValue.create("petrol", true)));

        // when
        AdPreview adPreview = new AdPreviewImpl.Builder().attributes(attributes).categories(categories).numberOfImages(3).numberOfVideos(1).build();

        // then
        assertThat(adPreview instanceof VehicleAdPreviewImpl, equalTo(true));
        VehicleAdPreviewImpl vehicleAdPreview = (VehicleAdPreviewImpl)adPreview;

        assertThat(vehicleAdPreview.getVehicleAttributes().size(), equalTo(4));

        //check the attributes are in the correct order.
        List<Attribute> sortedAttributes = (List<Attribute>)vehicleAdPreview.getVehicleAttributes();
        assertThat(VEHICLE_REGISTRATION_YEAR.getName(), equalTo(sortedAttributes.get(0).getType()));
        assertThat(VEHICLE_MILEAGE.getName(), equalTo(sortedAttributes.get(1).getType()));
        assertThat(VEHICLE_ENGINE_SIZE.getName(), equalTo(sortedAttributes.get(2).getType()));
        assertThat(VEHICLE_FUEL_TYPE.getName(), equalTo(sortedAttributes.get(3).getType()));

        assertThat(vehicleAdPreview.getNumberOfImages(), equalTo(3));
        assertThat(vehicleAdPreview.getNumberOfVideos(), equalTo(1));

    }

    @Test
    public void shouldReturnAnyAvailableVehicleAdPreviewAttributesIfInCarsCategory() throws InvalidAttributeValueException {
        // given
        List<Category> categories = new ArrayList<Category>();
        categories.add(new Category().primary(true).id(9311L).name("cars"));
        categories.add(new Category().primary(true).id(2551L).name("cars-vans-motorbikes"));
        categories.add(new Category().primary(true).id(10303L).name("bmw"));

        List<Attribute> attributes = new ArrayList<Attribute>();
        attributes.add(new DefaultAttribute(VEHICLE_ENGINE_SIZE.getName(), LongValue.create(new Long(1000L), "cc")));
        attributes.add(new DefaultAttribute(VEHICLE_MILEAGE.getName(), LongValue.create(new Long(300L), "")));

        // when
        AdPreview adPreview = new AdPreviewImpl.Builder().attributes(attributes).categories(categories).build();

        // then
        assertThat(adPreview instanceof VehicleAdPreviewImpl, equalTo(true));
        VehicleAdPreviewImpl vehicleAdPreview = (VehicleAdPreviewImpl)adPreview;

        assertThat(vehicleAdPreview.getVehicleAttributes().size(), equalTo(2));

        List<Attribute> sortedAttributes = (List<Attribute>)vehicleAdPreview.getVehicleAttributes();
        assertThat(VEHICLE_MILEAGE.getName(), equalTo(sortedAttributes.get(0).getType()));
        assertThat(VEHICLE_ENGINE_SIZE.getName(), equalTo(sortedAttributes.get(1).getType()));

    }

    @Test
    public void shouldSetRemainingTextAsDescriptionExtensionWhenDescriptionIsTooLong() {
        String initialDescription = generateStringOfLenght(130, WithSpaces.EVERY_10TH_CHARACTER) + "one two three four five";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescriptionExtensionOrNull(), is(" two three four five"));
    }

    @Test
    public void shouldTrimDescriptionExtensionWhenDescriptionIsVeryLong() {
        String initialDescription = generateStringOfLenght(490, WithSpaces.EVERY_10TH_CHARACTER) + "one two three four five";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescriptionExtensionOrNull().length(), is(367));
        assertThat(adPreview.getDescriptionExtensionOrNull(), endsWith("blablabla one..."));
    }

    @Test
    public void descriptionExtensionShouldBeNullWhenDescriptionIsLessThanThreshold() {
        String initialDescription = generateStringOfLenght(130, WithSpaces.EVERY_10TH_CHARACTER) + "ble";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        isNull(adPreview.getDescriptionExtensionOrNull());
    }

    @Test
    public void allowingForExtendedDescription_shouldUseInitialDescription_whenDescriptionIsLessThanThreshold() {
        String initialDescription = generateStringOfLenght(130, WithSpaces.EVERY_10TH_CHARACTER) + "bla";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(initialDescription));
        isNull(adPreview.getDescriptionExtensionOrNull());
    }

    @Test
    public void allowingForExtendedDescription_shouldUseInitialDescription_whenDescriptionIsEqualToThreshold() {
        String initialDescription = generateStringOfLenght(130, WithSpaces.EVERY_10TH_CHARACTER) + "blabla";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(initialDescription));
        isNull(adPreview.getDescriptionExtensionOrNull());
    }

    @Test
    public void allowingForExtendedDescription_shouldTrimInitialDescriptionAndSetDescrExtenstion_whenDescriptionIsMoreThanThreshold() {
        String expectedDescription = generateStringOfLenght(130, WithSpaces.EVERY_10TH_CHARACTER) + "x";
        String initialDescription = expectedDescription + " blablaaa";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(" blablaaa"));
    }

    @Test
    public void allowingForExtendedDescription_shouldTrimInitialDescriptionAndSetDescrExtenstion_whenDescriptionLastWordEndsOnThreshold() {
        String expectedDescription = generateStringOfLenght(130, WithSpaces.EVERY_10TH_CHARACTER) + "blabla";
        String initialDescription = expectedDescription + " ble";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(" ble"));
    }

    @Test
    public void allowingForExtendedDescription_shouldTrimInitialDescriptionAndSetDescrExtenstion_whenDescriptionLastWordEndsRightBeforeThreshold() {
        String expectedDescription = generateStringOfLenght(130, WithSpaces.EVERY_10TH_CHARACTER) + "blabl";
        String initialDescription = expectedDescription + " blee";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(" blee"));
    }

    @Test
    public void allowingForExtendedDescription_shouldTrimInitialDescriptionAndSetDescrExtenstion_whenDescriptionLastWordEndsAfterThreshold() {
        String expectedDescription = generateStringOfLenght(129, WithSpaces.EVERY_10TH_CHARACTER) + "x";
        String initialDescription = expectedDescription + " blobloblo bla";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(" blobloblo bla"));
    }

    @Test
    public void allowingForExtendedDescription_shouldTrimInitialDescriptionAndSetDescrExtenstion_whenDescriptionLastWordEndsRightAfterThreshold() {
        String expectedDescription = generateStringOfLenght(129, WithSpaces.EVERY_10TH_CHARACTER) + "x";
        String initialDescription = expectedDescription + " bloblo bla";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(" bloblo bla"));
    }

    @Test
    public void allowingForExtendedDescription_shouldUseInitialDescription_whenDescriptionIsEqualToThresholdNoSpaces() {
        String initialDescription = generateStringOfLenght(136, WithSpaces.NO);

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(initialDescription));
        isNull(adPreview.getDescriptionExtensionOrNull());
    }

    @Test
    public void allowingForExtendedDescription_shouldTrimInitialDescriptionAndSetDescrExtenstion_whenDescriptionIsMoreThanThresholdNoSpaces() {
        String expectedDescription = generateStringOfLenght(136, WithSpaces.NO);
        String initialDescription = expectedDescription + "zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is("zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"));
    }

    @Test
    public void allowingForExtendedDescription_shouldUseExtendedDescription_whenDescriptionIsMoreThanThresholdAndLessThanExtendedThreshold() {
        String expectedDescription = _136_CHARACTERS;
        String expectedDescriptionExtension = " " + generateStringOfLenght(358, WithSpaces.EVERY_10TH_CHARACTER);
        String initialDescription = expectedDescription + expectedDescriptionExtension;

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(expectedDescriptionExtension));
    }

    @Test
    public void allowingForExtendedDescription_shouldUseExtendedDescription_whenDescriptionIsEqualToExtendedThreshold() {
        String expectedDescription = _136_CHARACTERS;
        String expectedDescriptionExtension = _356_CHARACTERS_STARTING_WITH_SPACE + "xxxx";
        String initialDescription = expectedDescription + expectedDescriptionExtension;

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(expectedDescriptionExtension));
    }

    @Test
    public void allowingForExtendedDescription_shouldUseExtendedDescriptionAndSetDescrExtenstion_whenDescriptionIsMoreThanExtendedThreshold() {
        String expectedDescription = _136_CHARACTERS;
        String expectedDescriptionExtension = _356_CHARACTERS_STARTING_WITH_SPACE;
        String initialDescription = expectedDescription + expectedDescriptionExtension + " blobloblo";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(expectedDescriptionExtension + "..."));
    }

    @Test
    public void allowingForExtendedDescription_shouldUseExtendedDescriptionAndSetDescrExtenstion_whenDescriptionLastWordEndsOnExtendedThreshold() {
        String expectedDescription = _136_CHARACTERS;
        String expectedDescriptionExtension = _356_CHARACTERS_STARTING_WITH_SPACE + " blo";
        String initialDescription = expectedDescription + expectedDescriptionExtension;

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(expectedDescriptionExtension));
    }

    @Test
    public void allowingForExtendedDescription_shouldUseExtendedDescriptionAndSetDescrExtenstion_whenDescriptionLastWordEndsRightBeforeExtendedThreshold() {
        String expectedDescription = _136_CHARACTERS;
        String expectedDescriptionExtension = _356_CHARACTERS_STARTING_WITH_SPACE + " bl";
        String initialDescription = expectedDescription + expectedDescriptionExtension;

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(expectedDescriptionExtension));
    }

    @Test
    public void allowingForExtendedDescription_shouldUseExtendedDescriptionAndSetDescrExtenstion_whenDescriptionLastWordEndsRightAfterExtendedThreshold() {
        String expectedDescription = _136_CHARACTERS;
        String expectedDescriptionExtension = _356_CHARACTERS_STARTING_WITH_SPACE;
        String initialDescription = expectedDescription + expectedDescriptionExtension + " bloo";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(expectedDescriptionExtension + "..."));
    }

    @Test
    public void allowingForExtendedDescription_shouldUseExtendedDescriptionAndSetDescrExtenstion_whenDescriptionIsEqualToExtendedThresholdNoSpaces() {
        String expectedDescription = generateStringOfLenght(136, WithSpaces.NO);
        String expectedDescriptionExtension = generateStringOfLenght(360, WithSpaces.NO);
        String initialDescription = expectedDescription + expectedDescriptionExtension;

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(expectedDescriptionExtension));
    }

    @Test
    public void allowingForExtendedDescription_shouldUseExtendedDescriptionAndSetDescrExtenstion_whenDescriptionIsMoreThanExtendedThresholdNoSpaces() {
        String expectedDescription = generateStringOfLenght(136, WithSpaces.NO);
        String expectedDescriptionExtension = generateStringOfLenght(360, WithSpaces.NO);
        String initialDescription = expectedDescription + expectedDescriptionExtension + "bloo";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), is(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(expectedDescriptionExtension + "..."));
    }

    @Test
    public void allowingForExtendedDescription_shouldNormaliseWhiteCharactersInDescriptions_whenDescriptionIsLessThanExtendedThreshold() {
        String initialDescription = "   " + stringOfLenght(48)  + "\n\n" + stringOfLenght(49) + " \t \t" + stringOfLenght(36)
                + "\n" + stringOfLenght(102) + "   " + stringOfLenght(130);
        String expectedDescription = " " + stringOfLenght(48)    + " " + stringOfLenght(49) + " " + stringOfLenght(36);
        String expectedDescriptionExtension = " " + stringOfLenght(102) + " " + stringOfLenght(130);

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), equalTo(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), equalTo(expectedDescriptionExtension));
    }

    @Test
    public void allowingForExtendedDescription_shouldNormaliseWhiteCharactersInDescriptions_whenDescriptionIsGreaterThanThanExtendedThreshold() {
        String initialDescription = "   " + stringOfLenght(48)  + "\n\n" + stringOfLenght(49) + " \t \t" + stringOfLenght(36)
                + "\n" + stringOfLenght(98) + "   " + stringOfLenght(99) + "    " + stringOfLenght(159) + "x blabla";
        String expectedDescription = " " + stringOfLenght(48)    + " " + stringOfLenght(49) + " " + stringOfLenght(36);
        String expectedDescriptionExtension = " " + stringOfLenght(98) + " " + stringOfLenght(99) + " " + stringOfLenght(159) + "x...";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(initialDescription);

        assertThat(adPreview.getDescription(), equalTo(expectedDescription));
        assertThat(adPreview.getDescriptionExtensionOrNull(), equalTo(expectedDescriptionExtension));
    }

    @Test
    public void withoutDescriptionExtending_shouldTruncateMore_whenAPercentageOfCharactersAreUppercase() {
        String inputDescription = multiplyText("ABCDefghi ", 20);
        String expectedDescriptionStart = multiplyText("ABCDefghi ", 11) + "ABCDefghi...";

        AdPreview adPreview = getAdPreviewWithoutExtendedDescription(inputDescription);

        assertThat(adPreview.getDescription(), is(expectedDescriptionStart));
    }

    @Test
    public void withDescriptionExtending_shouldTruncateMore_whenAPercentageOfCharactersAreUppercase() {
        String inputDescription = multiplyText("XYxyxyZYzy ", 20) + "zzz";
        String expectedDescriptionStart = multiplyText("XYxyxyZYzy ", 11) + "XYxyxyZYzy";
        String expectedDescriptionExtension = multiplyText(" XYxyxyZYzy", 8) + " zzz";

        AdPreview adPreview = getAdPreviewWithExtendedDescription(inputDescription);

        assertThat(adPreview.getDescription(), is(expectedDescriptionStart));
        assertThat(adPreview.getDescriptionExtensionOrNull(), is(expectedDescriptionExtension));
    }

    private AdPreview getAdPreviewWithExtendedDescription(String description) {
        return new AdPreviewImpl.Builder()
                .categories(newArrayList(categoryWithId(44L), categoryWithId(4411L)))
                .extendedDescriptionL1Categories(newArrayList(44L))
                .description(description).build();
    }

    private Category categoryWithId(long id) {
        return new Category().id(id);
    }

    private AdPreview getAdPreviewWithoutExtendedDescription(String description) {
        return new AdPreviewImpl.Builder()
                .l1CategoryId(44L)
                .extendedDescriptionL1Categories(newArrayList(43L))
                .description(description).build();
    }

    private String stringOfLenght(int lenght) {
        return generateStringOfLenght(lenght, WithSpaces.EVERY_10TH_CHARACTER);
    }

    private String generateStringOfLenght(int lenght, WithSpaces mode) {
        String description = "";
        for (int i = 0; i < lenght / 10; i++) {
            description += "blablabla" + (mode == WithSpaces.EVERY_10TH_CHARACTER ? " " : "x");
        }
        for (int i = 0; i < lenght % 10; i++) {
            description += "z";
        }
        return description;
    }

    private enum WithSpaces {
        EVERY_10TH_CHARACTER, NO
    }

    private String multiplyText(String text, int multiplicator) {
        String result = "";
        for (int i = 0; i < multiplicator; i++) {
            result += text;
        }
        return result;
    }
}
