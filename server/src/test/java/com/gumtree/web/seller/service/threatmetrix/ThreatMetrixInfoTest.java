package com.gumtree.web.seller.service.threatmetrix;

import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrixTracking;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThreatMetrixInfoTest {

    @Mock
    private ThreatMetrixCookie mockCookie;

    @Mock
    private ThreatMetrixTracking mockTracking;

    private static final String TEST_SESSION_ID = "test-session-123";

    @Test
    public void testConstructor_WithValidData() {
        // When
        ThreatMetrixInfo info = new ThreatMetrixInfo(mockCookie, mockTracking, true);

        // Then
        assertTrue(info.isEnabled());
        assertEquals(mockCookie, info.getCookie());
        assertEquals(mockTracking, info.getTracking());
        assertTrue(info.hasValidData());
    }

    @Test
    public void testConstructor_WithDisabledState() {
        // When
        ThreatMetrixInfo info = new ThreatMetrixInfo(mockCookie, mockTracking, false);

        // Then
        assertFalse(info.isEnabled());
        assertEquals(mockCookie, info.getCookie());
        assertEquals(mockTracking, info.getTracking());
        assertFalse(info.hasValidData()); // Should be false because enabled is false
    }

    @Test
    public void testDisabled_StaticMethod() {
        // When
        ThreatMetrixInfo info = ThreatMetrixInfo.disabled();

        // Then
        assertFalse(info.isEnabled());
        assertNull(info.getCookie());
        assertNull(info.getTracking());
        assertFalse(info.hasValidData());
        assertNull(info.getSessionId());
    }

    @Test
    public void testGetSessionId_WithValidCookie() {
        // Given
        when(mockCookie.getDefaultValue()).thenReturn(TEST_SESSION_ID);
        ThreatMetrixInfo info = new ThreatMetrixInfo(mockCookie, mockTracking, true);

        // When
        String sessionId = info.getSessionId();

        // Then
        assertEquals(TEST_SESSION_ID, sessionId);
    }

    @Test
    public void testGetSessionId_WithNullCookie() {
        // Given
        ThreatMetrixInfo info = new ThreatMetrixInfo(null, mockTracking, true);

        // When
        String sessionId = info.getSessionId();

        // Then
        assertNull(sessionId);
    }

    @Test
    public void testHasValidData_AllValid() {
        // Given
        ThreatMetrixInfo info = new ThreatMetrixInfo(mockCookie, mockTracking, true);

        // When/Then
        assertTrue(info.hasValidData());
    }

    @Test
    public void testHasValidData_DisabledState() {
        // Given
        ThreatMetrixInfo info = new ThreatMetrixInfo(mockCookie, mockTracking, false);

        // When/Then
        assertFalse(info.hasValidData());
    }

    @Test
    public void testHasValidData_NullCookie() {
        // Given
        ThreatMetrixInfo info = new ThreatMetrixInfo(null, mockTracking, true);

        // When/Then
        assertFalse(info.hasValidData());
    }

    @Test
    public void testHasValidData_NullTracking() {
        // Given
        ThreatMetrixInfo info = new ThreatMetrixInfo(mockCookie, null, true);

        // When/Then
        assertFalse(info.hasValidData());
    }

    @Test
    public void testToString_WithValidData() {
        // Given
        when(mockCookie.getDefaultValue()).thenReturn(TEST_SESSION_ID);
        ThreatMetrixInfo info = new ThreatMetrixInfo(mockCookie, mockTracking, true);

        // When
        String result = info.toString();

        // Then
        assertTrue(result.contains("enabled=true"));
        assertTrue(result.contains("sessionId=" + TEST_SESSION_ID));
        assertTrue(result.contains("hasTracking=true"));
    }

    @Test
    public void testToString_WithDisabledState() {
        // Given
        ThreatMetrixInfo info = ThreatMetrixInfo.disabled();

        // When
        String result = info.toString();

        // Then
        assertTrue(result.contains("enabled=false"));
        assertTrue(result.contains("sessionId=null"));
        assertTrue(result.contains("hasTracking=false"));
    }

    @Test
    public void testToString_WithNullCookie() {
        // Given
        ThreatMetrixInfo info = new ThreatMetrixInfo(null, mockTracking, true);

        // When
        String result = info.toString();

        // Then
        assertTrue(result.contains("enabled=true"));
        assertTrue(result.contains("sessionId=null"));
        assertTrue(result.contains("hasTracking=true"));
    }

    @Test
    public void testToString_WithNullTracking() {
        // Given
        when(mockCookie.getDefaultValue()).thenReturn(TEST_SESSION_ID);
        ThreatMetrixInfo info = new ThreatMetrixInfo(mockCookie, null, true);

        // When
        String result = info.toString();

        // Then
        assertTrue(result.contains("enabled=true"));
        assertTrue(result.contains("sessionId=" + TEST_SESSION_ID));
        assertTrue(result.contains("hasTracking=false"));
    }

    @Test
    public void testGetSessionId_WithEmptySessionId() {
        // Given
        String emptySessionId = "";
        when(mockCookie.getDefaultValue()).thenReturn(emptySessionId);
        ThreatMetrixInfo info = new ThreatMetrixInfo(mockCookie, mockTracking, true);

        // When
        String sessionId = info.getSessionId();

        // Then
        assertEquals(emptySessionId, sessionId);
        assertTrue(info.hasValidData()); // Should still be valid with empty string
    }
}
