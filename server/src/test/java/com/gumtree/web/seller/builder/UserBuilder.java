package com.gumtree.web.seller.builder;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.KnownGoodReason;
import com.gumtree.api.SocialData;
import com.gumtree.api.User;
import com.gumtree.api.UserType;
import com.gumtree.seller.domain.user.entity.UserStatus;
import org.joda.time.DateTime;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * User builder to use in stubbed tests
 */
public class UserBuilder {
    private String email;
    private String firstName = "Test";
    private String lastName = "Stubbed";

    private Long id;
    private DateTime creationDate;
    private UserType type;
    private String phone;
    private Boolean phoneVerified;
    private DateTime phoneVerifiedDate;
    private String phoneVerifiedNumber;
    private Boolean captchaVerified;
    private DateTime captchaVerifiedDate;
    private Boolean knownGood;
    private KnownGoodReason knownGoodReason;
    private BushfireApiKey apiKey = new BushfireApiKey();
    private List<Long> accountIds = Arrays.asList(AccountBuilder.DEFAULT_ACC_ID);
    private UserStatus status;
    private Boolean optInMarketing = false;
    private Boolean inBushfire;
    private Boolean superUser = false;
    private Boolean ebayMotorsUser = false;
    private Boolean jobsUser = false;
    private Boolean selfServedProUser = false;
    private DateTime postingSince;
    private String gender;
    private List<SocialData> socialData = new ArrayList();
    private DateTime activationDate;
    private Boolean passwordExists;


    public static UserBuilder builder(){
        return new UserBuilder();
    }

    public UserBuilder withId(Long id){
        this.id = id;
        return this;
    }

    public UserBuilder withFirstName(String firstName){
        this.firstName = firstName;
        return this;
    }

    public UserBuilder withLastName(String lastName){
        this.lastName = lastName;
        return this;
    }

    public UserBuilder withEmail(String email){
        this.email = email;
        return this;
    }

    public UserBuilder withCreationDate(DateTime creationDate){
        this.creationDate = creationDate;
        return this;
    }

    public UserBuilder withType(UserType type){
        this.type = type;
        return this;
    }

    public UserBuilder withPhone(String phone){
        this.phone = phone;
        return this;
    }

    public UserBuilder withPhoneVerified(Boolean phoneVerified){
        this.phoneVerified = phoneVerified;
        return this;
    }

    public UserBuilder withPhoneVerifiedNumber(String phoneVerifiedNumber){
        this.phoneVerifiedNumber = phoneVerifiedNumber;
        return this;
    }

    public UserBuilder withCaptchaVerified(Boolean captchaVerified){
        this.captchaVerified = captchaVerified;
        return this;
    }

    public UserBuilder withCaptchaVerifiedDate(DateTime captchaVerifiedDate){
        this.captchaVerifiedDate = captchaVerifiedDate;
        return this;
    }

    public UserBuilder withKnownGood(Boolean knownGood){
        this.knownGood = knownGood;
        return this;
    }

    public UserBuilder withKnownGoodReason(KnownGoodReason knownGoodReason){
        this.knownGoodReason = knownGoodReason;
        return this;
    }

    public UserBuilder withApiKey(BushfireApiKey apiKey){
        this.apiKey = apiKey;
        return this;
    }

    public UserBuilder withAccountIds(List<Long> accountIds){
        this.accountIds = accountIds;
        return this;
    }

    public UserBuilder withStatus(UserStatus status){
        this.status = status;
        return this;
    }

    public UserBuilder withOptInMarketing(Boolean optInMarketing){
        this.optInMarketing = optInMarketing;
        return this;
    }

    public UserBuilder withInBushFire(Boolean inBushFire){
        this.inBushfire = inBushFire;
        return this;
    }

    public UserBuilder withSuperUser(Boolean superUser){
        this.superUser = superUser;
        return this;
    }

    public UserBuilder withEbayMotorsUser(Boolean ebayMotorsUser){
        this.ebayMotorsUser = ebayMotorsUser;
        return this;
    }

    public UserBuilder withJobsUser(Boolean jobsUser){
        this.jobsUser = jobsUser;
        return this;
    }

    public UserBuilder withSelfServedProUser(Boolean selfServedProUser){
        this.selfServedProUser = selfServedProUser;
        return this;
    }

    public UserBuilder withPostingSince(DateTime postingSince){
        this.postingSince = postingSince;
        return this;
    }

    public UserBuilder withGender(String gender){
        this.gender = gender;
        return this;
    }

    public UserBuilder withSocialData(List<SocialData> socialData){
        this.socialData = socialData;
        return this;
    }

    public UserBuilder withActivationDate(DateTime activationDate){
        this.activationDate = activationDate;
        return this;
    }

    public UserBuilder withPasswordExists(Boolean passwordExists){
        this.passwordExists = passwordExists;
        return this;
    }


    /**
     * set user email
     * @param email  email
     * @return this builder
     */
    public UserBuilder email(String email) {
        this.email = email;
        return this;
    }

    /**
     * builds user
     * @return the user
     */
    public User build() {
        User user = new User();
        user.setId(id);
        user.setCreationDate(creationDate);
        user.setType(type);
        user.setEmail(email);
        user.setFirstName(firstName);
        user.setLastName(lastName);
        user.setPhone(phone);
        user.setPhoneVerified(phoneVerified);
        user.setPhoneVerifiedDate(phoneVerifiedDate);
        user.setPhoneVerifiedNumber(phoneVerifiedNumber);
        user.setCaptchaVerified(captchaVerified);
        user.setCaptchaVerifiedDate(captchaVerifiedDate);
        user.setKnownGood(knownGood);
        user.setKnownGoodReason(knownGoodReason);
        user.setApiKey(apiKey);
        user.setAccountIds(accountIds);
        user.setStatus(status);
        user.setOptInMarketing(optInMarketing);
        user.setInBushfire(inBushfire);
        user.setSuperUser(superUser);
        user.setEbayMotorsUser(ebayMotorsUser);
        user.setJobsUser(jobsUser);
        user.setSelfServedProUser(selfServedProUser);
        user.setPostingSince(postingSince);
        user.setGender(gender);
        user.setSocialData(socialData);
        user.setActivationDate(activationDate);
        user.setPasswordExists(passwordExists);
        user.setAccountIds(accountIds);

        return user;
    }
}
