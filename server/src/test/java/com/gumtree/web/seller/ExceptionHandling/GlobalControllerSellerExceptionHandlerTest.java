package com.gumtree.web.seller.ExceptionHandling;

import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.exception.FormValidationException;
import com.gumtree.web.security.exception.InvalidUserException;
import com.gumtree.web.security.exception.UserNotRecognisedException;
import com.gumtree.web.seller.exception.GlobalControllerSellerExceptionHandler;
import com.gumtree.web.seller.page.common.model.CoreModel;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.SubjectContext;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GlobalControllerSellerExceptionHandlerTest {

    @Mock
    private HttpServletRequest servletRequest;

    @Mock
    private HttpServletResponse servletResponse;

    @Mock
    private UrlScheme urlScheme;

    @Mock
    private UserSessionService userSessionService;

    @Mock
    private CoreModel.BuilderFactory builderFactory;

    @InjectMocks
    private GlobalControllerSellerExceptionHandler exceptionHandler;

    @Before
    public void init() {
        when(urlScheme.urlFor(Actions.BUSHFIRE_LOGIN)).thenReturn("/test/login/url");
        SecurityManager securityManager = mock(SecurityManager.class);
        SecurityUtils.setSecurityManager(securityManager);
        when(securityManager.createSubject(any(SubjectContext.class))).thenReturn(mock(Subject.class));
    }

    @Test
    public void cookiesClearedAndRedirectedToLoginWhenUserNotRecognisedExceptionThrown() throws Exception {
        // given
        UserNotRecognisedException exception = new UserNotRecognisedException("Exception");

        // when
        ModelAndView modelAndView = exceptionHandler.handleUserNotRecognised(exception);

        // then
        assertThat(modelAndView.getViewName(), equalTo("redirect:/test/login/url"));
    }

    @Test
    public void cookiesClearedAndRedirectedToLoginWhenInvalidUserExceptionThrown() throws Exception {
        // given
        InvalidUserException exception = new InvalidUserException("Exception");

        // when
        ModelAndView modelAndView = exceptionHandler.handleInvalidUser(exception);

        // then
        assertThat(modelAndView.getViewName(), equalTo("redirect:/test/login/url"));
    }

    @Test
    public void formValidationExceptionIsHandled() {
        // given
        Map<String, List<String>> formErrors = new HashMap<>();
        formErrors.put("error", Arrays.asList("Invalid email address"));
        FormValidationException exception = new FormValidationException("Registration failed", formErrors);

        // when
        ResponseEntity entity = exceptionHandler.handleFormValidationException(exception);

        // then
        assertThat(entity.getStatusCode(), equalTo(HttpStatus.BAD_REQUEST));
        assertThat(entity.getBody(), equalTo(exception.getFormErrors()));
    }
}

