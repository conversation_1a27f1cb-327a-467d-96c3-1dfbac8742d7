package com.gumtree.web.seller.config;

import com.gumtree.web.storage.FakeJedis;
import com.gumtree.web.storage.FakeJedisPool;
import com.gumtree.web.storage.RedisTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

@Configuration
public class StubRedisConfig {

    @Bean
    public RedisTemplate redisTemplate() {
        MeterRegistry meterRegistry = new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());
        return new RedisTemplate(new FakeJedisPool(new FakeJedis()), null, null, meterRegistry);
    }
}
