package com.gumtree.web.seller.page.postad.validation;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableMap;
import com.gumtree.api.Image;
import com.gumtree.api.category.domain.Category;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import org.junit.Before;
import org.junit.Test;

import javax.validation.ConstraintValidatorContext;
import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class MinImagesPostedValidatorTest {

    private static Long PETS_CATEGORY = 9395L;

    private MinImagesPostedValidator validator;
    private MinImagesPostedCategory annotation;

    private ConstraintValidatorContext context;

    private PostAdDetail postAdDetail;

    private CategoryService categoryService;

    private List<Image> emptyImageList = new ArrayList<Image>();

    private Category pets;

    @Before
    public void init() {
        annotation = mock(MinImagesPostedCategory.class);
        context = mock(ConstraintValidatorContext.class);
        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.class);
        ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderDefinedContext nodeContext =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderDefinedContext.class);

        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(builder);
        when(builder.addNode(anyString())).thenReturn(nodeContext);

        when(annotation.fieldList()).thenReturn(new String[] {"file"});
        when(annotation.message()).thenReturn("postad.pets.needs_image");

        postAdDetail = new PostAdDetail();
        postAdDetail.setPostAdFormBean(new PostAdFormBean());
        categoryService = mock(CategoryService.class);
        validator = new MinImagesPostedValidator(categoryService, new Long[]{PETS_CATEGORY});
        validator.initialize(annotation);
        pets  = new Category(PETS_CATEGORY, "pets", "pets");
        when(categoryService.getLevelHierarchy(PETS_CATEGORY)).thenReturn(ImmutableMap.of(1, pets));
    }

    @Test
    public void testFailureWhenPetsIsSelectedWithZeroImages() {

        pets.setParentId(1234l);

        postAdDetail.setCategoryId(PETS_CATEGORY);
        postAdDetail.setImages(emptyImageList);

        assertThat(validator.isValid(postAdDetail, context), equalTo(false));
    }

    @Test
    public void testFailureWhenChildOfPetsIsSelectedWithZeroImages() {

        Category child = new Category(1234L, "child", "child");
        child.setParentId(pets.getId());
        when(categoryService.getLevelHierarchy(1234L)).thenReturn(ImmutableMap.of(1, pets, 2, child));

        postAdDetail.setCategoryId(1234L);
        postAdDetail.setImages(emptyImageList);

        when(categoryService.getById(1234L)).thenReturn(Optional.of(child));

        assertThat(validator.isValid(postAdDetail, context), equalTo(false));
    }

    @Test
    public void testPassesWhenPetsIsSelectedWithMoreThanZeroImages() {

        pets.setParentId(1L);

        postAdDetail.getPostAdFormBean().setCategoryId(PETS_CATEGORY);

        List<Image> images = new ArrayList<Image>();
        images.add(new Image());
        postAdDetail.setImages(images);

        assertThat(validator.isValid(postAdDetail, context), equalTo(true));
    }

    @Test
    public void testFailureWhenChildOfPetsIsSelectedWithMoreThanZeroImages() {

        Category child = new Category(1234L, "child", "child");
        child.setParentId(pets.getId());
        when(categoryService.isChild(child, pets)).thenReturn(true);


        when(categoryService.getById(1234L)).thenReturn(Optional.of(child));

        postAdDetail.getPostAdFormBean().setCategoryId((long) 1234);
        List<Image> images = new ArrayList<Image>();
        images.add(new Image());
        postAdDetail.setImages(images);

        assertThat(validator.isValid(postAdDetail, context), equalTo(true));
    }

    @Test
    public void testNotPetsCategoryReturnsTrue() {
        Category root = new Category(1L, "all", "All");
        Category child = new Category(1234L, "child", "child");
        child.setParentId(1L);


        postAdDetail.getPostAdFormBean().setCategoryId(1234L);
        when(categoryService.getById(1234L)).thenReturn(Optional.of(child));
        when(categoryService.getById(1L)).thenReturn(Optional.of(root));
        when(categoryService.isChild(child, root)).thenReturn(true);



        List<Image> images = new ArrayList<Image>();
        images.add(new Image());
        postAdDetail.setImages(images);

        assertThat(validator.isValid(postAdDetail, context), equalTo(true));
    }
}
