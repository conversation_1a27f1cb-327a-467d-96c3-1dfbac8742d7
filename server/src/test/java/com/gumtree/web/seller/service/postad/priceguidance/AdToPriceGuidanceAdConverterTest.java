package com.gumtree.web.seller.service.postad.priceguidance;

import com.gumtree.common.format.PriceFormatter;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.Ad;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.Category;
import com.gumtree.web.seller.converter.FlatAdConverterUtils;
import com.gumtree.web.seller.page.postad.model.PriceGuidanceAd;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class AdToPriceGuidanceAdConverterTest {
    @InjectMocks private AdToPriceGuidanceAdConverter converter;

    @Mock
    private FlatAdConverterUtils flatAdConverterUtils;

    @Mock
    private PriceFormatter priceFormatter;

    @Test
    public void shouldConvert() {
        Ad ad = new Ad()
                .id(654321L)
                .title("My title")
                .primaryImageUrl("http://image.primary.com/123")
                .putAttributeItem("vehicle_mileage", 60000)
                .putAttributeItem("price", 423400)
                .putAttributeItem("seller_type", "PRIVATE")
                .addCategoriesItem(new Category().id(Categories.CARS.getId()).primary(true));

        Long categoryId = Categories.CARS.getId();

        Map<String, Attribute> attributeMap = Collections.emptyMap();
        when(flatAdConverterUtils.getAdAttributeMap(categoryId, ad.getAttribute())).thenReturn(attributeMap);
        when(flatAdConverterUtils.getPrice(attributeMap)).thenReturn(new BigDecimal(4234));
        when(flatAdConverterUtils.getSellerType(categoryId, attributeMap)).thenReturn("PRIVATE");
        when(flatAdConverterUtils.getVehicleMileage(categoryId, attributeMap)).thenReturn(Optional.of("60,000 miles"));
        when(priceFormatter.format(new BigDecimal(4234), null, categoryId)).thenReturn("£4,234");

        // when
        PriceGuidanceAd priceGuidanceAd = converter.convert(ad);

        // then
        assertThat(priceGuidanceAd.getTitle()).isEqualTo("My title");
        assertThat(priceGuidanceAd.getPrice()).isEqualTo("£4,234");
        assertThat(priceGuidanceAd.getPrimaryImageURL()).isEqualTo("http://image.primary.com/123");
        assertThat(priceGuidanceAd.getSellerType()).isEqualTo("PRIVATE");
        assertThat(priceGuidanceAd.getMileage()).isEqualTo("60,000 miles");
    }
}