package com.gumtree.web.seller.page.adstats.controller;

import com.gumtree.web.seller.page.manageads.model.ManageAdStatus;
import com.gumtree.web.seller.service.adstats.AdvertStatisticData;
import org.joda.time.DateTime;
import org.junit.Test;

import static com.gumtree.web.seller.service.adstats.AdvertStatisticDataBuilder.advertStatisticData;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.collection.IsArrayContainingInOrder.arrayContaining;
import static org.hamcrest.collection.IsArrayWithSize.arrayWithSize;

public class AdvertStatisticDataCSVConverterTest {

    private AdvertStatisticDataCSVConverter converter = new AdvertStatisticDataCSVConverter();

    @Test
    public void shouldReplaceNullValuesWithEmptyStrings() {
        AdvertStatisticData advertStatisticData = advertStatisticData().with().noData().build();
        String[] cellValues = converter.convertEntry(advertStatisticData);
        assertThat(cellValues, is(arrayWithSize(12)));
        assertThat(cellValues, is(arrayContaining(""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
        )));
    }

    @Test
    public void shouldConvertAllValues() {
        AdvertStatisticData advertStatisticData =
                            advertStatisticData()
                                        .with()
                                            .advertId(1234L)
                                            .advertTitle("Super advert 1234")
                                            .vipUrl("some VIP url")
                                            .advertStatus(ManageAdStatus.PROCESSING)
                                            .contactEmail("<EMAIL>")
                                            .creationDate(dateTime().withDate(2011, 12, 31).withTime(12, 15, 45, 20))
                                            .lastPostedDate(dateTime().withDate(2012, 6, 30).withTime(10, 35, 20, 10))
                                            .lastModifiedDate(dateTime().withDate(2012, 10, 2).withTime(17, 0, 30, 40))
                                            .numberOfTimesReposted(78)
                                            .srpViews(390)
                                            .vipViews(187)
                                            .numberOfReplies(28)
                                        .build();
        String[] cellValues = converter.convertEntry(advertStatisticData);
        assertThat(cellValues, is(arrayContaining(   "1234"
                                                    ,"Super advert 1234"
                                                    ,"some VIP url"
                                                    ,ManageAdStatus.PROCESSING.getDisplayValue()
                                                    ,"<EMAIL>"
                                                    ,"31/12/2011 12:15"
                                                    ,"30/06/2012 10:35"
                                                    ,"02/10/2012 17:00"
                                                    ,"78"
                                                    ,"390"
                                                    ,"187"
                                                    ,"28"
        )));
    }

    static DateTime dateTime() {
        return new DateTime(0);
    }
}
