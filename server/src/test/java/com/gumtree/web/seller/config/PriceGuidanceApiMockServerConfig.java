package com.gumtree.web.seller.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.gumtree.config.api.MotorsPriceGuidanceServiceConfig;
import com.gumtree.config.api.SellerStubWireMockApi;
import com.gumtree.domain.category.Categories;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.Ad;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.Category;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.PriceGuidanceResponse;
import com.gumtree.seller.infrastructure.driven.motors.price.guidance.model.PriceStatistics;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;

import java.util.Map;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.gumtree.config.api.SellerStubWireMockApi.WIREMOCK_API_PORT;

import static com.gumtree.web.seller.builder.VehicleAttributeHelper.CARS_VEHICLE_ATTRIBUTE_MAP;
import static com.gumtree.web.seller.builder.VehicleAttributeHelper.MOTOR_BIKES_VEHICLE_ATTRIBUTE_MAP;
import static com.gumtree.web.seller.builder.VehicleAttributeHelper.CAR_CATEGORY_ID;
import static com.gumtree.web.seller.builder.VehicleAttributeHelper.MOTOR_BIKES_CATEGORY_ID;
import static com.gumtree.web.seller.builder.VehicleAttributeHelper.INVALID_CAR_VRM;
import static com.gumtree.web.seller.builder.VehicleAttributeHelper.VALID_CAR_VRM;
import static com.gumtree.web.seller.builder.VehicleAttributeHelper.VALID_MOTORBIKE_VRM;

@Configuration
public class PriceGuidanceApiMockServerConfig {

    private ObjectMapper objectMapper = createObjectMapper();

    @Bean
    public MotorsPriceGuidanceServiceConfig.PriceGuidanceApiProperties priceGuidanceApiProperties() {

        initMockServer(VALID_CAR_VRM, CAR_CATEGORY_ID, HttpStatus.OK.value(), priceGuidanceResponse(CARS_VEHICLE_ATTRIBUTE_MAP));
        initMockServer(VALID_MOTORBIKE_VRM, MOTOR_BIKES_CATEGORY_ID, HttpStatus.OK.value(), priceGuidanceResponse(MOTOR_BIKES_VEHICLE_ATTRIBUTE_MAP));
        initMockServer(INVALID_CAR_VRM, CAR_CATEGORY_ID, HttpStatus.NO_CONTENT.value(), null);

        return new MotorsPriceGuidanceServiceConfig.PriceGuidanceApiProperties(
                String.format("http://localhost:%d", WIREMOCK_API_PORT),
                1000,
                10000
        );

    }

    private void initMockServer(String vrn, Integer categoryId, int status, PriceGuidanceResponse response) {
        try {
            SellerStubWireMockApi.getServer()
                    .stubFor(get(urlPathEqualTo("/price-guidance"))
                            .withQueryParam("vrn", equalTo(vrn))
                            .withQueryParam("size", equalTo("20"))
                            .withQueryParam("category_id", equalTo(String.valueOf(categoryId)))
                            .withHeader("User-Agent", equalTo("seller"))
                            .willReturn(aResponse()
                                    .withStatus(status)
                                    .withHeader("Content-Type", "application/json")
                                    .withBody(null != response ? objectMapper.writeValueAsString(response) : null)));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private static ObjectMapper createObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
        objectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.disable(MapperFeature.USE_ANNOTATIONS);
        return objectMapper;
    }

    private PriceGuidanceResponse priceGuidanceResponse(Map<String, String > attributeMap) {
        return new PriceGuidanceResponse()
                .addAdsItem(createAd())
                .priceStatistics(new PriceStatistics().min(4234.0).avg(4234.0).max(4234.0))
                .attributes(attributeMap);
    }

    private Ad createAd() {
        return new Ad()
                .id(654321L)
                .title("My title")
                .primaryImageUrl("http://image.primary.com/123")
                .putAttributeItem("vehicle_mileage", 60000)
                .putAttributeItem("price", 423400)
                .putAttributeItem("seller_type", "PRIVATE")
                .addCategoriesItem(new Category().id(Categories.CARS.getId()).primary(true));
    }

}
