package com.gumtree.web.seller.page.postad.controller;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.gumtree.api.Account;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeValueMetadata;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.category.domain.srp.AttributeSrpFilterMetadata;
import com.gumtree.api.category.domain.srp.AttributeSrpMetadata;
import com.gumtree.api.category.domain.syi.AttributeSyiMetadata;
import com.gumtree.api.category.domain.syi.SyiAttributeValueMetadata;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.mobile.test.Fixtures;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdAttribute;
import com.gumtree.web.seller.page.postad.model.PostAdAttributeValue;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import org.fest.assertions.data.MapEntry;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Map;

import static com.gumtree.common.util.messages.ForSaleMessages.FOR_SALE_REVIEW_BUSINESS;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class SellerTypePanelForSalesTest {

    private static final java.lang.Long ACCOUNT_ID = 1L;
    private static final java.lang.Long CAT_ID = 10L;
    private static final String TRADE_VALUE = "trade";
    private static final String TRADE_LABEL = "Business";
    private static final String PRIVATE_VALUE = "private";
    private static final String PRIVATE_LABEL = "Private";
    private static final long PRO_ACCOUNT = 34L;

    private AdvertEditor editor = mock(AdvertEditor.class);
    private BushfireApi bushfireApi = mock(BushfireApi.class);
    private AccountApi accountApi = mock(AccountApi.class);
    private AttributeMetadata sellerTypeAttributeMetadata;
    private PostAdFormBean postAdFormBean;

    @Before
    public void beforeEach() {
        postAdFormBean = new PostAdFormBean();
        // editor
        when(editor.getAccountId()).thenReturn(ACCOUNT_ID);
        when(editor.getCategoryId()).thenReturn(CAT_ID);
        when(editor.getPostAdFormBean()).thenReturn(postAdFormBean);

        when(bushfireApi.accountApi()).thenReturn(accountApi);
        when(accountApi.getAccount(ACCOUNT_ID)).thenReturn(new Account());

        initialiseSellerTypeAttribute();
    }

    private void initialiseSellerTypeAttribute() {
        AttributeValueMetadata tradeSt = new AttributeValueMetadata(TRADE_VALUE, TRADE_LABEL);
        AttributeValueMetadata privateSt = new AttributeValueMetadata(PRIVATE_VALUE, PRIVATE_LABEL);

        sellerTypeAttributeMetadata = Fixtures.createSellerType()
                .withSyi(new AttributeSyiMetadata("Seller Type",
                        Arrays.asList(
                                new SyiAttributeValueMetadata(TRADE_VALUE, TRADE_LABEL),
                                new SyiAttributeValueMetadata(PRIVATE_VALUE, PRIVATE_LABEL)),
                        null,
                        null))
                .withSrp(AttributeSrpMetadata.builder()
                        .withLabel("Seller Type")
                        .addFilter(new AttributeSrpFilterMetadata(AttributeSrpFilterMetadata.Type.ENUM, Lists.newArrayList(tradeSt, privateSt)))
                        .build())
                .withSupportedSearchValues(tradeSt, privateSt)
                .build();
    }

    @Test
    public void shouldAlwaysPreselectPrivateAsDefault() throws Exception {
        //given
        SellerTypePanelForSales panel = new SellerTypePanelForSales(bushfireApi, editor, sellerTypeAttributeMetadata);

        //when
        PostAdAttribute postAdAttribute = panel.getAttribute();

        // then
        assertThat(postAdAttribute.getValues()).isEqualTo(Lists.newArrayList(
                new PostAdAttributeValue(TRADE_VALUE, TRADE_LABEL, false, false),
                new PostAdAttributeValue(PRIVATE_VALUE, PRIVATE_LABEL, true, false)));
    }

    @Test
    public void shouldAlwaysPreselectPrivateAsDefaultWhenNoUser() throws Exception {
        //given
        when(editor.getAccountId()).thenReturn(null);
        SellerTypePanelForSales panel = new SellerTypePanelForSales(bushfireApi, editor, sellerTypeAttributeMetadata);

        //when
        PostAdAttribute postAdAttribute = panel.getAttribute();

        // then
        assertThat(postAdAttribute.getValues()).isEqualTo(Lists.newArrayList(
                new PostAdAttributeValue(TRADE_VALUE, TRADE_LABEL, false, false),
                new PostAdAttributeValue(PRIVATE_VALUE, PRIVATE_LABEL, true, false)));
    }


    @Test
    public void shouldKeepTradeValueSelectedIfTradeWasSelectedInTheFormBefore() throws Exception {
        //given
        postAdFormBean.setAttributes(ImmutableMap.of(CategoryConstants.Attribute.SELLER_TYPE.getName(), TRADE_VALUE));
        SellerTypePanelForSales panel = new SellerTypePanelForSales(bushfireApi, editor, sellerTypeAttributeMetadata);

        //when
        PostAdAttribute postAdAttribute = panel.getAttribute();

        // then
        assertThat(postAdAttribute.getValues()).isEqualTo(Lists.newArrayList(
                new PostAdAttributeValue(TRADE_VALUE, TRADE_LABEL, true, false),
                new PostAdAttributeValue(PRIVATE_VALUE, PRIVATE_LABEL, false, false)));
    }

    @Test
    public void shouldKeepPrivateValueSelectedIfPrivateWasSelectedInTheFormBefore() throws Exception {
        //given
        postAdFormBean.setAttributes(ImmutableMap.of(CategoryConstants.Attribute.SELLER_TYPE.getName(), TRADE_VALUE));
        SellerTypePanelForSales panel = new SellerTypePanelForSales(bushfireApi, editor, sellerTypeAttributeMetadata);

        //when
        PostAdAttribute postAdAttribute = panel.getAttribute();

        // then
        assertThat(postAdAttribute.getValues()).isEqualTo(Lists.newArrayList(
                new PostAdAttributeValue(TRADE_VALUE, TRADE_LABEL, true, false),
                new PostAdAttributeValue(PRIVATE_VALUE, PRIVATE_LABEL, false, false)));
    }

    @Test
    public void shouldForcePostAsBusinessIfProAccount() throws Exception {
        //given
        initialiseMockProAccount();
        SellerTypePanelForSales panel = new SellerTypePanelForSales(bushfireApi, editor, sellerTypeAttributeMetadata);

        //when
        PostAdAttribute postAdAttribute = panel.getAttribute();

        //then
        assertThat(postAdAttribute.getValues()).isEqualTo(Lists.newArrayList(
                new PostAdAttributeValue(TRADE_VALUE, TRADE_LABEL, true, false),
                new PostAdAttributeValue(PRIVATE_VALUE, PRIVATE_LABEL, false, true)));
    }

    @Test
    public void shouldDisplayMessageWhenBusinessSellerTypeSelected() {
        // given
        postAdFormBean.setAttributes(ImmutableMap.of(CategoryConstants.Attribute.SELLER_TYPE.getName(), TRADE_VALUE));
        SellerTypePanelForSales panel = new SellerTypePanelForSales(bushfireApi, editor, sellerTypeAttributeMetadata);

        // when
        Map<String, String> text = panel.getText();

        // then
        assertThat(text).contains(MapEntry.entry("body", FOR_SALE_REVIEW_BUSINESS));
    }

    @Test
    public void shouldDisplayMessageWhenBusinessSellerTypeSelectedAndNoUser() {
        // given
        when(editor.getAccountId()).thenReturn(null);
        postAdFormBean.setAttributes(ImmutableMap.of(CategoryConstants.Attribute.SELLER_TYPE.getName(), TRADE_VALUE));
        SellerTypePanelForSales panel = new SellerTypePanelForSales(bushfireApi, editor, sellerTypeAttributeMetadata);

        // when
        Map<String, String> text = panel.getText();

        // then
        assertThat(text).contains(MapEntry.entry("body", FOR_SALE_REVIEW_BUSINESS));
    }

    @Test
    public void shouldDisplayMessageWhenProAccount() {
        // given
        initialiseMockProAccount();
        SellerTypePanelForSales panel = new SellerTypePanelForSales(bushfireApi, editor, sellerTypeAttributeMetadata);

        // when
        Map<String, String> text = panel.getText();

        // then
        assertThat(text).contains(MapEntry.entry("body", FOR_SALE_REVIEW_BUSINESS));
    }

    @Test
    public void shouldDisplayNoMessageWhenPrivateSellerTypeSelected() {
        // given
        postAdFormBean.setAttributes(ImmutableMap.of(CategoryConstants.Attribute.SELLER_TYPE.getName(), PRIVATE_VALUE));
        SellerTypePanelForSales panel = new SellerTypePanelForSales(bushfireApi, editor, sellerTypeAttributeMetadata);

        // when
        Map<String, String> text = panel.getText();

        // then
        assertThat(text).isNull();
    }

    @Test
    public void shouldDisplayNoMessageWhenNoAccount() {
        // given
        when(editor.getAccountId()).thenReturn(null);
        SellerTypePanelForSales panel = new SellerTypePanelForSales(bushfireApi, editor, sellerTypeAttributeMetadata);

        // when
        Map<String, String> text = panel.getText();

        // then
        assertThat(text).isNull();
    }

    private void initialiseMockProAccount() {
        when(editor.getAccountId()).thenReturn(PRO_ACCOUNT);
        Account proAccount = new Account();
        proAccount.setPro(true);
        when(accountApi.getAccount(PRO_ACCOUNT)).thenReturn(proAccount);
    }
}