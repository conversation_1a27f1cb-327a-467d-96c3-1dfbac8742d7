package com.gumtree.web.seller.page.postad.controller;

import com.google.common.base.Optional;
import com.gumtree.api.Account;
import com.gumtree.api.AccountStatus;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.executor.ApiCall;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.common.properties.GtPropertiesInitializer;
import com.gumtree.common.properties.GtProps;
import com.gumtree.domain.user.User;
import com.gumtree.web.common.ip.RemoteIP;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.security.UserSecurityManager;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.shiro.RedirectUtils;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.ManageAdsController;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdModel;
import com.gumtree.web.seller.service.PostAdWorkspace;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.commons.configuration.ConfigurationException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.AbstractUrlBasedView;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.Arrays;

import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PostAdStateControllerTest extends BaseSellerControllerTest {

    private PostAdFormBean postAdFormBean;
    private PostAdDetail postAdDetail;
    private AdvertEditor advertEditor;
    private PostAdWorkspace postAdWorkspace;
    private Category category;
    private UserSession userSession;
    private PostAdStateController controller;
    private UserSecurityManager userSecurityManager;

    private Account selectedAccount = new Account();
    private CustomMetricRegistry metrics = new CustomMetricRegistry(new SimpleMeterRegistry());

    @Before
    public void init() throws ConfigurationException {
        GtPropertiesInitializer.init("seller-server");
        postAdFormBean = new PostAdFormBean();
        postAdFormBean.setTitle("test-title");
        postAdDetail = new PostAdDetail();

        advertEditor = mock(AdvertEditor.class);
        postAdWorkspace = mock(PostAdWorkspace.class);
        category = mock(Category.class);
        userSession = mock(UserSession.class);
        userSecurityManager = mock(UserSecurityManager.class);

        when(advertEditor.getEditorId()).thenReturn("blahblahblah");
        when(advertEditor.getAdvertId()).thenReturn(1501L);
        when(advertEditor.getPostAdFormBean()).thenReturn(postAdFormBean);
        when(advertEditor.getAdvertDetail()).thenReturn(postAdDetail);

        controller = new PostAdStateController(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme,
                postAdWorkspace, userSession, categoryService, postAdWorkspace,
                userSecurityManager, pageContext, locationService, zenoService, userSessionService, metrics);
        when(postAdWorkspace.getEditor(eq("1"))).thenReturn(advertEditor);
        when(postAdWorkspace.createAndPersistEditor(eq(1L), anyLong(), eq(java.util.Optional.empty()),
                any(RemoteIP.class), any(PermanentCookie.class), any(ThreatMetrixCookie.class), eq(true))).thenReturn(advertEditor);
        when(categoryService.getById(20L)).thenReturn(Optional.of(category));
        when(category.getId()).thenReturn(20L);
        when(userSession.getSelectedAccountId()).thenReturn(1L);
        when(userSecurityManager.getAccount(1L, userSession)).thenReturn(selectedAccount);
        selectedAccount.setStatus(AccountStatus.ACTIVE);
        when(userSession.getUsername()).thenReturn("");
        com.gumtree.api.User user = new com.gumtree.api.User();
        user.setEbayMotorsUser(false);
        when(userSession.getUser()).thenReturn(user);

        autowireAbExperimentsService(controller);
    }

    @Test
    public void whenNoAccountSelectedThenShouldNotCheckToSeeIfAccountIsSuspended() {
        when(advertEditor.supportsChangeCategory()).thenReturn(false);
        when(advertEditor.supportsChangeLocation()).thenReturn(true);
        when(userSession.getSelectedAccountId()).thenReturn(null);
        controller.createEditor(1L, 10L, false, null, null);
        verifyZeroInteractions(userSecurityManager);
    }

    @Test
    public void whenAccountIsSuspendedThenTheUserShouldBeRedirectedToManageAdsWhenCreatingAd() {
        selectedAccount.setStatus(AccountStatus.SUSPENDED);
        ModelAndView mv = controller.createEditor(null, null, false, null, null);
        assertThat(getUrl(mv), equalTo(ManageAdsController.PAGE_PATH));
    }

    @Test
    public void whenAccountIsSuspendedThenTheUserShouldBeRedirectedToManageAdsWhenEditingAd() {
        selectedAccount.setStatus(AccountStatus.SUSPENDED);
        ModelAndView mv = controller.createEditor(10L, null, false,null, null);
        assertThat(getUrl(mv), equalTo(ManageAdsController.PAGE_PATH));
    }

    @Test
    public void whenAccountIsClosedThenTheUserShouldBeRedirectedToManageAdsWhenCreatingAd() {
        selectedAccount.setStatus(AccountStatus.CLOSED);
        ModelAndView mv = controller.createEditor(null, null, false, null, null);
        assertThat(getUrl(mv), equalTo(ManageAdsController.PAGE_PATH));
    }

    @Test
    public void whenAccountIsClosedThenTheUserShouldBeRedirectedToManageAdsWhenEditingAd() {
        selectedAccount.setStatus(AccountStatus.CLOSED);
        ModelAndView mv = controller.createEditor(10L, null, false, null, null);
        assertThat(getUrl(mv), equalTo(ManageAdsController.PAGE_PATH));
    }

    @Test
    public void whenUserIsEbayMotorsUserThenTheUserShouldBeRedirectedToManageAdsWhenCreatingAd() {
        com.gumtree.api.User user = new com.gumtree.api.User();
        user.setEbayMotorsUser(true);
        when(userSession.getUser()).thenReturn(user);
        ModelAndView mv = controller.createEditor(null, null, false, null, null);
        assertThat(getUrl(mv), equalTo(ManageAdsController.PAGE_PATH));
    }

    @Test
    public void whenUserIsEbayMotorsUserThenTheUserShouldBeRedirectedToManageAdsWhenEditingAd() {
        com.gumtree.api.User user = new com.gumtree.api.User();
        user.setEbayMotorsUser(true);
        when(userSession.getUser()).thenReturn(user);
        ModelAndView mv = controller.createEditor(10L, null, false, null, null);
        assertThat(getUrl(mv), equalTo(ManageAdsController.PAGE_PATH));
    }

    @Test
    public void whenCategoryIdIsNullThenRedirectsToCategoryPage() {
        when(category.getId()).thenReturn(1L);
        when(categoryModel.getRootCategory()).thenReturn(category);
        when(request.getRequestURL()).thenReturn(new StringBuffer("/req/url"));
        ApiCallResponse<User> response = mock(ApiCallResponse.class);
        when(apiCallExecutor.call(any(ApiCall.class))).thenReturn(response);
        when(response.getResponseObject()).thenReturn(null);
        assertThat(controller.showBasePage("1", request).getViewName(), equalTo("pages/syi/syi"));
    }

    @Test
    public void whenCategoryIdSetButNoValidLocationSetThenRedirectsToLocationPage() {
        when(advertEditor.getCategoryId()).thenReturn(2L);
        when(categoryModel.getRootCategory()).thenReturn(category);
        when(request.getRequestURL()).thenReturn(new StringBuffer("/req/url"));
        ApiCallResponse<User> response = mock(ApiCallResponse.class);
        when(apiCallExecutor.call(any(ApiCall.class))).thenReturn(response);
        when(response.getResponseObject()).thenReturn(null);
        assertThat(controller.showBasePage("1", request).getViewName(), equalTo("pages/syi/syi"));
    }

    @Test
    public void whenCategoryIdSetAndValidLocationSetThenRedirectsToFormPage() {
        when(advertEditor.getCategoryId()).thenReturn(2L);
        when(categoryModel.getRootCategory()).thenReturn(category);
        when(request.getRequestURL()).thenReturn(new StringBuffer("/req/url"));
        ApiCallResponse<User> response = mock(ApiCallResponse.class);
        when(apiCallExecutor.call(any(ApiCall.class))).thenReturn(response);
        when(response.getResponseObject()).thenReturn(null);
        assertThat(controller.showBasePage("1", request).getViewName(), equalTo("pages/syi/syi"));
    }

    @Test
    public void shouldContainActiveCategoryWhenCategoryIdSetForPostAdPage() {
        //given
        when(advertEditor.getCategoryId()).thenReturn(2L);
        when(categoryModel.getRootCategory()).thenReturn(category);
        when(advertEditor.isEditMode()).thenReturn(false);
        when(advertEditor.isCreateMode()).thenReturn(true);

        Category category1 = mock(Category.class);
        Category category2 = mock(Category.class);
        when(categoryModel.getHierarchy(anyLong())).thenReturn(Arrays.asList(category1, category2));

        when(request.getRequestURL()).thenReturn(new StringBuffer("/req/url"));
        ApiCallResponse<User> response = mock(ApiCallResponse.class);
        when(apiCallExecutor.call(any(ApiCall.class))).thenReturn(response);
        when(response.getResponseObject()).thenReturn(null);

        //when
        PostAdModel postAdModel = (PostAdModel) controller.showBasePage("1", request).getModel().get("model");

        //then
        assertThat(postAdModel.getCore().getActiveCategory(), equalTo(2L));
        assertThat(postAdModel.getCore().getActiveCategoryHierarchy().size(), equalTo(2));
        assertThat(postAdModel.getCore().getPage(), equalTo(Page.PostAd));
    }

    @Test
    public void shouldContainActiveCategoryWhenCategoryIdSetForEditAdPage() {
        //given
        when(advertEditor.getCategoryId()).thenReturn(2L);
        when(advertEditor.isEditMode()).thenReturn(true);
        when(categoryModel.getRootCategory()).thenReturn(category);

        Category category1 = mock(Category.class);
        Category category2 = mock(Category.class);
        when(categoryModel.getHierarchy(anyLong())).thenReturn(Arrays.asList(category1, category2));

        when(request.getRequestURL()).thenReturn(new StringBuffer("/req/url"));
        ApiCallResponse<User> response = mock(ApiCallResponse.class);
        when(apiCallExecutor.call(any(ApiCall.class))).thenReturn(response);
        when(response.getResponseObject()).thenReturn(null);

        //when
        PostAdModel postAdModel = (PostAdModel) controller.showBasePage("1", request).getModel().get("model");

        //then
        assertThat(postAdModel.getCore().getActiveCategory(), equalTo(2L));
        assertThat(postAdModel.getCore().getActiveCategoryHierarchy().size(), equalTo(2));
        assertThat(postAdModel.getCore().getPage(), equalTo(Page.EditAd));
        assertThat(postAdModel.getCore().getPage(), equalTo(Page.EditAd));
    }

    @Test
    public void whenValidVrmDataIsAvailableInSessionThenRedirectToPostAdWithVrmPanels() throws IOException {
        //        Given
        String expectedPayload = "attributes[vrn]=W90MOG&categoryId=1991";
        when(request.getRequestURL()).thenReturn(new StringBuffer("http://www.test.com/my/path"));
        when(request.getQueryString()).thenReturn("a=1&b=2&c=3");
        when(request.getRequestURI()).thenReturn("/my/path");
        when(request.getMethod()).thenReturn("POST");
        when(request.getReader()).thenReturn(new BufferedReader(new StringReader(expectedPayload)));
        when(postAdWorkspace.createAndPersistEditor(eq(1L), anyLong(), eq(java.util.Optional.of("W90MOG")),
                any(RemoteIP.class), any(PermanentCookie.class), any(ThreatMetrixCookie.class), eq(true))).thenReturn(advertEditor);
        RedirectUtils.saveHttpPostPayload(request);

        //        When
        ModelAndView mv = controller.createEditor(1L, 1991L, false, null, null);

        //        Then
        verify(postAdWorkspace).createAndPersistEditor(eq(1L), anyLong(), eq(java.util.Optional.of("W90MOG")), any(RemoteIP.class),
                any(PermanentCookie.class), any(ThreatMetrixCookie.class), eq(true));
        assertThat(getUrl(mv), equalTo("/postad/" + advertEditor.getEditorId()));
    }

    @Test
    public void whenInvalidVrmDataIsInSessionThenRedirectToPostAdWithoutVrmPanels() throws Exception {
        //        Given
        String expectedPayload = "invalidPayload";
        when(request.getRequestURL()).thenReturn(new StringBuffer("http://www.test.com/my/path"));
        when(request.getQueryString()).thenReturn("a=1&b=2&c=3");
        when(request.getRequestURI()).thenReturn("/my/path");
        when(request.getMethod()).thenReturn("POST");
        when(request.getReader()).thenReturn(new BufferedReader(new StringReader(expectedPayload)));
        when(postAdWorkspace.createAndPersistEditor(eq(1L), anyLong(), eq(java.util.Optional.empty()),
                any(RemoteIP.class), any(PermanentCookie.class), any(ThreatMetrixCookie.class), eq(true))).thenReturn(advertEditor);
        RedirectUtils.saveHttpPostPayload(request);

        //        When
        ModelAndView mv = controller.createEditor(1L, 1991L, false, null, null);

        //        Then
        verify(postAdWorkspace).createAndPersistEditor(eq(1L), anyLong(), eq(java.util.Optional.empty()),
                any(RemoteIP.class), any(PermanentCookie.class), any(ThreatMetrixCookie.class), eq(true));
        assertThat(getUrl(mv), equalTo("/postad/" + advertEditor.getEditorId()));
    }

    @Test
    public void whenAutoRepostTrueRedirectToBumpup() throws IOException {
        Boolean autoRepost = true;
        ModelAndView mv = controller.createEditor(1L, 1991L, autoRepost, null, null);

        assertThat(getUrl(mv), equalTo(GtProps.getStr("gumtree.host") + "/postad/bumpup?autoRepost=true&draftId=" + advertEditor.getEditorId()));
    }

    @Test
    public void whenAutoRepostFalseRedirectToPostad() throws IOException {
        Boolean autoRepost = false;
        ModelAndView mv = controller.createEditor(1L, 1991L, autoRepost, null, null);

        assertThat(getUrl(mv), equalTo("/postad/" + advertEditor.getEditorId()));
    }

    private String getUrl(ModelAndView modelAndView) {
        return ((AbstractUrlBasedView) modelAndView.getView()).getUrl();
    }

}
