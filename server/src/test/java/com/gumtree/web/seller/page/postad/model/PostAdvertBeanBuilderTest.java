package com.gumtree.web.seller.page.postad.model;

import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.domain.attribute.ApiAttribute;
import org.hamcrest.CoreMatchers;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

public class PostAdvertBeanBuilderTest {

    private PostAdvertBeanBuilder builder;

    @Before
    public void init() {
        builder = new PostAdvertBeanBuilder();
    }

    @Test
    public void dateAttributeWithNullValueNeverMakesItIntoOutboundMap() {
        builder.attribute(CategoryConstants.Attribute.EVENT_DATE.getName(), null, null);
        assertThat(builder.build().getAttributes().size(), equalTo(0));
    }

    @Test
    public void dateAttributeConvertedToCorrectApiFormatWhenSpecifiedInCorrectFrontEndFormat() {
        builder.attribute(CategoryConstants.Attribute.EVENT_DATE.getName(), "10/12/2005", AttributeType.DATETIME);
        assertThat(builder.build().getAttributes().get(0).getValue(), equalTo("20051210"));
    }

    @Test
    public void dateAttributeConvertedToCorrectApiFormatWhenSpecifiedInCorrectFrontEndFormatAndPaddedWithSpaces() {
        builder.attribute(CategoryConstants.Attribute.EVENT_DATE.getName(), " 10/12/2005 ", AttributeType.DATETIME);
        assertThat(builder.build().getAttributes().get(0).getValue(), equalTo("20051210"));
    }

    @Test
    public void dateAttributeSentAsIsIfNotAValidDate() {
        builder.attribute(CategoryConstants.Attribute.EVENT_DATE.getName(), "invalid, idiot", AttributeType.DATETIME);
        assertThat(builder.build().getAttributes().get(0).getValue(), equalTo("invalid, idiot"));
    }

    @Test
    public void priceAttributeWithNullValueNeverMakesItIntoOutboundMap() {
        builder.attribute(CategoryConstants.Attribute.PRICE.getName(), null, AttributeType.CURRENCY);
        assertThat(builder.build().getAttributes().size(), equalTo(0));
    }

    @Test
    public void priceAttributeConvertedToPenceWhenPlainNumber() {
        builder.attribute(CategoryConstants.Attribute.PRICE.getName(), "100000", AttributeType.CURRENCY);
        assertThat(builder.build().getAttributes().get(0).getValue(), equalTo("10000000"));
    }

    @Test
    public void priceAttributeConvertedToPenceWhenPlainNumberPaddedWithSpaces() {
        builder.attribute(CategoryConstants.Attribute.PRICE.getName(), " 100000 ", AttributeType.CURRENCY);
        assertThat(builder.build().getAttributes().get(0).getValue(), equalTo("10000000"));
    }

    @Test
    public void priceAttributeConvertedToPenceWhenCommasInValue() {
        builder.attribute(CategoryConstants.Attribute.PRICE.getName(), "1,000,00", AttributeType.CURRENCY);
        assertThat(builder.build().getAttributes().get(0).getValue(), equalTo("10000000"));
    }

    @Test
    public void priceAttributeSentAsIsIfCannotBeConvertedToANumber() {
        builder.attribute(CategoryConstants.Attribute.PRICE.getName(), "invalid, idiot", AttributeType.CURRENCY);
        assertThat(builder.build().getAttributes().get(0).getValue(), equalTo("invalid, idiot"));
    }

    @Test
    public void trimsPostcodeWhenNotNull() {
        builder.postcode("   CR2 8BB   ");
        assertThat(builder.build().getPostcode(), equalTo("CR2 8BB"));
    }

    @Test
    public void setsPostcodeAsEmptyStringWhenNull() {
        builder.postcode(null);
        assertThat(builder.build().getPostcode(), equalTo(""));
    }

    @Test
    public void setsLocalAreaIfHasLength() {
        builder.localArea("Some Local Area");
        assertThat(builder.build().getArea(), equalTo("Some Local Area"));
    }

    @Test
    public void setsLocalAreaAsNullIfLengthIs0() {
        builder.localArea("");
        assertThat(builder.build().getArea(), CoreMatchers.nullValue());
    }

    @Test
    public void setsVisibleOnMapAsFalseIfValueIsNull() {
        builder.visibleOnMap(null);
        assertThat(builder.build().isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void setsVisibleOnMapAsFalseIfValueIsFalse() {
        builder.visibleOnMap(false);
        assertThat(builder.build().isVisibleOnMap(), equalTo(false));
    }

    @Test
    public void setsVisibleOnMapAsTrueIfValueIsTrue() {
        builder.visibleOnMap(true);
        assertThat(builder.build().isVisibleOnMap(), equalTo(true));
    }

    @Test
    public void doesNotAddAttributeWhereNameIsNull() {
        builder.attribute(null, "someValue", AttributeType.STRING);
        assertThat(builder.build().getAttributes().size(), equalTo(0));
    }

    @Test
    public void doesNotAddAttributeWhereNameIsEmpty() {
        builder.attribute("", "someValue", AttributeType.STRING);
        assertThat(builder.build().getAttributes().size(), equalTo(0));
    }

    @Test
    public void doesNotAddAttributeWhereValueIsNull() {
        builder.attribute("attribute", null, AttributeType.STRING);
        assertThat(builder.build().getAttributes().size(), equalTo(0));
    }

    @Test
    public void doesNotAddAttributeWhereValueIsEmpty() {
        builder.attribute("attribute", "", AttributeType.STRING);
        assertThat(builder.build().getAttributes().size(), equalTo(0));
    }

    @Test
    public void addsAttributesWhereNameAndValueAreDefined() {
        builder.attribute("attribute1", "value1", AttributeType.STRING);
        builder.attribute("attribute2", "value2", AttributeType.STRING);
        List<ApiAttribute> attributeList = builder.build().getAttributes();
        assertThat(attributeList.size(), equalTo(2));
        assertThat(attributeList.get(0).getName(), equalTo("attribute1"));
        assertThat(attributeList.get(0).getValue(), equalTo("value1"));
        assertThat(attributeList.get(1).getName(), equalTo("attribute2"));
        assertThat(attributeList.get(1).getValue(), equalTo("value2"));
    }

    // TODO: Taking out attribute overrides category behaviour - for now. Causes issues in some categories.
    /*@Test
    public void attributeOverridesCategoryWhenMetadataImpliesItShould() {

        PostAdAttribute attribute1 = new PostAdAttribute.Builder().id(
                "test-attribute1")
                .withValue("value1-1", "Value1-1")
                .withValue("value1-2", "Value1-2", 2L).build();

        PostAdAttribute attribute2 = new PostAdAttribute.Builder().id(
                "test-attribute2").withValue("value2-1", "Value2-1").build();

        PostAdAttribute attribute3 = new PostAdAttribute.Builder().id(
                "test-attribute3").build();

        PostAdvertBeanBuilder builder = new PostAdvertBeanBuilder(Arrays.asList(attribute1, attribute2, attribute3));
        builder.attribute("test-attribute1", "value1-2");

        PostAdvertBean bean = builder.build();

        assertThat(bean.getAttributes().size(), equalTo(1));
        assertThat(bean.getAttributes().get(0).getName(), equalTo("test-attribute1"));
        assertThat(bean.getAttributes().get(0).getValue(), equalTo("value1-2"));
        assertThat(bean.getCategoryId(), equalTo(2L));
    }*/

    // TODO: Taking out attribute overrides category behaviour - for now. Causes issues in some categories.
   /* @Test
    public void whenMultipleAttributesOverridesCategoryThenLastOneInListShouldBeUsed() {
        PostAdAttribute attribute1 = new PostAdAttribute.Builder().id(
                "test-attribute1")
                .withValue("value1-1", "Value1-1")
                .withValue("value1-2", "Value1-2", 2L).build();

        PostAdAttribute attribute2 = new PostAdAttribute.Builder().id(
                "test-attribute2")
                .withValue("value2-1", "Value2-1")
                .withValue("value2-2", "Value2-2", 3L)
                .build();

        PostAdAttribute attribute3 = new PostAdAttribute.Builder().id(
                "test-attribute3").build();

        PostAdvertBeanBuilder builder = new PostAdvertBeanBuilder(Arrays.asList(attribute1, attribute2, attribute3));
        builder.attribute("test-attribute1", "value1-2");
        builder.attribute("test-attribute2", "value2-2");

        PostAdvertBean bean = builder.build();

        assertThat(bean.getAttributes().size(), equalTo(2));
        assertThat(bean.getAttributes().get(0).getName(), equalTo("test-attribute1"));
        assertThat(bean.getAttributes().get(0).getValue(), equalTo("value1-2"));
        assertThat(bean.getAttributes().get(1).getName(), equalTo("test-attribute2"));
        assertThat(bean.getAttributes().get(1).getValue(), equalTo("value2-2"));
        assertThat(bean.getCategoryId(), equalTo(3L));
    }*/

    @Test
    public void cookieValueIsSetWhenSet() {
        builder.cookie("abcd");
        assertThat(builder.build().getCookie(), equalTo("abcd"));
    }

    @Test
    public void ipAddressSetWhenSet() {
        builder.ipAddress("*******");
        assertThat(builder.build().getIp(), equalTo("*******"));
    }

    @Test
    public void contactUrlIsSetWhenSet() {
        builder.contactUrl("http://www.talktome.com");
        assertThat(builder.build().getContactUrl(), equalTo("http://www.talktome.com"));
    }

    @Test
    public void contactNameIsSetWhenSet() {
        builder.contactName("Geoffrey");
        assertThat(builder.build().getContactName(), equalTo("Geoffrey"));
    }

    @Test
    public void youtubeLinkIsSetWhenSet() {
        builder.youtubeLink("http://www.youtube.com/watch?v=dLpqIw0Wjoo");
        assertThat(builder.build().getYoutubeLink(), equalTo("http://www.youtube.com/watch?v=dLpqIw0Wjoo"));
    }

    @Test
    public void websiteUrlIsSetWhenSet() {
        builder.websiteUrl("http://www.mywebsite.com");
        assertThat(builder.build().getWebsiteUrl(), equalTo("http://www.mywebsite.com"));
    }
}
