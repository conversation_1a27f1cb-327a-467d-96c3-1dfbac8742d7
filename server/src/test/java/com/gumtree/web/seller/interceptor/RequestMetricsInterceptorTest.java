package com.gumtree.web.seller.interceptor;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class RequestMetricsInterceptorTest {
    public static final String URI_NAME = "MetricsLoggingRequestAttributeURI";
    public static final String METHOD_NAME = "MetricsLoggingRequestAttributeMethod";

    private RequestMetricsInterceptor interceptor;

    private HttpServletRequest request;

    private HttpServletResponse response;

    @Before
    public void setUp() {
        interceptor = new RequestMetricsInterceptor();
        request = mock(HttpServletRequest.class);
        response = mock(HttpServletResponse.class);

        // these should not be used for metrics
        when(request.getRequestURI()).thenReturn("/should-not-be-used/123");
        when(request.getRequestURL()).thenReturn(new StringBuffer("/should-not-be-used/123?q=test&page=1"));
    }

    @Test
    public void preHandle_shouldAddMetricsAttributes_methodLevelMapping() throws NoSuchMethodException {
        //given
        Method testMethod = MethodOnlyTestController.class.getMethod("userSummary");
        HandlerMethod handlerMethod = new HandlerMethod(new MethodOnlyTestController(), testMethod);
        when(request.getMethod()).thenReturn("GET");

        //when
        boolean result = interceptor.preHandle(request, response, handlerMethod);

        //then
        assertTrue(result);
        verify(request).setAttribute(URI_NAME, "/user/{id}/summary");
        verify(request).setAttribute(METHOD_NAME, "GET");
    }

    @Test
    public void preHandle_shouldAddMetricsAttributes_controllerLevelMapping() throws NoSuchMethodException {
        //given
        Method testMethod = ClassOnlyTestController.class.getMethod("getNumberOfUsers");
        HandlerMethod handlerMethod = new HandlerMethod(new ClassOnlyTestController(), testMethod);
        when(request.getMethod()).thenReturn("GET");

        //when
        boolean result = interceptor.preHandle(request, response, handlerMethod);

        //then
        assertTrue(result);
        verify(request).setAttribute(URI_NAME, "/users");
        verify(request).setAttribute(METHOD_NAME, "GET");
    }

    @Test
    public void preHandle_shouldAddMetricsAttributes_controllerAndMethodLevelMapping() throws NoSuchMethodException {
        //given
        Method testMethod = ClassAndMethodTestController.class.getMethod("deleteAny");
        HandlerMethod handlerMethod = new HandlerMethod(new ClassAndMethodTestController(), testMethod);
        when(request.getMethod()).thenReturn("DELETE");

        //when
        boolean result = interceptor.preHandle(request, response, handlerMethod);

        //then
        assertTrue(result);
        verify(request).setAttribute(URI_NAME, "/user/any");
        verify(request).setAttribute(METHOD_NAME, "DELETE");
    }

    @Test
    public void preHandle_shouldAddMetricsAttributes_defaultMapping() throws NoSuchMethodException {
        //given
        Method testMethod = DefaultTestController.class.getMethod("defaultEndpoint");
        HandlerMethod handlerMethod = new HandlerMethod(new DefaultTestController(), testMethod);
        when(request.getMethod()).thenReturn("GET");

        //when
        boolean result = interceptor.preHandle(request, response, handlerMethod);

        //then instead of "" metric contains "default" as uri which is more readable and easier to search for in metrics
        assertTrue(result);
        verify(request).setAttribute(URI_NAME, "default");
        verify(request).setAttribute(METHOD_NAME, "GET");
    }

    @Test
    public void preHandle_shouldNotAddMetricsAttributes_noRequestMappingMethod() throws NoSuchMethodException {
        //given
        Method testMethod = NoRequestMappingTestController.class.getMethod("display");
        HandlerMethod handlerMethod = new HandlerMethod(new NoRequestMappingTestController(), testMethod);
        when(request.getMethod()).thenReturn("GET");

        //when
        boolean result = interceptor.preHandle(request, response, handlerMethod);

        //then
        assertTrue(result);
        verify(request, never()).setAttribute(Mockito.anyString(), Mockito.anyObject());
    }

    @Test
    public void preHandle_shouldNotAddMetricsAttributes_handlerNotHandlerMethod() {
        //given
        Object handler = new Object();

        //when
        boolean result = interceptor.preHandle(request, response, handler);

        //then
        assertTrue(result);
        verify(request, never()).setAttribute(Mockito.anyString(), Mockito.anyObject());
    }

    private static class MethodOnlyTestController {
        @RequestMapping("/user/{id}/summary")
        public void userSummary() {
        }
    }

    @RequestMapping("/users")
    private static class ClassOnlyTestController {
        @RequestMapping(method = RequestMethod.GET)
        public void getNumberOfUsers() {
        }
    }

    @RequestMapping("/user")
    private static class ClassAndMethodTestController {

        @RequestMapping(value = "/any", method = RequestMethod.DELETE)
        public void deleteAny() {
        }
    }

    private static class DefaultTestController {

        @RequestMapping(method = RequestMethod.GET)
        public void defaultEndpoint() {
        }
    }

    private static class NoRequestMappingTestController {

        public void display() {
        }
    }
}
