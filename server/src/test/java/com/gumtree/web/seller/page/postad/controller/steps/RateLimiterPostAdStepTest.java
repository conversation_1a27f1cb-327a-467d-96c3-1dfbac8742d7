package com.gumtree.web.seller.page.postad.controller.steps;

import com.gumtree.api.Account;
import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdFormPanel;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.storage.ratelimit.RateCheckResult;
import com.gumtree.web.storage.ratelimit.RateLimiter;
import com.gumtree.zeno.core.service.ZenoService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Collections;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RateLimiterPostAdStepTest {
    private static final Long ACC_ID = 1L;
    private static final String USERNAME = "<EMAIL>";

    @InjectMocks private RateLimiterPostAdStep rateLimiterStep;

    @Mock private BushfireApi bushfireApi;
    @Mock private ZenoService zenoService;
    @Mock private RateLimiter rateLimiter;
    @Mock private UserSession authenticatedUserSession;
    @Mock private AdvertEditor editor;
    @Mock private AccountApi accountApi;
    @Mock private UserApi userApi;

    private Account account;
    private PostAdFormBean postAdFormBean;
    private PostAdDetail postAdDetail;
    private PostAdSubmitModel.Builder modelBuilder;

    @Before
    public void beforeEach() {
        postAdDetail = new PostAdDetail();
        postAdFormBean = new PostAdFormBean();
        modelBuilder = PostAdSubmitModel.builder();
        account = new Account();
        account.setId(ACC_ID);

        // api
        when(bushfireApi.accountApi()).thenReturn(accountApi);
        when(bushfireApi.userApi()).thenReturn(userApi);
        when(accountApi.getAccount(ACC_ID)).thenReturn(account);

        // editor
        when(editor.isCreateMode()).thenReturn(true);
        when(editor.getEditorId()).thenReturn("editor-id");
        when(editor.getPostAdFormBean()).thenReturn(postAdFormBean);
        when(editor.getAdvertDetail()).thenReturn(postAdDetail);

        // session
        when(authenticatedUserSession.getSelectedAccountId()).thenReturn(ACC_ID);
        when(authenticatedUserSession.getUsername()).thenReturn(USERNAME);
    }

    @Test
    public void shouldBlockPostAdOfLoggedInUserIfRateIsExceeded() {
        // given
        when(rateLimiter.checkRate(USERNAME)).thenReturn(new RateCheckResult(1, 1));

        // when
        boolean shouldContinue = rateLimiterStep.execute(modelBuilder, editor);

        // then
        assertThat(shouldContinue).isFalse();
    }

    @Test
    public void shouldContinueToNextStepIfRateIsNotExceeded() {
        // given
        when(rateLimiter.checkRate(USERNAME)).thenReturn(RateCheckResult.EMPTY);

        // when
        boolean shouldContinue = rateLimiterStep.execute(modelBuilder, editor);

        // then
        assertThat(shouldContinue).isTrue();
    }

    @Test
    public void shouldContinueToNextStepIfProAccount() throws Exception {
        // given
        account.setPro(true);

        // when
        boolean shouldContinue = rateLimiterStep.execute(modelBuilder, editor);

        // then
        assertThat(shouldContinue).isTrue();
    }
    
    @Test
    public void shouldBlockPostAdOfNonLoggedInUserIfRateIsExceeded() throws Exception {
        // given
        account.setId(null);
        when(authenticatedUserSession.getUsername()).thenReturn("");
        postAdFormBean.setEmailAddress(USERNAME);
        when(rateLimiter.checkRate(USERNAME)).thenReturn(new RateCheckResult(1, 1));

        // when
        boolean shouldContinue = rateLimiterStep.execute(modelBuilder, editor);

        // then
        assertThat(shouldContinue).isFalse();
    }

    @Test
    public void shouldBlockPostAdOfNonLoggedInUserWhoHasAlreadyRegistered() throws Exception {
        // given
        when(authenticatedUserSession.getUsername()).thenReturn("");
        postAdFormBean.setEmailAddress(USERNAME);
        when(rateLimiter.checkRate(USERNAME)).thenReturn(RateCheckResult.EMPTY);

        User user = new User();
        when(userApi.getUser(USERNAME)).thenReturn(user);

        // when
        boolean shouldContinue = rateLimiterStep.execute(modelBuilder, editor);

        // then
        assertThat(shouldContinue).isFalse();
    }

    @Test
    public void shouldNotBlockPostAdOfNonLoggedInUserWhoHasNotRegisteredBefore() throws Exception {
        // given
        when(authenticatedUserSession.getUsername()).thenReturn("");
        postAdFormBean.setEmailAddress(USERNAME);
        when(rateLimiter.checkRate(USERNAME)).thenReturn(RateCheckResult.EMPTY);

        when(userApi.getUser(USERNAME)).thenThrow(new RuntimeException("user not found"));

        // when
        boolean shouldContinue = rateLimiterStep.execute(modelBuilder, editor);

        // then
        assertThat(shouldContinue).isTrue();
    }

    @Test
    public void shouldNotDisplayVehicleSpecificationIfAlreadyRegistered() throws Exception {
        // given
        when(authenticatedUserSession.getUsername()).thenReturn("");
        postAdFormBean.setEmailAddress(USERNAME);
        when(rateLimiter.checkRate(USERNAME)).thenReturn(RateCheckResult.EMPTY);
        PostAdSubmitModel.Builder builder = modelBuilder.addPanels(Collections.singletonList(PostAdFormPanel.VEHICLE_SPECIFICATIONS));

        User user = new User();
        when(userApi.getUser(USERNAME)).thenReturn(user);

        // when
        boolean shouldContinue = rateLimiterStep.execute(builder, editor);

        // then
        assertThat(shouldContinue).isFalse();
        assertThat(builder.build().getPanels().contains(PostAdFormPanel.VEHICLE_SPECIFICATIONS.getId())).isFalse();
    }
}
