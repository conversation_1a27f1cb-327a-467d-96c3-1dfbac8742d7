package com.gumtree.web.seller.converter;

import com.google.common.collect.Lists;
import com.gumtree.api.Ad;
import com.gumtree.api.PriceFrequency;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.domain.newattribute.internal.DefaultAttributeService;

import com.gumtree.service.category.CategoryService;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.gumtree.liveadsearch.model.Location;
import com.gumtree.liveadsearch.model.FlatAd;
import com.gumtree.liveadsearch.model.Category;
import static com.google.common.base.Optional.of;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class FlatAdToApiAdConverterImplTest{
    private FlatAdConverterUtils flatAdConverterUtils;
    private FlatAdToApiAdConverterImpl flatAdToApiAdConverter;
    private DefaultAttributeService attributeService;
    private CategoryService categoryService;
    private static final String PRICE_ATTRIBUTE = "2000";

    @Before
    public void init() throws IOException {
        initAttributeServiceMock();
        flatAdToApiAdConverter = new FlatAdToApiAdConverterImpl(attributeService, categoryService);
        flatAdConverterUtils = new FlatAdConverterUtils(attributeService, categoryService);
    }


    @Test
    public void shouldConvertBasicField(){
        FlatAd flatAd = flatAdBuilder();
        Ad advert = flatAdToApiAdConverter.convert(flatAd);

        assertBasicField(advert, flatAd);
    }

    @Test
    public void shouldConvertPriceField(){
        FlatAd flatAd = flatAdBuilder().attribute(setAttributes("price",1250L));
        Ad advert = flatAdToApiAdConverter.convert(flatAd);
        assertBasicField(advert, flatAd);
        assertThat(advert.getPrice().getAmount(), equalTo(1250L));
    }

    @Test
    public void shouldConvertPriceFrequencyWeeklyField(){
        FlatAd flatAd = flatAdBuilder().attribute(setAttributes("price_frequency","per_week"));
        Ad advert = flatAdToApiAdConverter.convert(flatAd);
        assertBasicField(advert, flatAd);
        assertThat(advert.getPriceFrequency(), equalTo(PriceFrequency.WEEKLY));
    }

    @Test
    public void shouldConvertPriceFrequencyMonthlyField(){
        FlatAd flatAd = flatAdBuilder().attribute(setAttributes("price_frequency","per_month"));
        Ad advert = flatAdToApiAdConverter.convert(flatAd);
        assertBasicField(advert, flatAd);
        assertThat(advert.getPriceFrequency(), equalTo(PriceFrequency.MONTHLY));
    }

    private void assertBasicField(Ad advert, FlatAd flatAd) {
        assertThat(advert.getId(), equalTo(flatAd.getId()));
        assertThat(advert.getTitle(), equalTo(flatAd.getTitle()));
        assertThat(advert.getDescription(), equalTo(flatAd.getDescription()));
        assertThat(advert.getPostcode(), equalTo(flatAd.getPostcode()));
        assertThat(advert.getLocationId(), equalTo(flatAdConverterUtils.getPrimaryLocation(flatAd).getId()));
        assertThat(advert.getLocationString(), equalTo(flatAdConverterUtils.getPrimaryLocation(flatAd).getDisplayName()));
        assertThat(advert.getRepliesEmail(), equalTo(flatAd.getContactEmail()));
        assertThat(advert.getCategoryId(), equalTo(flatAdConverterUtils.getPrimaryCategory(flatAd).getId()));
        assertThat(advert.getLocations().length, equalTo(3));
    }

    private FlatAd flatAdBuilder() {
        return new FlatAd()
                .id(123L)
                .title("Test title")
                .description("Test description")
                .postcode("AB23CD")
                .contactEmail("<EMAIL>")
                .status("LIVE")
                .categories(Collections.singletonList(new Category().primary(true).id(1L).name("Cars")))
                .locations(
                        Arrays.asList(
                        locationBuilder(1L, "UK", "United Kingdom", false),
                        locationBuilder(2L, "Manchester", "Manchester", false),
                        locationBuilder(3L, "Ardwick", "Ardwick", true))
                );
    }

    private Location locationBuilder(Long id, String name, String displayName, Boolean isPrimary) {
        return new Location().id(id).name(name).displayName(displayName).primary(isPrimary);
    }

    private void initAttributeServiceMock() throws IOException {
        com.gumtree.api.category.domain.Category category = getCategory();
        categoryService = mock(CategoryService.class);
        when(categoryService.getById(any(Long.class))).thenReturn(of(category));
        when(categoryService.attributeValueDisplayLabel(any(Long.class), eq("seller_type"), eq("trade"))).thenReturn(of("Trader"));
        when(categoryService.attributeValueDisplayLabel(any(Long.class), eq("price"), any(String.class))).thenReturn(of(PRICE_ATTRIBUTE));

        attributeService = new DefaultAttributeService(categoryService);
    }

    private com.gumtree.api.category.domain.Category getCategory() {
        com.gumtree.api.category.domain.Category category = new com.gumtree.api.category.domain.Category();
        category.setName("flats");
        AttributeMetadata priceMetadata = getAttributeMetadata("price", AttributeType.CURRENCY);
        AttributeMetadata priceFrequencyMetadata = getAttributeMetadata("price_frequency", AttributeType.STRING);
        category.setAttributeMetadata(Lists.newArrayList(priceMetadata, priceFrequencyMetadata));
        return category;
    }

    private AttributeMetadata getAttributeMetadata(String name, AttributeType type) {
        AttributeMetadata metadata = new AttributeMetadata();
        metadata.setName(name);
        metadata.setType(type);
        return metadata;
    }

    public Map<String, Object> setAttributes(String key, Object value){
        Map<String, Object> attributes=new HashMap<>();
        attributes.put(key,value);
        return attributes;
    }
}
