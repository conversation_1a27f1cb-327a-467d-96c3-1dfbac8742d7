package com.gumtree.web.seller.page.logout.controller;

import com.google.common.base.Optional;
import com.gumtree.api.User;
import com.gumtree.web.security.exception.UnauthorizedUserException;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.logout.model.LogoutModel;
import com.gumtree.web.seller.security.apiauthentication.CSRFTokenService;
import com.gumtree.web.seller.security.apiauthentication.TimeSensitiveResponse;
import com.gumtree.web.seller.service.user.logout.UserLogoutService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

import static com.gumtree.metrics.AuthenticationMetrics.ACTION;
import static com.gumtree.metrics.AuthenticationMetrics.Action;
import static com.gumtree.metrics.AuthenticationMetrics.NAME;
import static com.gumtree.metrics.AuthenticationMetrics.STATUS;
import static com.gumtree.metrics.AuthenticationMetrics.Status;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LogoutControllerTest extends BaseSellerControllerTest {

    private LogoutController logoutController;

    @Mock
    private UserLogoutService userLogoutService;

    @Mock
    private CSRFTokenService csrfTokenService;

    private MeterRegistry meterRegistry;

    private static final String TOKEN = "token";

    @Before
    public void init() {
        meterRegistry = new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());

        logoutController = new LogoutController(
                cookieResolver,
                categoryModel,
                userSessionService,
                userLogoutService,
                meterRegistry,
                csrfTokenService);

        autowireAbExperimentsService(logoutController);
    }

    @Test
    public void logsOutUserWithoutCsrfTokenForBackwardCompatibility() {
        ModelAndView modelAndView = logoutController.logout(request, response);
        verify(userLogoutService).logoutUser(request, response);
        verifyNoMeaningfulInteractions(request);
        assertThat(((LogoutModel) modelAndView.getModelMap().get("model")).getCore().getUser().isUserLoggedIn(), is(false));

        assertMetricValue(1, Action.LOGOUT, Status.SUCCESS);
    }

    private void assertMetricValue(int expectedValue, Action action, Status status) {
        Counter counter = meterRegistry.counter(NAME, ACTION, action.toString(), STATUS, status.toString());

        assertEquals(expectedValue, counter.count(), 0.1D);
    }

    @Test
    public void logsOutUserWhenCSRFTokenIsPresentAndIsValid() throws Exception {
        // given
        User user = User.builder().withId(1L).build();
        when(csrfTokenService.verifyCsrfTokenForEmail(anyString(), anyString())).thenReturn(csrfToken(true));
        when(userSessionService.getUser()).thenReturn(Optional.of(user));
        when(csrfTokenService.csrfTokenForEmail(anyString())).thenReturn(TOKEN);

        // when
        ModelAndView modelAndView = logoutController.logout(TOKEN, request, response);

        // then
        verify(userLogoutService).logoutUser(request, response);
        verifyNoMeaningfulInteractions(request);
        assertThat(((LogoutModel) modelAndView.getModelMap().get("model")).getCore().getUser().isUserLoggedIn(), is(false));
        assertMetricValue(Status.SUCCESS);
    }

    @Test(expected = UnauthorizedUserException.class)
    public void logsOutUserFailsWhenCSRFTokenIsPresentAndIsInValid() throws Exception {
        // given
        User user = User.builder().withId(1L).build();
        when(csrfTokenService.verifyCsrfTokenForEmail(anyString(), anyString())).thenReturn(csrfToken(false));
        when(userSessionService.getUser()).thenReturn(Optional.of(user));
        when(csrfTokenService.csrfTokenForEmail(anyString())).thenReturn(TOKEN);

        // expects
        logoutController.logout(TOKEN, request, response);
    }

    @Test
    public void logsOutUserFailsWhenUserNotLogged() throws Exception {
        // given
        when(csrfTokenService.csrfTokenForEmail(anyString())).thenReturn(TOKEN);

        // when
        ModelAndView modelAndView = logoutController.logout(TOKEN, request, response);

        // then
        verifyZeroInteractions(userLogoutService);
        verifyNoMeaningfulInteractions(request);
        assertThat(((LogoutModel) modelAndView.getModelMap().get("model")).getCore().getUser().isUserLoggedIn(), is(false));
        assertMetricValue(Status.FAILURE);
    }

    private void assertMetricValue(Status status) {
        Counter counter = meterRegistry.counter(NAME, ACTION, Action.LOGOUT.toString(), STATUS, status.toString());
        assertEquals(1, counter.count(), 0.1D);
    }

    private static void verifyNoMeaningfulInteractions(HttpServletRequest request) {
        verify(request).getRequestURI();
        verify(request).getQueryString();
        verify(request).getHeader("user-agent");
        verifyNoMoreInteractions(request);
    }

    private TimeSensitiveResponse<Object> csrfToken(boolean isValid) {
        return new TimeSensitiveResponse.Builder<>()
                .withValid(isValid)
                .build();
    }

}
