package com.gumtree.web.seller.page.manageads;

import com.gumtree.api.Account;
import com.gumtree.api.User;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.builder.AccountBuilder;
import com.gumtree.web.seller.page.manageads.model.ManageAdsAccountSelectionFormBean;
import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.ui.ExtendedModelMap;
import org.springframework.ui.Model;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;

/**
 * Specification of ManageAdsHelperImpl
 */
public class ManageAdsHelperImplTest {

    @Mock
    private UrlScheme urlScheme;

    @InjectMocks
    private ManageAdsHelperImpl manageAdsHelper;

    @Before
    public void init() {
        Properties properties = new Properties();
        properties.setProperty("gumtree.salesforce.url", "");
        ConfigurationManager.loadProperties(properties);
        initMocks(this);
        buildUrlScheme();
    }

    @Test
    public void shouldPopulateManageAdsUrlsToMapModel() {
        //given
        SimpleLink manageAdsLink = new SimpleLink("Manage my ads",
                urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS));
        SimpleLink myDetailsLink = new SimpleLink("My details",
                urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ACCOUNT));
        SimpleLink currentPageLink = new SimpleLink("current page", "http://currentpage");

        HashMap<String, Object> model = new HashMap<>();

        //when
        manageAdsHelper.addManageAdsUrls(model, new SimpleLink("current page", "http://currentpage"));

        //then
        assertThat(model.get("manageAdsUrl").equals(manageAdsLink), is(true));
        assertThat(model.get("myDetailsUrl").equals(myDetailsLink), is(true));
        assertThat(model.get("currentPageUrl").equals(currentPageLink), is(true));

    }

    @Test
    public void shouldPopulateManageAdsUrlsToViewModel() {
        //given
        SimpleLink manageAdsLink = new SimpleLink("Manage my ads",
                urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS));
        SimpleLink myDetailsLink = new SimpleLink("My details",
                urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ACCOUNT));
        SimpleLink packageUsageLink = new SimpleLink("Package usage history",
                urlScheme.urlFor(Actions.BUSHFIRE_PACKAGE_USAGE));

        SimpleLink currentPageLink = new SimpleLink("current page", "http://currentpage");

        Model model = new ExtendedModelMap();

        //when
        manageAdsHelper.addManageAdsUrls(model, new SimpleLink("current page", "http://currentpage"));

        //then
        assertThat(model.asMap().get("manageAdsUrl").equals(manageAdsLink), is(true));
        assertThat(model.asMap().get("myDetailsUrl").equals(myDetailsLink), is(true));
        assertThat(model.asMap().get("packageUsageUrl").equals(packageUsageLink), is(true));
        assertThat(model.asMap().get("currentPageUrl").equals(currentPageLink), is(true));

    }

    @Test
    public void shouldCreateAccountSelectionFormFromUserSession() {
        //given
        Account a1 = new AccountBuilder().id(1L).name("Account 1").build();
        Account a2 = new AccountBuilder().id(2L).name("Account 2").proAccount().build();
        Account a3 = new AccountBuilder().id(3L).name("Account 3").proAccount().build();
        List<Account> accounts = Arrays.asList(a3, a1, a2);

        User user = aUser("<EMAIL>", "Barney");

        UserSession userSession = mock(UserSession.class);
        when(userSession.getSelectableAccounts()).thenReturn(accounts);
        when(userSession.getUser()).thenReturn(user);
        when(userSession.getSelectedAccountId()).thenReturn(2L);

        //when
        ManageAdsAccountSelectionFormBean form = manageAdsHelper.getAccountSelectionForm(userSession);

        //then
        assertThat(form.getAccounts().size(), is(3));
        assertThat(form.getAccounts().get(0).getValue(), is("2"));
        assertThat(form.getAccounts().get(0).getDisplayValue(), is("Account 2 (2)"));
        assertThat(form.getAccounts().get(1).getValue(), is("1"));
        assertThat(form.getAccounts().get(1).getDisplayValue(), is("Barney's account"));
        assertThat(form.getAccounts().get(2).getValue(), is("3"));
        assertThat(form.getAccounts().get(2).getDisplayValue(), is("Account 3 (3)"));
    }

    private User aUser(String email, String name) {
        User user = new User();
        user.setEmail(email);
        user.setFirstName(name);
        return user;
    }

    /**
     * There is no simple UrlScheme implementation so we have to build one
     */
    private void buildUrlScheme() {
        when(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS)).thenReturn(Actions.BUSHFIRE_MANAGE_ADS.getUrl());
        when(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ACCOUNT)).thenReturn(Actions.BUSHFIRE_MANAGE_ACCOUNT.getUrl());
        when(urlScheme.urlFor(Actions.BUSHFIRE_PACKAGE_USAGE)).thenReturn(
                Actions.BUSHFIRE_PACKAGE_USAGE.getUrl());
        when(urlScheme.urlFor(Actions.SAVED_SEARCHES)).thenReturn(
                Actions.SAVED_SEARCHES.getUrl());
    }
}
