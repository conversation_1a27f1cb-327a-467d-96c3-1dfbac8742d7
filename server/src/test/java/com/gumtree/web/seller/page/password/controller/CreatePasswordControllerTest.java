package com.gumtree.web.seller.page.password.controller;

import com.gumtree.api.User;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.util.model.Actions;
import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.password.api.CreatePasswordApiCall;
import com.gumtree.web.seller.page.password.api.ValidateActivationKeyApiCall;
import com.gumtree.web.seller.page.password.model.UpdatePasswordModel;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.subject.Subject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.ui.Model;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.io.IOException;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.core.IsInstanceOf.instanceOf;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.eq;
import static org.mockito.Matchers.isA;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CreatePasswordControllerTest extends BaseSellerControllerTest {

    private static final String USER_NAME = "<EMAIL>";
    private static final String ACTIVATION_KEY = "activationKey";
    private static final String CREATE_PASSWORD_URL = "createPasswordUrl";
    private static final String MANAGE_ADS_URL = "manageAdsUrl";

    private CreatePasswordController controller;
    private ApiCallResponse<Void> validateActivationKeyApiCallResponse = mock(ApiCallResponse.class);
    private ApiCallResponse<User> createPasswordApiCallResponse = mock(ApiCallResponse.class);
    private Model model = mock(Model.class);
    private ModelAndView controllerActionResult;
    private Subject subject = mock(Subject.class);

    @Before
    public void init() {
        when(urlScheme.urlFor(Actions.BUSHFIRE_CREATE_PASSWORD)).thenReturn(CREATE_PASSWORD_URL);
        when(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS)).thenReturn(MANAGE_ADS_URL);
        controller = new CreatePasswordController(cookieResolver, categoryModel, apiCallExecutor, messageResolver,
                urlScheme, mock(UserSessionService.class));
        autowireAbExperimentsService(controller);
    }

    @Test
    public void shouldShowPageForValidKey() throws IOException {
        // given
        givenActivationKeyIsValid();

        // when
        controllerActionResult = controller.showPage(USER_NAME, ACTIVATION_KEY, request);

        // then
        assertThatCreatePasswordPageDisplayed();
        assertThatFormActionSet();
    }

    @Test
    public void shouldShowPageForInvalidKeyOrUserId() throws IOException {
        // given
        givenActivationKeyNotValid();

        // when
        controllerActionResult = controller.showPage(USER_NAME, ACTIVATION_KEY, request);

        // then
        assertThatCreatePasswordPageDisplayed();
        assertThatErrorPageIsDisplayed();
    }

    @Test
    public void shouldReturnErrorWhenPasswordsNotValid() throws IOException {
        // given
        givenActivationKeyIsValid();
        givenPasswordsFailValidation();

        // when
        controllerActionResult = controller.createPassword(USER_NAME, ACTIVATION_KEY, "", "", model, subject, request);

        // then
        assertThatCreatePasswordPageDisplayed();
        assertThatErrorsPopulatedWithResponse();
    }

    @Test
    public void shouldSuccessfullySetPassword() throws IOException {
        // given
        givenActivationKeyIsValid();
        givenPasswordsPassValidation();

        // when
        controllerActionResult = controller.createPassword(USER_NAME, ACTIVATION_KEY, "password123", "password123", model, subject, request);

        // then
        assertThatManageAdsPageIsDisplayed();
    }

    private void givenActivationKeyNotValid() {
        when(apiCallExecutor.call(isA(ValidateActivationKeyApiCall.class))).thenReturn(
                validateActivationKeyApiCallResponse);
        when(validateActivationKeyApiCallResponse.isErrorResponse()).thenReturn(true);
    }

    private void givenPasswordsFailValidation() {
        when(apiCallExecutor.call(isA(CreatePasswordApiCall.class))).thenReturn(createPasswordApiCallResponse);
        when(createPasswordApiCallResponse.isErrorResponse()).thenReturn(true);
        when(createPasswordApiCallResponse.getErrorCode()).thenReturn(ApiErrorCode.VALIDATION_FAILED);
    }

    private void givenPasswordsPassValidation() {
        when(apiCallExecutor.call(isA(CreatePasswordApiCall.class))).thenReturn(createPasswordApiCallResponse);
        when(createPasswordApiCallResponse.isErrorResponse()).thenReturn(false);
    }

    private void givenActivationKeyIsValid() {
        when(apiCallExecutor.call(isA(ValidateActivationKeyApiCall.class))).thenReturn(
                validateActivationKeyApiCallResponse);
        when(validateActivationKeyApiCallResponse.isErrorResponse()).thenReturn(false);
    }

    private void assertThatFormActionSet() {
        UpdatePasswordModel model = getUpdatePasswordModel();
        final String formAction = model.getFormAction();
        assertThat(formAction, equalTo("createPasswordUrl?id=<EMAIL>&key=activationKey"));
    }

    private void assertThatCreatePasswordPageDisplayed() throws IOException {
        assertThat(controllerActionResult.getViewName(), equalTo(Page.CreatePassword.getTemplateName()));
    }

    private void assertThatErrorPageIsDisplayed() throws IOException {
        UpdatePasswordModel model = getUpdatePasswordModel();
        assertTrue(model.isErrorPage());
    }

    private void assertThatManageAdsPageIsDisplayed() throws IOException {
        verify(subject).login(isA(AuthenticationToken.class));
        assertThat(controllerActionResult.getView(), instanceOf(RedirectView.class));
        String url = ((RedirectView) controllerActionResult.getView()).getUrl();
        assertThat(url, equalTo(MANAGE_ADS_URL));
    }

    private void assertThatErrorsPopulatedWithResponse() {
        verify(model).addAttribute(eq("errors"), isA(ReportableErrorsMessageResolvingErrorSource.class));
        verify(createPasswordApiCallResponse).report(isA(ReportableErrorsMessageResolvingErrorSource.class));
    }

    private UpdatePasswordModel getUpdatePasswordModel() {
        return (UpdatePasswordModel) controllerActionResult.getModel().get(CommonModel.MODEL_KEY);
    }
}

