package com.gumtree.web.seller.page.postad.controller.steps;

import com.google.common.collect.Lists;
import com.gumtree.api.Image;
import com.gumtree.service.category.CategoryService;
import com.gumtree.web.seller.page.postad.converter.PostAdImageConverter;
import com.gumtree.web.seller.page.postad.model.AdvertEditor;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import com.gumtree.web.seller.page.postad.model.PostAdSubmitModel;
import com.gumtree.web.seller.page.postad.service.PostAdFormDescriptionHintService;
import com.gumtree.web.seller.service.PostAdWorkspace;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ImagesPostAdStepTest {

    public static final String EDITOR_ID = "editor-id";

    @InjectMocks
    private ImagesPostAdStep imagesPostAdStep;
    @Mock
    private PostAdWorkspace postAdWorkspace;
    @Mock
    private PostAdImageConverter postAdImageConverter;
    @Mock
    private PostAdFormDescriptionHintService descriptionHintService;
    @Mock
    private CategoryService categoryService;
    @Mock
    private AdvertEditor advertEditor;

    private PostAdFormBean postAdFormBean;

    Long[] categoriesWithImageRequired = {2526L,10442L};
    @Before
    public void beforeEach() {
        postAdFormBean = new PostAdFormBean();
        // editor
        when(advertEditor.getEditorId()).thenReturn(EDITOR_ID);
        when(advertEditor.getPostAdFormBean()).thenReturn(postAdFormBean);
        long categoryId = 9311L;
        when(advertEditor.getCategoryId()).thenReturn(categoryId);
        ReflectionTestUtils.setField(imagesPostAdStep,"categoriesWithImageRequired",categoriesWithImageRequired);
        when(categoryService.getLevelHierarchy(categoryId)).thenReturn(Collections.emptyMap());
    }

    @Test
    public void shouldOrderImages() {

        PostAdSubmitModel.Builder model = PostAdSubmitModel.builder();
        long imageIdA = 1L;
        long imageIdB = 2L;
        long imageIdC = 3L;
        List<Long> imageIds = Lists.newArrayList(imageIdA, imageIdB, imageIdC);
        Image imageA = new Image();
        imageA.setId(imageIdA);
        Image imageB = new Image();
        imageB.setId(imageIdB);
        Image imageC = new Image();
        imageC.setId(imageIdC);
        List<Image> images = Lists.newArrayList(imageC,imageA,imageB);

        postAdFormBean.setImageIds(imageIds);

        when(advertEditor.getImages()).thenReturn(images);

        imagesPostAdStep.execute(model, advertEditor);

        verify(postAdWorkspace).updateEditor(EDITOR_ID, advertEditor);
        assertThat(postAdFormBean.getImageIds().size()).isEqualTo(3);
        assertThat(postAdFormBean.getImageIds().get(0)).isEqualTo(imageIdA);
        assertThat(postAdFormBean.getImageIds().get(1)).isEqualTo(imageIdB);
        assertThat(postAdFormBean.getImageIds().get(2)).isEqualTo(imageIdC);
    }


}
