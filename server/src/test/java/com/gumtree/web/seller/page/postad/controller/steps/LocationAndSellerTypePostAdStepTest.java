package com.gumtree.web.seller.page.postad.controller.steps;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.Account;
import com.gumtree.api.SellerType;
import com.gumtree.api.ValueResult;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AccountApi;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.location.entity.LocationEntity;
import com.gumtree.mobile.test.Fixtures;
import com.gumtree.web.common.sapi.AdvertAttributeNames;
import com.gumtree.service.location.LocationService;
import com.gumtree.web.seller.page.postad.controller.MandatorySellerTypePanel;
import com.gumtree.web.seller.page.postad.controller.PanelFactory;
import com.gumtree.web.seller.page.postad.model.*;
import com.gumtree.web.seller.page.postad.model.location.BrowseLocation;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;
import com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState;
import com.gumtree.web.seller.service.pricing.PricingService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Map;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.SELLER_TYPE;
import static com.gumtree.common.util.messages.FSBOMessages.FPAD_WHY_EXPLAIN;
import static com.gumtree.common.util.messages.FSBOMessages.FPAD_WHY_SELECTED;
import static com.gumtree.web.seller.page.postad.model.PostAdFormPanel.CATEGORY;
import static com.gumtree.web.seller.page.postad.model.PostAdFormPanel.CONTINUE;
import static com.gumtree.web.seller.page.postad.model.PostAdFormPanel.LOCATION;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LocationAndSellerTypePostAdStepTest {
    private static final Long CAT_ID = 1L;
    private static final Long ACC_ID = 1L;

    private static final Comparator<BrowseLocation> LOCATION_COMPARATOR = Comparator.comparing(BrowseLocation::getId);

    @InjectMocks
    private LocationAndSellerTypePostAdStep locationAndSellerTypePostAdStep;

    @Mock
    private BushfireApi bushfireApi;
    @Mock
    private AccountApi accountApi;
    @Mock
    private CategoryModel categoryModel;
    @Mock
    private LocationService locationService;
    @Mock
    private AdvertEditor editor;
    @Mock
    private PanelFactory panelFactory;
    @Mock
    private PricingService pricingService;

    private Account account;
    private PostAdFormBean postAdFormBean;
    private PostAdDetail postAdDetail;
    private PostAdSubmitModel.Builder modelBuilder;

    private static final Location UK = newLocation(********, "UK");
    private static final Location LONDON = newLocation(********, "London");
    private static final Location SOUTH_WEST = newLocation(375, "South West London");
    private static final Location RICHMOND = newLocation(203, "Richmond");
    private static final BrowseLocation[] EXPECTED_LOCATION_CRUMB = {new BrowseLocation(LONDON), new BrowseLocation(SOUTH_WEST), new BrowseLocation(RICHMOND)};

    @Before
    public void beforeEach() {
        postAdDetail = new PostAdDetail();
        postAdFormBean = new PostAdFormBean();
        postAdFormBean.setVrmStatus(VrmStatus.VRM_VALID);
        modelBuilder = PostAdSubmitModel.builder();
        account = new Account();
        account.setId(ACC_ID);

        // api
        Map<Integer, Location> hierarchy = Maps.newHashMap();
        hierarchy.put(1, UK);
        hierarchy.put(3, LONDON);
        hierarchy.put(4, SOUTH_WEST);
        hierarchy.put(5, RICHMOND);

        when(bushfireApi.accountApi()).thenReturn(accountApi);
        when(locationService.getById(RICHMOND.getId())).thenReturn(RICHMOND);
        when(locationService.getLocationHierarchy(RICHMOND)).thenReturn(hierarchy);

        // editor
        when(editor.getEditorId()).thenReturn("editor-id");
        when(editor.getPostAdFormBean()).thenReturn(postAdFormBean);
        when(editor.getAdvertDetail()).thenReturn(postAdDetail);
        when(editor.getCategoryId()).thenReturn(CAT_ID);
        when(editor.getAccountId()).thenReturn(ACC_ID);
        when(editor.getLocationId()).thenReturn(RICHMOND.getId().longValue());
    }

    @Test
    public void noLocationSelected() {
        //given
        postingInLocationOnlyCategory();

        // when
        boolean doNext = locationAndSellerTypePostAdStep.execute(modelBuilder, editor);

        // then
        assertThat(doNext).isFalse();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.LOCATION);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), LOCATION.getId(), CONTINUE.getId()));
    }

    @Test
    public void vrnNotValid() {
        //given
        postingInFSBOMotorsCategory();
        PostAdFormBean pafm = new PostAdFormBean();
        pafm.setVrmStatus(VrmStatus.VRM_INVALID_OR_EMPTY);
        when(editor.getPostAdFormBean()).thenReturn(pafm);

        // when
        boolean doNext = locationAndSellerTypePostAdStep.execute(modelBuilder, editor);

        // then
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.LOCATION);

        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(PostAdFormPanel.CATEGORY.getId(),
                PostAdFormPanel.VEHICLE_SPECIFICATIONS.getId(),
                PostAdFormPanel.LOCATION.getId(),
                PostAdFormPanel.SELLER_TYPE.getId(),
                PostAdFormPanel.CONTINUE.getId()));
    }

    @Test
    public void locationSelected() {
        // given
        postingInLocationOnlyCategory();
        when(editor.isValidLocationSelected()).thenReturn(true);

        // when
        boolean doNext = locationAndSellerTypePostAdStep.execute(modelBuilder, editor);

        // then
        assertThat(doNext).isTrue();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.LOCATION);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), LOCATION.getId()));
        assertThat(model.getLocationCrumb()).usingElementComparator(LOCATION_COMPARATOR).containsExactly(EXPECTED_LOCATION_CRUMB);
        assertThat(model.getMapDetails().getLongitude()).isEqualTo(BigDecimal.TEN);
        assertThat(model.getMapDetails().getLatitude()).isEqualTo(BigDecimal.ONE);
    }

    @Test
    public void noLocationInFSBOCategoryWithNoAccount() {
        // given
        postingInFSBOMotorsCategory();

        // when
        boolean doNext = locationAndSellerTypePostAdStep.execute(modelBuilder, editor);

        // then
        assertThat(doNext).isFalse();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.LOCATION);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), LOCATION.getId(), PostAdFormPanel.SELLER_TYPE.getId(), CONTINUE.getId()));

        // and
        assertThat(postAdFormBean.getAttributes()).doesNotContainKey(SELLER_TYPE.getName());

        // and
        assertThat(model.getSellerType().getLabel()).isEqualTo("Seller Type");
        assertThat(model.getSellerType().getPopUp()).isNull();
    }

    @Test
    public void noLocationInFSBOCategoryWithNoTradeAccount() {
        // given
        when(accountApi.getAccount(ACC_ID)).thenReturn(account);
        when(accountApi.getSellerType(ACC_ID, CAT_ID)).thenReturn(new ValueResult<>(SellerType.BUSINESS));
        postingInFSBOMotorsCategory();

        // when
        boolean doNext = locationAndSellerTypePostAdStep.execute(modelBuilder, editor);

        // then
        assertThat(doNext).isFalse();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.LOCATION);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), LOCATION.getId(), PostAdFormPanel.SELLER_TYPE.getId(), CONTINUE.getId()));

        // and
        assertThat(postAdFormBean.getAttributes().get(SELLER_TYPE.getName())).isEqualTo("trade");
        assertThat(model.getSellerType().getLabel()).isEqualTo("Seller Type");
        assertThat(model.getSellerType().getPopUp()).isEqualTo(ImmutableMap.of("title", FPAD_WHY_SELECTED, "body", FPAD_WHY_EXPLAIN, "link", FPAD_WHY_SELECTED));
    }

    @Test
    public void locationSelectedSellerTypeNotSelectedInMotorsWithSellerType() {
        // given
        postingInFSBOMotorsCategory();
        when(editor.isValidLocationSelected()).thenReturn(true);

        // when
        boolean doNext = locationAndSellerTypePostAdStep.execute(modelBuilder, editor);

        // then
        assertThat(doNext).isFalse();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), LOCATION.getId(), PostAdFormPanel.SELLER_TYPE.getId(), CONTINUE.getId()));
        assertThat(model.getLocationCrumb()).usingElementComparator(LOCATION_COMPARATOR).containsExactly(EXPECTED_LOCATION_CRUMB);
    }

    @Test
    public void locationSelectedSellerTypeSelectedInMotorsWithSellerType() {
        // given
        when(editor.isValidLocationSelected()).thenReturn(true);

        // and
        when(accountApi.getAccount(ACC_ID)).thenReturn(account);
        when(accountApi.getSellerType(ACC_ID, CAT_ID)).thenReturn(new ValueResult<>(SellerType.BUSINESS));

        // and
        postingInFSBOMotorsCategory();

        // when
        boolean doNext = locationAndSellerTypePostAdStep.execute(modelBuilder, editor);

        // then
        assertThat(doNext).isTrue();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), LOCATION.getId(), PostAdFormPanel.SELLER_TYPE.getId()));
        assertThat(model.getLocationCrumb()).usingElementComparator(LOCATION_COMPARATOR).containsExactly(EXPECTED_LOCATION_CRUMB);
    }


    @Test
    public void shouldFailIfOutcodeSelectedSellerTypeLocationFlow() {
        // given
        when(editor.isValidLocationSelected()).thenReturn(true);
        PostcodeSelectionState state = PostcodeSelectionState.OUTCODE_RECOGNISED;
        String postcode = "DY2 8JL";
        Long locationId = Long.valueOf(RICHMOND.getId());
        postAdDetail.setPostAdFormBean(postAdFormBean);
        PostcodeLookupResponse postcodeLookupResponse = new PostcodeLookupResponse(state,postcode,locationId);
        postAdDetail.setPostcodeLocation(postcodeLookupResponse);

        // and
        when(accountApi.getAccount(ACC_ID)).thenReturn(account);
        when(accountApi.getSellerType(ACC_ID, CAT_ID)).thenReturn(new ValueResult<>(SellerType.BUSINESS));

        // and
        postingInFSBOMotorsCategory();

        // when
        boolean doNext = locationAndSellerTypePostAdStep.execute(modelBuilder, editor);

        // then
        assertThat(doNext).isTrue();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), LOCATION.getId(),
                PostAdFormPanel.SELLER_TYPE.getId()));
        assertThat(model.getLocationCrumb()).usingElementComparator(LOCATION_COMPARATOR).containsExactly(EXPECTED_LOCATION_CRUMB);
        assertThat(editor.getPostAdFormBean().getLocationId()).isEqualTo(RICHMOND.getId());
        assertThat(editor.getPostAdFormBean().getPostcode()).isNull();
    }

    @Test
    public void shouldFailToProgressWhenOutcodeEnteredLocationOnlyFlow() {
        // given
        postingInLocationOnlyCategory();
        when(editor.isValidLocationSelected()).thenReturn(true);
        PostcodeSelectionState state = PostcodeSelectionState.OUTCODE_RECOGNISED;
        String postcode = "DY2 8JL";
        Long locationId = Long.valueOf(RICHMOND.getId());
        postAdDetail.setPostAdFormBean(postAdFormBean);
        PostcodeLookupResponse postcodeLookupResponse = new PostcodeLookupResponse(state,postcode,locationId);
        postAdDetail.setPostcodeLocation(postcodeLookupResponse);

        // when
        boolean doNext = locationAndSellerTypePostAdStep.execute(modelBuilder, editor);

        // then
        assertThat(doNext).isTrue();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.LOCATION);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), LOCATION.getId()));
        assertThat(model.getLocationCrumb()).usingElementComparator(LOCATION_COMPARATOR).containsExactly(EXPECTED_LOCATION_CRUMB);
        assertThat(editor.getPostAdFormBean().getLocationId()).isEqualTo(RICHMOND.getId());
        assertThat(editor.getPostAdFormBean().getPostcode()).isNull();
    }

    @Test // ie : in property for agency
    public void noLocationInSellerTypeSupportedCategoryWithoutPriceSensitiveAttribute() {
        // given
        postingInSellerTypeSupportedCategoryThatIsNotPriceSensitive();

        // when
        boolean doNext = locationAndSellerTypePostAdStep.execute(modelBuilder, editor);

        // then
        assertThat(doNext).isFalse();

        // and
        PostAdSubmitModel model = modelBuilder.build();
        assertThat(model.getStatus()).isEqualTo(PostAdFormStatus.LOCATION);
        assertThat(model.getPanels()).isEqualTo(Lists.newArrayList(CATEGORY.getId(), LOCATION.getId(), CONTINUE.getId()));

        // and
        assertThat(postAdFormBean.getAttributes().get(SELLER_TYPE.getName())).isNull();
    }

    private void postingInFSBOMotorsCategory() {
        when(categoryModel.isChild(CategoryConstants.MOTORS_ID, CAT_ID)).thenReturn(true);
        when(categoryModel.isSupportedAttribute(AdvertAttributeNames.SELLER_TYPE.getName(), CAT_ID)).thenReturn(true);

        when(categoryModel.findAttributesByNameForGivenCategory(AdvertAttributeNames.SELLER_TYPE.getName(), CAT_ID)).thenReturn(Optional.of(Fixtures.SELLER_TYPE));
        MandatorySellerTypePanel sellerTypePanelForMotors = new MandatorySellerTypePanel(bushfireApi, editor, Fixtures.SELLER_TYPE, pricingService, categoryModel);
        when(panelFactory.createSellerTypePanel(editor, Fixtures.SELLER_TYPE)).thenReturn(sellerTypePanelForMotors);
    }

    private void postingInLocationOnlyCategory() {
        when(categoryModel.findAttributesByNameForGivenCategory(AdvertAttributeNames.SELLER_TYPE.getName(), CAT_ID)).thenReturn(Optional.of(new AttributeMetadata()));
    }

    private void postingInSellerTypeSupportedCategoryThatIsNotPriceSensitive() {
        when(categoryModel.isChild(CategoryConstants.MOTORS_ID, CAT_ID)).thenReturn(true);
        when(categoryModel.isSupportedAttribute(AdvertAttributeNames.SELLER_TYPE.getName(), CAT_ID)).thenReturn(true);
        AttributeMetadata attribute = new AttributeMetadata();
        attribute.setPriceSensitive(false);
        when(categoryModel.findAttributesByNameForGivenCategory(AdvertAttributeNames.SELLER_TYPE.getName(), CAT_ID)).thenReturn(Optional.of(attribute));
    }

    private static Location newLocation(int id, String name) {
        return LocationEntity.Builder.entity()
                .withId(id)
                .withName(name)
                .withLatitude(BigDecimal.ONE)
                .withLongitude(BigDecimal.TEN)
                .withDisplayName(name)
                .build();
    }
}