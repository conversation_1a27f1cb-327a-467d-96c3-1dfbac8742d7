package com.gumtree.web.seller.builder;

import com.google.common.collect.Lists;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.VehicleAttribute;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public final class VehicleAttributeHelper {

    public static final String INVALID_CAR_VRM = "ka57inv";
    public static final String VALID_CAR_VRM = "ka57nhu";
    public static final String VALID_MOTORBIKE_VRM = "y536hwy";

    public static final Integer CAR_CATEGORY_ID = 9311;
    public static final Integer MOTOR_BIKES_CATEGORY_ID = 10442;

    public static final List<VehicleAttribute> CARS_VEHICLE_ATTRIBUTE_LIST = Lists.newArrayList(
            new VehicleAttribute().name("vrn").value(VALID_CAR_VRM),
            new VehicleAttribute().name("vehicle_make").value("chevrolet"),
            new VehicleAttribute().name("vehicle_model").value("MATIZ"),
            new VehicleAttribute().name("body_type").value("hatchback"),
            new VehicleAttribute().name("vehicle_colour").value("SILVER"),
            new VehicleAttribute().name("vehicle_registration_year").value("2008"),
            new VehicleAttribute().name("vehicle_body_type").value("hatchback"),
            new VehicleAttribute().name("vehicle_fuel_type").value("petrol"),
            new VehicleAttribute().name("vehicle_transmission").value("manual"),
            new VehicleAttribute().name("vehicle_engine_size").value("995"),
            new VehicleAttribute().name("vehicle_not_stolen").value("true"),
            new VehicleAttribute().name("vehicle_not_scrapped").value("true"),
            new VehicleAttribute().name("vehicle_not_exported").value("true"),
            new VehicleAttribute().name("vehicle_road_worthy").value("true"),
            new VehicleAttribute().name("vehicle_not_writeoff").value("true"),
            new VehicleAttribute().name("vehicle_uk_model").value("true"),
            new VehicleAttribute().name("vehicle_original_number_plate").value("true"),
            new VehicleAttribute().name("vehicle_original_colour").value("true"),
            new VehicleAttribute().name("vehicle_vhc_checked").value("true"));


    public static final List<VehicleAttribute> MOTOR_BIKES_VEHICLE_ATTRIBUTE_LIST = Lists.newArrayList(
            new VehicleAttribute().name("vrn").value(VALID_MOTORBIKE_VRM),
            new VehicleAttribute().name("motorbike_make").value("aprilia-motorbikes"),
            new VehicleAttribute().name("vehicle_colour").value("BLACK"),
            new VehicleAttribute().name("vehicle_registration_year").value("2001"),
            new VehicleAttribute().name("vehicle_engine_size").value("49"),
            new VehicleAttribute().name("vehicle_not_stolen").value("true"),
            new VehicleAttribute().name("vehicle_not_scrapped").value("true"),
            new VehicleAttribute().name("vehicle_not_exported").value("true"),
            new VehicleAttribute().name("vehicle_road_worthy").value("true"),
            new VehicleAttribute().name("vehicle_not_writeoff").value("true"),
            new VehicleAttribute().name("vehicle_uk_model").value("true"),
            new VehicleAttribute().name("vehicle_original_number_plate").value("true"),
            new VehicleAttribute().name("vehicle_original_colour").value("true"),
            new VehicleAttribute().name("vehicle_vhc_checked").value("true"));

    public static final Map<String, String> CARS_VEHICLE_ATTRIBUTE_MAP = CARS_VEHICLE_ATTRIBUTE_LIST.stream()
            .collect(Collectors.toMap(VehicleAttribute::getName, VehicleAttribute::getValue));

    public static final Map<String, String> MOTOR_BIKES_VEHICLE_ATTRIBUTE_MAP = MOTOR_BIKES_VEHICLE_ATTRIBUTE_LIST.stream()
            .collect(Collectors.toMap(VehicleAttribute::getName, VehicleAttribute::getValue));

}
