package com.gumtree.web.seller.storage;

import com.google.common.base.Optional;
import com.gumtree.api.User;
import com.gumtree.draftsapi.client.command.CreateOrUpdateDraftCommand;
import com.gumtree.draftsapi.client.command.DeleteDraftCommand;
import com.gumtree.draftsapi.client.command.GetDraftCommand;
import com.gumtree.draftsapi.model.ApiResponse;
import com.gumtree.draftsapi.model.IdResponse;
import com.gumtree.draftsapi.model.error.DraftApiErrorCode;
import com.gumtree.draftsapi.model.error.DraftApiErrors;
import com.gumtree.draftsapi.spec.DraftsApiClient;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.seller.page.postad.model.PostAdDetail;
import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;


@RunWith(MockitoJUnitRunner.class)
public class ApiDraftAdvertServiceTest {

    private static final Long USER_ID = 987125L;
    private static final User USER = User.builder().withId(USER_ID).build();
    private static final String COOKIE_VALUE = "10152";
    private static final GetDraftCommand<PostAdDetail> GET_DRAFT_COMMAND = new GetDraftCommand<>(USER_ID, PostAdDetail.class);

    @InjectMocks private ApiDraftAdvertService service;

    @Mock private UserSessionService userSessionService;
    @Mock private DraftsApiClient draftsApiClient;

    @Before
    public void setUp() throws Exception {
        // set: user logged in
        when(userSessionService.getUser()).thenReturn(Optional.of(USER));
    }

    @Test
    public void testRetrieveReturnsAbsentIfUserNotLoggedIn() {
        // set user not logged in
        when(userSessionService.getUser()).thenReturn(Optional.<User>absent());

        // when
        Optional<PostAdDetail> result = service.retrieve();

        // then
        assertThat(result).isEqualTo(Optional.<PostAdDetail>absent());

        // and get never invoked
        verifyNoMoreInteractions(draftsApiClient);
    }

    @Test
    public void testRetrieveReturnsAbsentIfUserLoggedInButValueInStorageIsAbsent() {
        // user is logged in but redis return nothing
        when(draftsApiClient.execute(GET_DRAFT_COMMAND))
                .thenReturn(ApiResponse.<PostAdDetail>error(DraftApiErrors.create(DraftApiErrorCode.DRAFT_NOT_FOUND)));

        // when
        Optional<PostAdDetail> result = service.retrieve();

        // then
        assertThat(result).isEqualTo(Optional.<PostAdDetail>absent());

        // and get invoked proper with namespace modifier
        verify(draftsApiClient).execute(GET_DRAFT_COMMAND);
    }

    @Test
    public void testRetrieveReturnsDetailIfUserLoggedAndStorageContainsValue() {
        // todo
        // set user logged in & draft api returns stuff
        when(draftsApiClient.execute(GET_DRAFT_COMMAND))
                .thenReturn(ApiResponse.of(createDetail()));

        // when
        Optional<PostAdDetail> result = service.retrieve();

        // then
        assertThat(result.get().getCookie()).isEqualTo(COOKIE_VALUE); // post ad detail hasn't got equals implemented

        // and get invoked with proper namespace modifier
        verify(draftsApiClient).execute(GET_DRAFT_COMMAND);
    }

    @Test
    public void testPersistReturnsFalseIfUserNotLoggedIn() {
        // set user not logged in
        when(userSessionService.getUser()).thenReturn(Optional.<User>absent());

        // when
        boolean result = service.persist(createDetail());

        // then
        assertThat(result).isFalse();

        // and set never invoked
        verifyZeroInteractions(draftsApiClient);
    }

    @Test
     public void testPersistReturnsFalseIfUserLoggedInButCategoryIsNotSet() {
        // for logged in user but category is null

        // when
        boolean result = service.persist(createDetail());

        // then
        assertThat(result).isFalse();

        // and set never invoked
        verifyZeroInteractions(draftsApiClient);
    }

    @Test
    public void testPersistReturnsTrueIfUserLoggedInAndCategoryIsSet() {
        // setup category
        PostAdDetail detail = createDetail();
        detail.getPostAdFormBean().setCategoryId(101L);

        when(draftsApiClient.execute(new CreateOrUpdateDraftCommand(USER_ID, detail))).thenReturn(ApiResponse.of(new IdResponse()));

        // when
        boolean result = service.persist(detail);

        // then
        assertThat(result).isTrue();
    }

    @Test
    public void testClearNotInvokedIfUserNotLoggedIn() {
        // set user is not logged in
        when(userSessionService.getUser()).thenReturn(Optional.<User>absent());

        // when
        service.clear();

        // then delete never invoked
        verifyZeroInteractions(draftsApiClient);
    }

    @Test
    public void testClearInvokedFineIfUserLoggedIn() {
        // when
        service.clear();

        // then delete
        verify(draftsApiClient).execute(new DeleteDraftCommand(USER_ID));
    }

    private static PostAdDetail createDetail() {
        PostAdDetail detail = new PostAdDetail();
        detail.setCookie(COOKIE_VALUE);
        PostAdFormBean formBean = new PostAdFormBean();
        detail.setPostAdFormBean(formBean);
        return detail;
    }
}