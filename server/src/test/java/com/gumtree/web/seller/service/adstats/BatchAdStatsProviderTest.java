package com.gumtree.web.seller.service.adstats;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounterService;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounters;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static com.gumtree.api.ApiAdBuilder.advert;
import static java.util.Arrays.asList;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BatchAdStatsProviderTest {

    private static final Long AD1_ID = 176L;
    private static final Long AD2_ID = 935L;
    private AdvertStatisticDataFactoryOfFactory factoryOfFactory;
    private UrlScheme urlScheme;

    @Mock
    private AdCounterService adCounterService;


    @Before
    public void setup(){
        urlScheme = mock(UrlScheme.class);
        factoryOfFactory = new AdvertStatisticDataFactoryOfFactory(urlScheme);
    }

    @Test
    public void shouldConvertGivenAds() {
        //given
        List<Ad> ads = getStubAdvertList();
        when(adCounterService.getAdvertCounters(asList(AD1_ID, AD2_ID))).thenReturn(getStubAdCounters());
        when(urlScheme.urlFor(any(Ad.class))).thenReturn("/stubbed/url");

        //when
        BatchAdStatsProvider provider = new BatchAdStatsProvider(factoryOfFactory,adCounterService);
        List<AdvertStatisticData> stats = provider.getStatsFor(ads);
        //then
        assertThat(stats.size(),is(2));
        AdvertStatisticData adStats1 = stats.get(0);
        assertThat(adStats1.getAdvertId(),is(AD1_ID.toString()));
        assertThat(adStats1.getSRPViews().intValue(),is(342)); // From Stats
        assertThat(adStats1.getVIPViews().intValue(),is(78)); // From CData

        AdvertStatisticData adStats2 = stats.get(1);
        assertThat(adStats2.getAdvertId(),is(AD2_ID.toString()));
        assertThat(adStats2.getSRPViews().intValue(),is(1289)); // From Stats
        assertThat(adStats2.getVIPViews().intValue(),is(627)); // From CData
    }

    @Test
    public void shouldHandleErrorsGracefully() {
        //given
        List<Ad> ads = getStubAdvertList();
        when(adCounterService.getAdvertCounters(asList(AD1_ID, AD2_ID))).thenReturn(Collections.emptyMap());
        when(urlScheme.urlFor(any(Ad.class))).thenReturn("/stubbed/url");

        //when
        BatchAdStatsProvider provider = new BatchAdStatsProvider(factoryOfFactory,adCounterService);
        List<AdvertStatisticData> stats = provider.getStatsFor(ads);
        //then
        assertThat(stats.size(),is(2));

        AdvertStatisticData adStats1 = stats.get(0);
        assertThat(adStats1.getAdvertId(),is(AD1_ID.toString()));
        assertThat(adStats1.getSRPViews().intValue(),is(0)); // From Stats
        assertThat(adStats1.getVIPViews().intValue(),is(0)); // From CData

        AdvertStatisticData adStats2 = stats.get(1);
        assertThat(adStats2.getAdvertId(),is(AD2_ID.toString()));
        assertThat(adStats2.getSRPViews().intValue(),is(0)); // From Stats
        assertThat(adStats2.getVIPViews().intValue(),is(0)); // From CData

    }

    private List<Ad> getStubAdvertList(){
        Ad ad1 = advert()
                .id(AD1_ID)
                .status(AdStatus.LIVE)
                .bumpUpCount(2)
                .build();

        Ad ad2 = advert()
                .id(AD2_ID)
                .status(AdStatus.LIVE)
                .bumpUpCount(0)
                .build();
        return  Arrays.asList( ad1, ad2  );
    }

    private HashMap<Long, AdCounters> getStubAdCounters(){
        HashMap<Long, AdCounters> adCountersStats = new HashMap<Long, AdCounters>();
        adCountersStats.put(AD1_ID,new AdCounters
                .Builder()
                .setAdvertId(AD1_ID)
                .setViewsCounter(78)
                .setSearchImpressionCounter(342)
                .build()
        );

        adCountersStats.put(AD2_ID,new AdCounters
                .Builder()
                .setAdvertId(AD2_ID)
                .setViewsCounter(627)
                .setSearchImpressionCounter(1289)
                .build()
        );
        return adCountersStats;
    }
}
