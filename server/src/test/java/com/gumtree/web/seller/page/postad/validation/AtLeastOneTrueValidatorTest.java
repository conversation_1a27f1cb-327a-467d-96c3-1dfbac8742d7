package com.gumtree.web.seller.page.postad.validation;

import com.gumtree.web.seller.page.postad.model.PostAdFormBean;
import org.junit.Before;
import org.junit.Test;

import javax.validation.ConstraintValidatorContext;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class AtLeastOneTrueValidatorTest {
    private AtLeastOneTrueValidator validator;
    private ConstraintValidatorContext context;
    private AtLeastOneTrue alot;

    @Before
    public void setup() {
        validator = new AtLeastOneTrueValidator();
        context = mock(ConstraintValidatorContext.class);
        alot = mock(AtLeastOneTrue.class);

        ConstraintValidatorContext.ConstraintViolationBuilder builder =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.class);
        ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderDefinedContext nodeContext =
                mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderDefinedContext.class);

        when(context.buildConstraintViolationWithTemplate(anyString())).thenReturn(builder);
        when(builder.addNode(anyString())).thenReturn(nodeContext);

        when(alot.fieldList()).thenReturn(new String[] {"useEmail", "usePhone"});
        when(alot.message()).thenReturn("postad.contact.neither.selected");

        validator.initialize(alot);
    }

    @Test
    public void testIsValidWithBothSet() throws Exception {
        PostAdFormBean bean = makeTestBean(true, true);

        boolean result = validator.isValid(bean, context);
        assertTrue(result);
    }

    @Test
    public void testIsValidWithOneTrueOneFalse() throws Exception {
        PostAdFormBean bean = makeTestBean(true, false);

        boolean result = validator.isValid(bean, context);
        assertTrue(result);
    }

    @Test
    public void testIsInvalidWithNeitherSet() throws Exception {
        PostAdFormBean bean = makeTestBean(false, false);

        boolean result = validator.isValid(bean, context);
        assertFalse(result);
        verify(context).buildConstraintViolationWithTemplate("postad.contact.neither.selected");
        verify(context).disableDefaultConstraintViolation();
    }

    private PostAdFormBean makeTestBean(boolean useEmail, boolean usePhone) {
        PostAdFormBean bean = new PostAdFormBean();
        bean.setUseEmail(useEmail);
        bean.setUsePhone(usePhone);

        return bean;
    }
}
