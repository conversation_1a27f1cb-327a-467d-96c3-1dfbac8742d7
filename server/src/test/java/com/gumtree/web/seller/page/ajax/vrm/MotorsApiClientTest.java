package com.gumtree.web.seller.page.ajax.vrm;

import com.google.common.collect.Maps;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.ApiException;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.VehicleDataApi;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.StandardiseVehicleDataRequest;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.StandardisedVehicleDataResponse;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.VehicleAttribute;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_VHC_CHECKED;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VRN;
import static java.util.stream.Collectors.toList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MotorsApiClientTest {

    private static final String VALID_VRM = "valid vrm";
    private static final String INVALID_VRM = "invalid vrm";
    private static final Long CAR_CATEGORY_ID = 9311L;
    private static final String CLIENT_ID = "seller";

    @Mock
    private VehicleDataApi vehicleDataApiMock;

    @Mock
    private StandardisedVehicleDataResponse vehicleDataResponseMock;

    @InjectMocks
    private MotorsApiClient motorsApiClient;

    @Test
    public void vehicleDataLookupShouldReturnStandardisedVehicleDataResponseWhenSuccessful() throws Exception {
        when(vehicleDataApiMock.lookupVehicleData(VALID_VRM, CAR_CATEGORY_ID.intValue(), CLIENT_ID)).thenReturn(vehicleDataResponseMock);

        Optional<StandardisedVehicleDataResponse> response = motorsApiClient.lookupVehicleData(VALID_VRM, CAR_CATEGORY_ID);

        assertThat(response, equalTo(Optional.of(vehicleDataResponseMock)));
    }

    @Test
    public void vehicleDataLookupShowReturnNoneWhenUnsuccessful() throws Exception {
        when(vehicleDataApiMock.lookupVehicleData(INVALID_VRM, CAR_CATEGORY_ID.intValue(), CLIENT_ID)).thenThrow(new ApiException());

        Optional<StandardisedVehicleDataResponse> response = motorsApiClient.lookupVehicleData(INVALID_VRM, CAR_CATEGORY_ID);

        assertThat(response, equalTo(Optional.empty()));
    }

    @Test
    public void standardiseVehicleDataShouldReturnStandardisedVehicleDataResponseWhenSuccessful() throws Exception {
        Map<String, String> attributesToStandardise = Maps.newHashMap();
        attributesToStandardise.put(VRN.getName(), "test");
        List<VehicleAttribute> attributes = attributesToStandardise.entrySet().stream()
                .map(entry -> new VehicleAttribute().name(entry.getKey()).value(entry.getValue())).collect(toList());
        when(vehicleDataApiMock.standardiseVehicleData(new StandardiseVehicleDataRequest()
                .categoryId(CAR_CATEGORY_ID.intValue())
                .attributes(attributes), CLIENT_ID))
                .thenReturn(vehicleDataResponseMock);
        when(vehicleDataResponseMock.getAttributes()).thenReturn(attributes);

        Map<String, String> response = motorsApiClient.standardiseVehicleData(CAR_CATEGORY_ID, attributesToStandardise);

        assertThat(response, equalTo(attributesToStandardise));
    }

    @Test
    public void standardiseVehicleDataShouldReturnTheOriginalMapWhenResponseUnsuccessful() throws Exception {
        Map<String, String> attributesToStandardise = Maps.newHashMap();
        attributesToStandardise.put(VRN.getName(), "test");
        List<VehicleAttribute> attributes = attributesToStandardise.entrySet().stream()
                .map(entry -> new VehicleAttribute().name(entry.getKey()).value(entry.getValue())).collect(toList());
        when(vehicleDataApiMock.standardiseVehicleData(new StandardiseVehicleDataRequest()
                .categoryId(CAR_CATEGORY_ID.intValue())
                .attributes(attributes), CLIENT_ID))
                .thenThrow(new RuntimeException("Cannot process"));

        Map<String, String> response = motorsApiClient.standardiseVehicleData(CAR_CATEGORY_ID, attributesToStandardise);

        attributesToStandardise.put(VEHICLE_VHC_CHECKED.getName(), "false");
        assertThat(response, equalTo(attributesToStandardise));
    }
}
