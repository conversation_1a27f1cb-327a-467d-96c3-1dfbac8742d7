package com.gumtree.web.seller.page.payment.util;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.common.properties.GtPropertiesInitializer;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutAdvert;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.postad.model.meta.MetaPathInfo;
import com.gumtree.web.seller.page.postad.model.meta.PageActionType;
import com.gumtree.web.seller.page.postad.model.meta.PagePaymentType;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.service.CheckoutContainer;
import org.apache.commons.configuration.ConfigurationException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Created by mdivilioglu on 6/18/17.
 */
@RunWith(MockitoJUnitRunner.class)
public class PaymentSuccessUrlTest {
    private CheckoutContainer checkoutContainer;
    private Checkout checkout;
    @Before
    public void init() throws ConfigurationException {
        GtPropertiesInitializer.init("seller-server");
        checkoutContainer = mock(CheckoutContainer.class);
        checkout = mock(Checkout.class);

    }

    @Test
    public void shouldCreateURLWithCorrectSufficForMultipleAdsNo() {
        ApiOrder order = new ApiOrder();
        order.setId(20L);
        Ad ad = new Ad();
        ad.setId(10L);
        ad.setStatus(AdStatus.EXPIRED);
        Checkout checkout = new CheckoutImpl();
        CheckoutAdvert checkoutAdvert = getCheckoutAdvert(ad);
        checkout.setAdvert(checkoutAdvert);
        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), false));
        PaymentSuccessUrl url = new PaymentSuccessUrl(checkout);

        assertThat(url.get().split("\\?")[1], equalTo("action=post&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=no"));
    }

    @Test
    public void shouldCreateURLWithCorrectSufficForMultipleAdsYes() {
        Checkout checkout = new CheckoutImpl();

        checkout.setKey("thekey");
        checkout.setMetaPathInfo(new MetaPathInfo(new ArrayList<ProductType>(Arrays.asList(ProductType.BUMP_UP, ProductType.INSERTION)),
                PageActionType.POST, new ArrayList<PagePaymentType>(Arrays.asList(PagePaymentType.INSERTION, PagePaymentType.FEATURE)), true));
        PaymentSuccessUrl url = new PaymentSuccessUrl(checkout);

        assertThat(url.get().split("\\?")[1], equalTo("action=post&payment=insertion,feature&type=BUMP_UP,INSERTION&multiple=yes"));
    }

    private CheckoutAdvert getCheckoutAdvert(Ad ad) {
        return CheckoutAdvert.Builder.builder()
                .categoryId(ad.getCategoryId())
                .locationId(ad.getLocationId())
                .id(ad.getId()).build();
    }
}
