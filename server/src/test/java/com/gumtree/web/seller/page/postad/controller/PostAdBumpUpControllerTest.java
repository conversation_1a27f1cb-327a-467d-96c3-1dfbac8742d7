package com.gumtree.web.seller.page.postad.controller;

import com.google.common.base.Optional;
import com.google.gson.JsonParser;
import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.client.executor.ApiCall;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.api.domain.user.beans.RegisterUserBean;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.common.properties.GtPropertiesInitializer;
import com.gumtree.config.DefaultPropertyInitializer;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.service.location.LocationService;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.common.page.util.RedirectViewWithRequiredModelKeys;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.security.UserLoginStatus;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.manageads.api.CreateOrderApiCall;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.metric.Metric;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.controller.PaymentCheckoutController;
import com.gumtree.web.seller.page.postad.model.*;
import com.gumtree.web.seller.page.postad.model.path.PostAdBumpupPath;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import com.gumtree.web.seller.page.postad.model.products.ProductPrice;
import com.gumtree.web.seller.page.postad.model.products.ProductType;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.web.seller.service.CheckoutMetaInjector;
import com.gumtree.web.seller.service.PostAdWorkspace;
import com.gumtree.web.seller.service.pricing.PricingContext;
import com.gumtree.web.seller.service.pricing.PricingService;
import com.gumtree.web.seller.service.threatmetrix.ThreatMetrixService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.http.HttpHeaders;
import org.fest.assertions.api.Assertions;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Matchers;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.validation.BindingResult;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.AbstractUrlBasedView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.fest.assertions.data.MapEntry.entry;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.instanceOf;
import static org.hamcrest.Matchers.notNullValue;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 *
 */
@RunWith(MockitoJUnitRunner.class)
public class PostAdBumpUpControllerTest extends BaseSellerControllerTest {

    private static final long USER_ID = 1L;
    private static final String BRITISH_SHORTHAIR = "british_shorthair";
    private static final String CAT = "cat";
    private static final String CAT_BREED = "cat_breed";
    private static final String CHECKOUT_KEY = "checkoutKey";
    private static final String CHIHUAHUA = "Chihuahua";
    private static final String DOG = "dog";
    private static final String DOG_BREED = "dog_breed";
    private static final String EDITOR_ID = "1";
    private static final String JACK_RUSSELL = "jack_russell";
    private static final String USER_EMAIL = "<EMAIL>";

    private CheckoutContainer checkoutContainer;

    private User mockUser = buildUser(USER_EMAIL);

    private PricingMetadata pricingMetadata;

    private PostAdWorkspace postAdWorkspace;

    private AdvertEditor advertEditor;

    private ApiCallResponse postAdApiCallResponse;

    private ApiCallResponse registerUserApiCallResponse;

    private RegisterUserBean registerUserBean = new RegisterUserBean();

    private ApiCallResponse orderApiCallResponse;

    private UserSession authenticatedUserSession;

    private BindingResult bindingResult;

    private ApiOrder order;

    private PostAdBumpUpController controller;

    private Ad ad;

    private BushfireApiKey apiKey;

    private BumpUpFormBean bumpUpFormBean;

    private ThreatMetrixCookie threatMetrixCookie;

    private ThreatMetrixService threatMetrixService;

    private CheckoutMetaInjector checkoutMetaInjector;

    private CustomMetricRegistry customMetricRegistry;

    private String threatMetrixSessionId = "123-456";

    @BeforeClass
    public static void initContext() {
        new DefaultPropertyInitializer().initialize(null);
    }

    @Before
    public void init() throws ConfigurationException {
        GtPropertiesInitializer.init("seller-server");
        checkoutContainer = mock(CheckoutContainer.class);
        Checkout checkout1 = mock(Checkout.class);
        PricingService pricingService = mock(PricingService.class);
        postAdWorkspace = mock(PostAdWorkspace.class);
        advertEditor = mock(AdvertEditor.class);
        pricingMetadata = mock(PricingMetadata.class);
        ProductPrice bumpUpPrice = mock(ProductPrice.class);
        apiCallExecutor = mock(ApiCallExecutor.class);
        postAdApiCallResponse = mock(ApiCallResponse.class);
        registerUserApiCallResponse = mock(ApiCallResponse.class);
        orderApiCallResponse = mock(ApiCallResponse.class);
        authenticatedUserSession = mock(UserSession.class);
        bindingResult = mock(BindingResult.class);
        pageContext = mock(GumtreePageContext.class);
        locationService = mock(LocationService.class);
        threatMetrixCookie = mock(ThreatMetrixCookie.class);
        request = mock(HttpServletRequest.class);
        response = mock(HttpServletResponse.class);
        userSessionService = mock(UserSessionService.class);
        checkoutMetaInjector = mock(CheckoutMetaInjector.class);
        threatMetrixService = mock(ThreatMetrixService.class);
        customMetricRegistry = new CustomMetricRegistry(new SimpleMeterRegistry());


        when(postAdWorkspace.getEditor(EDITOR_ID)).thenReturn(advertEditor);
        when(advertEditor.getEditorId()).thenReturn(EDITOR_ID);
        when(advertEditor.getDisplayActionVerb()).thenReturn("someVerb");
        when(advertEditor.toRegisterUserBean()).thenReturn(registerUserBean);

        when(pricingService.getPriceInformation(Matchers.any(PricingContext.class))).thenReturn(pricingMetadata);
        when(pricingMetadata.getBumpUpPrice()).thenReturn(bumpUpPrice);
        when(bumpUpPrice.getDisplayValue()).thenReturn("&pound;2.99");
        when(bumpUpPrice.getPrice()).thenReturn(new BigDecimal("2.99"));
        when(bumpUpPrice.getProductName()).thenReturn(ProductName.BUMP_UP.name());
        when(bumpUpPrice.getProductType()).thenReturn(ProductType.BUMP_UP);

        when(apiCallExecutor.call(advertEditor)).thenReturn(postAdApiCallResponse);
        when(apiCallExecutor.call((ApiCall) argThat(instanceOf(ApiCall.class)))).thenReturn(
                postAdApiCallResponse);
        when(apiCallExecutor.call((ApiCall) argThat(instanceOf(CreateOrderApiCall.class)))).thenReturn(
                orderApiCallResponse);

        ad = new Ad();
        ad.setId(10L);

        when(postAdApiCallResponse.getResponseObject()).thenReturn(ad);

        when(authenticatedUserSession.getSelectedAccountId()).thenReturn(5L);
        when(authenticatedUserSession.getUserType()).thenReturn(UserLoginStatus.EXISTING);
        when(authenticatedUserSession.registerNewUser(registerUserBean)).thenReturn(registerUserApiCallResponse);

        when(authenticatedUserSession.getUser()).thenReturn(mockUser);
        when(cookieResolver.resolve(pageContext.getHttpServletRequest(), ThreatMetrixCookie.class)).thenReturn(threatMetrixCookie);
        when(threatMetrixCookie.getDefaultValue()).thenReturn(threatMetrixSessionId);

        doAnswer(invocationOnMock -> {
            ShoppingCart cart = (ShoppingCart) invocationOnMock.getArguments()[0];
            cart.addProduct(ProductName.FEATURE_3_DAY);
            cart.addProduct(ProductName.HOMEPAGE_SPOTLIGHT);
            return null;
        }).when(advertEditor).populateShoppingCart(Matchers.<ShoppingCart>any());

        apiKey = new BushfireApiKey();
        when(authenticatedUserSession.getApiKey()).thenReturn(apiKey);

        order = new ApiOrder();
        order.setId(20L);

        when(orderApiCallResponse.getResponseObject()).thenReturn(order);
        when(checkoutContainer.createCheckout(order, ad, true)).thenReturn(checkout1);

        when(checkout1.getKey()).thenReturn("101010");

        bumpUpFormBean = new BumpUpFormBean();

        Checkout checkout = mock(Checkout.class);
        when(checkout.getKey()).thenReturn("ABCD");

        when(checkoutContainer.createCheckout(Matchers.<ApiOrder>any(), eq(ad), anyBoolean())).thenReturn(checkout);
        when(checkoutMetaInjector.injectTrackingForPost(any(Checkout.class), eq(ad))).thenReturn(checkout);
        when(checkoutMetaInjector.injectTrackingForEditorUpdate(any(Checkout.class), any(Ad.class), eq(AdStatus.LIVE))).thenReturn(checkout);
        when(checkoutMetaInjector.injectTrackingForEditorUpdate(any(Checkout.class), eq(ad), eq(AdStatus.DELETED_USER))).thenReturn(checkout);
        when(checkoutMetaInjector.injectTrackingForEditorUpdate(any(Checkout.class), eq(ad), eq(AdStatus.EXPIRED))).thenReturn(checkout);

        when(checkout.getOrder()).thenReturn(order);

        when(request.getRequestURL()).thenReturn(new StringBuffer());

        when(userSessionService.getUser()).thenReturn(Optional.<com.gumtree.api.User>absent());

        controller = new PostAdBumpUpController(cookieResolver, categoryModel, apiCallExecutor, null, null,
                postAdWorkspace, authenticatedUserSession, null, checkoutContainer, pricingService,
                pageContext, locationService, zenoService, metricRegistry, userSessionService, checkoutMetaInjector, customMetricRegistry,threatMetrixService);

        controller.initCounters();
        setValidAdvertEditor(false);
        setCreateMode(false);
        setRequiresBumpUp(false);
        setSupportsBumpUp(false);
        setPostAdFails(false);
        setRegisterUserFails(false);
        setOrderFails(false);
        setBindingValidationFails(false);

        autowireAbExperimentsService(controller);
    }

    @Test
    public void viewBumpUpPageRedirectsToPostAdFormIfEditorIsNotValid() throws IOException {
        //given
        setValidAdvertEditor(false);

        when(advertEditor.isValid()).thenReturn(false);

        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        assertThat(getUrl(redirect), equalTo("/postad/1"));
    }

    @Test
    public void repostRedirectsToEditAdFormIfEditorIsNotValid() throws IOException {
        //given
        setValidAdvertEditor(false);
        when(advertEditor.isValid()).thenReturn(false);

        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, true, pageContext, remoteIP);

        //then
        Assert.assertTrue(getUrl(redirect).contains("/postad/repost?advertId="));
    }

    @Test
    public void viewIsARedirectWhenViewingBumpUpPageInEditModeForForNonExpiredAdInDraftStatus() throws IOException {
        //given
        setValidAdvertEditor(true);
        mockAdEditorWithBreedAttributes(9389L, DOG_BREED, JACK_RUSSELL);

        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        assertThat(getUrl(redirect), equalTo("/checkout/{checkoutKey}"));
        Assertions.assertThat(redirect.getModel()).contains(entry(CHECKOUT_KEY, "ABCD"));
        assertMetricValue(DOG, 1, Metric.BREED_SELECTED.name(), JACK_RUSSELL);
    }

    @Test
    public void modelPopulatedCorrectlyWhenViewingBumpUpPageInEditModeForExpiredAd() throws IOException {
        //given
        setValidAdvertEditor(true);
        setRequiresBumpUp(true);
        setSupportsBumpUp(true);

        //when
        ModelAndView modelAndView = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        assertThat(modelAndView.getViewName(), equalTo(Page.PostAdBumpUp.getTemplateName()));
        assertModelObjectsPopulated(modelAndView, null);
    }


    @Test
    public void redirectsToPostAdFormWhenFailsToPostAdToApiInCreateMode() throws IOException {
        //given
        setValidAdvertEditor(true);
        setCreateMode(true);
        setPostAdFails(true);
        setOrderFails(true);

        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        assertThat(getUrl(redirect), equalTo("/postad/1"));
    }

    @Test
    public void redirectsToPostAdFormWhenFailsToRegisterUserInApiInCreateMode() throws IOException {
        //given
        when(authenticatedUserSession.getUserType()).thenReturn(UserLoginStatus.NEW_UNREGISTERED);
        setValidAdvertEditor(true);
        setCreateMode(true);
        setPostAdFails(true);
        setRegisterUserFails(true);
        setOrderFails(true);

        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        assertThat(getUrl(redirect), equalTo("/postad/1"));
    }

    @Test
    public void doesNotTryToRegisterNewRegisteredUserInCreateMode() throws IOException {
        //given
        when(authenticatedUserSession.getUserType()).thenReturn(UserLoginStatus.NEW_REGISTERED);
        setValidAdvertEditor(true);
        setCreateMode(true);
        setPostAdFails(true);
        setRegisterUserFails(true);
        setOrderFails(true);
        verify(authenticatedUserSession, never()).registerNewUser(Matchers.<RegisterUserBean>any());

        //when
        controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
    }

    @Test
    public void redirectsToErrorPageWhenCreatingOrderFailsInCreateMode() throws IOException {
        //given
        setValidAdvertEditor(true);
        setCreateMode(true);
        setOrderFails(true);

        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        verify(postAdWorkspace).removeEditor(EDITOR_ID, advertEditor.isCreateMode());
        assertThat(getUrl(redirect), equalTo(OrderErrorController.PAGE_PATH));
    }

    @Test
    public void redirectsToDuplicatedAdvertPageWhenAdvertIsDuplicated() throws IOException {
        //given
        setValidAdvertEditor(true);
        setCreateMode(true);
        setOrderFails(true);
        when(orderApiCallResponse.getErrorCode()).thenReturn(ApiErrorCode.DUPLICATED_ADVERT);

        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        verify(postAdWorkspace).removeEditor("1", advertEditor.isCreateMode());
        assertThat(getUrl(redirect), equalTo(DuplicatedAdController.PAGE_PATH));
    }

    @Test
    public void redirectsToCheckoutWhenPaidOrder() {
        //given
        setValidAdvertEditor(true);
        setCreateMode(true);
        setRequiresBumpUp(true);
        setBindingValidationFails(true);
        order.setTotalIncVat(100L);
        mockAdEditorWithBreedAttributes(9389L, DOG_BREED, JACK_RUSSELL);

        //when
        ModelAndView redirect = controller.submitBumpUpForm(bumpUpFormBean, bindingResult,
                request, EDITOR_ID, pageContext, remoteIP);

        //then
        assertThat(getUrl(redirect), equalTo("/checkout/{checkoutKey}"));
        Assertions.assertThat(redirect.getModel()).contains(entry(CHECKOUT_KEY, "ABCD"));
        assertMetricValue(DOG, 1, Metric.BREED_SELECTED.name(), JACK_RUSSELL);
    }

    @Test
    public void jsonRedirectUrlWhenPaidOrderAndAjaxCall() {
        //given
        setValidAdvertEditor(true);
        setCreateMode(true);
        setRequiresBumpUp(true);
        setBindingValidationFails(true);
        order.setTotalIncVat(100L);
        mockAdEditorWithBreedAttributes(9389L, DOG_BREED, CHIHUAHUA);

        //when
        String json = controller.submitBumpUpFormAjax(bumpUpFormBean, bindingResult,
                request, EDITOR_ID, pageContext, remoteIP);

        //then
        String redirectUrl = new JsonParser().parse(json).getAsJsonObject().get("redirectUrl").getAsString();
        assertThat(redirectUrl, equalTo("/checkout/ABCD"));
        assertMetricValue(DOG,1, Metric.BREED_SELECTED.name(), CHIHUAHUA);
    }

    @Test
    public void populatesShoppingCartCorrectlyAndRedirectsToPaymentFlowWhenOrderSucceedsInCreateMode() throws IOException {
        //given
        setValidAdvertEditor(true);
        setCreateMode(true);
        mockAdEditorWithBreedAttributes(9389L, DOG_BREED, JACK_RUSSELL);

        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        ArgumentCaptor<? extends ApiCall> callCaptor = ArgumentCaptor.forClass(ApiCall.class);

        verify(apiCallExecutor, atLeast(1)).call(callCaptor.capture());

        java.util.Optional<CreateOrderApiCall> createOrderApiCall = getFirstApiCall(callCaptor);
        assertThat(createOrderApiCall.isPresent(), equalTo(true));

        CreateOrderBean orderBean = createOrderApiCall.get().getCreateOrderBean();
        assertOrder(createOrderApiCall.get(), orderBean);


        assertThat(getUrl(redirect), equalTo(PaymentCheckoutController.PAGE_PATH));
        Assertions.assertThat(redirect.getModel()).contains(entry(CHECKOUT_KEY, "ABCD"));

        verify(postAdWorkspace).removeEditor(EDITOR_ID, advertEditor.isCreateMode());
        verify(checkoutContainer).createCheckout(order, ad, true);
        assertMetricValue(DOG, 1, Metric.BREED_SELECTED.name(), JACK_RUSSELL);
    }

    @Test
    public void viewBumpUpPage_redirectsToPaymentFlowWhenAutoRepostIsTrue() throws IOException {
        //given
        setValidAdvertEditor(true);
        setSupportsBumpUp(true);
        mockAdEditorWithBreedAttributes(9389L, DOG_BREED, JACK_RUSSELL);

        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, true, pageContext, remoteIP);

        //then
        Assertions.assertThat(redirect.getModel()).contains(entry(CHECKOUT_KEY, "ABCD"));

        RedirectViewWithRequiredModelKeys view = (RedirectViewWithRequiredModelKeys) redirect.getView();
        Assertions.assertThat(view.getUrl()).isEqualTo("/checkout/{checkoutKey}");
        assertMetricValue(DOG, 1, Metric.BREED_SELECTED.name(), JACK_RUSSELL);
    }

    @Test
    public void viewBumpUpPage_doesNotRedirectToPaymentFlowWhenAutoRepostIsFalse() throws IOException {
        //given
        setValidAdvertEditor(true);
        setSupportsBumpUp(true);

        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        Assertions.assertThat(redirect.getModel()).doesNotContainKey(CHECKOUT_KEY);
        Assertions.assertThat(redirect.getViewName()).isEqualTo("pages/checkout/bump-up");
    }

    @Test
    public void populatesShoppingCartCorrectlyAndRedirectsToPaymentFlowWhenOrderSucceedsWhenEditingExpiredAd() {
        //given
        setValidAdvertEditor(true);
        setRequiresBumpUp(true);
        setBindingValidationFails(true);
        mockAdEditorWithBreedAttributes(9389L, DOG_BREED, JACK_RUSSELL);

        //when
        ModelAndView redirect = controller.submitBumpUpForm(bumpUpFormBean, bindingResult,
                request, EDITOR_ID, pageContext, remoteIP);

        //then
        ArgumentCaptor<? extends ApiCall> callCaptor = ArgumentCaptor.forClass(ApiCall.class);

        verify(apiCallExecutor, atLeast(1)).call(callCaptor.capture());

        java.util.Optional<CreateOrderApiCall> createOrderApiCall = getFirstApiCall(callCaptor);

        assertThat(createOrderApiCall.isPresent(), equalTo(true));

        CreateOrderBean orderBean = createOrderApiCall.get().getCreateOrderBean();
        assertOrder(createOrderApiCall.get(), orderBean, true);

        assertThat(getUrl(redirect), equalTo(PaymentCheckoutController.PAGE_PATH));
        Assertions.assertThat(redirect.getModel()).contains(entry(CHECKOUT_KEY, "ABCD"));

        verify(postAdWorkspace).removeEditor(EDITOR_ID, advertEditor.isCreateMode());
        verify(checkoutContainer).createCheckout(order, ad, true);
        assertMetricValue(DOG, 1, Metric.BREED_SELECTED.name(), JACK_RUSSELL);
    }

    @Test
    public void populatesShoppingCartCorrectlyAndRedirectsToPaymentFlowWhenOrderSucceedsWhenEditingAdAndUserWantsToBumpUp() {
        //given
        setValidAdvertEditor(true);
        setSupportsBumpUp(true);
        bumpUpFormBean.setBumpUp(true);
        mockAdEditorWithBreedAttributes(9390L, CAT_BREED, BRITISH_SHORTHAIR);

        //when
        ModelAndView redirect = controller.submitBumpUpForm(bumpUpFormBean, bindingResult,
                request, EDITOR_ID, pageContext, remoteIP);

        //then
        ArgumentCaptor<? extends ApiCall> callCaptor = ArgumentCaptor.forClass(ApiCall.class);

        verify(apiCallExecutor, atLeast(1)).call(callCaptor.capture());

        java.util.Optional<CreateOrderApiCall> createOrderApiCall = getFirstApiCall(callCaptor);

        assertThat(createOrderApiCall.isPresent(), equalTo(true));

        CreateOrderBean orderBean = createOrderApiCall.get().getCreateOrderBean();
        assertOrder(createOrderApiCall.get(), orderBean, true);

        assertThat(getUrl(redirect), equalTo(PaymentCheckoutController.PAGE_PATH));
        Assertions.assertThat(redirect.getModel()).contains(entry(CHECKOUT_KEY, "ABCD"));

        verify(postAdWorkspace).removeEditor(EDITOR_ID, advertEditor.isCreateMode());
        verify(checkoutContainer).createCheckout(order, ad, true);
        assertMetricValue(CAT, 1, Metric.BREED_SELECTED.name(), BRITISH_SHORTHAIR);
    }

    @Test
    public void populatesShoppingCartCorrectlyAndRedirectsToPaymentFlowWhenOrderSucceedsWhenEditingAdAndUserDoesNotWantToBumpUp() {
        //given
        setValidAdvertEditor(true);
        setSupportsBumpUp(true);
        bumpUpFormBean.setBumpUp(false);
        mockAdEditorWithBreedAttributes(9389L, DOG_BREED, JACK_RUSSELL);

        //when
        controller.submitBumpUpForm(bumpUpFormBean, bindingResult,
                request, EDITOR_ID, pageContext, remoteIP);

        //then
        ArgumentCaptor<? extends ApiCall> callCaptor = ArgumentCaptor.forClass(ApiCall.class);

        verify(apiCallExecutor, atLeast(1)).call(callCaptor.capture());

        java.util.Optional<CreateOrderApiCall> createOrderApiCall = getFirstApiCall(callCaptor);

        assertThat(createOrderApiCall.isPresent(), equalTo(true));

        CreateOrderBean orderBean = createOrderApiCall.get().getCreateOrderBean();
        assertOrder(createOrderApiCall.get(), orderBean);
        assertMetricValue(DOG, 1, Metric.BREED_SELECTED.name(), JACK_RUSSELL);
    }

    private java.util.Optional<CreateOrderApiCall> getFirstApiCall(ArgumentCaptor<? extends ApiCall> callCaptor) {
        return callCaptor.getAllValues().stream().filter(a -> a instanceof CreateOrderApiCall).map(a -> (CreateOrderApiCall) a).findFirst();
    }

    @Test
    public void returnsToBumpUpPageIfEditingNonExpiredAdAndUserHasNotMadeAChoiceOnWhetherToBumpUpOrNot() {
        setValidAdvertEditor(true);
        setSupportsBumpUp(true);
        setBindingValidationFails(true);

        bumpUpFormBean.setBumpUp(null);
        ModelAndView modelAndView = controller.submitBumpUpForm(bumpUpFormBean, bindingResult,
                request, EDITOR_ID, pageContext, remoteIP);

        assertModelObjectsPopulated(modelAndView, bumpUpFormBean);

        assertThat(modelAndView.getViewName(), equalTo(Page.PostAdBumpUp.getTemplateName()));

        assertThat(extractModel(modelAndView, PostAdBumpUpModel.class).getErrors(), notNullValue());
    }


    private void assertOrder(CreateOrderApiCall apiCall, CreateOrderBean orderBean) {
        assertOrder(apiCall, orderBean, false);
    }

    private void assertOrder(CreateOrderApiCall apiCall, CreateOrderBean orderBean, boolean includesBumpUp) {
        assertThat(apiCall.getApiKey(), equalTo(apiKey));
        assertThat(orderBean.getItems().size(), equalTo(includesBumpUp ? 3 : 2));
        assertThat(orderBean.getItems().get(0).getAdvertId(), equalTo(10L));
        assertThat(orderBean.getItems().get(0).getProductName(), equalTo(ProductName.FEATURE_3_DAY));
        assertThat(orderBean.getItems().get(1).getAdvertId(), equalTo(10L));
        assertThat(orderBean.getItems().get(1).getProductName(), equalTo(ProductName.HOMEPAGE_SPOTLIGHT));

        if (includesBumpUp) {
            assertThat(orderBean.getItems().get(2).getAdvertId(), equalTo(10L));
            assertThat(orderBean.getItems().get(2).getProductName(), equalTo(ProductName.BUMP_UP));
        }
    }

    private void setBindingValidationFails(boolean bindingValidationFails) {
        when(bindingResult.hasErrors()).thenReturn(bindingValidationFails);
    }

    private void setOrderFails(boolean orderFails) {
        when(orderApiCallResponse.isErrorResponse()).thenReturn(orderFails);
    }

    private void setRegisterUserFails(boolean registerUserFails) {
        when(registerUserApiCallResponse.isErrorResponse()).thenReturn(registerUserFails);
    }

    private void setPostAdFails(boolean postAdFails) {
        when(postAdApiCallResponse.isErrorResponse()).thenReturn(postAdFails);
    }

    private void setSupportsBumpUp(boolean supportsBumpUp) {
        when(advertEditor.supportsBumpUp()).thenReturn(supportsBumpUp);
    }

    private void setRequiresBumpUp(boolean requiresBumpUp) {
        when(advertEditor.requiresBumpUp()).thenReturn(requiresBumpUp);
    }

    private void setCreateMode(boolean createMode) {
        when(advertEditor.isCreateMode()).thenReturn(createMode);
    }

    private void setValidAdvertEditor(boolean isValid) {
        when(advertEditor.isValid()).thenReturn(isValid);
    }

    private void mockAdEditorWithBreedAttributes(Long category, String param, String value) {
        Map<String, String> attributes = new HashMap<>();
        attributes.put(param, value);
        PostAdFormBean bean = new PostAdFormBean();
        bean.setAttributes(attributes);
        PostAdDetail detail = new PostAdDetail();
        detail.setPostAdFormBean(bean);
        detail.setCategoryId(category);
        when(advertEditor.getAdvertDetail()).thenReturn(detail);
    }

    private void assertMetricValue(String prefix,int expectedValue, String name, String value) {
        Counter counter = customMetricRegistry.counter(prefix + "BreedSelected", name, value);
        Assert.assertEquals(expectedValue, counter.count(), 0.1D);
    }

    private void assertModelObjectsPopulated(ModelAndView mav, BumpUpFormBean formBean) {

        PostAdBumpUpModel model = extractModel(mav, PostAdBumpUpModel.class);

        assertThat(model.isRequiresBumpUp(), equalTo(advertEditor.requiresBumpUp()));
        assertThat(model.getBumpUpFormAction(), equalTo(PostAdBumpupPath.MAPPING.replaceFirst(BasePostAdController.EDITOR_ID_REPLACEMENT, EDITOR_ID)));
        if (formBean != null) {
            assertThat(model.getForm(), equalTo(formBean));
        } else {
            assertThat(model.getForm(), notNullValue());
        }
        assertThat(model.getVerb(), equalTo("someVerb"));
        assertThat(model.getPricingMetadata(), equalTo(pricingMetadata));
        assertThat(model.getBumpUpOptions(), contains(
                new BumpUpOption("Bump my ad to the top of the listings - &pound;2.99", "true"),
                new BumpUpOption("Keep it where it is", "false")
        ));
    }

    private String getUrl(ModelAndView modelAndView) {
        return ((AbstractUrlBasedView) modelAndView.getView()).getUrl();
    }

    private User buildUser(String email) {
        User user = new User();
        user.setEmail(email);
        user.setId(USER_ID);
        return user;
    }

    @Test
    public void callsMetaDataInjectionWithRightArgumentsToPaymentFlowWhenOrderSucceedsWhenEditingExpiredAd() {
        PostAdDetail adDetail = new PostAdDetail();
        adDetail.setStatus(AdStatus.EXPIRED);
        when(advertEditor.getAdvertDetail()).thenReturn(adDetail);

        //given
        setValidAdvertEditor(true);
        setRequiresBumpUp(true);
        setBindingValidationFails(true);

        //when
        ModelAndView redirect = controller.submitBumpUpForm(bumpUpFormBean, bindingResult,
                request, EDITOR_ID, pageContext, remoteIP);

        //then
        ArgumentCaptor<? extends ApiCall> callCaptor = ArgumentCaptor.forClass(ApiCall.class);

        verify(apiCallExecutor, atLeast(1)).call(callCaptor.capture());

        java.util.Optional<CreateOrderApiCall> createOrderApiCall = getFirstApiCall(callCaptor);

        assertThat(getUrl(redirect), equalTo(PaymentCheckoutController.PAGE_PATH));
        Assertions.assertThat(redirect.getModel()).contains(entry(CHECKOUT_KEY, "ABCD"));

        verify(postAdWorkspace).removeEditor(EDITOR_ID, advertEditor.isCreateMode());
        verify(checkoutContainer).createCheckout(order, ad, true);
        verify(checkoutMetaInjector).injectTrackingForEditorUpdate(any(Checkout.class), any(Ad.class), eq(AdStatus.EXPIRED));
    }

    @Test
    public void callsMetaDataInjectionWithRightArgumentsToPaymentFlowWhenOrderSucceedsWhenEditingNonExpiredAd() {
        PostAdDetail adDetail = new PostAdDetail();
        adDetail.setStatus(AdStatus.LIVE);
        Ad adWithStatus = new Ad();
        adWithStatus.setId(10L);
        adWithStatus.setStatus(AdStatus.LIVE);
        when(postAdApiCallResponse.getResponseObject()).thenReturn(adWithStatus);

        when(advertEditor.getAdvertDetail()).thenReturn(adDetail);
        Checkout checkout = mock(Checkout.class);
        when(checkoutContainer.createCheckout(any(),any(),anyBoolean())).thenReturn(checkout);
        when(checkout.getKey()).thenReturn("123");

        //given
        setValidAdvertEditor(true);
        setRequiresBumpUp(true);
        setBindingValidationFails(true);

        //when
        ModelAndView redirect = controller.submitBumpUpForm(bumpUpFormBean, bindingResult,
                request, EDITOR_ID, pageContext, remoteIP);

        //then
        ArgumentCaptor<? extends ApiCall> callCaptor = ArgumentCaptor.forClass(ApiCall.class);

        verify(apiCallExecutor, atLeast(1)).call(callCaptor.capture());

        java.util.Optional<CreateOrderApiCall> createOrderApiCall = getFirstApiCall(callCaptor);

        assertThat(createOrderApiCall.isPresent(), equalTo(true));

        CreateOrderBean orderBean = createOrderApiCall.get().getCreateOrderBean();
        assertOrder(createOrderApiCall.get(), orderBean, true);

        assertThat(getUrl(redirect), equalTo(PaymentCheckoutController.PAGE_PATH));
        Assertions.assertThat(redirect.getModel()).contains(entry(CHECKOUT_KEY, "ABCD"));

        verify(postAdWorkspace).removeEditor(EDITOR_ID, advertEditor.isCreateMode());
        verify(checkoutContainer).createCheckout(order, adWithStatus, true);
        verify(checkoutMetaInjector).injectTrackingForEditorUpdate(any(Checkout.class), any(Ad.class), eq(AdStatus.LIVE));

    }

    @Test
    public void callsMetaDataInjectionWithRightArgumentsToPaymentFlowWhenOrderSucceedsWhenEditingDeletedAd() {
        PostAdDetail adDetail = new PostAdDetail();
        adDetail.setStatus(AdStatus.DELETED_USER);
        when(advertEditor.getAdvertDetail()).thenReturn(adDetail);

        //given
        setValidAdvertEditor(true);
        setRequiresBumpUp(true);
        setBindingValidationFails(true);

        //when
        ModelAndView redirect = controller.submitBumpUpForm(bumpUpFormBean, bindingResult,
                request, EDITOR_ID, pageContext, remoteIP);

        //then
        ArgumentCaptor<? extends ApiCall> callCaptor = ArgumentCaptor.forClass(ApiCall.class);

        verify(apiCallExecutor, atLeast(1)).call(callCaptor.capture());

        java.util.Optional<CreateOrderApiCall> createOrderApiCall = getFirstApiCall(callCaptor);

        assertThat(createOrderApiCall.isPresent(), equalTo(true));

        CreateOrderBean orderBean = createOrderApiCall.get().getCreateOrderBean();
        assertOrder(createOrderApiCall.get(), orderBean, true);

        assertThat(getUrl(redirect), equalTo(PaymentCheckoutController.PAGE_PATH));
        Assertions.assertThat(redirect.getModel()).contains(entry(CHECKOUT_KEY, "ABCD"));

        verify(postAdWorkspace).removeEditor(EDITOR_ID, advertEditor.isCreateMode());
        verify(checkoutContainer).createCheckout(order, ad, true);
        verify(checkoutMetaInjector).injectTrackingForEditorUpdate(any(Checkout.class), any(Ad.class), eq(AdStatus.DELETED_USER));

    }

    @Test
    public void submitBumpUpFormCallsMetaDataInjectionWithAdStatus() throws IOException {
        PostAdDetail adDetail = new PostAdDetail();
        adDetail.setStatus(AdStatus.DELETED_USER);
        when(advertEditor.getAdvertDetail()).thenReturn(adDetail);

        //given
        setValidAdvertEditor(true);

        when(advertEditor.isValid()).thenReturn(true);
        when(advertEditor.supportsBumpUp()).thenReturn(false);
        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        verify(checkoutContainer).createCheckout(order, ad, true);
        verify(checkoutMetaInjector).injectTrackingForEditorUpdate(any(Checkout.class), any(Ad.class), eq(AdStatus.DELETED_USER));
    }

    @Test
    public void submitBumpUpFormAjaxCallsMetaDataInjectionWithAdStatus() throws IOException {
        PostAdDetail adDetail = new PostAdDetail();
        adDetail.setStatus(AdStatus.DELETED_USER);
        when(advertEditor.getAdvertDetail()).thenReturn(adDetail);

        //given
        setValidAdvertEditor(true);

        when(advertEditor.isValid()).thenReturn(true);
        when(advertEditor.supportsBumpUp()).thenReturn(false);
        //when
        ModelAndView redirect = controller.viewBumpUpPage(request, response, EDITOR_ID, false, pageContext, remoteIP);

        //then
        verify(checkoutContainer).createCheckout(order, ad, true);
        verify(checkoutMetaInjector).injectTrackingForEditorUpdate(any(Checkout.class), any(Ad.class), eq(AdStatus.DELETED_USER));

    }
}
