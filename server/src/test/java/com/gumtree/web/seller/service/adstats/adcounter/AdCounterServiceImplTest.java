package com.gumtree.web.seller.service.adstats.adcounter;

import com.gumtree.adcounters.CountersApi;
import com.gumtree.adcounters.model.SearchRequest;
import com.gumtree.google.authservice.GoogleAuthService;
import com.gumtree.util.UuidProvider;
import org.apache.commons.lang.StringUtils;
import org.hamcrest.collection.IsMapContaining;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import rx.Single;
import rx.observers.TestSubscriber;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeoutException;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Matchers.isNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AdCounterServiceImplTest {

    @Mock
    private GoogleAuthService googleAuthService;
    @Mock
    private CountersApi countersApi;

    @Mock
    private UuidProvider uuidProvider;

    private AdCounterServiceImpl adCounterService;

    private static final Long AD1_ID = 20025L;
    private static final Long AD2_ID = 30021L;
    private List<Long> adIds;
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");

    @Before
    public void setup() {
        when(googleAuthService.getAuthKey()).thenReturn("valid-auth-token");
        when(uuidProvider.uuid()).thenReturn(UUID.nameUUIDFromBytes("valid-uuid".getBytes()));
        adCounterService = new AdCounterServiceImpl(countersApi, googleAuthService, uuidProvider);
    }

    @Test
    public void shouldReturnSingleOfMapsForValidListOfIds() {

        ArgumentCaptor<SearchRequest> searchRequestArgumentCaptor = ArgumentCaptor.forClass(SearchRequest.class);

        //given
        adIds = Arrays.asList(1L, 2L);
        int numberOfViews = 5;
        Map<String, Integer> cdataAdsResponse = new HashMap<>();
        cdataAdsResponse.put("1.views.total", numberOfViews);
        cdataAdsResponse.put("2.views.total", numberOfViews);
        cdataAdsResponse.put("1.impressions.total", numberOfViews);
        cdataAdsResponse.put("2.impressions.total", numberOfViews);

        List<String> expectedIncludeList = Arrays.asList(
                "1.views.total",
                "2.views.total",
                "1.impressions.total",
                "2.impressions.total",
                "1.replies.total",
                "2.replies.total"
        );

        String expectedAdCorrelationId = generateAdCorrelationId(adIds);
        String expectedAdCountersToken = googleAuthService.getAuthKey();

        //when
        when(countersApi.searchCounters(searchRequestArgumentCaptor.capture(), eq(expectedAdCorrelationId), isNull(String.class), eq(expectedAdCountersToken)))
                .thenReturn(Single.just(cdataAdsResponse));

        Map<String, Integer> map = adCounterService.getsCountersFromCDataOrTsaAdCounters(adIds).toBlocking().value();
        //then
        assertThat(map.size(), is(4));
        assertThat(map, IsMapContaining.hasKey("1.views.total"));
        assertThat(map, IsMapContaining.hasEntry("1.views.total", numberOfViews));
        assertThat(searchRequestArgumentCaptor.getValue().getInclude(), containsInAnyOrder(expectedIncludeList.toArray()));
    }

    @Test
    public void shouldReturnEmptyDataDataForFailureInAdCounters() {
        //given
        adIds = Arrays.asList(1L, 2L);

        //when
        when(countersApi.searchCounters(any(), any(), any(), any())).thenReturn(Single.error(new Exception()));


        Map<String, Integer> map = adCounterService.getsCountersFromCDataOrTsaAdCounters(adIds).toBlocking().value();
        //then
        assertThat(map.size(), is(0));
    }

    @Test
    public void shouldReturnEmptyMapForBothApiException() {
        //given
        adIds = Arrays.asList(3L, 4L);
        TestSubscriber<Map<String, Integer>> testSubscriber = TestSubscriber.create();
        //when
        when(countersApi.searchCounters(any(SearchRequest.class), anyString(), isNull(String.class), anyString()))
                .thenReturn(Single.error(new Exception()));

        adCounterService.getsCountersFromCDataOrTsaAdCounters(adIds).subscribe(testSubscriber);
        //then
        testSubscriber.assertValue(new HashMap<>());
    }

    @Test
    public void shouldReturnTotalCountersForAdvertList() {

        ArgumentCaptor<SearchRequest> searchRequestArgumentCaptor = ArgumentCaptor.forClass(SearchRequest.class);
        List<Long> advertIds = Arrays.asList(AD1_ID, AD2_ID);
        SearchRequest expectedAdCountersRequest = generateTotalSearchRequest();
        String expectedAdCorrelationId = generateAdCorrelationId(advertIds);
        String expectedAdCountersToken = googleAuthService.getAuthKey();

        when(countersApi.searchCounters(searchRequestArgumentCaptor.capture(), eq(expectedAdCorrelationId), isNull(String.class), eq(expectedAdCountersToken)))
                .thenReturn(generateTotalResponse());
        Map<Long, AdCounters> advertCounters = adCounterService.getAdvertCounters(advertIds);
        assertThat(advertCounters.size(), is(2));
        assertThat(advertCounters.get(AD1_ID).getViewsCounter(), is(222)); //CData
        assertThat(advertCounters.get(AD1_ID).getSearchImpressionCounter(), is(333)); //Ad-Counters
        assertThat(advertCounters.get(AD2_ID).getSearchImpressionCounter(), is(444)); //Ad-Counters
        assertThat(advertCounters.get(AD2_ID).getRepliesCount(), is(777)); //StatsAPI
        assertThat(searchRequestArgumentCaptor.getValue().getInclude(), containsInAnyOrder(expectedAdCountersRequest.getInclude().toArray()));
    }

    @Test
    public void shouldReturnDefaultValuesForBothException() {
        List<Long> advertIds = Arrays.asList(AD1_ID, AD2_ID);
        when(countersApi.searchCounters(any(SearchRequest.class), anyString(), isNull(String.class), anyString()))
                .thenReturn(Single.error(new TimeoutException("API Request Failed"))); // Gumtree AdCounters API returns exception
        Map<Long, AdCounters> advertCounters = adCounterService.getAdvertCounters(advertIds);
        assertThat(advertCounters.size(), is(2));

        assertThat(advertCounters.get(AD1_ID).getViewsCounter(), is(0)); //Default
        assertThat(advertCounters.get(AD1_ID).getSearchImpressionCounter(), is(0)); //Default
        assertThat(advertCounters.get(AD1_ID).getRepliesCount(), is(0)); //Default
    }

    @Test
    public void shouldReturnDailyAdvertStats() {
        //given

        LocalDate now = LocalDate.now();
        Runnable assertThatAdCountersRequestContainsCompleteIncludeList = stubAdCountersDailyStatsResponse(10);

        //when
        Map<String, AdCounters> adCounters = adCounterService.getAdvertCounters(1000330512L, null);

        //then
        assertThat(adCounters.size(), is(10));
        assertThat(adCounters.get(formatter.format(now.minusDays(5))).getViewsCounter(), is(5));
        assertThat(adCounters.get(formatter.format(now.minusDays(7))).getViewsCounter(), is(7));
        assertThat(adCounters.get(formatter.format(now.minusDays(15))), nullValue());
        assertThatAdCountersRequestContainsCompleteIncludeList.run();
    }

    @Test
    public void shouldReturnDailyAdvertStatsFor2Days() {

        //given
        LocalDate now = LocalDate.now();
        stubAdCountersDailyStatsResponse(2);
        //when
        Map<String, AdCounters> adCounters = adCounterService.getAdvertCounters(1000330512L, 2);

        //then
        assertThat(adCounters.size(), is(2));
        assertThat(adCounters.get(formatter.format(now.minusDays(0))).getViewsCounter(), is(0));
        assertThat(adCounters.get(formatter.format(now.minusDays(1))).getViewsCounter(), is(1));
        assertThat(adCounters.get(formatter.format(now.minusDays(5))), nullValue());
    }

    @Test
    public void shouldHandleErrorsForDailyStats() {

        //given
        Long adId = 1000330512L;
        LocalDate now = LocalDate.now();
        List<String> expectedAdCountersRequest = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            String key = "1000330512.views.daily." + formatter.format(LocalDate.now().minusDays(i));
            expectedAdCountersRequest.add(key);
            key = "1000330512.impressions.daily." + formatter.format(LocalDate.now().minusDays(i));
            expectedAdCountersRequest.add(key);
            key = "1000330512.replies.daily." + formatter.format(LocalDate.now().minusDays(i));
            expectedAdCountersRequest.add(key);
        }
        String expectedAdCorrelationId = generateAdCorrelationId(Collections.singletonList(adId));
        String expectedAdCountersToken = googleAuthService.getAuthKey();
        ArgumentCaptor<SearchRequest> searchRequestArgumentCaptor = ArgumentCaptor.forClass(SearchRequest.class);

        //when
        when(countersApi.searchCounters(searchRequestArgumentCaptor.capture(), eq(expectedAdCorrelationId), isNull(String.class), eq(expectedAdCountersToken)))
                .thenReturn(Single.error(new TimeoutException("API Request Failed")));

        Map<String, AdCounters> adCounters = adCounterService.getAdvertCounters(adId, 2);

        //then
        assertThat(adCounters.size(), is(2));
        assertThat(adCounters.get(formatter.format(now.minusDays(0))).getViewsCounter(), is(0));
        assertThat(adCounters.get(formatter.format(now.minusDays(1))).getViewsCounter(), is(0));
        assertThat(searchRequestArgumentCaptor.getValue().getInclude(), containsInAnyOrder(expectedAdCountersRequest.toArray()));
    }

    private SearchRequest generateTotalSearchRequest() {
        List<String> includes = Arrays.asList(
                "20025.views.total",
                "20025.impressions.total",
                "20025.replies.total",
                "30021.views.total",
                "30021.impressions.total",
                "30021.replies.total"
        );
        return new SearchRequest().include(includes);
    }

    private Single<Map<String, Integer>> generateTotalResponse() {
        Map<String, Integer> counterMap = new HashMap<>();
        counterMap.put("20025.views.total", 222);
        counterMap.put("30021.views.total", 111);
        counterMap.put("20025.impressions.total", 333);
        counterMap.put("30021.impressions.total", 444);
        counterMap.put("20025.replies.total", 555);
        counterMap.put("30021.replies.total", 777);
        return Single.just(counterMap);
    }

    private Runnable stubAdCountersDailyStatsResponse(int noOfDays) {
        List<String> expectedAdCountersRequest = new ArrayList<>();
        Map<String, Integer> cdataAdsResponse = new HashMap<>();
        for (int i = 0; i < noOfDays; i++) {
            String key = "1000330512.views.daily." + formatter.format(LocalDate.now().minusDays(i));
            expectedAdCountersRequest.add(key);
            cdataAdsResponse.put(key, i);

            key = "1000330512.impressions.daily." + formatter.format(LocalDate.now().minusDays(i));
            expectedAdCountersRequest.add(key);
            cdataAdsResponse.put(key, i);

            key = "1000330512.replies.daily." + formatter.format(LocalDate.now().minusDays(i));
            expectedAdCountersRequest.add(key);
            cdataAdsResponse.put(key, i);
        }

        ArgumentCaptor<SearchRequest> searchRequestArgumentCaptor = ArgumentCaptor.forClass(SearchRequest.class);
        String expectedAdCorrelationId = generateAdCorrelationId(Collections.singletonList(1000330512L));
        String expectedAdCountersToken = googleAuthService.getAuthKey();

        when(countersApi.searchCounters(searchRequestArgumentCaptor.capture(), eq(expectedAdCorrelationId), isNull(String.class), eq(expectedAdCountersToken)))
                .thenReturn(Single.just(cdataAdsResponse));

        return () -> assertThat(searchRequestArgumentCaptor.getValue().getInclude(), containsInAnyOrder(expectedAdCountersRequest.toArray()));
    }

    private String generateAdCorrelationId(List<Long> adId) {
        String uuid = UUID.nameUUIDFromBytes("valid-uuid".getBytes()).toString();
        return StringUtils.join(adId, "-") + "-" + "seller" + "-" + uuid;
    }
}
