package com.gumtree.web.seller.security;

import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.ManageAdsController;
import com.gumtree.web.seller.page.postad.controller.PostAdBumpUpController;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.HttpServletRequest;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 */
public class SuperUserAccountOverrideInterceptorTest {

    private UserSession userSession;

    private HttpServletRequest request;

    private SuperUserAccountOverrideInterceptor interceptor;

    private HandlerMethod handlerMethod;

    @Before
    public void init() {
        userSession = mock(UserSession.class);
        request = mock(HttpServletRequest.class);
        interceptor = new SuperUserAccountOverrideInterceptor();
        handlerMethod = mock(HandlerMethod.class);
        Class clazz = ManageAdsController.class;
        when(handlerMethod.getBeanType()).thenReturn(clazz);
        ReflectionTestUtils.setField(interceptor, "userSession", userSession);
    }

    @Test
    public void whenNotManageAdsControllerThenNoActivityShouldOccur() throws Exception {
        Class clazz = PostAdBumpUpController.class;
        when(handlerMethod.getBeanType()).thenReturn(clazz);
        assertThat(interceptor.preHandle(request, null, handlerMethod), equalTo(true));
        verifyZeroInteractions(userSession);
        verifyZeroInteractions(request);
    }

    @Test
    public void whenUserIsASuperUserAndNumericAccountIdIsSpecifiedInRequestThenSelectedAccountIdShouldBeSet() throws Exception {
        when(userSession.isSuperUser()).thenReturn(true);
        when(request.getParameter("accountId")).thenReturn("1500");
        assertThat(interceptor.preHandle(request, null, handlerMethod), equalTo(true));
        verify(userSession).isSuperUser();
        verify(userSession).setSuperUserOverrideAccountId(1500L);
        verifyNoMoreInteractions(userSession);
    }

    @Test
    public void whenUserIsNotASuperUserAndNumericAccountIdIsSpecifiedInRequestThenSelectedAccountIdShouldNotBeSet() throws Exception {
        when(userSession.isSuperUser()).thenReturn(false);
        when(request.getParameter("accountId")).thenReturn("1500");
        assertThat(interceptor.preHandle(request, null, handlerMethod), equalTo(true));
        verify(userSession).isSuperUser();
        verify(userSession, never()).setSuperUserOverrideAccountId(anyLong());
        verifyNoMoreInteractions(userSession);
    }

    @Test
    public void whenUserIsASuperUserAndAccountIdIsNotSpecifiedInRequestThenSelectedAccountIdShouldNotBeSet() throws Exception {
        when(userSession.isSuperUser()).thenReturn(true);
        assertThat(interceptor.preHandle(request, null, handlerMethod), equalTo(true));
        verify(userSession).isSuperUser();
        verify(userSession, never()).setSuperUserOverrideAccountId(anyLong());
        verifyNoMoreInteractions(userSession);
    }

    @Test
    public void whenUserIsNotASuperUserAndAccountIdIsNotSpecifiedInRequestThenSelectedAccountIdShouldNotBeSet() throws Exception {
        when(userSession.isSuperUser()).thenReturn(false);
        assertThat(interceptor.preHandle(request, null, handlerMethod), equalTo(true));
        verify(userSession).isSuperUser();
        verify(userSession, never()).setSuperUserOverrideAccountId(anyLong());
        verifyNoMoreInteractions(userSession);
    }

    @Test
    public void whenUserIsASuperUserAndNonNumericAccountIdIsSpecifiedInRequestThenSelectedAccountIdShouldNotBeSet() throws Exception {
        when(userSession.isSuperUser()).thenReturn(false);
        when(request.getParameter("accountId")).thenReturn("non-numeric");
        assertThat(interceptor.preHandle(request, null, handlerMethod), equalTo(true));
        verify(userSession).isSuperUser();
        verify(userSession, never()).setSuperUserOverrideAccountId(anyLong());
        verifyNoMoreInteractions(userSession);
    }
}
