package com.gumtree.motors.webapi.controller;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.gumtree.api.Image;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.motors.webapi.service.MotorsService;
import com.gumtree.motorssyi.client.model.CreateMotorAdRequest;
import com.gumtree.motorssyi.client.model.VrmDisplayAttribute;
import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.common.ip.DefaultRemoteIP;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie<PERSON>utter;
import com.gumtree.web.seller.page.manageads.model.CheckoutAdvert;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.page.payment.controller.PaymentCheckoutController;
import com.gumtree.web.seller.page.postad.model.location.PostcodeLookupResponse;
import com.gumtree.web.seller.page.postad.model.location.PostcodeSelectionState;
import com.gumtree.web.seller.service.image.ImageUploadService;
import com.gumtree.web.seller.service.location.PostAdLocationService;
import javaslang.Tuple;
import javaslang.Tuple2;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import rx.Single;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.gumtree.mobile.test.Fixtures.AUDI;
import static com.gumtree.mobile.test.Fixtures.BMW;
import static com.gumtree.mobile.test.Fixtures.createVehicleMake;
import static com.gumtree.mobile.test.Fixtures.createVehicleRegistrationYear;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildInternalError;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildMotorAdFail;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildMotorAdSuccess;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildMotorUploadImageFail;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildMotorUploadImageSuccess;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildPostCodeLookUpApiResponse;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildVrmAttributeDropDownValue;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildVrmDisplayAttribute;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.buildVrmLookupApiResponse;
import static com.gumtree.motors.webapi.converter.MotorsSYIConverters.convertToMotorsAd;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;

@RunWith(MockitoJUnitRunner.class)
public class MotorsSYIWebApiControllerTest {

    @InjectMocks
    MotorsSYIWebApiController motorsSYIWebApiController;

    @Mock
    private PostAdLocationService locationService;

    @Mock
    private CookieResolver cookieResolver;

    @Mock
    private MotorsService motorsService;

    @Mock
    private HttpServletRequest request;

    @Mock
    private ImageUploadService imageUploadService;

    @Before
    public void setUp() {
    }

    @Test
    public void shouldValidatePostCodeIfValid() {
        //        Given
        String postcode = "TW91Dh";

        PostcodeLookupResponse lookupResponse = new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_RECOGNISED, postcode);
        when(locationService.lookupPostcode(postcode)).thenReturn(lookupResponse);

        //        When
        ResponseEntity response = motorsSYIWebApiController.validatePostCode(postcode);

        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(buildPostCodeLookUpApiResponse(postCodeLookUpApiResponse -> {
            postCodeLookUpApiResponse.setIsValid(true);
            postCodeLookUpApiResponse.setPostcode(postcode);
        }));
    }

    @Test
    public void shouldValidatePostCodeIfInvalid() {
        //        Given
        String postcode = "TW91Dhsadadd";

        PostcodeLookupResponse lookupResponse = new PostcodeLookupResponse(PostcodeSelectionState.POSTCODE_INVALID, postcode);
        when(locationService.lookupPostcode(postcode)).thenReturn(lookupResponse);

        //        When
        ResponseEntity response = motorsSYIWebApiController.validatePostCode(postcode);

        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(buildPostCodeLookUpApiResponse(postCodeLookUpApiResponse -> {
            postCodeLookUpApiResponse.setIsValid(false);
            postCodeLookUpApiResponse.setPostcode(postcode);
        }));
    }


    @Test
    public void shouldGetSimpleVrmAttributes() {
        //        Given
        String vrm = "W90MOG";
        long categoryId = 9311L;

        ArrayList<AttributeMetadata> attributeMetadata = Lists.newArrayList(createVehicleRegistrationYear().build());
        ImmutableMap<String, String> vehiclesValues = ImmutableMap.of(
                "vehicle_registration_year", "2015");
        Tuple2<List<AttributeMetadata>, Map<String, String>> vrmAttributes = Tuple.of(attributeMetadata, vehiclesValues);
        when(motorsService.getVrmAttributes(vrm, categoryId)).thenReturn(vrmAttributes);

        //        When
        ResponseEntity response = motorsSYIWebApiController.getVrmAttributes(vrm, categoryId);


        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(buildVrmLookupApiResponse(vrmLookupApiResponse -> {
            VrmDisplayAttribute vrmDisplayAttribute1 = buildVrmDisplayAttribute(vrmDisplayAttribute -> {
                vrmDisplayAttribute.setName("vehicle_registration_year");
                vrmDisplayAttribute.setLabel("Age");
                vrmDisplayAttribute.isRequired(false);
                vrmDisplayAttribute.setType(AttributeType.YEAR.name());
                vrmDisplayAttribute.setValue("2015");
            });
            vrmLookupApiResponse.setAttributes(Arrays.asList(vrmDisplayAttribute1));
        }));
    }

    @Test
    public void shouldGetVrmAttributesWithNullValuesIfNoVehiclesValuesAvailable() {
        //        Given
        String vrm = "W90MOG";
        long categoryId = 9311L;

        ArrayList<AttributeMetadata> attributeMetadata = Lists.newArrayList(createVehicleRegistrationYear().build());
        Map<String, String> vehiclesValues = Collections.emptyMap();
        Tuple2<List<AttributeMetadata>, Map<String, String>> vrmAttributes = Tuple.of(attributeMetadata, vehiclesValues);
        when(motorsService.getVrmAttributes(vrm, categoryId)).thenReturn(vrmAttributes);

        //        When
        ResponseEntity response = motorsSYIWebApiController.getVrmAttributes(vrm, categoryId);


        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(buildVrmLookupApiResponse(vrmLookupApiResponse -> {
            VrmDisplayAttribute aVrmDisplayAttribute = buildVrmDisplayAttribute(vrmDisplayAttribute -> {
                vrmDisplayAttribute.setName("vehicle_registration_year");
                vrmDisplayAttribute.setLabel("Age");
                vrmDisplayAttribute.isRequired(false);
                vrmDisplayAttribute.setType(AttributeType.YEAR.name());
                vrmDisplayAttribute.setValue(null);
            });
            vrmLookupApiResponse.setAttributes(Arrays.asList(aVrmDisplayAttribute));
        }));
    }

    @Test
    public void shouldGetVrmAttributesWithDropDownValues() {
        //        Given
        String vrm = "W90MOG";
        long categoryId = 9311L;

        ArrayList<AttributeMetadata> attributeMetadata = Lists.newArrayList(createVehicleMake(AUDI, BMW).build());
        ImmutableMap<String, String> vehiclesValues = ImmutableMap.of("vehicle_make", "audi");
        Tuple2<List<AttributeMetadata>, Map<String, String>> vrmAttributes = Tuple.of(attributeMetadata, vehiclesValues);
        when(motorsService.getVrmAttributes(vrm, categoryId)).thenReturn(vrmAttributes);

        //        When
        ResponseEntity response = motorsSYIWebApiController.getVrmAttributes(vrm, categoryId);


        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(buildVrmLookupApiResponse(vrmLookupApiResponse -> {
            VrmDisplayAttribute aVrmDisplayAttribute = buildVrmDisplayAttribute(vrmDisplayAttribute -> {
                vrmDisplayAttribute.setName("vehicle_make");
                vrmDisplayAttribute.setLabel("Make");
                vrmDisplayAttribute.isRequired(false);
                vrmDisplayAttribute.setType(AttributeType.ENUM.name());
                vrmDisplayAttribute.setValue("audi");
                vrmDisplayAttribute.setOptions(Arrays.asList(
                        buildVrmAttributeDropDownValue(vrmAttributeDropDownValue -> {
                            vrmAttributeDropDownValue.setName("audi");
                            vrmAttributeDropDownValue.setLabel("Audi");
                            vrmAttributeDropDownValue.setIsSelected(true);
                        }),
                        buildVrmAttributeDropDownValue(vrmAttributeDropDownValue -> {
                                    vrmAttributeDropDownValue.setName("bmw");
                                    vrmAttributeDropDownValue.setLabel("BMW");
                                    vrmAttributeDropDownValue.setIsSelected(false);
                                }
                        )));
            });
            vrmLookupApiResponse.setAttributes(Arrays.asList(aVrmDisplayAttribute));
        }));
    }

    @Test
    public void shouldBeAbleToPostANewMotorAd() {
        //        Given
        CreateMotorAdRequest createMotorAdRequest = givenDefaultMotorAdRequest();
        PermanentCookie permanentCookie = givenPermanentCookie();
        ThreatMetrixCookie threatMetrixCookie = givenThreatMetrixCookie();
        DefaultRemoteIP remoteIP = new DefaultRemoteIP("127.0.0.1");
        String checkoutKey = givenCreateMotorAdSuccess(createMotorAdRequest, permanentCookie, remoteIP,
                threatMetrixCookie.getDefaultValue());

        //        When
        ResponseEntity response = motorsSYIWebApiController.postMotorsAd(createMotorAdRequest, remoteIP,
                request);

        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(buildMotorAdSuccess(motorAdSuccess -> {
            String redirect = "/" + PaymentCheckoutController.VIEW_NAME + "/" + checkoutKey;
            motorAdSuccess.setRedirect(redirect);
        }));
    }

    @Test
    public void shouldReturnAnewMotorAdFailIfValidationFailed() {
        //        Given
        CreateMotorAdRequest createMotorAdRequest = givenDefaultMotorAdRequest();
        PermanentCookie permanentCookie = givenPermanentCookie();
        DefaultRemoteIP remoteIP = new DefaultRemoteIP("127.0.0.1");
        ThreatMetrixCookie threatMetrixCookie = givenThreatMetrixCookie();
        WebApiErrorException webApiErrorException = givenCreateMotorAdFailBecauseOfValidation(createMotorAdRequest,
                permanentCookie, remoteIP, threatMetrixCookie);

        //        When
        ResponseEntity response = motorsSYIWebApiController.postMotorsAd(createMotorAdRequest, remoteIP,
                request);

        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isEqualTo(buildMotorAdFail(motorAdFail -> {
            WebApiErrorResponse error = webApiErrorException.getError();
            motorAdFail.setCode(error.getCode());
            motorAdFail.setMessage(error.getMessage());
            motorAdFail.setErrors(Collections.emptyList());
        }));
    }

    @Test
    public void shouldReturnAnInternalErrorOnCreatingAdIfServiceInternalServerError() {
        //        Given
        CreateMotorAdRequest createMotorAdRequest = givenDefaultMotorAdRequest();
        PermanentCookie permanentCookie = givenPermanentCookie();
        DefaultRemoteIP remoteIP = new DefaultRemoteIP("127.0.0.1");
        ThreatMetrixCookie threatMetrixCookie = givenThreatMetrixCookie();
        WebApiErrorException webApiErrorException = givenCreateMotorAdFailBecauseOfInternalError(createMotorAdRequest,
                permanentCookie, remoteIP, threatMetrixCookie);

        //        When
        ResponseEntity response = motorsSYIWebApiController.postMotorsAd(createMotorAdRequest, remoteIP,
                request);

        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody()).isEqualTo(buildInternalError(internalError -> {
            WebApiErrorResponse error = webApiErrorException.getError();
            internalError.setErrorCode(error.getCode());
            internalError.setErrorDetail(error.getMessage());
            internalError.setUserMessage("Unexpected error");
        }));
    }

    @Test
    public void shouldReturnAnInternalErrorOnCreatingIfThrowable() {
        //        Given
        CreateMotorAdRequest createMotorAdRequest = givenDefaultMotorAdRequest();
        PermanentCookie permanentCookie = givenPermanentCookie();
        DefaultRemoteIP remoteIP = new DefaultRemoteIP("127.0.0.1");
        ThreatMetrixCookie threatMetrixCookie = givenThreatMetrixCookie();
        givenCreateMotorAdFailBecauseOfUncheckedException(createMotorAdRequest, permanentCookie, remoteIP, threatMetrixCookie);

        //        When
        ResponseEntity response = motorsSYIWebApiController.postMotorsAd(createMotorAdRequest, remoteIP,
                request);

        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody()).isEqualTo(buildInternalError(internalError -> {
            internalError.setErrorCode(INTERNAL_SERVER_ERROR.getReasonPhrase());
            internalError.setErrorDetail("internal-error");
            internalError.setUserMessage("Something went wrong");
        }));
    }

    @Test
    public void shouldBeAbleToUploadAnImage() {
        //        Given
        MultipartFile imageFile = mock(MultipartFile.class);
        when(imageFile.getOriginalFilename()).thenReturn("shinyNewImage.jpg");
        Image image = givenUploadImageServiceOk(imageFile);

        //        When
        ResponseEntity response = motorsSYIWebApiController.uploadImage(imageFile);

        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo(buildMotorUploadImageSuccess(motorUploadImageSuccess -> {
            motorUploadImageSuccess.setFileName(imageFile.getOriginalFilename());
            motorUploadImageSuccess.setId(image.getId());
            motorUploadImageSuccess.setUrl(image.getUrl());
            motorUploadImageSuccess.setSize(image.getSize());
            motorUploadImageSuccess.setThumbnailUrl(image.getThumbnailUrl());
        }));
    }

    @Test
    public void shouldReturnValidationErrorOnImageUploadIfValidationErrorInService() {
        //        Given
        MultipartFile imageFile = mock(MultipartFile.class);
        when(imageFile.getOriginalFilename()).thenReturn("shinyNewImage.jpg");
        WebApiErrorResponse webApiErrorResponse = givenUploadImageServiceKoValidationError(imageFile);

        //        When
        ResponseEntity response = motorsSYIWebApiController.uploadImage(imageFile);

        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isEqualTo(buildMotorUploadImageFail(motorUploadImageFail -> {
            motorUploadImageFail.setCode(webApiErrorResponse.getCode());
            motorUploadImageFail.setMessage(webApiErrorResponse.getMessage());
        }));
    }

    @Test
    public void shouldReturnInternalErrorIfImageUploadServiceKoForInternalError() {
        //        Given
        MultipartFile imageFile = mock(MultipartFile.class);
        when(imageFile.getOriginalFilename()).thenReturn("shinyNewImage.jpg");
        WebApiErrorResponse webApiErrorResponse = givenUploadImageServiceKoInternalError(imageFile);

        //        When
        ResponseEntity response = motorsSYIWebApiController.uploadImage(imageFile);

        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody()).isEqualTo(buildInternalError(internalError -> {
            internalError.setErrorDetail(webApiErrorResponse.getMessage());
            internalError.setErrorCode(webApiErrorResponse.getCode());
            internalError.setUserMessage("Unexpected error");
        }));
    }

    @Test
    public void shouldReturnInternalErrorIfUnsupportedExceptionWhenUploadImage() {
        //        Given
        MultipartFile imageFile = mock(MultipartFile.class);
        when(imageFile.getOriginalFilename()).thenReturn("shinyNewImage.jpg");
        givenUploadImageServiceUnsupportedException(imageFile);

        //        When
        ResponseEntity response = motorsSYIWebApiController.uploadImage(imageFile);

        //        Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody()).isEqualTo(buildInternalError(internalError -> {
            internalError.setErrorCode(INTERNAL_SERVER_ERROR.getReasonPhrase());
            internalError.setErrorDetail("internal-error");
            internalError.setUserMessage("Something went wrong");
        }));
    }

    private Image givenUploadImageServiceOk(MultipartFile imageFile) {
        Image image = new Image();
        image.setId(5L);
        image.setUrl("https://epssomething.com/myimage");
        image.setSize("54");
        image.setThumbnailUrl("https://epssomething.com/myThumbnail");
        when(imageUploadService.uploadImageRX(imageFile)).thenReturn(Single.just(image));
        return image;
    }

    private WebApiErrorResponse givenUploadImageServiceKoValidationError(MultipartFile imageFile) {
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.BAD_REQUEST,
                "102", "validation failed");
        WebApiErrorException webApiErrorException = new WebApiErrorException(webApiErrorResponse);
        when(imageUploadService.uploadImageRX(imageFile)).thenReturn(Single.error(webApiErrorException));
        return webApiErrorResponse;
    }

    private WebApiErrorResponse givenUploadImageServiceKoInternalError(MultipartFile imageFile) {
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR,
                HttpStatus.INTERNAL_SERVER_ERROR.toString(), "internal error innit");
        WebApiErrorException webApiErrorException = new WebApiErrorException(webApiErrorResponse);
        when(imageUploadService.uploadImageRX(imageFile)).thenReturn(Single.error(webApiErrorException));
        return webApiErrorResponse;
    }

    private void givenUploadImageServiceUnsupportedException(MultipartFile imageFile) {
        when(imageUploadService.uploadImageRX(imageFile)).thenReturn(Single.error(new RuntimeException("I'm a bad exception")));
    }

    private String givenCreateMotorAdSuccess(CreateMotorAdRequest createMotorAdRequest, PermanentCookie permanentCookie,
                                             DefaultRemoteIP remoteIP, String threatmetrixSessionId) {
        CheckoutImpl checkout = new CheckoutImpl();
        String checkoutKey = "5478547";
        checkout.setKey(checkoutKey);
        CheckoutAdvert advert = CheckoutAdvert.Builder.builder().id(54L).build();
        checkout.setAdvert(advert);
        when(motorsService.createMotorAd(convertToMotorsAd(createMotorAdRequest), remoteIP
                .getIpAddress(), permanentCookie, threatmetrixSessionId)).thenReturn(Single.just(checkout));
        return checkoutKey;
    }

    private WebApiErrorException givenCreateMotorAdFailBecauseOfValidation(CreateMotorAdRequest createMotorAdRequest,
                                                                           PermanentCookie permanentCookie,
                                                                           DefaultRemoteIP remoteIP,
                                                                           ThreatMetrixCookie threatMetrixCookie) {
        CheckoutImpl checkout = new CheckoutImpl();
        String checkoutKey = "5478547";
        checkout.setKey(checkoutKey);
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.BAD_REQUEST, "validation error", "validation " +
                "error " +
                "message");
        WebApiErrorException exception = new WebApiErrorException(webApiErrorResponse);
        when(motorsService.createMotorAd(convertToMotorsAd(createMotorAdRequest), remoteIP
                .getIpAddress(), permanentCookie, threatMetrixCookie.getDefaultValue())).thenReturn(Single.error(exception));
        return exception;
    }

    private WebApiErrorException givenCreateMotorAdFailBecauseOfInternalError(CreateMotorAdRequest createMotorAdRequest,
                                                                              PermanentCookie permanentCookie,
                                                                              DefaultRemoteIP remoteIP,
                                                                              ThreatMetrixCookie threatMetrixCookie) {
        CheckoutImpl checkout = new CheckoutImpl();
        String checkoutKey = "5478547";
        checkout.setKey(checkoutKey);
        WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "validation error",
                "validation error " +
                        "message");
        WebApiErrorException exception = new WebApiErrorException(webApiErrorResponse);
        when(motorsService.createMotorAd(convertToMotorsAd(createMotorAdRequest), remoteIP
                .getIpAddress(), permanentCookie, threatMetrixCookie.getDefaultValue())).thenReturn(Single.error(exception));
        return exception;
    }

    private void givenCreateMotorAdFailBecauseOfUncheckedException(CreateMotorAdRequest createMotorAdRequest,
                                                                   PermanentCookie permanentCookie,
                                                                   DefaultRemoteIP remoteIP,
                                                                   ThreatMetrixCookie threatMetrixCookie) {
        when(motorsService.createMotorAd(convertToMotorsAd(createMotorAdRequest), remoteIP
                .getIpAddress(), permanentCookie, threatMetrixCookie.getDefaultValue()))
                .thenReturn(Single.error(new RuntimeException("I'm a bad exception")));
    }

    private PermanentCookie givenPermanentCookie() {
        PermanentCookie permanentCookie = new PermanentCookie("defaultDomain", 5686665, "");
        when(cookieResolver.resolve(request, PermanentCookie.class)).thenReturn(permanentCookie);
        return permanentCookie;
    }

    private ThreatMetrixCookie givenThreatMetrixCookie() {
        ThreatMetrixCookie threatMetrixCookie = new ThreatMetrixCookieCutter("domain").cutNew();
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(threatMetrixCookie);
        return threatMetrixCookie;
    }

    private CreateMotorAdRequest givenDefaultMotorAdRequest() {
        CreateMotorAdRequest createMotorAdRequest = new CreateMotorAdRequest();
        createMotorAdRequest.setTitle("test ad");
        createMotorAdRequest.setDescription("test description");
        createMotorAdRequest.setPostcode("tw91dh");
        createMotorAdRequest.setPrice(5478500L);
        createMotorAdRequest.setCategoryId(9311L);
        createMotorAdRequest.setShowMapOnAd(true);
        createMotorAdRequest.setVrmAttributes(Collections.emptyList());
        return createMotorAdRequest;
    }

}
