package com.gumtree.motors.webapi.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.gumtree.api.Ad;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.domain.advert.PostAdvertBean;
import com.gumtree.api.domain.attribute.ApiAttribute;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.api.error.ApiError;
import com.gumtree.api.error.ApiErrorCode;
import com.gumtree.api.error.ApiErrors;
import com.gumtree.motors.webapi.model.MotorsAd;
import com.gumtree.web.common.sapi.AdvertAttributeNames;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.StandardisedVehicleDataResponse;
import com.gumtree.seller.infrastructure.driven.motors.vehicledata.model.VehicleAttribute;
import com.gumtree.web.api.WebApiError;
import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.ajax.vrm.MotorsApiClient;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutImpl;
import com.gumtree.web.seller.service.CheckoutContainer;
import javaslang.Tuple2;
import org.jboss.resteasy.client.ClientResponse;
import org.jboss.resteasy.client.ClientResponseFailure;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import rx.Single;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.gumtree.mobile.test.Fixtures.AUDI_WITH_ASSOCIATED_CATEGORY;
import static com.gumtree.mobile.test.Fixtures.DEFAULT_ASSOCIATED_CATEGORY;
import static com.gumtree.mobile.test.Fixtures.createSellerType;
import static com.gumtree.mobile.test.Fixtures.createVehicleMake;
import static com.gumtree.mobile.test.Fixtures.createVehicleRegistrationYear;
import static com.gumtree.motors.webapi.model.MotorsAd.buildMotorsAd;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.argThat;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static util.RxAssertions.verifyError;

@RunWith(MockitoJUnitRunner.class)
public class MotorsServiceTest {

    public static final String ATTRIBUTE_WITH_ASSOCIATED_CATEGORY = "vehicle_make";

    @Mock
    private MotorsApiClient motorsApiClient;

    @Mock
    private CategoryModel categoryModel;

    @Mock
    private BushfireApi bushfireApi;

    @Mock
    private CheckoutContainer checkoutContainer;

    @Mock
    private AdvertApi advertApi;

    @Mock
    private OrderApi orderApi;

    @Mock
    private UserSession userSession;

    @InjectMocks
    private MotorsService motorsService;

    private String threatmetrixSessionId = UUID.randomUUID().toString();

    @Before
    public void setUp() {
        User user = givenDefaultUserWithSession();
        when(bushfireApi.advertApi()).thenReturn(advertApi);
        when(bushfireApi.create(any(), any())).thenAnswer(invocationOnMock -> {
            Object[] arguments = invocationOnMock.getArguments();
            String apiName = ((Class) arguments[0]).getSimpleName();
            String advertApiName = AdvertApi.class.getSimpleName();
            if (apiName.equals(advertApiName)) {
                return advertApi;
            } else {
                return orderApi;
            }
        });
        givenVehicleMakeAsAnAssociatedCategory();
        givenThereIsAnAttributeTypeForAttributes();
    }

    @Test
    public void shouldGetVrmAttributes() {
        //        Given
        String vrm = "W90MOG";
        Long categoryId = 9311L;

        StandardisedVehicleDataResponse standardisedVehicleDataResponse = givenStandardiseVehicleDataExist(vrm, categoryId);

        List<AttributeMetadata> attributeMetadatas = givenVehicleAttributeMetadatasExist(categoryId);

        //        When
        Tuple2<List<AttributeMetadata>, Map<String, String>> response = motorsService.getVrmAttributes(vrm, categoryId);

        //        Then
        assertThat(response._1).isEqualTo(attributeMetadatas);
        assertThat(response._2).isEqualTo(ImmutableMap.of(standardisedVehicleDataResponse.getAttributes().get(0).getName(),
                standardisedVehicleDataResponse.getAttributes().get(0).getValue()));
    }

    @Test
    public void shouldAttributesToOnlyKeepVehicleAttributes() {
        //        Given
        String vrm = "W90MOG";
        Long categoryId = 9311L;

        StandardisedVehicleDataResponse standardisedVehicleDataResponse = givenStandardiseVehicleDataExist(vrm, categoryId);

        List<AttributeMetadata> attributeMetadatas = givenAttributeMetadatasExistWithNonVehicleSpecificAttribute(categoryId);

        //        When
        Tuple2<List<AttributeMetadata>, Map<String, String>> response = motorsService.getVrmAttributes(vrm, categoryId);

        //        Then
        assertThat(response._1).isEqualTo(attributeMetadatas);
        assertThat(response._2).isEqualTo(ImmutableMap.of(standardisedVehicleDataResponse.getAttributes().get(0).getName(),
                standardisedVehicleDataResponse.getAttributes().get(0).getValue()));
    }


    @Test
    public void shouldReturnEmptyVehicleValuesIfMotorsApiFails() {
        //        Given
        String vrm = "W90MOG";
        Long categoryId = 9311L;

        givenStandardiseVehicleDataDontExist(vrm, categoryId);

        List<AttributeMetadata> attributeMetadatas = givenVehicleAttributeMetadatasExist(categoryId);

        //        When
        Tuple2<List<AttributeMetadata>, Map<String, String>> response = motorsService.getVrmAttributes(vrm, categoryId);

        //        Then
        assertThat(response._1).isEqualTo(attributeMetadatas);
        assertThat(response._2).isEqualTo(Collections.emptyMap());
    }

    @Test
    public void shouldReturnEmptyAttributeMetadatasIfCategoryApiFails() {
        //        Given
        String vrm = "W90MOG";
        Long categoryId = 9311L;

        StandardisedVehicleDataResponse standardisedVehicleDataResponse = givenStandardiseVehicleDataExist(vrm, categoryId);

        givenAttributeMetadatasDontExist(categoryId);

        //        When
        Tuple2<List<AttributeMetadata>, Map<String, String>> response = motorsService.getVrmAttributes(vrm, categoryId);

        //        Then
        assertThat(response._1).isEqualTo(Collections.emptyList());
        assertThat(response._2).isEqualTo(ImmutableMap.of(standardisedVehicleDataResponse.getAttributes().get(0).getName(),
                standardisedVehicleDataResponse.getAttributes().get(0).getValue()));
    }

    @Test
    public void shouldCreateMotorsAd() {
        //        Given
        String ipAddress = "127.0.0.1";
        PermanentCookie permanentCookie = givenAPermanentCookie();
        MotorsAd aMotorsAd = givenAMotorsAd();
        givenStandardiseVehicleDataExist(aMotorsAd.getCategoryId(), aMotorsAd.getAttributes());
        Ad ad = givenBapiPostAdOk(aMotorsAd, DEFAULT_ASSOCIATED_CATEGORY);
        ApiOrder apiOrder = givenBapiCreateOrderOk(ad);
        CheckoutImpl checkout = givenBapiCreateCheckoutOk(ad, apiOrder);

        //        When
        Checkout response = motorsService.createMotorAd(aMotorsAd, ipAddress, permanentCookie, threatmetrixSessionId)
                .toBlocking().value();

        //        Then
        assertThat(response).isEqualTo(checkout);
    }

    @Test
    public void shouldUseMotorAdCategoryIfNoAssociatedCategory() {
        //        Given
        String ipAddress = "127.0.0.1";
        PermanentCookie permanentCookie = givenAPermanentCookie();
        MotorsAd aMotorsAd = givenAMotorsAdWithoutAssociatedCategory();
        givenStandardiseVehicleDataExist(aMotorsAd.getCategoryId(), aMotorsAd.getAttributes());
        Ad ad = givenBapiPostAdOk(aMotorsAd, aMotorsAd.getCategoryId());
        ApiOrder apiOrder = givenBapiCreateOrderOk(ad);
        CheckoutImpl checkout = givenBapiCreateCheckoutOk(ad, apiOrder);

        //        When
        Checkout response = motorsService.createMotorAd(aMotorsAd, ipAddress, permanentCookie, threatmetrixSessionId)
                .toBlocking().value();

        //        Then
        assertThat(response).isEqualTo(checkout);
    }

    @Test
    public void shouldReturnErrorWithValidationIfValidationFails() {
        //        Given
        String ipAddress = "127.0.0.1";
        PermanentCookie permanentCookie = givenAPermanentCookie();
        MotorsAd aMotorsAd = givenAMotorsAd();
        givenStandardiseVehicleDataExist(aMotorsAd.getCategoryId(), aMotorsAd.getAttributes());
        ApiErrors apiErrors = givenThereIsAValidationError();


        //        When
        Single<Checkout> response = motorsService.createMotorAd(aMotorsAd, ipAddress, permanentCookie, threatmetrixSessionId);

        //        Then
        verifyError(response, (error) -> {
            ApiError apiError = apiErrors.getErrors().get(0);
            WebApiError webApiError = new WebApiError(apiError.getMessageCode(), apiError.getDefaultMessage(),
                    apiError.getField());
            WebApiErrorResponse webApiErrorResponse = new WebApiErrorResponse(HttpStatus.BAD_REQUEST,
                    apiErrors.getErrorCode().asString(), "Validation failed for 1 or more fields", Arrays.asList(webApiError));
            assertThat(error).isInstanceOf(WebApiErrorException.class);
            assertThat(((WebApiErrorException) error).getError()).isEqualTo(webApiErrorResponse);
        });
    }

    @Test
    public void shouldReturnInternalErrorIfUnexpectedErrorWhileValidating() throws Exception {
        //        Given
        String ipAddress = "127.0.0.1";
        PermanentCookie permanentCookie = givenAPermanentCookie();
        MotorsAd aMotorsAd = givenAMotorsAd();
        givenStandardiseVehicleDataExist(aMotorsAd.getCategoryId(), aMotorsAd.getAttributes());
        String exeptionMessage = givenThereIsAnUnexpectedErrorWhileValidating();


        //        When
        Single<Checkout> response = motorsService.createMotorAd(aMotorsAd, ipAddress, permanentCookie, threatmetrixSessionId);

        //        Then
        verifyError(response, (error) -> {
            assertThat(error).isInstanceOf(WebApiErrorException.class);
            assertThat(((WebApiErrorException) error).getError()).isEqualTo(buildDefaultInternalError(exeptionMessage));
        });
    }

    @Test
    public void shouldReturnInternalErrorIfUnexpectedErrorBapiCreateCall() throws Exception {
        //        Given
        String ipAddress = "127.0.0.1";
        PermanentCookie permanentCookie = givenAPermanentCookie();
        MotorsAd aMotorsAd = givenAMotorsAd();
        givenStandardiseVehicleDataExist(aMotorsAd.getCategoryId(), aMotorsAd.getAttributes());
        String exeptionMessage = givenThereIsAnUnexpectedErrorWhilebapiCreateAd();


        //        When
        Single<Checkout> response = motorsService.createMotorAd(aMotorsAd, ipAddress, permanentCookie, threatmetrixSessionId);

        //        Then
        verifyError(response, (error) -> {
            assertThat(error).isInstanceOf(WebApiErrorException.class);
            assertThat(((WebApiErrorException) error).getError()).isEqualTo(buildDefaultInternalError(exeptionMessage));
        });
    }

    @Test
    public void shouldReturnInternalErrorIfUnexpectedErrorOrderCreate() throws Exception {
        //        Given
        String ipAddress = "127.0.0.1";
        PermanentCookie permanentCookie = givenAPermanentCookie();
        MotorsAd aMotorsAd = givenAMotorsAd();
        givenStandardiseVehicleDataExist(aMotorsAd.getCategoryId(), aMotorsAd.getAttributes());
        givenBapiPostAdOk(aMotorsAd, DEFAULT_ASSOCIATED_CATEGORY);
        String exeptionMessage = givenThereIsAnUnexpectedErrorWhileCreateOrder();

        //        When
        Single<Checkout> response = motorsService.createMotorAd(aMotorsAd, ipAddress, permanentCookie, threatmetrixSessionId);

        //        Then
        verifyError(response, (error) -> {
            assertThat(error).isInstanceOf(WebApiErrorException.class);
            assertThat(((WebApiErrorException) error).getError()).isEqualTo(buildDefaultInternalError(exeptionMessage));
        });
    }

    @Test
    public void shouldReturnInternalErrorIfUnexpectedErrorCheckoutCreate() throws Exception {
        //        Given
        String ipAddress = "127.0.0.1";
        PermanentCookie permanentCookie = givenAPermanentCookie();
        MotorsAd aMotorsAd = givenAMotorsAd();
        givenStandardiseVehicleDataExist(aMotorsAd.getCategoryId(), aMotorsAd.getAttributes());
        Ad ad = givenBapiPostAdOk(aMotorsAd, DEFAULT_ASSOCIATED_CATEGORY);
        ApiOrder apiOrder = givenBapiCreateOrderOk(ad);
        String exeptionMessage = givenThereIsAnUnexpectedErrorWhileCheckoutCreate();

        //        When
        Single<Checkout> response = motorsService.createMotorAd(aMotorsAd, ipAddress, permanentCookie, threatmetrixSessionId);

        //        Then
        verifyError(response, (error) -> {
            assertThat(error).isInstanceOf(WebApiErrorException.class);
            assertThat(((WebApiErrorException) error).getError()).isEqualTo(buildDefaultInternalError(exeptionMessage));
        });
    }

    private WebApiErrorResponse buildDefaultInternalError(String exeptionMessage) {
        return new WebApiErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR,
                HttpStatus.INTERNAL_SERVER_ERROR.toString(), "Internal error while trying to post a car ad: " + exeptionMessage);
    }

    private ApiErrors givenThereIsAValidationError() {
        ClientResponseFailure clientResponseFailure = mock(ClientResponseFailure.class);
        ClientResponse clientResponse = mock(ClientResponse.class);
        doThrow(clientResponseFailure).when(advertApi).validateAd(any());
        when(clientResponseFailure.getResponse()).thenReturn(clientResponse);
        ApiErrors apiErrors = new ApiErrors();
        apiErrors.setErrorCode(ApiErrorCode.POSTCODE_UNRECOGNISED);
        ApiError apiError = new ApiError();
        apiError.setField("postcode");
        apiError.setDefaultMessage("invalid postcode");
        apiError.setMessageCode("invalid_postcode");
        apiErrors.setErrors(Arrays.asList(apiError));
        when(clientResponse.getEntity(ApiErrors.class)).thenReturn(apiErrors);
        return apiErrors;
    }

    private String givenThereIsAnUnexpectedErrorWhileValidating() {
        String exceptionMessage = "something went wrong";
        doThrow(new RuntimeException(exceptionMessage)).when(advertApi).validateAd(any());
        return exceptionMessage;
    }

    private String givenThereIsAnUnexpectedErrorWhilebapiCreateAd() {
        String exceptionMessage = "something went wrong";
        doThrow(new RuntimeException(exceptionMessage)).when(advertApi).postAd(any());
        return exceptionMessage;
    }

    private String givenThereIsAnUnexpectedErrorWhileCreateOrder() {
        String exceptionMessage = "something went wrong";
        doThrow(new RuntimeException(exceptionMessage)).when(orderApi).createOrder(any());
        return exceptionMessage;
    }

    private String givenThereIsAnUnexpectedErrorWhileCheckoutCreate() {
        String exceptionMessage = "something went wrong";
        doThrow(new RuntimeException(exceptionMessage)).when(checkoutContainer).createCheckout(any(ApiOrder.class), any(Ad.class), any
                (Boolean.class));
        return exceptionMessage;
    }

    private CheckoutImpl givenBapiCreateCheckoutOk(Ad ad, ApiOrder apiOrder) {
        String editorId = "423125";
        CheckoutImpl checkout = new CheckoutImpl();
        checkout.setKey(editorId);
        when(checkoutContainer.createCheckout(apiOrder, ad, true)).thenReturn(checkout);
        return checkout;
    }

    private ApiOrder givenBapiCreateOrderOk(Ad ad) {
        ApiOrder apiOrder = new ApiOrder();
        when(orderApi.createOrder(argThat(new ArgumentMatcher<CreateOrderBean>() {
            @Override
            public boolean matches(Object object) {
                if (object instanceof CreateOrderBean) {
                    CreateOrderBean createOrderBean = (CreateOrderBean) object;
                    return createOrderBean.getAccountId().equals(userSession.getSelectedAccountId())
                            && createOrderBean.getItems().get(0).getAdvertId().equals(ad.getId());
                } else {
                    return false;
                }
            }
        }))).thenReturn(apiOrder);
        return apiOrder;
    }

    private Ad givenBapiPostAdOk(MotorsAd aMotorsAd, long expectedCategory) {
        final Ad ad = new Ad();
        ad.setId(1L);
        when(advertApi.postAd(argThat(new ArgumentMatcher<PostAdvertBean>() {
            @Override
            public boolean matches(Object object) {
                if (object instanceof PostAdvertBean) {
                    PostAdvertBean postAdvertBean = (PostAdvertBean) object;

                    boolean isUsingAssociatedCategory = postAdvertBean.getCategoryId().equals(expectedCategory);
                    Map<String, String> attributes = postAdvertBean.getAttributes().stream().collect(Collectors.toMap(ApiAttribute::getName,
                            ApiAttribute::getValue));

                    boolean isPriceCorrect = attributes.get(CategoryConstants.Attribute.PRICE.getName()).equals(String.valueOf(aMotorsAd
                            .getPrice() / 100));

                    boolean isSellerTypePrivate = attributes.get(AdvertAttributeNames.SELLER_TYPE.getName()).equals("private");
                    return isUsingAssociatedCategory && isPriceCorrect && isSellerTypePrivate;
                } else {
                    return false;
                }
            }
        }))).thenReturn(ad);
        return ad;
    }

    private void givenThereIsAnAttributeTypeForAttributes() {
        when(categoryModel.getAttributeType(any())).thenReturn(com.google.common.base.Optional.of
                (AttributeType.ENUM));
    }

    private void givenVehicleMakeAsAnAssociatedCategory() {
        when(categoryModel.findAttributesByNameForGivenCategory(anyString(), anyLong())).thenAnswer(invocationOnMock -> {
            String attributeName = invocationOnMock.getArgumentAt(0, String.class);
            if (attributeName.equals(ATTRIBUTE_WITH_ASSOCIATED_CATEGORY)) {
                return com.google.common.base.Optional.of(createVehicleMake(AUDI_WITH_ASSOCIATED_CATEGORY).build());
            } else {
                return com.google.common.base.Optional.absent();
            }
        });
    }

    private PermanentCookie givenAPermanentCookie() {
        PermanentCookie permanentCookie = new PermanentCookie("gumtree.com", 2133, "");
        permanentCookie.setId("test-cookie");
        return permanentCookie;
    }

    private MotorsAd givenAMotorsAd() {
        return buildMotorsAd(motorsAd -> {
            motorsAd.setTitle("test title");
            motorsAd.setDescription("test description");
            motorsAd.setCategoryId(9311L);
            motorsAd.setPostcode("TW9 1DH");
            motorsAd.setPrice(5000L);
            motorsAd.setShowMapOnAd(true);
            motorsAd.setAttributes(Maps.newHashMap(ImmutableMap.of(ATTRIBUTE_WITH_ASSOCIATED_CATEGORY, "audi")));
        });
    }

    private MotorsAd givenAMotorsAdWithoutAssociatedCategory() {
        return buildMotorsAd(motorsAd -> {
            motorsAd.setTitle("test title");
            motorsAd.setDescription("test description");
            motorsAd.setCategoryId(9311L);
            motorsAd.setPostcode("TW9 1DH");
            motorsAd.setPrice(5000L);
            motorsAd.setShowMapOnAd(true);
            motorsAd.setAttributes(Maps.newHashMap(ImmutableMap.of("vehicle_model", "a6")));
        });
    }


    private Map<String, String> givenStandardiseVehicleDataExist(Long categoryId, Map<String, String> vehicleAttributes) {
        when(motorsApiClient.standardiseVehicleData(categoryId, vehicleAttributes)).thenReturn(vehicleAttributes);
        return vehicleAttributes;
    }

    private StandardisedVehicleDataResponse givenStandardiseVehicleDataExist(String vrm, Long categoryId) {
        StandardisedVehicleDataResponse standardisedVehicleDataResponse = new StandardisedVehicleDataResponse();
        standardisedVehicleDataResponse.setCategoryId(Integer.valueOf(categoryId.toString()));
        VehicleAttribute vehicleAttribute = new VehicleAttribute();
        vehicleAttribute.setName("vehicle_make");
        vehicleAttribute.setValue("morgan");
        standardisedVehicleDataResponse.setAttributes(Arrays.asList(vehicleAttribute));
        when(motorsApiClient.lookupVehicleData(vrm, categoryId)).thenReturn(Optional.of(standardisedVehicleDataResponse));
        return standardisedVehicleDataResponse;
    }

    private void givenStandardiseVehicleDataDontExist(String vrm, Long categoryId) {
        when(motorsApiClient.lookupVehicleData(vrm, categoryId)).thenReturn(Optional.empty());
    }

    private List<AttributeMetadata> givenVehicleAttributeMetadatasExist(Long categoryId) {
        List<AttributeMetadata> attributeMetadatas = Arrays.asList
                (createVehicleRegistrationYear().build());
        when(categoryModel.getCategoryAttributes(categoryId)).thenReturn(com.google.common.base.Optional.of(attributeMetadatas));
        return attributeMetadatas;
    }

    private List<AttributeMetadata> givenAttributeMetadatasExistWithNonVehicleSpecificAttribute(Long categoryId) {
        List<AttributeMetadata> attributeMetadatas = Arrays.asList
                (createVehicleRegistrationYear().build(), createSellerType().withOrderNumber(null).build());
        when(categoryModel.getCategoryAttributes(categoryId)).thenReturn(com.google.common.base.Optional.of(attributeMetadatas));
        return attributeMetadatas;
    }

    private void givenAttributeMetadatasDontExist(Long categoryId) {
        when(categoryModel.getCategoryAttributes(categoryId)).thenReturn(com.google.common.base.Optional.absent());
    }

    private User givenDefaultUserWithSession() {
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setFirstName("Test");
        when(userSession.getUser()).thenReturn(user);
        when(userSession.getSelectedAccountId()).thenReturn(1L);
        when(userSession.getApiKey()).thenReturn(new BushfireApiKey("test", "test"));
        return user;
    }
}