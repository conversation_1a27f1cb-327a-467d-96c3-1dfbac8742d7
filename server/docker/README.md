## intellij:
seller running on port 8081

buyer is configured to point to qa3, if you want to have it running locally, uncomment line 45 in haproxy.cfg 
buyer must be running on port 8080 (or you can modify haproxy.cfg accordingly)

## /etc/hosts
127.0.0.1       localhost www.dev.gumtree.com dev.gumtree.com

## ~/.gumtree/config/mobile-web.properties
gumtree.host=http://dev.gumtree.com:1080
gumtree.host.mygumtree=http://dev.gumtree.com:1081

gumtree.api.host=http://bapi.qa3.gumtree.com/api
gumtree.cookies.default_domain=dev.gumtree.com

gumtree.static.assets.seller.host=//dev.gumtree.com:1081
gumtree.static.assets.host=//dev.gumtree.com:1081

gumtree.url.buyer.base_uri=http://dev.gumtree.com:1080
gumtree.url.seller.base_uri=http://dev.gumtree.com:1081
gumtree.url.seller.secure.base_uri=http://dev.gumtree.com:1081


## ~/.gumtree/config/seller-server.properties
gumtree.host=http://dev.gumtree.com:1080
gumtree.host.mygumtree=http://dev.gumtree.com:1081

gumtree.api.host=http://bapi.qa6.gumtree.com/api
gumtree.cookies.default_domain=dev.gumtree.com

gumtree.static.assets.seller.host=//dev.gumtree.com:1081
gumtree.static.assets.host=//dev.gumtree.com:1081
gumtree.static.assets.debug=true

gumtree.url.buyer.base_uri=http://dev.gumtree.com:1080
gumtree.url.seller.base_uri=http://dev.gumtree.com:1081
gumtree.url.seller.secure.base_uri=http://dev.gumtree.com:1081


## in this directory run
- OSX
```shell
     docker run -d --name gt_proxy -e HOST_IP=`boot2docker ip` -p 1080:1080 -p 1081:1081 -p 1082:1082 -v ${PWD}:/haproxy-override docker.qa.gt.ecg.so/gumtree/haproxy
```
- Linux
```shell
    docker run -d --name gt_proxy -e HOST_IP=`/sbin/ifconfig docker0 | grep "inet addr" | awk -F: '{print $2}' | awk '{print $1}'` -p 1080:1080 -p 1081:1081 -p 1082:1082 -v ${PWD}:/haproxy-override docker.qa.gt.ecg.so/gumtree/haproxy
```
- Windows
```shell
    ???
```

go to http://dev.gumtree.com:1081/manage/ads

buyer running on port 1080, seller on 1081, haproxy stats on 1082
