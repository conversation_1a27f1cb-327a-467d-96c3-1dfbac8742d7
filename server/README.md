# Dependencies
The application uses the following services to provide its functionality

### Internal
* **BAPI** - To access the business logic of Gumtree
* **Elasticsearch Advert Index** - For browsing users ads, looking up for the postcode recently used by user,
category suggesting
* **Redis cache** - to cache locations, homepage
* **Convers** - handles messaging between site users
* **Draft Ads API** - the service responsible for storing drafts of sellers' ads
* **CV Store** - the service responsible for storing saved CVs of users
* **Grafana** - collect the metrics
* **Motors API** - service responsible for looking up data about motors
* **Category API** - to retrieve directory of categories
* **User Service** - to manage logging on/off users
* **User Reviews** -allows users to review each other

### External
* **EPS** - to store and retrieve images attached to adverts
* **Braintree payments** - for payment handling
* **Salesforce** - support for Pro users
* **Facebook** - logging in using Facebook account
* **Google** - logging in using Google account
* **Google reCaptcha** - to provide CAPTCHA functionality
* **Google Analytics** - to provide analytics
* **Threat Metrix** - fraud protection


# Configuration
The application settings are being loaded from the following locations:
- system properties specified when running the application
- main/resources/seller-server.properties file within the classpath
- /etc/gumtree/seller-server.properties file in the local filesystem

## Properties supported by the application

### General

* gumtree.http.port
* gumtree.host
* gumtree.host.mygumtree
* gumtree.cookies.default_domain
* gumtree.cookies.secure
* gumtree.static.assets.seller.host
* gumtree.rememberme.aes128.base64.key

### Freemarker settings
* gumtree.web.templates.path
* gumtree.web.templates.update_delay
* gumtree.web.templates.freemarker.exceptions_strategy

### Redirect URI's used by frontend app
* gumtree.url.buyer.base_uri
* gumtree.url.seller.base_uri
* gumtree.url.seller.secure.base_uri
* gumtree.url.reply.base_uri

### Enabling/disabling specific app features
* gumtree.createaccount.enabled - switch that allows to enable/disable account creation
* gumtree.savedsearch.enabled - switch that allows to enable/disable saved searches

### Access to Ad-Counters API
* gumtree.tsa.adcounters.api.baseurl
* gumtree.tsa.adcounters.audience

### Access to BAPI
* gumtree.bapi.host
* gumtree.bapi.max.connections.per.route
* gumtree.bapi.max.connections
* gumtree.bapi.connection.timeout
* gumtree.bapi.socket.timeout
* gumtree.bapi.keepalive.ms

### Access to the EPS service
* gumtree.images.eps.httpshost

### Access to Grafana
* gumtree.metrics.report.graphite.host
* gumtree.metrics.report.graphite.port
* gumtree.metrics.report.graphite.run

### Access to Redis Cache
* gumtree.redis.enabled - if true, we would use cassandra as a cache with fallback to redis
* gumtree.redis.host
* gumtree.redis.port
* gumtree.redis.timeout
* gumtree.redis.master.name
* gumtree.redis.sentinel.hostList
* gumtree.redis.secondary.host
* gumtree.redis.secondary.port
* gumtree.redis.secondary.timeout
* gumtree.redis.secondary.master.name
* gumtree.redis.secondary.sentinel.hostList
* gumtree.redis.secondary.enabled
* gumtree.redis.data.ttlseconds
* gumtree.redis.complete.checkout.ttlseconds

### Access to Cassandra session storage
gumtree.seller.db.cassandra.servers=localhost:9042
gumtree.seller.db.cassandra.keyspace=seller
gumtree.seller.db.cassandra.username=
gumtree.seller.db.cassandra.password=

### Access to Category API
* gumtree.category.api.hostname
* gumtree.category.api.port
* gumtree.category.api.cache.reload.interval

### Diagnostics and debugging
* gumtree.image.domain.dev
* gumtree.paypal.htmlFormPost.enabled //GTEPICS-236
* gumtree.zeno.model.enabled - enables appending zeno data to the page model
* gumtree.model.log.enabled - if true, all models built by controllers are being logged to logs
* gumtree.access.control.allow.origin - if true, Access-Control-Allow-Orgin is enabled (TODO -explain it)
* gumtree.model.display.header.secret - `x-gt-get-model` header value  to displays raw model of the page as a JSON
(see [Display the model JSON for debugging](../doc/display_model_README.md)).

### Antispam protection settings
* gumtree.postad.rate.per.interval -
* gumtree.postad.rate.interval.seconds
* gumtree.bumpup.restriction.minutes

### Settings related to business logic and validation
* gumtree.reply.fileattachmentscategoryid - determines list of categories for which user can reply with attachment (currently JOBS)
* gumtree.dateformat - date format used on the site
* gumtree.images.maxUploadSize - maximum size of the uploaded image

### Access to payment system
* gumtree.seller.paypal.ipn.notify_url
* gumtree.seller.paypal.ipn.notify_url.enabled

### Access to Salesforce
* gumtree.salesforce.url
* gumtree.salesforce.clientId
* gumtree.salesforce.clientSecret
* gumtree.salesforce.redirectUri
* gumtree.salesforce.jwtSecret

### UserService host
* gumtree.user.baseurl
* gumtree.user.connection_timeout
* gumtree.user.socket_timeout

### Access to social logging
* facebook.api.appId
* google.api.clientId

### Access to convers and configuration of messagecentre
* gumtree.convers.client.ignore_https
* gumtree.convers.host
* gumtree.convers.port
* gumtree.messagecentre.enabled
* gumtree.messagecentre.link.enabled
* gumtree.messagecentre.pollingfrequency.seconds
* gumtree.messagecentre.inactive.pollingfrequency.seconds
* gumtree.messagecentre.notification.pollingfrequency.minutes
* gumtree.replytsapi.enabled - if true, we use real replyts, otherwise we use stub

### Access to DraftAds API
* gumtree.drafts.api.host
* gumtree.drafts.api.port
* gumtree.drafts.api.verify_ssl_certs
* gumtree.drafts.api.enabled
* gumtree.drafts.api.connection_timeout
* gumtree.drafts.api.socket_timeout
* gumtree.drafts.api.stub.enabled - if true, we use stub of the cv store instead of real service

### Access to CV Store
* gumtree.cv_store.host
* gumtree.cv_store.port
* gumtree.cv_store.verify_ssl_certs
* gumtree.cv_store.enabled
* gumtree.cv_store.connection_timeout
* gumtree.cv_store.socket_timeout
* gumtree.cv_store.stub.enabled - if true, we use stub of the cv store instead of real service

### Access to Google Analytics
* gumtree.analytics.ga.trackingid
* gumtree.analytics.ga.host
* gumtree.analytics.ga.server.stub.enabled

### Access to Google reCaptcha
* gumtree.recaptcha.siteKey
* gumtree.recaptcha.secret
* gumtree.recaptcha.httpclient.socket.timeout
* gumtree.recaptcha.httpclient.connection.timeout
* gumtree.recaptcha.httpclient.retry.count
* gumtree.recaptcha.enabled - if true some functionalities of gumtree are protected by recaptcha

### Access to Motors service API
* gumtree.motors.api.host
* gumtree.motors.api.port
* gumtree.motors.api.connection_timeout
* gumtree.motors.api.read_timeout
* gumtree.seller.motors.vrm.lookup.service

### Access to Payment API
* gumtree.payment.api.host
* gumtree.payment.api.port
* gumtree.payment.api.connection_timeout
* gumtree.payment.api.read_timeout


# User Reviews
* gumtree.userreviews.api.host
* gumtree.userreviews.api.connection_timeout
* gumtree.userreviews.api.read_timeout

### Obsolete (TO BE CHECKED)
* gumtree.clientlog.enabled -- seems that this is not used (sets clientlog in FTL's)
* gumtree.viewDecider.email.module -- some feature which is not used now
* gumtree.viewDecider.email.threshold -- some feature which is not used now
* gumtree.homepage.category.version -- somehow related to very old homepage
* gumtree.session.map.expiry.checkms - unused - property to be removed
* gumtree.rememberme.decrypt.old.cookie -- unused - property to be removed
* gumtree.savedsearch.server.enabled -- unused

### Unknown
* gumtree.dailydeals
* gumtree.cookies.lnu.enabled
* gumtree.legacy.proxy.mode
* gumtree.cookies.lnu.regex
