package com.gumtree.web.cookie;

import com.gumtree.api.UserResolver;
import com.gumtree.web.cookie.cutters.session.SessionCookie;
import org.junit.Before;
import org.junit.Test;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class DefaultUserSessionServiceTest {

    private HttpServletRequest request;
    private CookieResolver cookieResolver;
    private UserResolver userResolver;

    @Before
    public void setup() {
        request = mock(HttpServletRequest.class);
        cookieResolver = mock(CookieResolver.class);
        HttpSession session = mock(HttpSession.class);
        userResolver = mock(UserResolver.class);

        when(request.getSession()).thenReturn(session);

        when(session.getId()).thenReturn("new-session-id");
    }

    @Test
    public void existingCookieIdUsedWhenPresent() {
        SessionCookie sessionCookie = new SessionCookie("", 0, "");
        when(cookieResolver.resolve(request, SessionCookie.class)).thenReturn(sessionCookie);
        sessionCookie.setId("existing-session-id");

        new DefaultUserSessionService(cookieResolver, request, userResolver);

        String value = sessionCookie.getId();
        assertThat(value, equalTo("existing-session-id"));
    }

    @Test
    public void sessionIdUsedWhenCookieValueNotPresent() {
        SessionCookie sessionCookie = new SessionCookie("", 0, "");
        when(cookieResolver.resolve(request, SessionCookie.class)).thenReturn(sessionCookie);
        sessionCookie.setId("");

        new DefaultUserSessionService(cookieResolver, request, userResolver);

        String value = sessionCookie.getId();
        assertThat(value, equalTo("new-session-id"));
    }

}