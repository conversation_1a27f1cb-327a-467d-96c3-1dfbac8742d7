package com.gumtree.web.cookie;

import com.gumtree.common.properties.Env;
import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Properties;
import java.util.UUID;

import static org.fest.assertions.api.Assertions.assertThat;

public class MadgexCookieHelperTest {

    static {
        Properties properties = new Properties();
        properties.setProperty("gumtree.env", Env.PROD.name());
        properties.setProperty("gumtree.cookies.secure", "false");
        properties.setProperty("gumtree.cookies.default_domain",".gumtree.com");
        ConfigurationManager.loadProperties(properties);
    }

    private MadgexCookieHelper madgexCookieHelper;

    @Before
    public void setup() {
        this.madgexCookieHelper = new MadgexCookieHelper();
    }

    @Test
    @DisplayName("Remove Madgex Cookie if cookie name matches")
    public void removeCookieSuccesfully()  {
        //given
        String cookieName ="GtjSsoRefresh"; //correct name
        String domain =".gumtree.com"; //correct domain
        ServletRequest request = aRequest(getCookie(cookieName,domain));
        ServletResponse response = aResponse();

        //when
        madgexCookieHelper.removeMadgexCookie((HttpServletRequest)request, (HttpServletResponse)response);

        //then
        assertThat(((MockHttpServletResponse)response).getCookie(cookieName)).isNotNull();
        assertThat(((MockHttpServletResponse)response).getCookie(cookieName).getMaxAge()).isEqualTo(0);
    }

    @Test
    @DisplayName("Do not remove Madgex Cookie if cookie name doesn't match")
    public void cookieWillNotBeRemovedIfNameDoNotMatch()  {
        //given
        String cookieName ="Madgex-cookie"; //different name
        String domain =".gumtree.com"; //correct domain
        ServletRequest request = aRequest(getCookie(cookieName,domain));
        ServletResponse response = aResponse();

        //when
        madgexCookieHelper.removeMadgexCookie((HttpServletRequest)request, (HttpServletResponse)response);

        //then
        assertThat(((MockHttpServletResponse)response).getCookie(cookieName)).isNull();
    }

    private ServletResponse aResponse() {
        return new MockHttpServletResponse();
    }

    private ServletRequest aRequest(Cookie cookie) {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setCookies(cookie);
        return request;
    }

    private Cookie getCookie(String cookieName,String domain){
        Cookie cookie = new Cookie(cookieName, UUID.randomUUID().toString());
        cookie.setDomain(domain);
        cookie.setMaxAge(100);
        cookie.setPath("/");
        cookie.setSecure(false);
        cookie.setHttpOnly(true);
        return cookie;
    }
}
