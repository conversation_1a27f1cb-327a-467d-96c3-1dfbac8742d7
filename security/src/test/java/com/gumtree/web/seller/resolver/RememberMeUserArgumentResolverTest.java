package com.gumtree.web.seller.resolver;

import com.google.common.base.Optional;
import com.gumtree.api.User;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.executor.command.GetUserApiCall;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.support.SubjectThreadState;
import org.apache.shiro.util.ThreadState;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RememberMeUserArgumentResolverTest {
    private static final String USER_EMAIL = "<EMAIL>";

    @InjectMocks private RememberMeUserArgumentResolver resolver;
    @Mock private ApiCallExecutor apiCallExecutor;
    @Mock private MethodParameter methodParameter;
    @Mock private ModelAndViewContainer mavContainer;
    @Mock private NativeWebRequest webRequest;
    @Mock private WebDataBinderFactory binderFactory;
    @Mock private Subject subject;
    @Mock private User user;

    @Before
    public void before() {
        ThreadState subjectThreadState = new SubjectThreadState(subject);
        subjectThreadState.bind();

        when(subject.getPrincipal()).thenReturn(USER_EMAIL);
    }

    @Test
    public void shouldSupportOptionalOfTypeUser() {
        // given
        ParameterizedType parameterizedType = ParameterizedTypeImpl.make(Optional.class, new Type[]{User.class}, null);
        when(methodParameter.getGenericParameterType()).thenReturn(parameterizedType);

        // when
        boolean isSupported = resolver.supportsParameter(methodParameter);

        // then
        assertThat(isSupported).isTrue();
    }

    @Test
    public void shouldNotSupportOptionalOfTypeOtherThanUser() {
        // given
        ParameterizedType parameterizedType = ParameterizedTypeImpl.make(Optional.class, new Type[]{String.class}, null);
        when(methodParameter.getGenericParameterType()).thenReturn(parameterizedType);

        // when
        boolean isSupported = resolver.supportsParameter(methodParameter);

        // then
        assertThat(isSupported).isFalse();
    }

    @Test
    public void shouldNotSupportTypeOtherThanOptional() {
        // given
        ParameterizedType parameterizedType = ParameterizedTypeImpl.make(List.class, new Type[]{String.class}, null);
        when(methodParameter.getGenericParameterType()).thenReturn(parameterizedType);

        // when
        boolean isSupported = resolver.supportsParameter(methodParameter);

        // then
        assertThat(isSupported).isFalse();
    }

    @Test
    public void shouldNotSupportNonGenericType() {
        // given
        when(methodParameter.getGenericParameterType()).thenReturn(null);

        // when
        boolean isSupported = resolver.supportsParameter(methodParameter);

        // then
        assertThat(isSupported).isFalse();
    }

    @Test
    public void shouldResolveArgumentToAbsentIfUserIsNotLoggedIn() throws Exception {
        // given
        when(subject.isAuthenticated()).thenReturn(false);
        when(subject.isRemembered()).thenReturn(false);

        // when
        Object result = resolver.resolveArgument(methodParameter, mavContainer, webRequest, binderFactory);

        // then
        assertThat(result).isEqualTo(Optional.absent());
        verifyZeroInteractions(apiCallExecutor);
    }

    @Test
    public void shouldResolveArgumentToUserIfUserIsRemembered() throws Exception {
        // given
        when(subject.isRemembered()).thenReturn(true);

        // given
        ApiCallResponse apiCallResponse = mock(ApiCallResponse.class);
        when(apiCallResponse.getResponseObject()).thenReturn(user);
        when(apiCallExecutor.call(new GetUserApiCall(USER_EMAIL))).thenReturn(apiCallResponse);

        // when
        Object result = resolver.resolveArgument(methodParameter, mavContainer, webRequest, binderFactory);

        // then
        assertThat(result).isEqualTo(Optional.of(user));
    }

    @Test
    public void shouldResolveArgumentToUserIfUserIsAuthenticated() throws Exception {
        // given
        when(subject.isAuthenticated()).thenReturn(true);

        // given
        ApiCallResponse apiCallResponse = mock(ApiCallResponse.class);
        when(apiCallResponse.getResponseObject()).thenReturn(user);
        when(apiCallExecutor.call(new GetUserApiCall(USER_EMAIL))).thenReturn(apiCallResponse);

        // when
        Object result = resolver.resolveArgument(methodParameter, mavContainer, webRequest, binderFactory);

        // then
        assertThat(result).isEqualTo(Optional.of(user));
    }
}
