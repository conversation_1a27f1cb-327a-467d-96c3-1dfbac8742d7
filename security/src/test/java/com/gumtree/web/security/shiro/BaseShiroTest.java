package com.gumtree.web.security.shiro;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.UnavailableSecurityManagerException;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.support.SubjectThreadState;
import org.apache.shiro.util.LifecycleUtils;
import org.apache.shiro.util.ThreadState;
import org.junit.AfterClass;

/**
 * Abstract test case enabling <PERSON><PERSON> in test environments.
 */
public abstract class BaseShiroTest {

    private static ThreadState subjectThreadState;

    /**
     * Constructor.
     */
    public BaseShiroTest() {
    }

    /**
     * Allows subclasses to set the currently executing {@link org.apache.shiro.subject.Subject} instance.
     *
     * @param subject the Subject instance
     */
    protected final void setSubject(Subject subject) {
        clearSubject();
        subjectThreadState = createThreadState(subject);
        subjectThreadState.bind();
    }

    protected final Subject getSubject() {
        return SecurityUtils.getSubject();
    }

    protected final ThreadState createThreadState(Subject subject) {
        return new SubjectThreadState(subject);
    }

    /**
     * Clears <PERSON><PERSON>'s thread state, ensuring the thread remains clean for future test execution.
     */
    protected final void clearSubject() {
        doClearSubject();
    }

    private static void doClearSubject() {
        if (subjectThreadState != null) {
            subjectThreadState.clear();
            subjectThreadState = null;
        }
    }

    protected static void setSecurityManager(org.apache.shiro.mgt.SecurityManager securityManager) {
        SecurityUtils.setSecurityManager(securityManager);
    }

    protected static SecurityManager getSecurityManager() {
        return SecurityUtils.getSecurityManager();
    }

    /**
     * Tear down.
     */
    @AfterClass
    public static void tearDownShiro() {
        doClearSubject();
        try {
            SecurityManager securityManager = getSecurityManager();
            LifecycleUtils.destroy(securityManager);
            setSecurityManager(null);
        } catch (UnavailableSecurityManagerException e) {
            // This is here to prevent checkstyle from complaining about empty catch blocks
            setSecurityManager(null);
        }
    }
}
