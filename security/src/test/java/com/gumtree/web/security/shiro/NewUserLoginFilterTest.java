package com.gumtree.web.security.shiro;

import com.gumtree.web.security.SecurityHelper;
import com.gumtree.web.security.login.LoginUtils;
import org.apache.shiro.subject.Subject;
import org.junit.Before;
import org.junit.Test;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class NewUserLoginFilterTest extends BaseShiroTest {

    private LoginUtils loginUtils;

    private SecurityHelper securityHelper;

    private Subject subject;

    private NewUserLoginFilter filter;

    private ServletRequest request;

    private ServletResponse response;

    @Before
    public void init() {
        loginUtils = mock(LoginUtils.class);
        subject = mock(Subject.class);
        request = mock(ServletRequest.class);
        response = mock(ServletResponse.class);
        securityHelper = mock(SecurityHelper.class);
        filter = new NewUserLoginFilter(loginUtils, securityHelper);
        setSubject(subject);
    }

    @Test
    public void userCanPassThroughIfExistingRememberedUserAndValidAccessToken() {
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn(null);
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(true);
        when(subject.isAuthenticated()).thenReturn(false);
        when(subject.isRemembered()).thenReturn(true);
        when(securityHelper.verifyAccessTokenAndLogoutIfInvalid(any(Subject.class))).thenReturn(true);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(true));
    }

    @Test
    public void userCanNotPassThroughIfExistingRememberedUserAndInvalidAccessToken() {
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn(null);
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(true);
        when(subject.isAuthenticated()).thenReturn(false);
        when(subject.isRemembered()).thenReturn(true);
        when(securityHelper.verifyAccessTokenAndLogoutIfInvalid(any(Subject.class))).thenReturn(false);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(false));
    }

    @Test
    public void userCanPassThroughIfExistingAuthenticatedUser() {
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn(null);
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(true);
        when(subject.isRemembered()).thenReturn(false);
        when(subject.isAuthenticated()).thenReturn(true);
        when(securityHelper.verifyAccessTokenAndLogoutIfInvalid(any(Subject.class))).thenReturn(true);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(true));
    }

    @Test
    public void newUserCanPassThroughIfEmailIsInSession() {
        when(subject.isAuthenticated()).thenReturn(false);
        when(subject.isRemembered()).thenReturn(false);
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn("<EMAIL>");
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(true);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(true));
    }

    @Test
    public void newUserCannotPassThroughIfEmailAddressNotInSession() {
        when(subject.isAuthenticated()).thenReturn(false);
        when(subject.isRemembered()).thenReturn(false);
        when(loginUtils.getNewUserEmailAddressFromSession()).thenReturn(null);
        when(loginUtils.newUserMustLoginToStartPostAdFlow()).thenReturn(false);
        assertThat(filter.internalIsAccessAllowed(request, response), equalTo(false));
    }
}
