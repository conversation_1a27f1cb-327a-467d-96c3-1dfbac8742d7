package com.gumtree.web.security;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.executor.command.GetUserApiCall;
import com.gumtree.web.security.DefaultUserSecurityManager;
import com.gumtree.web.security.exception.UserNotRecognisedException;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class DefaultUserSecurityManagerTest {

    private BushfireApiKey apiKey;

    private ApiCallExecutor apiCallExecutor;

    private ApiCallResponse apiCallResponse;

    private DefaultUserSecurityManager securityManager;

    private BushfireApi bushfireApi;

    @Before
    public void init() {
        apiKey = new BushfireApiKey();
        apiCallExecutor = mock(ApiCallExecutor.class);
        apiCallResponse = mock(ApiCallResponse.class);
        bushfireApi = mock(BushfireApi.class);
        securityManager = new DefaultUserSecurityManager(apiCallExecutor, apiKey, bushfireApi);
    }

    @Test(expected = UserNotRecognisedException.class)
    public void throwsUserNotRecognisedExceptionWhenFetchingUnknownUser() {
        when(apiCallExecutor.call(any(GetUserApiCall.class))).thenReturn(apiCallResponse);
        when(apiCallResponse.isErrorResponse()).thenReturn(true);
        securityManager.getExistingUser("<EMAIL>");
    }

    @Test
    public void returnsUserFromApiWhenExists() {
        User user = new User();
        when(apiCallExecutor.call(any(GetUserApiCall.class))).thenReturn(apiCallResponse);
        when(apiCallResponse.isErrorResponse()).thenReturn(false);
        when(apiCallResponse.getResponseObject()).thenReturn(user);
        assertThat(securityManager.getExistingUser("<EMAIL>"), equalTo(user));
    }
}
