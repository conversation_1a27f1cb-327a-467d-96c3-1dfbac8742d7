package com.gumtree.web.security.shiro;

import com.google.common.collect.Lists;
import com.gumtree.common.properties.GtPropManager;
import com.gumtree.common.util.security.exception.MissingPasswordException;
import com.gumtree.common.util.security.exception.MissingUsernameAndPasswordException;
import com.gumtree.common.util.security.exception.MissingUsernameException;
import com.gumtree.common.util.security.exception.UserAccountSuspendedException;
import com.gumtree.config.SellerProperty;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.UserServiceFacade;
import com.gumtree.user.service.model.AuthenticationProvider;
import com.gumtree.user.service.model.AuthenticationRequest;
import com.gumtree.user.service.model.UserApiErrorCode;
import com.gumtree.user.service.model.UserRegistrationRequest;
import com.gumtree.user.service.support.builder.AuthenticationRequestBuilder;
import com.gumtree.user.service.support.builder.AuthenticationResponseBuilder;
import com.gumtree.user.service.support.builder.RegisteredUserBuilder;
import com.gumtree.user.service.support.builder.UserApiErrorsBuilder;
import com.gumtree.user.service.support.builder.UserRegistrationRequestBuilder;
import com.gumtree.user.service.support.factory.GumtreeAccessTokenFactory;
import com.gumtree.userapi.model.GumtreeAccessToken;
import com.gumtree.web.security.exception.UserApiAuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;

import static com.gumtree.user.service.model.AuthenticationProvider.FACEBOOK;
import static com.gumtree.user.service.model.AuthenticationProvider.GOOGLE;
import static com.gumtree.user.service.model.AuthenticationProvider.GUMTREE;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.fail;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GumtreeRealmTest {
    private static final String USERNAME = "username";
    private static final String PASSWORD = "token";
    private static final String GT_ACCESS_TOKEN = "gt-access-token";
    private static final String IP_ADDRESS = "*********";
    private static final String TM_SESSION_ID = "abc-123";

    @Rule
    public ExpectedException exception = ExpectedException.none();

    @InjectMocks
    private GumtreeRealm realm;

    @Mock
    private UserServiceFacade userServiceFacade;

    static {
        GtPropManager.setProperty(SellerProperty.RECAPTCHA_ENABLED.getPropertyName(), "false");
    }

    @Test
    public void supportCorrectTokenType() {
        assertThat(realm.supports(new GumtreeAuthenticationToken())).isTrue();
        assertThat(realm.supports(new UsernamePasswordToken())).isTrue();
    }

    @Test(expected = MissingUsernameException.class)
    public void throwsMissingUsernameExceptionWhenUsernameIsNull() {
        GumtreeAuthenticationToken upToken = createAuthToken(null, "myPassword");
        realm.doGetAuthenticationInfo(upToken);
    }

    @Test(expected = MissingUsernameException.class)
    public void throwsMissingUsernameExceptionWhenUsernameIsEmpty() {
        GumtreeAuthenticationToken upToken = createAuthToken("", "myPassword");
        realm.doGetAuthenticationInfo(upToken);
    }

    @Test(expected = MissingPasswordException.class)
    public void throwsMissingPasswordExceptionWhenPasswordIsNull() {
        GumtreeAuthenticationToken upToken = createAuthToken(USERNAME, null);
        realm.doGetAuthenticationInfo(upToken);
    }

    @Test(expected = MissingPasswordException.class)
    public void throwsMissingPasswordExceptionWhenPasswordIsEmpty() {
        GumtreeAuthenticationToken upToken = createAuthToken(USERNAME, "");
        realm.doGetAuthenticationInfo(upToken);
    }

    @Test(expected = MissingUsernameAndPasswordException.class)
    public void throwsMissingUsernameAndPasswordExceptionWhenUsernameAndPasswordAreNull() {
        GumtreeAuthenticationToken upToken = createAuthToken(null, null);
        realm.doGetAuthenticationInfo(upToken);
    }

    @Test(expected = MissingUsernameAndPasswordException.class)
    public void throwsMissingUsernameAndPasswordExceptionWhenUsernameAndPasswordAreEmpty() {
        GumtreeAuthenticationToken upToken = createAuthToken("", "");
        realm.doGetAuthenticationInfo(upToken);
    }

    @Test(expected = MissingUsernameAndPasswordException.class)
    public void throwsMissingUsernameAndPasswordExceptionWhenUsernameIsNullAndPasswordIsEmpty() {
        GumtreeAuthenticationToken upToken = createAuthToken(null, "");
        realm.doGetAuthenticationInfo(upToken);
    }

    @Test(expected = MissingUsernameAndPasswordException.class)
    public void throwsMissingUsernameAndPasswordExceptionWhenUsernameIsEmptyAndPasswordIsNull() {
        GumtreeAuthenticationToken upToken = createAuthToken("", null);
        realm.doGetAuthenticationInfo(upToken);
    }

    @Test
    public void realmNameIsSetCorrectly() {
        assertThat(realm.getName()).isEqualTo(GumtreeRealm.GUMTREE_REALM_NAME);
    }

    @Test(expected = IllegalArgumentException.class)
    public void throwsIllegalArgumentExceptionIfTokenIsNotOfCorrectType() {
        realm.doGetAuthenticationInfo(Mockito.mock(AuthenticationToken.class));
    }

    @Test
    public void returnsAuthenticationInfoFromApiResponseWhenAuthenticationIsSuccessful() {
        // given
        AuthenticationRequest authRequest = AuthenticationRequestBuilder.builder().setUsername(USERNAME)
                .setPassword(PASSWORD).setAuthProvider(GUMTREE).setThreatMetrixSessionId(TM_SESSION_ID).setIpAddress(IP_ADDRESS).build();
        when(userServiceFacade.authenticate(authRequest))
                .thenReturn(ApiResponse.of(AuthenticationResponseBuilder.builder().setUsername(USERNAME).setGumtreeAccessToken("10").build()));

        GumtreeAuthenticationToken token = GumtreeAuthenticationToken.builder()
                .withUsername(USERNAME)
                .withPassword(PASSWORD)
                .withAuthenticationProvider(GUMTREE)
                .withIpAddress(Optional.of(IP_ADDRESS))
                .withthreatmetrixSessionId(Optional.of(TM_SESSION_ID))
                .build();

        // when
        AuthenticationInfo authInfo = realm.doGetAuthenticationInfo(token);

        // then
        assertThat(authInfo).isInstanceOf(UserAuthenticationInfo.class);
        UserAuthenticationInfo usrAuthInfo = (UserAuthenticationInfo) authInfo;

        assertThat(usrAuthInfo.getUsername()).isEqualTo(USERNAME);
        assertThat(usrAuthInfo.getPrincipals())
                .isEqualTo(new SimplePrincipalCollection(Lists.newArrayList(USERNAME, GumtreeAccessToken.create(10L)), GumtreeRealm.GUMTREE_REALM_NAME));
        assertThat(usrAuthInfo.getPrincipals().getPrimaryPrincipal()).isEqualTo(USERNAME);
    }

    @Test
    public void shouldAuthenticateUsingUsernameAndPasswordToken() {
        // given
        AuthenticationRequest authRequest = AuthenticationRequestBuilder.builder().setUsername(USERNAME)
                .setPassword(PASSWORD).setAuthProvider(GUMTREE).build();
        when(userServiceFacade.authenticate(authRequest)).thenReturn(ApiResponse.of(AuthenticationResponseBuilder.builder().setUsername(USERNAME).build()));

        // when
        UsernamePasswordToken token = new UsernamePasswordToken();
        token.setUsername(USERNAME);
        token.setPassword(PASSWORD.toCharArray());
        AuthenticationInfo authInfo = realm.doGetAuthenticationInfo(token);

        // then
        assertThat(authInfo).isInstanceOf(UserAuthenticationInfo.class);
        UserAuthenticationInfo usrAuthInfo = (UserAuthenticationInfo) authInfo;

        assertThat(usrAuthInfo.getUsername()).isEqualTo(USERNAME);
        assertThat(usrAuthInfo.getPrincipals())
                .isEqualTo(new SimplePrincipalCollection(Lists.newArrayList(USERNAME), GumtreeRealm.GUMTREE_REALM_NAME));
    }

    @Test
    public void userApiAuthenticationErrorIsWrapInAuthenticationException() {
        exception.expect(IncorrectCredentialsException.class);
        exception.expectMessage(UserApiErrorCode.INVALID_USER_TOKEN.name());

        // given
        when(userServiceFacade.authenticate(any(AuthenticationRequest.class)))
                .thenReturn(ApiResponse.error(
                        UserApiErrorsBuilder.builder().withErrorCode(UserApiErrorCode.INVALID_USER_TOKEN).build()));

        // when
        GumtreeAuthenticationToken gumtreeAuthenticationToken = GumtreeAuthenticationToken.builder()
                .withUsername(USERNAME).withPassword(PASSWORD).build();

        realm.doGetAuthenticationInfo(gumtreeAuthenticationToken);
        fail("unreachable");
    }

    @Test
    public void shouldAutoRegisterUserIfUserIsNotFoundIfLoginViaFacebook() {
        AuthenticationProvider provider = FACEBOOK;
        // given
        mockAuthentication(provider, Lists.newArrayList(UserApiErrorCode.USER_NOT_FOUND, null));
        mockRegistration(provider, null);

        // when
        GumtreeAuthenticationToken gumtreeAuthenticationToken = GumtreeAuthenticationToken.builder()
                .withUsername(USERNAME)
                .withPassword(PASSWORD)
                .withAuthenticationProvider(provider)
                .withIpAddress(Optional.of(IP_ADDRESS))
                .withthreatmetrixSessionId(Optional.of(TM_SESSION_ID))
                .build();

        AuthenticationInfo authInfo = realm.doGetAuthenticationInfo(gumtreeAuthenticationToken);

        // then
        assertThat(authInfo).isInstanceOf(UserAuthenticationInfo.class);
        UserAuthenticationInfo usrAuthInfo = (UserAuthenticationInfo) authInfo;
        assertThat(usrAuthInfo.getUsername()).isEqualTo(USERNAME);
    }

    @Test
    public void shouldAutoRegisterUserIfUserIsInactiveIfLoginViaFacebook() {
        AuthenticationProvider provider = FACEBOOK;

        // given
        mockAuthentication(provider, Lists.newArrayList(UserApiErrorCode.USER_INACTIVE, null));
        mockRegistration(provider, null);

        // when
        GumtreeAuthenticationToken gumtreeAuthenticationToken = GumtreeAuthenticationToken.builder()
                .withUsername(USERNAME)
                .withPassword(PASSWORD)
                .withAuthenticationProvider(provider)
                .withIpAddress(Optional.of(IP_ADDRESS))
                .withthreatmetrixSessionId(Optional.of(TM_SESSION_ID))
                .build();

        AuthenticationInfo authInfo = realm.doGetAuthenticationInfo(gumtreeAuthenticationToken);

        // then
        assertThat(authInfo).isInstanceOf(UserAuthenticationInfo.class);
        UserAuthenticationInfo usrAuthInfo = (UserAuthenticationInfo) authInfo;
        assertThat(usrAuthInfo.getUsername()).isEqualTo(USERNAME);
    }

    @Test
    public void shouldNotAutoRegisterUserIfUserIsSuspended() {
        exception.expect(UserAccountSuspendedException.class);

        AuthenticationProvider provider = FACEBOOK;

        // given
        mockAuthentication(provider, Lists.newArrayList(UserApiErrorCode.USER_SUSPENDED, null));
        mockRegistration(provider, null);

        // when
        GumtreeAuthenticationToken gumtreeAuthenticationToken = GumtreeAuthenticationToken.builder()
                .withUsername(USERNAME).withPassword(PASSWORD).withAuthenticationProvider(provider)
                .withthreatmetrixSessionId(Optional.of(TM_SESSION_ID)).withIpAddress(Optional.of(IP_ADDRESS)).build();

        realm.doGetAuthenticationInfo(gumtreeAuthenticationToken);

        fail("should never get that far");
    }

    @Test
    public void shouldNotAuthenticateUserIfPostAutoRegistrationAuthenticationFails() {
        exception.expect(IncorrectCredentialsException.class);
        exception.expectMessage(is(UserApiErrorCode.UNEXPECTED_ERROR.name()));

        AuthenticationProvider provider = FACEBOOK;

        // given
        mockAuthentication(provider, Lists.newArrayList(UserApiErrorCode.USER_NOT_FOUND, UserApiErrorCode.UNEXPECTED_ERROR));
        mockRegistration(provider, null);

        // when
        GumtreeAuthenticationToken gumtreeAuthenticationToken = GumtreeAuthenticationToken.builder()
                .withUsername(USERNAME).withPassword(PASSWORD).withAuthenticationProvider(provider)
                .withthreatmetrixSessionId(Optional.of(TM_SESSION_ID)).withIpAddress(Optional.of(IP_ADDRESS)).build();

        realm.doGetAuthenticationInfo(gumtreeAuthenticationToken);

        fail("should never get that far");
    }

    @Test
    public void shouldNotAuthenticateUserIfAutoRegistrationFails() {
        exception.expect(UserApiAuthenticationException.class);
        exception.expectMessage(is("Auto registration error: VALIDATION_ERROR"));

        AuthenticationProvider provider = FACEBOOK;

        // given
        mockAuthentication(provider, Lists.newArrayList(UserApiErrorCode.USER_NOT_FOUND, null));
        mockRegistration(provider, UserApiErrorCode.VALIDATION_ERROR);

        // when
        GumtreeAuthenticationToken gumtreeAuthenticationToken = GumtreeAuthenticationToken.builder()
                .withUsername(USERNAME).withPassword(PASSWORD).withAuthenticationProvider(provider)
                .withthreatmetrixSessionId(Optional.of(TM_SESSION_ID)).withIpAddress(Optional.of(IP_ADDRESS)).build();

        realm.doGetAuthenticationInfo(gumtreeAuthenticationToken);

        fail("should never get that far");
    }

    @Test
    public void shouldAutoRegisterUserIfUserIsNotFoundIfLoginViaGoogle() {
        AuthenticationProvider provider = GOOGLE;
        // given
        mockAuthentication(provider, Lists.newArrayList(UserApiErrorCode.USER_NOT_FOUND, null));
        mockRegistration(provider, null);

        // when
        GumtreeAuthenticationToken gumtreeAuthenticationToken = GumtreeAuthenticationToken.builder()
                .withUsername(USERNAME).withPassword(PASSWORD).withAuthenticationProvider(provider)
                .withthreatmetrixSessionId(Optional.of(TM_SESSION_ID)).withIpAddress(Optional.of(IP_ADDRESS)).build();

        AuthenticationInfo authInfo = realm.doGetAuthenticationInfo(gumtreeAuthenticationToken);

        // then
        assertThat(authInfo).isInstanceOf(UserAuthenticationInfo.class);
    }

    @Test
    public void shouldNotAutoRegisterUserIfUserIsNotFoundIfLoginViaBapi() {
        exception.expect(IncorrectCredentialsException.class);
        exception.expectMessage(is(UserApiErrorCode.USER_NOT_FOUND.name()));

        AuthenticationProvider provider = GUMTREE;

        // given
        mockAuthentication(provider, Lists.newArrayList(UserApiErrorCode.USER_NOT_FOUND, null));
        mockRegistration(provider, null);

        // when
        GumtreeAuthenticationToken gumtreeAuthenticationToken = GumtreeAuthenticationToken.builder()
                .withUsername(USERNAME).withPassword(PASSWORD).withAuthenticationProvider(provider)
                .withthreatmetrixSessionId(Optional.of(TM_SESSION_ID)).withIpAddress(Optional.of(IP_ADDRESS)).build();

        realm.doGetAuthenticationInfo(gumtreeAuthenticationToken);

        fail("should never get that far");
    }

    private GumtreeAuthenticationToken createAuthToken(String username, String password) {
        return GumtreeAuthenticationToken.builder().withUsername(username).withPassword(password).build();
    }

    private void mockRegistration(AuthenticationProvider provider, UserApiErrorCode error) {
        UserRegistrationRequest req = new UserRegistrationRequestBuilder()
                .setUsername(USERNAME)
                .setAccessToken(PASSWORD)
                .setAuthenticationProvider(provider)
                .build();

        if (error == null) {
            when(userServiceFacade.registerUser(req)).thenReturn(ApiResponse.of(RegisteredUserBuilder.builder()
                    .setUserId(1L)
                    .setUserStatus("ACTIVE")
                    .build()));
        } else {
            when(userServiceFacade.registerUser(req))
                    .thenReturn(ApiResponse.error(UserApiErrorsBuilder.builder().withErrorCode(error).build()));
        }
    }

    private void mockAuthentication(AuthenticationProvider provider, final List<UserApiErrorCode> errors) {
        Assert.notEmpty(errors);
        AuthenticationRequest req = AuthenticationRequestBuilder.builder()
                .setUsername(USERNAME)
                .setPassword(PASSWORD)
                .setAuthProvider(provider)
                .setThreatMetrixSessionId(TM_SESSION_ID)
                .setIpAddress(IP_ADDRESS)
                .build();

        when(userServiceFacade.authenticate(req)).thenAnswer(new Answer<Object>() {
            int count = -1;

            @Override
            public Object answer(InvocationOnMock invocation) {
                count++;

                UserApiErrorCode error = errors.get(count);
                if (error != null) {
                    return ApiResponse.error(UserApiErrorsBuilder.builder().withErrorCode(error).build());
                } else {
                    return ApiResponse.of(AuthenticationResponseBuilder.builder()
                            .setUsername(USERNAME).setGumtreeAccessToken(GumtreeAccessTokenFactory.createFromString(GT_ACCESS_TOKEN)).build());
                }
            }
        });
    }
}
