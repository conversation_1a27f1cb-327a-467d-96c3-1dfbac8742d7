package com.gumtree.web.security.login;

import com.gumtree.common.util.security.exception.MissingPasswordException;
import com.gumtree.common.util.security.exception.MissingUsernameAndPasswordException;
import com.gumtree.common.util.security.exception.MissingUsernameException;
import com.gumtree.common.util.security.exception.UnknownUserAccountException;
import com.gumtree.common.util.security.exception.UserAccountNotActiveException;
import com.gumtree.common.util.security.exception.UserAccountSuspendedException;
import com.gumtree.common.util.error.ErrorReporter;
import com.gumtree.common.util.error.ReportableErrorsArguments;
import com.gumtree.common.util.error.ReportableErrorsArgumentsImpl;
import com.gumtree.web.security.exception.UserExistsException;
import com.gumtree.web.security.exception.UsernameInvalidException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

/**
 */
public class LoginFailureTest {

    private ErrorReporter reporter;

    @Before
    public void init() {
        reporter = mock(ErrorReporter.class);
    }

    @Test
    public void genericAuthenticationErrorIsFallbackForGeneralAuthenticationException() {
        assertThat(LoginFailure.failureFor(new AuthenticationException()), equalTo(LoginFailure.GENERIC_AUTHENTICATION_ERROR));
    }

    @Test
    public void genericAuthenticationErrorReportsCorrectError() {
        LoginFailure.GENERIC_AUTHENTICATION_ERROR.report(reporter);
        verify(reporter).globalError(LoginFailureMessageCodes.DEFAULT_LOGIN_ERROR_MESSAGE_CODE);
        verifyNoMoreInteractions(reporter);
    }

    @Test
    public void unknownAccountIsFailureForUnknownUserAccountException() {
        assertThat(LoginFailure.failureFor(new UnknownUserAccountException()), equalTo(LoginFailure.UNKNOWN_ACCOUNT));
    }

    @Test
    public void unknownAccountReportsCorrectError() {
        LoginFailure.UNKNOWN_ACCOUNT.report(reporter);
        verify(reporter).fieldError(LoginFailure.USERNAME_FIELD, LoginFailureMessageCodes.USERNAME_PASSWORD_INVALID_MESSAGE_CODE);
        verify(reporter).fieldError(LoginFailure.PASSWORD_FIELD, LoginFailureMessageCodes.USERNAME_PASSWORD_INVALID_MESSAGE_CODE);
        verifyNoMoreInteractions(reporter);
    }

    @Test
    public void userAccountSuspendedIsFailureForUserAccountSuspendedException() {
        assertThat(LoginFailure.failureFor(new UserAccountSuspendedException()), equalTo(LoginFailure.USER_ACCOUNT_SUSPENDED));
    }

    @Test
    public void userAccountSuspendedReportsCorrectError() {
        LoginFailure.USER_ACCOUNT_SUSPENDED.report(reporter);
        verify(reporter).fieldError(LoginFailure.USERNAME_FIELD, LoginFailureMessageCodes.SUSPENDED_USER_MESSAGE_CODE);
        verifyNoMoreInteractions(reporter);
    }

    @Test
    public void userAccountNotActiveIsFailureForUserAccountNotActiveException() {
        assertThat(LoginFailure.failureFor(new UserAccountNotActiveException()), equalTo(LoginFailure.USER_ACCOUNT_NOT_ACTIVE));
    }

    @Test
    public void userAccountNotActiveReportsCorrectError() {
        ReportableErrorsArguments args = ReportableErrorsArgumentsImpl.getInstance();
        args.add(LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE, "someArg");
        LoginFailure.USER_ACCOUNT_NOT_ACTIVE.report(reporter, args);

        verify(reporter).fieldError(eq(LoginFailure.USERNAME_FIELD), eq(
                LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE), any());
        verifyNoMoreInteractions(reporter);
    }

    @Test
    public void incorrectCredentialsIsFailureForIncorrectCredentialsException() {
        assertThat(LoginFailure.failureFor(new IncorrectCredentialsException()), equalTo(LoginFailure.INCORRECT_CREDENTIALS));
    }

    @Test
    public void incorrectCredentialsReportsCorrectError() {
        LoginFailure.INCORRECT_CREDENTIALS.report(reporter);
        verify(reporter).fieldError(LoginFailure.USERNAME_FIELD, LoginFailureMessageCodes.USERNAME_PASSWORD_INVALID_MESSAGE_CODE);
        verify(reporter).fieldError(LoginFailure.PASSWORD_FIELD, LoginFailureMessageCodes.USERNAME_PASSWORD_INVALID_MESSAGE_CODE);
        verifyNoMoreInteractions(reporter);
    }

    @Test
    public void missingUsernameIsFailureForMissingUsernameException() {
        assertThat(LoginFailure.failureFor(new MissingUsernameException()), equalTo(LoginFailure.MISSING_USERNAME));
    }

    @Test
    public void missingUsernameReportsCorrectError() {
        LoginFailure.MISSING_USERNAME.report(reporter);
        verify(reporter).fieldError(LoginFailure.USERNAME_FIELD, LoginFailureMessageCodes.MISSING_USERNAME_MESSAGE_CODE);
        verifyNoMoreInteractions(reporter);
    }

    @Test
    public void missingPasswordIsFailureForMissingPasswordException() {
        assertThat(LoginFailure.failureFor(new MissingPasswordException()), equalTo(LoginFailure.MISSING_PASSWORD));
    }

    @Test
    public void missingPasswordReportsCorrectError() {
        LoginFailure.MISSING_PASSWORD.report(reporter);
        verify(reporter).fieldError(LoginFailure.PASSWORD_FIELD, LoginFailureMessageCodes.MISSING_PASSWORD_MESSAGE_CODE);
        verifyNoMoreInteractions(reporter);
    }

    @Test
    public void missingUsernameAndPasswordIsFailureForMissingUsernameAndPasswordException() {
        assertThat(LoginFailure.failureFor(new MissingUsernameAndPasswordException()), equalTo(LoginFailure.MISSING_USERNAME_AND_PASSWORD));
    }

    @Test
    public void missingUsernameAndPasswordReportsCorrectError() {
        LoginFailure.MISSING_USERNAME_AND_PASSWORD.report(reporter);
        verify(reporter).fieldError(LoginFailure.USERNAME_FIELD, LoginFailureMessageCodes.MISSING_USERNAME_MESSAGE_CODE);
        verify(reporter).fieldError(LoginFailure.PASSWORD_FIELD, LoginFailureMessageCodes.MISSING_PASSWORD_MESSAGE_CODE);
        verifyNoMoreInteractions(reporter);
    }

    @Test
    public void userExistsIsFailureForUserExistsException() {
        assertThat(LoginFailure.failureFor(new UserExistsException()), equalTo(LoginFailure.USER_EXISTS_ERROR));
    }

    @Test
    public void userExistsReportsCorrectError() {
        LoginFailure.USER_EXISTS_ERROR.report(reporter);
        verify(reporter).fieldError(LoginFailure.USERNAME_FIELD, LoginFailureMessageCodes.USERNAME_EXISTS_MESSAGE_CODE);
        verifyNoMoreInteractions(reporter);
    }

    @Test
    public void usernameInvalidIsFailureForUsernameInvalidException() {
        assertThat(LoginFailure.failureFor(new UsernameInvalidException()), equalTo(LoginFailure.INVALID_USERNAME_ERROR));
    }

    @Test
    public void usernameInvalidReportsCorrectError() {
        LoginFailure.INVALID_USERNAME_ERROR.report(reporter);
        verify(reporter).fieldError(LoginFailure.USERNAME_FIELD, LoginFailureMessageCodes.USERNAME_INVALID_MESSAGE_CODE);
        verifyNoMoreInteractions(reporter);
    }
}
