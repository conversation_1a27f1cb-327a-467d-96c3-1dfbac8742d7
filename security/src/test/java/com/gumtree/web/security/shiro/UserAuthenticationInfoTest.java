package com.gumtree.web.security.shiro;

import com.gumtree.userapi.model.GumtreeAccessToken;
import org.apache.shiro.subject.PrincipalCollection;
import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class UserAuthenticationInfoTest {
    @Test
    public void shouldGetPrincipals() {
        // given
        UserAuthenticationInfo info = new UserAuthenticationInfo();
        info.setUsername("michal");
        info.setAccessToken(com.gumtree.userapi.model.GumtreeAccessToken.create(99L));

        // when
        PrincipalCollection principals = info.getPrincipals();

        // then
        assertThat(principals.getPrimaryPrincipal()).isEqualTo("michal");
        assertThat(principals.byType(GumtreeAccessToken.class)).containsOnly(GumtreeAccessToken.create(99L));
    }

}