package com.gumtree.web.security.shiro;

import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.api.domain.user.beans.RegisterUserBean;
import com.gumtree.common.properties.GtPropManager;
import com.gumtree.config.SellerProperty;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.MessageCentreCookieHelper;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.login.LoginUtils;
import io.cucumber.java.Before;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.apache.shiro.web.util.SavedRequest;
import org.jboss.resteasy.client.ClientResponse;
import org.jboss.resteasy.client.ClientResponseFailure;
import org.mockito.Matchers;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URI;

import static com.gumtree.web.security.login.LoginFailure.INVALID_USERNAME_ERROR;
import static com.gumtree.web.security.login.LoginFailure.MISSING_USERNAME;
import static com.gumtree.web.security.login.LoginFailure.USER_EXISTS_ERROR;
import static com.gumtree.web.security.shiro.GumtreeFormAuthenticationFilter.LOGIN_FAILURE;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class AmazonStyleLoginSteps extends BaseShiroTest {

    private HttpServletRequest servletRequest;

    private HttpServletResponse servletResponse;

    private GumtreeFormAuthenticationFilter filter;

    private UrlScheme urlScheme;

    private BushfireApi bushfireApi;

    private UserApi userApi;

    private LoginUtils loginUtils;

    private Subject subject;

    private SavedRequest savedRequest;

    private User user;

    private MessageCentreCookieHelper messageCentreCookieHelper;

    private boolean onAccessDeniedResult = false;

    private CookieResolver cookieResolver;

    private ThreatMetrixCookie threatMetrixCookie;

    private String username;

    private UserSession userSession;

    private MeterRegistry meterRegistry;

    @Before
    public void init() {
        subject = mock(Subject.class);
        servletRequest = mock(HttpServletRequest.class);
        servletResponse = mock(HttpServletResponse.class);
        loginUtils = mock(LoginUtils.class);
        urlScheme = mock(UrlScheme.class);
        bushfireApi = mock(BushfireApi.class);
        userApi = mock(UserApi.class);
        messageCentreCookieHelper = mock(MessageCentreCookieHelper.class);
        when(bushfireApi.userApi()).thenReturn(userApi);
        userSession = mock(UserSession.class);
        cookieResolver = mock(CookieResolver.class);
        threatMetrixCookie = mock(ThreatMetrixCookie.class);
        meterRegistry = mock(MeterRegistry.class);

        when(cookieResolver.resolve(servletRequest, ThreatMetrixCookie.class)).thenReturn(threatMetrixCookie);
        when(threatMetrixCookie.getDefaultValue()).thenReturn("123-432");

        filter =
            new GumtreeFormAuthenticationFilter(urlScheme, bushfireApi, loginUtils, userSession,
                    messageCentreCookieHelper, cookieResolver, meterRegistry) {

                @Override
                protected boolean executeLogin(ServletRequest request, ServletResponse response) throws Exception {
                    // do nothing in test
                    return true;
                }

                @Override
                protected void issueSuccessRedirect(ServletRequest request, ServletResponse response) throws Exception {
                    // do nothing in test
                }
            };

        when(urlScheme.postAdUrl()).thenReturn("http://www.gumtree.com/legacy/postad/url");
        when(urlScheme.urlFor(Actions.CREATE_ACCOUNT)).thenReturn("http://www.gumtree.com/create-account");
        when(urlScheme.bushfirePostAdUrlForCategoryId(anyLong())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocationOnMock) throws Throwable {
                Long id = (Long) invocationOnMock.getArguments()[0];
                if (id == null) {
                    return "http://www.gumtree.com/postad";
                } else {
                    return "http://www.gumtree.com/postad?categoryId=" + id;
                }
            }
        });
        setSubject(subject);

        // Standard login request
        filter.setLoginUrl("http://gumtree.com/login/url");
        when(servletRequest.getContextPath()).thenReturn("");
        when(servletRequest.getRequestURI()).thenReturn("/login/url");
        when(servletRequest.getMethod()).thenReturn(AccessControlFilter.POST_METHOD);
    }

    @Given("^a user who has visited the login page directly$")
    public void a_user_who_has_visited_the_login_page_directly() throws Exception {
        savedRequest = null;
        when(loginUtils.getSavedRequest(servletRequest)).thenReturn(null);
    }

    @When("^the user enters an existing email address for an active user$")
    public void the_user_enters_an_existing_email_address_for_an_active_bushfire_user() {
        userEntersEmailAddress("<EMAIL>");
        setExistingUser("<EMAIL>", UserStatus.ACTIVE, true);
    }

    @When("^the user says they are new$")
    public void the_user_says_they_are_new() {
        when(servletRequest.getParameter("newUser")).thenReturn(Boolean.toString(true));
    }

    @When("^the user clicks continue$")
    public void the_user_clicks_continue() throws Exception {
        onAccessDeniedResult = filter.onAccessDenied(servletRequest, servletResponse);
    }

    @Then("^the user should be shown an existing user error$")
    public void the_user_should_be_shown_an_existing_user_error() {
        assertThat(onAccessDeniedResult, equalTo(true));
        verify(servletRequest).setAttribute(LOGIN_FAILURE, USER_EXISTS_ERROR);
    }

    @Given("^a user who has visited the login page via non-post ad url$")
    public void a_user_who_has_visited_the_login_page_via_non_post_ad_url() throws Exception {
        savedRequest = createSavedRequest("GET", "/account/ads", null);
        when(loginUtils.getSavedRequest(servletRequest)).thenReturn(savedRequest);
    }

    @When("^the user enters no email address$")
    public void the_user_enters_no_email_address() {
        when(servletRequest.getParameter("username")).thenReturn("");
    }

    @Then("^the user should be shown a missing username error$")
    public void the_user_should_be_shown_a_missing_username_error() {
        assertThat(onAccessDeniedResult, equalTo(true));
        verify(servletRequest).setAttribute(LOGIN_FAILURE, MISSING_USERNAME);
    }

    @When("^the user enters an invalid email address$")
    public void the_user_enters_an_invalid_email_address() {
        userEntersEmailAddress("<EMAIL>");
        userIsInvalid();
    }

    @Then("^the user should be shown an invalid username error$")
    public void the_user_should_be_shown_an_invalid_username_error() {
        assertThat(onAccessDeniedResult, equalTo(true));
        verify(servletRequest).setAttribute(LOGIN_FAILURE, INVALID_USERNAME_ERROR);
    }

    @When("^the user enters a valid non-existing email address$")
    public void the_user_enters_a_valid_non_existing_email_address() {
        userEntersEmailAddress("<EMAIL>");
        setNonExistingUser("<EMAIL>");
    }

    @Then("^authentication cookies should be cleared$")
    public void authentication_cookies_should_be_cleared() {
        verify(userSession, atLeastOnce()).clearSession();
    }

    @Then("^the user should have to login to post an ad$")
    public void the_user_should_have_to_login_to_post_a_Bushfire_ad() {
        verify(loginUtils, atLeastOnce()).setNewUserMustLoginToStartPostAdFlow(true);
        verify(loginUtils, never()).setNewUserMustLoginToStartPostAdFlow(false);
    }

    @Then("^the user dirty state should be true$")
    public void the_user_dirty_state_should_be_true() {
        verify(loginUtils, atLeastOnce()).setUserDirty(true);
        verify(loginUtils, never()).setUserDirty(false);
    }

    @Then("^the user should be redirected to the create account page url$")
    public void the_user_should_be_redirected_to_the_create_account_page_url() throws Exception {
        assertThat(onAccessDeniedResult, equalTo(false));
        verify(loginUtils).clearSavedRequest(servletRequest);
        verify(servletResponse).sendRedirect("http://www.gumtree.com/create-account");
    }

    @When("^the user enters an existing email address for an unactivated user$")
    public void the_user_enters_an_existing_email_address_for_an_unactivated_bushfire_user() {
        userEntersEmailAddress("<EMAIL>");
        setExistingUser("<EMAIL>", UserStatus.AWAITING_ACTIVATION, true);
    }

    @When("^the user enters an existing email address for a deactivated user$")
    public void the_user_enters_an_existing_email_address_for_a_deactivated_bushfire_user() {
        userEntersEmailAddress("<EMAIL>");
        setExistingUser("<EMAIL>", UserStatus.DEACTIVATED, true);
    }

    @Given("^a user who has been redirected to the login page from a legacy post ad url$")
    public void a_user_who_has_been_redirected_to_the_login_page_from_a_legacy_post_ad_url() throws Exception {
        savedRequest = createSavedRequest("GET", "/add_posting.html", null);
        when(loginUtils.getSavedRequest(servletRequest)).thenReturn(savedRequest);
    }

    @Then("^the user should be redirected to the saved request$")
    public void the_user_should_be_redirected_to_the_saved_request() throws Exception {
        assertThat(onAccessDeniedResult, equalTo(false));
        verify(loginUtils, never()).clearSavedRequest(Matchers.<ServletRequest>any());
        verify(loginUtils).redirectToSavedRequest(servletRequest, servletResponse,
                "http://www.gumtree.com/legacy/postad/url");
    }

    @Then("^the user should not have to login to post an ad$")
    public void the_user_should_not_have_to_login_to_post_a_Bushfire_ad() {
        verify(loginUtils, atLeastOnce()).setNewUserMustLoginToStartPostAdFlow(false);
        verify(loginUtils, never()).setNewUserMustLoginToStartPostAdFlow(true);
    }

    @Given("^a user who has been redirected to the login page from a bushfire post ad url$")
    public void a_user_who_has_been_redirected_to_the_login_page_from_a_bushfire_post_ad_url() throws Exception {
        savedRequest = createSavedRequest("GET", "/postad", null);
        when(loginUtils.getSavedRequest(servletRequest)).thenReturn(savedRequest);
    }

    @Then("^the user's email address should be stored in the session$")
    public void the_user_s_email_address_should_be_stored_in_the_session() {
        verify(loginUtils, atLeastOnce()).storeNewUserEmailAddressInSession(username);
    }

    @Then("^the user should be redirected to the post ad flow$")
    public void the_user_should_be_redirected_to_the_bushfire_post_ad_flow() throws Exception {
        assertThat(onAccessDeniedResult, equalTo(false));
        verify(loginUtils).clearSavedRequest(servletRequest);
        verify(servletResponse).sendRedirect("http://www.gumtree.com/postad");
    }

    @Given("^a user who has been redirected to the login page after visiting (.*)")
    public void a_user_who_has_been_redirected_to_the_login_page_after_visiting(String url) throws Exception {
        URI uri = URI.create("http://www.gumtree.com" + url);
        savedRequest = createSavedRequest("GET", uri.getPath(), uri.getQuery());
        when(loginUtils.getSavedRequest(servletRequest)).thenReturn(savedRequest);
    }

    @Then("^the user should be redirected directly to (.*)$")
    public void the_user_should_be_redirected_directly_to(String redirectUrl) throws Exception {
        assertThat(onAccessDeniedResult, equalTo(false));
        verify(loginUtils).clearSavedRequest(servletRequest);
        verify(servletResponse).sendRedirect("http://www.gumtree.com" + redirectUrl);
    }

    @Then("^the user's email address should not be stored in the session$")
    public void the_user_s_email_address_should_not_be_stored_in_the_session() {
        verify(loginUtils, atLeastOnce()).clearNewUserEmailAddressFromSession();
    }

    private void userEntersEmailAddress(String emailAddress) {
        this.username = emailAddress;
        when(servletRequest.getParameter("username")).thenReturn(emailAddress);
    }

    private void userIsInvalid() {
        ClientResponseFailure failure = mock(ClientResponseFailure.class);
        ClientResponse clientResponse = mock(ClientResponse.class);
        when(failure.getResponse()).thenReturn(clientResponse);
        when(clientResponse.getStatus()).thenReturn(400);
        when(bushfireApi.userApi().getUserByEmailAddress(anyString())).thenThrow(failure);
    }

    private void setExistingUser(String emailAddress, UserStatus status, boolean isBushfireUser) {
        user = new User();
        user.setEmail(emailAddress);
        user.setStatus(status);
        user.setInBushfire(isBushfireUser);
        when(userApi.getUserByEmailAddress(emailAddress)).thenReturn(user);
    }

    private void setNonExistingUser(String emailAddress) {
        ClientResponseFailure failure = mock(ClientResponseFailure.class);
        ClientResponse clientResponse = mock(ClientResponse.class);
        when(failure.getResponse()).thenReturn(clientResponse);
        when(clientResponse.getStatus()).thenReturn(404);
        when(userApi.getUserByEmailAddress(emailAddress)).thenThrow(failure);
        when(bushfireApi.userApi().validateEmailAddress(Matchers.<RegisterUserBean>any())).thenReturn(null);
    }

    private SavedRequest createSavedRequest(String method, String path, String queryString) {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getRequestURI()).thenReturn(path);
        when(request.getMethod()).thenReturn(method);
        when(request.getQueryString()).thenReturn(queryString);
        return new SavedRequest(request);
    }
}
