package com.gumtree.web.security.shiro;

import com.netflix.config.ConfigurationManager;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.util.Map;
import java.util.Properties;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ContactLoginFilterTest {

    @Mock
    private HttpServletRequest httpRequest;
    private String callBackUri = "seller/reveal/number/**********?_=*************";
    private String vipCallBackAfterLogin = ContactLoginFilter.host + "/p/toyota/vip-page/**********";
    private String token = "465s57ad254rsf";

    @BeforeClass
    public static void setupUp() {
        loadProperties();
        ContactLoginFilter.host = "http://www.gumtree.com";
    }

    private static void loadProperties() {
        Properties properties = new Properties();
        properties.setProperty("gumtree.url.buyer.base_uri", "");
        properties.setProperty("gumtree.url.seller.base_uri", "");
        properties.setProperty("gumtree.url.seller.secure.base_uri", "");
        properties.setProperty("gumtree.url.reply.base_uri", "");
        ConfigurationManager.loadProperties(properties);
    }

    @Test
    public void shouldBuildCorrectCallBackUrlWhenAjaxRedirect() throws Exception {
        //        Given
        when(httpRequest.getRequestURI()).thenReturn("/ajax/account/" + callBackUri);
        when(httpRequest.getHeader("Referer")).thenReturn(vipCallBackAfterLogin);
        when(httpRequest.getHeader("X-GUMTREE-TOKEN")).thenReturn(token);


        //        When
        String parametersWhenAjax = ContactLoginFilter.getParametersWhenAjax(httpRequest);

        //        Then
        String encodedCallBackFromLogin = URLEncoder.encode(ContactLoginFilter.host + "/reveal/number/**********?_=*************", "UTF-8");
        String doubleEncodedQueryParametersWithCbToVipAndToken = URLEncoder.encode(URLEncoder.encode("cb=" + URLEncoder.encode(vipCallBackAfterLogin, "UTF-8") +
                        "&rt=" + token,
                "UTF-8"), "UTF-8");
        assertThat(parametersWhenAjax, equalTo("?cb=" + encodedCallBackFromLogin + "&qp=" +
                doubleEncodedQueryParametersWithCbToVipAndToken));
    }


    @Test
    public void shouldBuildCorrectCallBackUrlWhenNormalRedirect() throws Exception {
        //        Given
        when(httpRequest.getParameter(ContactLoginFilter.AllowedParam.CB.name().toLowerCase())).thenReturn(vipCallBackAfterLogin);
        when(httpRequest.getParameter(ContactLoginFilter.AllowedParam.RT.name().toLowerCase())).thenReturn(token);
        when(httpRequest.getRequestURI()).thenReturn(callBackUri);

        //        When
        Map<String, String> parametersMap = ContactLoginFilter.getParametersMap(httpRequest);

        //        Then
        assertThat(parametersMap.get(RedirectUtils.CALLBACK_PARAM_NAME), equalTo(ContactLoginFilter.host + callBackUri));
        assertThat(parametersMap.get(RedirectUtils.CALLBACK_QUERY_PARAMS_KEY), equalTo(URLEncoder.encode("cb=" + vipCallBackAfterLogin +
                "&rt=" + token, "UTF-8")));
    }

    @Test(expected = InvalidRedirectUrlException.class)
    public void shouldReturnInvalidRedirectExceptionWhenHostIsNotValid() throws Exception {
        // given
        when(httpRequest.getParameter(ContactLoginFilter.AllowedParam.CB.name().toLowerCase())).thenReturn("www.google.com/p/toyota/vip-page/**********");
        when(httpRequest.getParameter(ContactLoginFilter.AllowedParam.RT.name().toLowerCase())).thenReturn(token);
        when(httpRequest.getRequestURI()).thenReturn(callBackUri);

        // when - then
        ContactLoginFilter.getParametersMap(httpRequest);
    }

}
