package com.gumtree.user.service;

import com.gumtree.user.service.api.ThirdPartyMarketingConsentApi;
import com.gumtree.user.service.model.ThirdPartyConsentPreference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import rx.Single;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThirdPartyConsentServiceTest {

    @Mock
    ThirdPartyMarketingConsentApi thirdPartyMarketingConsentApi;
    @InjectMocks
    ThirdPartyConsentService thirdPartyConsentService;
    Long userId = 1L;

    @Test
    public void shouldGetCorrectUserThirdPartyResponse() {
        //given
        ThirdPartyConsentPreference consentPreference =
                new ThirdPartyConsentPreference().status(ThirdPartyConsentPreference.StatusEnum.ACCEPTED);

        when(thirdPartyMarketingConsentApi.getConsent(userId))
                .thenReturn(Single.just(consentPreference));

        //when
        ThirdPartyConsentPreference responsePreference =
                thirdPartyConsentService.getUserThirdPartyResponse(userId);

        //then
        assertThat(responsePreference.getStatus().getValue(), is("ACCEPTED"));
    }

    @Test
    public void shouldGetUserWithNotConsentedIfUserIsNonExistent() {
        //given
        when(thirdPartyMarketingConsentApi.getConsent(userId))
                .thenReturn(Single.just(null));

        //when
        ThirdPartyConsentPreference responsePreference =
                thirdPartyConsentService.getUserThirdPartyResponse(userId);

        //then
        assertThat(responsePreference.getStatus().getValue(), is("NOT_YET_CONSENTED"));
    }

    @Test
    public void shouldReturnEmptyConsentPreferenceOnError() {
        //given
        when(thirdPartyMarketingConsentApi.getConsent(userId))
                .thenReturn(Single.error(new RuntimeException()));

        //when
        ThirdPartyConsentPreference responsePreference =
                thirdPartyConsentService.getUserThirdPartyResponse(userId);

        //then
        assertThat(responsePreference, equalTo(new ThirdPartyConsentPreference()));
    }


    @Test
    public void shouldPutUserThirdPartyResponse() {
        //given
        ThirdPartyConsentPreference consentPreference =
                new ThirdPartyConsentPreference().status(ThirdPartyConsentPreference.StatusEnum.ACCEPTED);

        when(thirdPartyMarketingConsentApi.putConsent(userId, consentPreference))
                .thenReturn(Single.just(consentPreference));

        //when
        ThirdPartyConsentPreference responsePreference =
                thirdPartyConsentService.putUserThirdPartyResponse(consentPreference, userId);

        //then
        assertThat(responsePreference.getStatus().getValue(), is("ACCEPTED"));
    }
}
