package com.gumtree.user.service.support.exception;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.user.service.model.UserApiErrors;
import com.gumtree.user.service.support.UserServiceClient;
import com.gumtree.user.service.support.builder.UserApiErrorsBuilder;
import com.gumtree.user.service.support.factory.ApiErrorFactory;
import feign.Response;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

@DisplayName("Decoding of Errors from User Service Client")
public class UserManagementServerErrorDecoderTest {

    private Logger LOGGER = LoggerFactory.getLogger(UserManagementServerErrorDecoderTest.class);

    private UserManagementServerErrorDecoder userManagementServerErrorDecoder;
    private String methodKey = "----";
    private ObjectMapper OBJECT_MAPPER = UserServiceClient.userServiceObjectMapper();

    @BeforeEach
    public void before() {
        userManagementServerErrorDecoder = new UserManagementServerErrorDecoder(OBJECT_MAPPER);
    }

    @ParameterizedTest(name = "Decode with Error Body: Status {0}")
    @ValueSource(ints = {400, 401, 404, 404})
    public void decodeResponseWithClientErrorBody(int statusCode) throws JsonProcessingException {
        String errorCode = "errorCode";
        UserApiErrors userApiErrors = UserApiErrorsBuilder.builder()
                .withErrorCode(errorCode)
                .addError(ApiErrorFactory.createApiError("field", "errormessage"))
                .build();
        String json = OBJECT_MAPPER.writeValueAsString(userApiErrors);

        Exception e = userManagementServerErrorDecoder.decode(methodKey, Response.builder()
                .body(json, UTF_8)
                .headers(Collections.emptyMap())
                .status(statusCode)
                .build());

        assertThat(e instanceof UserApiClientException, is(true));
        UserApiClientException uae = (UserApiClientException)e;
        LOGGER.info("Error: {}}", uae.getUserApiErrors().get());
        uae.getUserApiErrors().get().getErrors().stream().forEach(apiError -> {
            assertThat(apiError.getError(), is("errormessage"));
            assertThat(apiError.getField(), is("field"));
        });

        assertThat(e.getMessage(), is(errorCode));
    }

    @ParameterizedTest(name = "Decode with Error Body: Status {0}")
    @ValueSource(ints = {500, 502, 503})
    public void decodeResponseWithServerErrorBody(int statusCode) throws JsonProcessingException {
        String errorCode = "errorCode";
        UserApiErrors userApiErrors = UserApiErrorsBuilder.builder()
                .withErrorCode(errorCode)
                .addError(ApiErrorFactory.createApiError("field", "errormessage"))
                .build();
        String json = OBJECT_MAPPER.writeValueAsString(userApiErrors);

        Exception e = userManagementServerErrorDecoder.decode(methodKey, Response.builder()
                .body(json, UTF_8)
                .headers(Collections.emptyMap())
                .status(statusCode)
                .build());

        assertThat(e instanceof UserApiServerException, is(true));
        UserApiServerException uae = (UserApiServerException)e;
        LOGGER.info("Error: {}}", uae.getUserApiErrors().get());
        uae.getUserApiErrors().get().getErrors().stream().forEach(apiError -> {
            assertThat(apiError.getError(), is("errormessage"));
            assertThat(apiError.getField(), is("field"));
        });

        assertThat(e.getMessage(), is(errorCode));
    }

    @ParameterizedTest(name = "Decode Client Exceptions no Error Body: Status {0}")
    @ValueSource(ints = {400, 401, 404, 404})
    public void decodeClientErrorDefaultResponseNoBody(int statusCode) {
        Exception e = userManagementServerErrorDecoder.decode(methodKey, Response.builder()
                .headers(Collections.emptyMap())
                .status(statusCode).build());

        assertThat(e instanceof UserApiClientException, is(true));
        assertThat(e.getMessage(), is(defaultErrorMessage(statusCode)));
    }

    @ParameterizedTest(name = "Decode Server Exceptions no Error Body: Status {0}")
    @ValueSource(ints = {500, 502, 503})
    public void decodeServerErrorDefaultResponseNoBody(int statusCode) {
        Exception e = userManagementServerErrorDecoder.decode(methodKey, Response.builder()
                .headers(Collections.emptyMap())
                .status(statusCode).build());

        assertThat(e instanceof UserApiServerException, is(true));
        assertThat(e.getMessage(), is(defaultErrorMessage(statusCode)));

    }

    @Test
    public void decodeUnauthorized() {
        Exception e = userManagementServerErrorDecoder.decode(methodKey, Response.builder()
                .headers(Collections.emptyMap())
                .status(401).build());

        assertThat(e instanceof UnauthorizedException, is(true));
    }

    @Test
    public void decodeForbiddenException() {
        Exception e = userManagementServerErrorDecoder.decode(methodKey, Response.builder()
                .headers(Collections.emptyMap())
                .status(403).build());

        assertThat(e instanceof ForbiddenException, is(true));
    }

    @Test
    public void decodeNotFoundException() {
        Exception e = userManagementServerErrorDecoder.decode(methodKey, Response.builder()
                .headers(Collections.emptyMap())
                .status(404).build());

        assertThat(e instanceof NotFoundException, is(true));
    }

    private String defaultErrorMessage(int status) {
        return String.format("Error response received with status HTTP %s", status);
    }

}