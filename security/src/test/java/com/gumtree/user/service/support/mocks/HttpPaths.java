package com.gumtree.user.service.support.mocks;

public interface HttpPaths {

    String CHANGE_PASSWORD = "/password/change";
    String PASSWORD_RESET = "/password/reset";
    String AUTHENTICATE = "/authenticate";
    String VERIFY_ACCESS_TOKEN = "/access-token/verify";
    String AUTHENTICATE_CAPI_ACCESS_TOKEN = "/authenticate/capi/token";
    String ACTIVATE = "/activate";

    String USER_TRUST_SCORE = "/user/trust-score";

    String CREATE_USER = "/user";
    String REGISTER_USER = "/user/register";
    String GET_USER_ID = "/user/id";
    String LOGOUT = "/user/logout";

    String DEACTIVATE_TOKEN_OK = "/user/([0-9]+)/deactivate/token";
    String DEACTIVATE_TOKEN_FAILED = "/user/123456/deactivate/token";

    String DEACTIVATE_ACCOUNT = "/user/deactivate";
}
