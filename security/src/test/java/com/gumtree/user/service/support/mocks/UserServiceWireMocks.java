package com.gumtree.user.service.support.mocks;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.MappingBuilder;
import com.gumtree.user.service.model.AuthenticationProvider;
import com.gumtree.user.service.model.AuthenticationResponse;
import com.gumtree.user.service.model.GumtreeAccessToken;
import com.gumtree.user.service.model.RegisteredUser;
import com.gumtree.user.service.model.UserApiErrorCode;
import com.gumtree.user.service.model.UserApiErrors;
import com.gumtree.user.service.model.VerificationDetails;
import com.gumtree.user.service.model.VerifiedUser;
import com.gumtree.user.service.support.UserServiceClient;
import com.gumtree.user.service.support.builder.AuthenticationResponseBuilder;
import com.gumtree.user.service.support.builder.RegisteredUserBuilder;
import com.gumtree.user.service.support.builder.UserApiErrorsBuilder;
import com.gumtree.user.service.support.factory.GumtreeAccessTokenFactory;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.badRequest;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.forbidden;
import static com.github.tomakehurst.wiremock.client.WireMock.matchingJsonPath;
import static com.github.tomakehurst.wiremock.client.WireMock.notFound;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;

public class UserServiceWireMocks {

    private static final ObjectMapper OBJECT_MAPPER = UserServiceClient.userServiceObjectMapper();

    public static final String OK = "ok";
    public static final String OK_EMAIL = OK + "@gumtree.com";
    public static final String INVALID_CREDENTIALS = "invalidcredentials";
    public static final String INVALID_CREDENTIALS_EMAIL = INVALID_CREDENTIALS + "@gumtree.com";
    public static final String FORBIDDEN = "forbidden";
    public static final String INVALID_TOKEN = "invalid-token";
    public static final LocalDate DATE_OF_BIRTH = LocalDate.now().minusYears(17);
    public static final String POSTCODE = "postcode";

    public static final VerificationDetails VERIFICATION_DETAILS = new VerificationDetails()
            .username(OK_EMAIL)
            .token(new GumtreeAccessToken().value(OK))
            .createdDateTime(OffsetDateTime.of(2020, 10, 15, 11, 22, 0, 0, ZoneOffset.UTC));

    public static final String FORBIDDEN_EMAIL = "<EMAIL>";

    public static final String NOTFOUND = "notfound";
    public static final String NOTFOUND_EMAIL = NOTFOUND + "@gumtree.com";
    public static final String OK_DEACTIVATION_TOKEN = "ef9ac03c-3753-41fe-8394-0dfbd07c0ed5";
    public static final String EXPIRED_DEACTIVATION_TOKEN = "d46db57d-362d-4b2e-9026-9c08c89bed91";

    public static MappingBuilder verifyAccessTokenBadData() {
        return post(urlPathEqualTo(HttpPaths.VERIFY_ACCESS_TOKEN))
                .withRequestBody(equalToJson("{\"token\":null,\"username\":null}"))
                .willReturn(badRequest());
    }

    public static MappingBuilder verifyAccessTokenOK() throws JsonProcessingException {
        VerifiedUser verifiedUser = new VerifiedUser().username(OK_EMAIL);
        return post(urlPathEqualTo(HttpPaths.VERIFY_ACCESS_TOKEN))
                .withRequestBody(matchingJsonPath("[?(@.username == '" + OK + "@gumtree.com')]"))
                .withRequestBody(matchingJsonPath("[?(@.token.value == '" + OK + "')]"))
                .willReturn(aResponse()
                        .withBody(OBJECT_MAPPER.writeValueAsString(verifiedUser))
                        .withStatus(200));
    }

    public static MappingBuilder verifyAccessTokenForbidden() {
        return post(urlPathEqualTo(HttpPaths.VERIFY_ACCESS_TOKEN))
                .withRequestBody(matchingJsonPath("[?(@.username == '" + FORBIDDEN + "@gumtree.com')]"))
                .withRequestBody(matchingJsonPath("[?(@.token.value == '" + FORBIDDEN + "')]"))
                .willReturn(forbidden());
    }

    public static MappingBuilder verifyCapiAccessTokenBadData() {
        return post(urlPathEqualTo(HttpPaths.AUTHENTICATE_CAPI_ACCESS_TOKEN))
                .withHeader("authorization", equalTo(INVALID_TOKEN))
                .willReturn(aResponse().withStatus(400));
    }

    public static MappingBuilder verifyCapiAccessTokenOK() throws JsonProcessingException {
        return post(urlPathEqualTo(HttpPaths.AUTHENTICATE_CAPI_ACCESS_TOKEN))
                .withHeader("authorization", equalTo(OK))
                .willReturn(aResponse()
                        .withBody(OBJECT_MAPPER.writeValueAsString(VERIFICATION_DETAILS))
                        .withStatus(200));
    }

    public static MappingBuilder verifyCapiAccessTokenForbidden() {
        return post(urlPathEqualTo(HttpPaths.AUTHENTICATE_CAPI_ACCESS_TOKEN))
                .withHeader("authorization", equalTo(FORBIDDEN))
                .willReturn(aResponse().withStatus(403));
    }

    /**
     * REQ: {"authProvider":"GUMTREE","password":"abc123","username": "<EMAIL>","client":"test"}
     *
     * @return #AuthenticationResponse 200
     */
    public static MappingBuilder authenticateOk() throws JsonProcessingException {
        AuthenticationResponse authenticationResponse = AuthenticationResponseBuilder.builder()
                .setGumtreeAccessToken(UUID.randomUUID().toString()).setUsername(OK_EMAIL).setAccountId(1L).build();
        return post(urlPathEqualTo(HttpPaths.AUTHENTICATE))
                .withRequestBody(matchingJsonPath("[?(@.authProvider == '" + AuthenticationProvider.GUMTREE.name() + "')]"))
                .withRequestBody(matchingJsonPath("[?(@.username == '" + OK_EMAIL + "')]"))
                .withRequestBody(matchingJsonPath("[?(@.password == '" + OK + "')]"))
                .willReturn(aResponse().withBody(OBJECT_MAPPER.writeValueAsString(authenticationResponse)).withStatus(200));
    }

    public static MappingBuilder authenticateUserNotFound() throws JsonProcessingException {
        UserApiErrors userApiErrors = UserApiErrorsBuilder.builder().withErrorCode(UserApiErrorCode.USER_NOT_FOUND).build();
        return post(urlPathEqualTo(HttpPaths.AUTHENTICATE))
                .withRequestBody(matchingJsonPath("[?(@.username == '" + NOTFOUND + "@gumtree.com')]"))
                .withRequestBody(matchingJsonPath("[?(@.password == '" + NOTFOUND + "')]"))
                .willReturn(notFound()
                        .withBody(OBJECT_MAPPER.writeValueAsString(userApiErrors)));
    }

    /**
     * REQ: {"confirmedPassword":"newpwd","password":"newpwd","username":"<EMAIL>","verificationKey":"oldpwd","verificationKeyType":"CURRENT_USER_PASSWORD","client":"test"}
     *
     * @return #GumtreeAccessToken
     * @throws JsonProcessingException
     */
    public static MappingBuilder changePasswordOK() throws JsonProcessingException {
        return post(urlPathEqualTo(HttpPaths.CHANGE_PASSWORD))
                .withRequestBody(matchingJsonPath("[?(@.username == '" + OK_EMAIL + "')]"))
                .willReturn(aResponse()
                        .withBody(OBJECT_MAPPER.writeValueAsString(GumtreeAccessTokenFactory.createFromString(UUID.randomUUID().toString())))
                        .withStatus(200));
    }

    public static MappingBuilder changePasswordFailed() throws JsonProcessingException {
        UserApiErrors userApiErrors = UserApiErrorsBuilder.builder().withErrorCode(UserApiErrorCode.INCORRECT_CREDENTIALS).build();
        return post(urlPathEqualTo(HttpPaths.CHANGE_PASSWORD))
                .withRequestBody(matchingJsonPath("[?(@.username == '" + INVALID_CREDENTIALS_EMAIL + "')]"))
                .willReturn(aResponse()
                        .withBody(OBJECT_MAPPER.writeValueAsString(userApiErrors))
                        .withStatus(401));
    }

    public static MappingBuilder logoutOk() {
        return post(urlPathEqualTo(HttpPaths.LOGOUT))
                .withRequestBody(matchingJsonPath("[?(@.token == '" + OK + "')]"))
                .willReturn(aResponse()
                        .withStatus(200));
    }

    public static MappingBuilder registerUserOk() throws JsonProcessingException {
        RegisteredUser registeredUser = RegisteredUserBuilder.builder().setUserId(1L).setUserStatus("ACTIVE").build();

        return post(urlPathEqualTo(HttpPaths.REGISTER_USER))
                .withRequestBody(matchingJsonPath("[?(@.username == '" + OK_EMAIL + "')]"))
                .willReturn(aResponse().withBody(OBJECT_MAPPER.writeValueAsString(registeredUser))
                        .withStatus(200));
    }

    public static MappingBuilder activateUserOk() {

        return post(urlPathEqualTo(HttpPaths.ACTIVATE))
                .withRequestBody(matchingJsonPath("[?(@.username == '" + OK_EMAIL + "')]"))
                .willReturn(aResponse().withStatus(200));
    }

    public static MappingBuilder resetPasswordOk() {

        return post(urlPathEqualTo(HttpPaths.PASSWORD_RESET))
                .withRequestBody(matchingJsonPath("[?(@.username == '" + OK_EMAIL + "')]"))
                .willReturn(aResponse().withStatus(200));
    }

    public static MappingBuilder resetPasswordUserApiError() {

        return post(urlPathEqualTo(HttpPaths.PASSWORD_RESET))
                .withRequestBody(matchingJsonPath("[?(@.username == '" + NOTFOUND_EMAIL + "')]"))
                .willReturn(aResponse().withStatus(500));
    }

    public static MappingBuilder initiateAccountDeactivationOk() {

        return post(urlMatching(HttpPaths.DEACTIVATE_TOKEN_OK))
                .willReturn(aResponse().withStatus(200));
    }

    public static MappingBuilder initiateAccountDeactivationFailed() {

        return post(urlMatching(HttpPaths.DEACTIVATE_TOKEN_FAILED))
                .willReturn(aResponse().withStatus(500));
    }

    public static MappingBuilder deactivateAccountOk() {

        return post(urlMatching(HttpPaths.DEACTIVATE_ACCOUNT))
                .withRequestBody(matchingJsonPath("[?(@.token == '" + OK_DEACTIVATION_TOKEN + "')]"))
                .willReturn(aResponse().withStatus(200));
    }

    public static MappingBuilder deactivateAccountExpired() {

        return post(urlMatching(HttpPaths.DEACTIVATE_ACCOUNT))
                .withRequestBody(matchingJsonPath("[?(@.token == '" + EXPIRED_DEACTIVATION_TOKEN + "')]"))
                .willReturn(aResponse().withStatus(410));
    }
}
