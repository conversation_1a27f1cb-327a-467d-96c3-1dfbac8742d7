package com.gumtree.user.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.gumtree.api.CommonApiClient;
import com.gumtree.seller.domain.deactivation.entity.DeactivationReason;
import com.gumtree.user.service.api.AccountDeactivationApi;
import com.gumtree.user.service.api.MarketingPreferenceApi;
import com.gumtree.user.service.api.UserInformationApi;
import com.gumtree.user.service.api.UserManagementApi;
import com.gumtree.user.service.model.AuthenticationProvider;
import com.gumtree.user.service.model.AuthenticationRequest;
import com.gumtree.user.service.model.AuthenticationResponse;
import com.gumtree.user.service.model.ChangePasswordRequest;
import com.gumtree.user.service.model.GumtreeAccessToken;
import com.gumtree.user.service.model.LogoutRequest;
import com.gumtree.user.service.model.RegisteredUser;
import com.gumtree.user.service.model.ResetPasswordRequest;
import com.gumtree.user.service.model.UserActivationRequest;
import com.gumtree.user.service.model.UserApiErrors;
import com.gumtree.user.service.model.UserRegistrationRequest;
import com.gumtree.user.service.model.VerificationDetails;
import com.gumtree.user.service.model.VerificationKeyType;
import com.gumtree.user.service.model.VerifyAccessTokenRequest;
import com.gumtree.user.service.support.builder.AuthenticationRequestBuilder;
import com.gumtree.user.service.support.builder.ChangePasswordRequestBuilder;
import com.gumtree.user.service.support.builder.UserApiErrorsBuilder;
import com.gumtree.user.service.support.builder.UserRegistrationRequestBuilder;
import com.gumtree.user.service.support.exception.UserManagementServerErrorDecoder;
import com.gumtree.user.service.support.factory.GumtreeAccessTokenFactory;
import com.gumtree.user.service.support.factory.LogoutRequestFactory;
import com.gumtree.user.service.support.factory.ResetPasswordRequestFactory;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.gumtree.user.service.model.UserApiErrorCode.INCORRECT_CREDENTIALS;
import static com.gumtree.user.service.model.UserApiErrorCode.USER_NOT_FOUND;
import static com.gumtree.user.service.support.UserServiceClient.userServiceObjectMapper;
import static com.gumtree.user.service.support.mocks.UserServiceWireMocks.*;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Wiremock doesn't support Junit 5 out of the box. So these tests will be Junit 4.
 */
public class UserServiceFacadeIntegrationTest {

    private static final String CLIENT = "test";
    private static final Logger LOGGER = LoggerFactory.getLogger(UserServiceFacadeIntegrationTest.class);

    private static final WireMockServer SERVER = new WireMockServer(WireMockConfiguration.wireMockConfig().dynamicPort());

    private static final int connectionTimeout = 5000;
    private static final int readTimeout = 5000;

    private UserServiceFacade userServiceFacade;

    @BeforeClass
    public static void init() throws JsonProcessingException {
        SERVER.start();
        SERVER.resetMappings();
        SERVER.stubFor(verifyAccessTokenBadData());
        SERVER.stubFor(verifyAccessTokenOK());
        SERVER.stubFor(verifyAccessTokenForbidden());
        SERVER.stubFor(verifyCapiAccessTokenBadData());
        SERVER.stubFor(verifyCapiAccessTokenOK());
        SERVER.stubFor(verifyCapiAccessTokenForbidden());
        SERVER.stubFor(authenticateUserNotFound());
        SERVER.stubFor(authenticateOk());
        SERVER.stubFor(changePasswordOK());
        SERVER.stubFor(changePasswordFailed());
        SERVER.stubFor(logoutOk());
        SERVER.stubFor(registerUserOk());
        SERVER.stubFor(activateUserOk());
        SERVER.stubFor(resetPasswordOk());
        SERVER.stubFor(resetPasswordUserApiError());
        SERVER.stubFor(initiateAccountDeactivationOk());
        SERVER.stubFor(initiateAccountDeactivationFailed());
        SERVER.stubFor(deactivateAccountOk());
        SERVER.stubFor(deactivateAccountExpired());
    }

    @Before
    public void setup() {
        String basePath = String.format("http://localhost:%s", SERVER.port());
        ObjectMapper objectMapper = userServiceObjectMapper();
        UserManagementApi userManagementApi = new CommonApiClient(basePath, "tests", connectionTimeout, readTimeout,
                "UserApi", 3)
                .builder(UserManagementApi.class)
                .withEncoder(new JacksonEncoder(objectMapper))
                .withDecoder(new JacksonDecoder(objectMapper))
                .withErrorDecoder(new UserManagementServerErrorDecoder(objectMapper))
                .withExecutionTimeout(connectionTimeout + readTimeout)
                .buildClient();

        UserInformationApi userInformationApi = new CommonApiClient(basePath, "tests", connectionTimeout, readTimeout,
                "UserApi", 3)
                .builder(UserInformationApi.class)
                .withEncoder(new JacksonEncoder(objectMapper))
                .withDecoder(new JacksonDecoder(objectMapper))
                .withErrorDecoder(new UserManagementServerErrorDecoder(objectMapper))
                .withExecutionTimeout(connectionTimeout + readTimeout)
                .buildClient();

        MarketingPreferenceApi marketingPreferenceApi = new CommonApiClient(basePath, "tests", connectionTimeout, readTimeout,
                "UserApi", 3)
                .builder(MarketingPreferenceApi.class)
                .withEncoder(new JacksonEncoder(objectMapper))
                .withDecoder(new JacksonDecoder(objectMapper))
                .withErrorDecoder(new UserManagementServerErrorDecoder(objectMapper))
                .withExecutionTimeout(connectionTimeout + readTimeout)
                .buildClient();
        AccountDeactivationApi accountDeactivationApi = new CommonApiClient(basePath, "tests", connectionTimeout, readTimeout,
                "UserApi", 3)
                .builder(AccountDeactivationApi.class)
                .withEncoder(new JacksonEncoder(objectMapper))
                .withDecoder(new JacksonDecoder(objectMapper))
                .withErrorDecoder(new UserManagementServerErrorDecoder(objectMapper))
                .withExecutionTimeout(connectionTimeout + readTimeout)
                .buildClient();
        userServiceFacade = new UserServiceFacade(userManagementApi, marketingPreferenceApi, accountDeactivationApi, userInformationApi);
    }

    @Test
    public void shouldVerifyTokenNoUsernameOrToken() {
        VerifyAccessTokenRequest verifyAccessTokenRequest = new VerifyAccessTokenRequest();
        ApiResponse<Boolean> response = userServiceFacade.verifyAccessToken(verifyAccessTokenRequest);
        LOGGER.info("No username or token for token verification: {}", response);
        assertThat(response.isDefined(), is(false));
    }

    @Test
    public void shouldVerifyTokenOK() {
        VerifyAccessTokenRequest verifyAccessTokenRequest = new VerifyAccessTokenRequest();
        verifyAccessTokenRequest.setUsername(OK_EMAIL);
        verifyAccessTokenRequest.setToken(GumtreeAccessTokenFactory.createFromString(OK));
        ApiResponse<Boolean> response = userServiceFacade.verifyAccessToken(verifyAccessTokenRequest);
        LOGGER.info("Token valid: {}", response);
        assertThat(response.isDefined(), is(true));
    }

    @Test
    public void shouldVerifyTokenNotValid() {
        VerifyAccessTokenRequest verifyAccessTokenRequest = new VerifyAccessTokenRequest();
        verifyAccessTokenRequest.setUsername(FORBIDDEN_EMAIL);
        verifyAccessTokenRequest.setToken(GumtreeAccessTokenFactory.createFromString(FORBIDDEN));
        ApiResponse<Boolean> response = userServiceFacade.verifyAccessToken(verifyAccessTokenRequest);
        LOGGER.info("Token not valid: {}", response);
        assertThat(response.isDefined(), is(false));
    }

    @Test
    public void shouldVerifyCapiTokenNoUsernameOrToken() {
        ApiResponse<VerificationDetails> response = userServiceFacade.verifyCapiAccessToken(INVALID_TOKEN);
        LOGGER.info("No cookie for verification: {}", response);
        assertThat(response.isDefined(), is(false));
    }

    @Test
    public void shouldVerifyCapiTokenOK() {
        ApiResponse<VerificationDetails> response = userServiceFacade.verifyCapiAccessToken(OK);
        LOGGER.info("Cookie valid: {}", response);
        assertThat(response.isDefined(), is(true));
        assertThat(response.get(), is(VERIFICATION_DETAILS));
    }

    @Test
    public void shouldVerifyCapiTokenNotValid() {
        ApiResponse<VerificationDetails> response = userServiceFacade.verifyCapiAccessToken(FORBIDDEN);
        LOGGER.info("Token not valid: {}", response);
        assertThat(response.isDefined(), is(false));
    }

    @Test
    public void shouldAuthenticateOk() {
        AuthenticationRequest authenticationRequest = AuthenticationRequestBuilder.builder()
                .setAuthProvider(AuthenticationProvider.GUMTREE)
                .setUsername(OK_EMAIL)
                .setPassword(OK)
                .setClient(CLIENT)
                .build();

        ApiResponse<AuthenticationResponse> response = userServiceFacade.authenticate(authenticationRequest);
        LOGGER.info("Authenticate OK: {}", response);
        assertThat(response.isDefined(), is(true));
        AuthenticationResponse authenticationResponse = response.get();
        assertThat(authenticationResponse.getAccountId(), is(1L));
        assertThat(authenticationResponse.getUsername(), is(OK_EMAIL));
        assertThat(authenticationResponse.getGumtreeAccessToken().getValue(), notNullValue());
    }

    @Test
    public void shouldFailToAuthenticateUserNotFound() {
        AuthenticationRequest authenticationRequest = AuthenticationRequestBuilder.builder()
                .setAuthProvider(AuthenticationProvider.GUMTREE)
                .setUsername(NOTFOUND_EMAIL)
                .setPassword(NOTFOUND)
                .setClient(CLIENT)
                .build();

        ApiResponse<AuthenticationResponse> response = userServiceFacade.authenticate(authenticationRequest);
        LOGGER.info("Fail to Authenticate, not found: {}", response);
        assertThat(response.isDefined(), is(false));
        UserApiErrors userApiErrors = response.getError();
        assertThat(userApiErrors.getErrorCode(), is(USER_NOT_FOUND.getValue()));
    }

    @Test
    public void shouldChangePasswordOk() {
        ChangePasswordRequest changePasswordRequest = ChangePasswordRequestBuilder.builder()
                .setUsername(OK_EMAIL)
                .setClient(CLIENT)
                .setPassword("newpwd")
                .setConfirmedPassword("newpwd")
                .setVerificationKey("oldpwd")
                .setVerificationKeyType(VerificationKeyType.CURRENT_USER_PASSWORD)
                .build();

        ApiResponse<GumtreeAccessToken> response = userServiceFacade.changePassword(changePasswordRequest);
        LOGGER.info("Change Password OK, Token: {}", response);
        assertThat(response.isDefined(), is(true));
        assertThat(response.get(), notNullValue());
    }

    @Test
    public void shouldFailToChangePassword() {
        ChangePasswordRequest changePasswordRequest = ChangePasswordRequestBuilder.builder()
                .setUsername(INVALID_CREDENTIALS_EMAIL)
                .setClient(CLIENT)
                .setPassword("newpwd")
                .setConfirmedPassword("newpwd")
                .setVerificationKey("wrong pwd")
                .setVerificationKeyType(VerificationKeyType.CURRENT_USER_PASSWORD)
                .build();

        ApiResponse<GumtreeAccessToken> response = userServiceFacade.changePassword(changePasswordRequest);
        LOGGER.info("Fail to change Password, response: {}", response);
        assertThat(response.isDefined(), is(false));
        assertThat(response.getError(), notNullValue());
        assertThat(response.getError().getErrorCode(), is(INCORRECT_CREDENTIALS.getValue()));
    }

    @Test
    public void shouldLogoutOK() {
        LogoutRequest logoutRequest = LogoutRequestFactory.createLogoutRequest(OK);
        userServiceFacade.logout(logoutRequest);
    }

    @Test
    public void shouldRegisterUsedOk() {
        UserRegistrationRequest userRegistrationRequest = UserRegistrationRequestBuilder.builder()
                .setAccessToken("abc13")
                .setAuthenticationProvider(AuthenticationProvider.GUMTREE)
                .setUsername(OK_EMAIL)
                .setDateOfBirth(DATE_OF_BIRTH)
                .setPostcode(POSTCODE)
                .build();

        ApiResponse<RegisteredUser> response = userServiceFacade.registerUser(userRegistrationRequest);
        RegisteredUser registeredUser = response.get();

        assertThat(registeredUser.getUserId(), is(1L));
        assertThat(registeredUser.getUserStatus(), is("ACTIVE"));
    }

    @Test
    public void shouldActivateUsedOk() {
        UserActivationRequest activationRequest = new UserActivationRequest();
        activationRequest.setUsername(OK_EMAIL);
        activationRequest.setToken(OK);
        ApiResponse<Boolean> response = userServiceFacade.activate(activationRequest);
        assertThat(response.get(), is(true));
    }

    @Test
    public void shouldResetPasswordOk() {
        ResetPasswordRequest activationRequest = ResetPasswordRequestFactory.createResetPasswordRequest(OK_EMAIL);
        ApiResponse<Boolean> response = userServiceFacade.resetPassword(activationRequest);
        assertThat(response.get(), is(true));
    }

    @Test
    public void shouldResetPasswordUserApiError500ErrorNoPayload() {
        ResetPasswordRequest activationRequest = ResetPasswordRequestFactory.createResetPasswordRequest(NOTFOUND_EMAIL);
        ApiResponse<Boolean> response = userServiceFacade.resetPassword(activationRequest);
        assertThat(response.getError(), is(UserApiErrorsBuilder.builder()
                .withErrorCode("Error response received with status HTTP 500")
                .build()));
    }

    @Test
    public void shouldInitiateAccountDeactivationOk() {
        // Given
        DeactivationReason reason = DeactivationReason.OTHER;
        // When
        ApiResponse<Boolean> response = userServiceFacade.initiateAccountDeactivation(103457L, reason);
        // Then
        assertThat(response.get(), is(true));
    }

    @Test
    public void shouldInitiateAccountDeactivationFailed() {
        // Given
        DeactivationReason reason = DeactivationReason.OTHER;
        // When
        ApiResponse<Boolean> response = userServiceFacade.initiateAccountDeactivation(123456L, reason);
        // Then
        assertThat(response.isDefined(), is(false));
        assertThat(response.getError().getErrorCode(), is("Error response received with status HTTP 500"));
    }

    @Test
    public void shouldDeactivateAccountOk() {
        // Given
        String deactivationToken = OK_DEACTIVATION_TOKEN;
        // When
        ApiResponse<Boolean> response = userServiceFacade.finalizeAccountDeactivation(deactivationToken);
        // Then
        assertThat(response.get(), is(true));
    }

    @Test
    public void shouldDeactivateAccountExpired() {
        // Given
        String deactivationToken = EXPIRED_DEACTIVATION_TOKEN;
        // When
        ApiResponse<Boolean> response = userServiceFacade.finalizeAccountDeactivation(deactivationToken);
        // Then
        assertThat(response.isDefined(), is(false));
        assertThat(response.getError().getErrorCode(), is("Error response received with status HTTP 410"));
    }
}
