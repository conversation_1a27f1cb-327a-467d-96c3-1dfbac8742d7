package com.gumtree.user.service;

import com.gumtree.user.service.model.ApiError;
import com.gumtree.user.service.model.UserApiErrorCode;
import com.gumtree.user.service.support.builder.UserApiErrorsBuilder;
import com.gumtree.user.service.support.factory.ApiErrorFactory;
import org.junit.jupiter.api.Test;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

class ApiResponseTest {

    @Test
    void get() {
        String value = "String";
        assertThat(ApiResponse.of(value).get(), is(value));
    }

    @Test
    void isDefined() {
        String value = "String";
        assertThat(ApiResponse.of(value).isDefined(), is(true));
    }

    @Test
    void getErrorCode() {
        ApiResponse<Integer> response = ApiResponse.error(UserApiErrorsBuilder.builder().withErrorCode("code").build());
        assertThat(response.isDefined(), is(false));
        assertThat(response.getError().getErrorCode(), is("code"));
        assertThat(response.getError().getErrors().isEmpty(), is(true));
    }

    @Test
    void getErrors() {
        ApiError apiError1 = ApiErrorFactory.createApiError("field1", "message1");
        ApiError apiError2 = ApiErrorFactory.createApiError("field2", "message2");

        ApiResponse<Integer> response = ApiResponse.error(UserApiErrorsBuilder.builder()
                .withErrorCode(UserApiErrorCode.USER_NOT_FOUND)
                .addError(apiError1)
                .addError(apiError2)
                .build());
        assertThat(response.isDefined(), is(false));
        assertThat(response.getError().getErrorCode(), is(UserApiErrorCode.USER_NOT_FOUND.getValue()));
        assertThat(response.getError().getErrors().get(0), is(apiError1));
        assertThat(response.getError().getErrors().get(1), is(apiError2));

    }

    @Test
    void nullErrorCode() {
        UserApiErrorCode code = null;
        ApiResponse<Integer> response = ApiResponse.error(UserApiErrorsBuilder.builder()
                .withErrorCode(code).build());
        assertThat(response.isDefined(), is(false));
        assertThat(response.getError().getErrorCode(), nullValue());
        assertThat(response.getError().getErrors().isEmpty(), is(true));
    }

    @Test
    void apiResponseOfNull() {
        assertThrows(IllegalArgumentException.class, () -> ApiResponse.of(null));
    }

    @Test
    void apiResponseErrorNull() {
        assertThrows(IllegalArgumentException.class, () -> ApiResponse.error(null));
    }
}
