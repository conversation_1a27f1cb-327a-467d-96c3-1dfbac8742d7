package org.apache.shiro.web.mgt;

import com.gumtree.web.security.shiro.RememberMeJsonSerializer;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.codec.Base64;
import org.apache.shiro.crypto.CipherService;
import org.apache.shiro.mgt.AbstractRememberMeManager;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.SubjectContext;
import org.apache.shiro.web.servlet.Cookie;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.util.WebUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;


/**
 * RememberMe manager to be used to generate rememberMe cookie based on token generated in the past
 * MaxAge is dynamic and accounts for time difference
 */
public class RemainingMaxAgeRememberMeManager extends CookieRememberMeManager {

    public RemainingMaxAgeRememberMeManager(byte[] encryptionKey,
                                            CipherService cipherService,
                                            SimpleCookie simpleCookie,
                                            RememberMeJsonSerializer rememberMeJsonSerializer) {
        this.setCipherKey(encryptionKey);
        this.setCipherService(cipherService);
        this.setCookie(simpleCookie);
        this.setSerializer(rememberMeJsonSerializer);
    }

    /**
     * Inspired by {@link AbstractRememberMeManager#onSuccessfulLogin}
     */
    public void onSuccessfulLogin(Subject subject, AuthenticationInfo info, OffsetDateTime createdTime) {
        //always clear any previous identity:
        forgetIdentity(subject);

        rememberIdentityWithMaxAge(subject, info, createdTime);
    }

    /**
     * Copy of {@link AbstractRememberMeManager#rememberIdentity(Subject, AuthenticationToken, AuthenticationInfo)}
     */
    private void rememberIdentityWithMaxAge(Subject subject, AuthenticationInfo authenticationInfo, OffsetDateTime createdTime) {
        PrincipalCollection principals = getIdentityToRemember(subject, authenticationInfo);
        byte[] bytes = convertPrincipalsToBytes(principals);
        rememberSerializedIdentity(subject, bytes, createdTime);
    }

    /**
     * Copy of {@link CookieRememberMeManager#rememberSerializedIdentity} but sets MaxAge dynamically
     */
    private void rememberSerializedIdentity(Subject subject, byte[] serialized, OffsetDateTime createdTime) {
        if (!WebUtils.isHttp(subject)) {
            return;
        }

        HttpServletRequest request = WebUtils.getHttpRequest(subject);
        HttpServletResponse response = WebUtils.getHttpResponse(subject);

        //base 64 encode it and store as a cookie:
        String base64 = Base64.encodeToString(serialized);

        Cookie template = getCookie(); //the class attribute is really a template for the outgoing cookies
        Cookie cookie = new SimpleCookie(template);
        cookie.setValue(base64);
        cookie.setMaxAge(calculateRemainingMaxAge(createdTime, template.getMaxAge()));
        cookie.saveTo(request, response);
    }


    /**
     * Returning 0 will delete cookie - see {@link org.apache.shiro.web.servlet.SimpleCookie#removeFrom}
     *
     * @param createdTime when auth header/token was created
     * @param maxAge      cookie max age in seconds
     * @return remaining max age in seconds
     */
    static int calculateRemainingMaxAge(OffsetDateTime createdTime, int maxAge) {
        if (createdTime.isAfter(OffsetDateTime.now())) {
            return 0;
        }

        long passedSoFar = ChronoUnit.SECONDS.between(createdTime, OffsetDateTime.now());

        if (passedSoFar > Integer.MAX_VALUE) { // int max value = around 68 years
            return 0;
        }

        return Math.max(0, maxAge - (int) passedSoFar);
    }

    @Override
    public void onSuccessfulLogin(Subject subject, AuthenticationToken token, AuthenticationInfo info) {
        throw new UnsupportedOperationException("Not to be used by Security Manager");
    }

    @Override
    public void onFailedLogin(Subject subject, AuthenticationToken token, AuthenticationException ae) {
        throw new UnsupportedOperationException("Not to be used by Security Manager");
    }

    @Override
    public PrincipalCollection getRememberedPrincipals(SubjectContext subjectContext) {
        throw new UnsupportedOperationException("Not to be used by Security Manager");
    }

    @Override
    protected PrincipalCollection onRememberedPrincipalFailure(RuntimeException e, SubjectContext context) {
        throw new UnsupportedOperationException("Not to be used by Security Manager");
    }

    @Override
    public void onLogout(Subject subject) {
        throw new UnsupportedOperationException("Not to be used by Security Manager");
    }
}
