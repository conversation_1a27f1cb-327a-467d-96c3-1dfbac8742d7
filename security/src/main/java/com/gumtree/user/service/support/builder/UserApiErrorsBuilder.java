package com.gumtree.user.service.support.builder;

import com.gumtree.user.service.model.ApiError;
import com.gumtree.user.service.model.UserApiErrorCode;
import com.gumtree.user.service.model.UserApiErrors;

import java.util.ArrayList;
import java.util.List;

public final class UserApiErrorsBuilder {

    private String errorCode;
    private List<ApiError> errors = new ArrayList<>();

    private UserApiErrorsBuilder() {

    }

    public static UserApiErrorsBuilder builder() {
        return new UserApiErrorsBuilder();
    }

    public UserApiErrors build() {
        UserApiErrors userApiErrors = new UserApiErrors();
        userApiErrors.setErrorCode(errorCode);
        userApiErrors.setErrors(errors);
        return userApiErrors;
    }

    public UserApiErrorsBuilder addError(ApiError error) {
        this.errors.add(error);
        return this;
    }

    public UserApiErrorsBuilder withErrorCode(UserApiErrorCode error) {
        if(error != null) {
            this.errorCode = error.name();
        }
        return this;
    }

    public UserApiErrorsBuilder withErrorCode(String errorCode) {
        this.errorCode = errorCode;
        return this;
    }
}
