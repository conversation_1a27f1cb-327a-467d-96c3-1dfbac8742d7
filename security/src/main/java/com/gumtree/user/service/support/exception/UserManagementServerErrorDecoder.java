package com.gumtree.user.service.support.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.user.service.model.UserApiErrors;
import feign.Response;
import feign.codec.ErrorDecoder;

import java.util.Optional;

public class UserManagementServerErrorDecoder implements ErrorDecoder {

    private final ObjectMapper objectMapper;

    public UserManagementServerErrorDecoder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public Exception decode(String methodKey, Response response) {

        Optional<UserApiErrors> errorResponse = parseErrorResponse(response);

        switch (response.status()) {
            case 400:
                return errorResponse.map(UserApiClientException::new).orElse(new UserApiClientException(createErrorMessage(response)));
            case 404:
                return errorResponse.map(NotFoundException::new).orElse(new NotFoundException(createErrorMessage(response)));
            case 403:
                return errorResponse.map(ForbiddenException::new).orElse(new ForbiddenException(createErrorMessage(response)));
            case 401:
                return errorResponse.map(UnauthorizedException::new).orElse(new UnauthorizedException(createErrorMessage(response)));
            default:
                return errorResponse.map(UserApiServerException::new).orElse(new UserApiServerException(createErrorMessage(response)));
        }

    }

    private String createErrorMessage(Response resp) {
        return String.format("Error response received with status HTTP %s", resp.status());
    }

    private Optional<UserApiErrors> parseErrorResponse(Response resp) {
        try {
            return Optional.of(objectMapper.readValue(resp.body().asInputStream(), UserApiErrors.class));
        } catch (Exception ex) {
            return Optional.empty();
        }
    }

}
