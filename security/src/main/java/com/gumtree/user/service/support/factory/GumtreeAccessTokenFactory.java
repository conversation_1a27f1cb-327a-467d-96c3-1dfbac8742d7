package com.gumtree.user.service.support.factory;

import com.gumtree.user.service.model.GumtreeAccessToken;

public final class GumtreeAccessTokenFactory {

    private GumtreeAccessTokenFactory() {

    }

    public static GumtreeAccessToken createFromString(String value) {
        GumtreeAccessToken gumtreeAccessToken = new GumtreeAccessToken();
        if (value == null) {
            gumtreeAccessToken.setValue("");
        } else {
            gumtreeAccessToken.setValue(value);
        }
        return gumtreeAccessToken;
    }

    public static GumtreeAccessToken create(Long value) {
        return createFromString(String.valueOf(value));
    }
}
