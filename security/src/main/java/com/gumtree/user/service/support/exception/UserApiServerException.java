package com.gumtree.user.service.support.exception;

import com.gumtree.shared.client.FeignHystrixServerException;
import com.gumtree.user.service.model.UserApiErrors;

import java.util.Optional;

public class UserApiServerException extends FeignHystrixServerException {

    private UserApiErrors userApiErrors;

    public UserApiServerException(String message) {
        super(message);
    }

    public UserApiServerException(UserApiErrors userApiErrors) {
        super(userApiErrors.getErrorCode());
        this.userApiErrors =userApiErrors;
    }

    public Optional<UserApiErrors> getUserApiErrors() {
        return userApiErrors == null ? Optional.empty() : Optional.of(userApiErrors);
    }
}
