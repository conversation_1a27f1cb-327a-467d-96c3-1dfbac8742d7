package com.gumtree.web.security.shiro;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.web.common.util.CategoryHierarchyChecker;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nonnull;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

public class CategoryFilterService {

    private final AdCategoryIdProvider adCategoryProvider;
    private final CategoryHierarchyChecker categoryHierarchyChecker;
    private final boolean isLoginRequiredForContactEnabled;

    // TODO remove the reply specific dependencies; make it generic
    public CategoryFilterService(AdCategoryIdProvider adCategoryIdProvider,
                                 CategoryModel categoryModel,
                                 Set<Long> contactForceLoginWhitelistedCategories,
                                 boolean isLoginRequiredForContactEnabled) {
        this.adCategoryProvider = adCategoryIdProvider;
        this.isLoginRequiredForContactEnabled = isLoginRequiredForContactEnabled;
        this.categoryHierarchyChecker = new CategoryHierarchyChecker(categoryModel, contactForceLoginWhitelistedCategories);
    }

    public boolean isLoginRequiredToContact(@Nonnull String path) {
        Objects.requireNonNull(path);

        if (isLoginRequiredForContactEnabled) {
            return getAdvertIdFromPath(path)
                    .flatMap(adCategoryProvider::getAdvertCategoryId)
                    .map(categoryId -> !categoryHierarchyChecker.isAdvertCategoryWhiteListed(categoryId))
                    .orElse(true);
        }

        return false;
    }

    // TODO GTALL-864: revisit this (originally built for GTALL-5111)
    public boolean isLoginRequiredToContact(long advertCategoryId) {
        if(isLoginRequiredForContactEnabled){
            return !categoryHierarchyChecker.isAdvertCategoryWhiteListed(advertCategoryId);
        }
        return true;
    }

    private Optional<Long> getAdvertIdFromPath(@Nonnull String path) {
        Objects.requireNonNull(path);
        String[] parts = path.split("/");
        String advertId = parts[parts.length - 1];
        return StringUtils.isNumeric(advertId) ? Optional.ofNullable(Long.parseLong(advertId)) : Optional.empty();
    }

    public interface AdCategoryIdProvider {
        Optional<Long> getAdvertCategoryId(Long advertId);
    }
}
