package com.gumtree.web.security.shiro;

import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.security.login.LoginUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.util.SavedRequest;
import org.apache.shiro.web.util.WebUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;

/**
 * Shiro implementation of {@link com.gumtree.web.security.login.LoginUtils}.
 * Reduces need to call the Shiro static utility methods.
 */
public final class ShiroLoginUtils implements LoginUtils {

    public static final String NEW_USER_EMAIL_ADDRESS = "newUserEmailAddress";

    public static final String POST_AD_LOGIN_FLAG = "postAdLoginFlag";

    public static final String USER_IS_DIRTY = "userIsDirty";

    private CookieResolver cookieResolver;

    public ShiroLoginUtils(CookieResolver cookieResolver) {
        this.cookieResolver = cookieResolver;
    }


    @Override
    public void login(Subject subject, String username, String password) {
        UsernamePasswordToken token = new UsernamePasswordToken(username, password);
        token.setRememberMe(true);
        subject.login(token);
        setUserDirty(true);
    }

    @Override
    public void setUserDirty(boolean dirty) {
        if (dirty) {
            SecurityUtils.getSubject().getSession().setAttribute(USER_IS_DIRTY, Boolean.TRUE);
        } else {
            SecurityUtils.getSubject().getSession().removeAttribute(USER_IS_DIRTY);
        }
    }

    @Override
    public boolean getAndSetUserDirty(boolean dirty) {
        boolean previousDirtyState = isUserDirty();
        setUserDirty(dirty);
        return previousDirtyState;
    }

    @Override
    public boolean isUserDirty() {
        Boolean flag = (Boolean) SecurityUtils.getSubject().getSession().getAttribute(USER_IS_DIRTY);
        return flag != null ? flag : false;
    }

    @Override
    public boolean newUserMustLoginToStartPostAdFlow() {
        Boolean flag = (Boolean) SecurityUtils.getSubject().getSession().getAttribute(POST_AD_LOGIN_FLAG);
        return flag != null ? flag : true;
    }

    @Override
    public void setNewUserMustLoginToStartPostAdFlow(boolean flag) {
        if (flag) {
            SecurityUtils.getSubject().getSession().removeAttribute(POST_AD_LOGIN_FLAG);
        } else {
            SecurityUtils.getSubject().getSession().setAttribute(POST_AD_LOGIN_FLAG, Boolean.FALSE);
        }
    }

    @Override
    public void storeNewUserEmailAddressInSession(String emailAddress) {
        Subject subject = SecurityUtils.getSubject();
        Session session = subject.getSession();
        session.setAttribute(NEW_USER_EMAIL_ADDRESS, emailAddress);
    }

    @Override
    public void clearNewUserEmailAddressFromSession() {
        Subject subject = SecurityUtils.getSubject();
        Session session = subject.getSession();
        session.removeAttribute(NEW_USER_EMAIL_ADDRESS);
    }

    @Override
    public String getNewUserEmailAddressFromSession() {
        Subject subject = SecurityUtils.getSubject();
        Session session = subject.getSession();
        return (String) session.getAttribute(NEW_USER_EMAIL_ADDRESS);
    }

    @Override
    public void saveRequest(ServletRequest request) {
        RedirectUtils.saveRequest(request);
    }

    @Override
    public void redirectToSavedRequest(
            ServletRequest req,
            ServletResponse resp,
            String defaultRedirectUrl) throws IOException {
        RedirectUtils.redirectToSavedRequest(req, resp, defaultRedirectUrl, cookieResolver);
    }

    @Override
    public SavedRequest getSavedRequest(ServletRequest req) throws IOException {
        return WebUtils.getSavedRequest(req);
    }

    @Override
    public void clearSavedRequest(ServletRequest req) throws IOException {
        WebUtils.getAndClearSavedRequest(req);
    }

    @Override
    public void redirectToLogin(ServletRequest req, ServletResponse resp, String loginUrl) throws IOException {
        WebUtils.issueRedirect(req, resp, loginUrl, null, false, false);
    }

}
