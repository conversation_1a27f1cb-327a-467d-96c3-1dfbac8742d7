package com.gumtree.web.security.shiro;

import com.gumtree.web.security.SecurityHelper;
import com.gumtree.web.security.shiro.filter.BaseCustomLoginFilter;
import org.apache.shiro.subject.Subject;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

/**
 * Filter for soft login.
 */
public class GumtreeSoftLoginFilter extends BaseCustomLoginFilter {
    private final SecurityHelper securityHelper;
    private final boolean accessTokenValidationEnabled;

    public GumtreeSoftLoginFilter(SecurityHelper securityHelper, boolean accessTokenValidationEnabled) {
        this.securityHelper = securityHelper;
        this.accessTokenValidationEnabled = accessTokenValidationEnabled;
    }

    @Override
    protected boolean internalIsAccessAllowed(ServletRequest request, ServletResponse response) {
        Subject subject = getSubject(request, response);
        return (subject.isRemembered() || subject.isAuthenticated()) && (!accessTokenValidationEnabled || isAccessTokenValid(subject));
    }

    protected boolean isAccessTokenValidIfAuthenticated(ServletRequest request, ServletResponse response) {
        Subject subject = getSubject(request, response);
        boolean isNotAuthenticated = !(subject.isRemembered() || subject.isAuthenticated());
        return isNotAuthenticated ? true : isAccessTokenValid(subject);
    }

    protected boolean isAccessTokenValid(Subject subject) {
        return securityHelper.verifyAccessTokenAndLogoutIfInvalid(subject);
    }
}
