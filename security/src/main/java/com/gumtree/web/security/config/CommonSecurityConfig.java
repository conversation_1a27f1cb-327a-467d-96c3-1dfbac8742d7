package com.gumtree.web.security.config;

import com.gumtree.api.Bushfire<PERSON>pi<PERSON>ey;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.web.cookie.CookieCutter;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.GumtreeCookieProperty;
import com.gumtree.web.filter.ClickjackingFilter;
import com.gumtree.web.security.DefaultUserSecurityManager;
import com.gumtree.web.security.UserSecurityManager;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.security.shiro.RememberMeJsonSerializer;
import com.gumtree.web.security.shiro.ShiroLoginUtils;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.codec.binary.Base64;
import org.apache.shiro.crypto.AesCipherService;
import org.apache.shiro.crypto.CipherService;
import org.apache.shiro.mgt.RememberMeManager;
import org.apache.shiro.web.mgt.CookieRememberMeManager;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.mgt.RemainingMaxAgeRememberMeManager;
import org.apache.shiro.web.mgt.RotatingKeyCookieRememberMeManager;
import org.apache.shiro.web.servlet.Cookie;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.util.Assert;

@Configuration
@ImportResource({"classpath:META-INF/config/spring/security-config.xml"})
public abstract class CommonSecurityConfig {

    public static final String BUSHFIRE_SOFT_LOGIN_COOKIE_NAME = "gt_rememberMe";

    // Needs updating to 256. Please raise a Jira. Do No Remove this line - defaults are dangerous
    private static final Integer KEY_SIZE = 128;

    public static Logger logger = LoggerFactory.getLogger(CommonSecurityConfig.class);

    private String encryptionKey = GtProps.getStr(SellerProperty.GUMTREE_REMEMBERME_AES128_BASE64_KEY);
    private String previousEncryptionKey = GtProps.getStr(SellerProperty.GUMTREE_REMEMBERME_AES128_BASE64_PREVIOUS_KEY);
    private String nextEncryptionKey = GtProps.getStr(SellerProperty.GUMTREE_REMEMBERME_AES128_BASE64_NEXT_KEY);

    @Autowired
    private BushfireApi bushfireApi;

    @Autowired
    @Qualifier("defaultBushfireApiKey")
    private BushfireApiKey defaultApiKey;

    @Autowired
    private ApiCallExecutor apiCallExecutor;

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private CookieResolver cookieResolver;

    @Bean
    public RememberMeManager rememberMeManager() {
        Assert.hasLength(encryptionKey);
        Assert.hasLength(previousEncryptionKey);

        final CookieRememberMeManager rememberMeManager = new RotatingKeyCookieRememberMeManager(
                Base64.decodeBase64(encryptionKey),
                Base64.decodeBase64(nextEncryptionKey),
                Base64.decodeBase64(previousEncryptionKey),
                meterRegistry);
        rememberMeManager.setCipherService(cipherService());
        rememberMeManager.setCookie(rememberMeCookieTemplate());
        rememberMeManager.setSerializer(new RememberMeJsonSerializer());

        return rememberMeManager;
    }

    @Bean
    public RemainingMaxAgeRememberMeManager remainingMaxAgeRememberMeManager() {
        byte[] encKey = Base64.decodeBase64(encryptionKey);
        return new RemainingMaxAgeRememberMeManager(encKey, cipherService(), rememberMeCookieTemplate(), new RememberMeJsonSerializer());
    }

    @Bean
    public LoginUtils loginUtils() throws Exception {
        return new ShiroLoginUtils(cookieResolver);
    }

    @Bean
    public org.apache.shiro.mgt.SecurityManager securityManager() throws Exception {
        DefaultWebSecurityManager manager = new DefaultWebSecurityManager();
        manager.setRememberMeManager(rememberMeManager());

        return manager;
    }

    @Bean
    public UserSecurityManager userSecurityManager() throws Exception {
        return new DefaultUserSecurityManager(apiCallExecutor, defaultApiKey, bushfireApi);
    }

    @Bean
    public ClickjackingFilter clickjackingFilter() {
        return new ClickjackingFilter();
    }

    private CipherService cipherService() {
        // we need a 128bit encryption suite for decrypting/encrypting cookie
        AesCipherService cipherService = new AesCipherService();
        cipherService.setKeySize(KEY_SIZE);
        cipherService.setInitializationVectorSize(KEY_SIZE);

        return cipherService;
    }

    private SimpleCookie rememberMeCookieTemplate() {
        final SimpleCookie cookieTemplate =
                new SimpleCookie(CookieCutter.getName(BUSHFIRE_SOFT_LOGIN_COOKIE_NAME, GtProps.getEnv()));
        cookieTemplate.setMaxAge(Cookie.ONE_YEAR);
        cookieTemplate.setHttpOnly(true);
        cookieTemplate.setSecure(GtProps.getBool(GumtreeCookieProperty.COOKIES_SECURE));
        cookieTemplate.setDomain(GtProps.getStr(GumtreeCookieProperty.COOKIES_DOMAIN));

        return cookieTemplate;
    }
}
