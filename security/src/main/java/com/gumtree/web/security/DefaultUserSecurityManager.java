package com.gumtree.web.security;

import com.gumtree.api.Account;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.domain.user.beans.RegisterUserBean;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.executor.ApiCallResponse;
import com.gumtree.api.client.executor.ApiKeyProvider;
import com.gumtree.api.client.executor.command.GetUserApiCall;
import com.gumtree.api.client.executor.command.GetAccountsApiCall;
import com.gumtree.api.client.executor.command.GetAccountCommand;
import com.gumtree.api.client.executor.command.RegisterUserApiCall;
import com.gumtree.api.client.executor.command.ValidateRegistrationFormApiCall;
import com.gumtree.web.security.exception.UserNotRecognisedException;
import org.apache.commons.lang.math.NumberUtils;

import java.util.Arrays;
import java.util.List;

/**
 * Default implementation of {@link UserSecurityManager}.
 */
public final class DefaultUserSecurityManager implements UserSecurityManager {

    private ApiCallExecutor apiCallExecutor;
    private BushfireApi bushfireApi;
    private BushfireApiKey defaultApiKey;

    /**
     * Constructor.
     *
     * @param apiCallExecutor for executing api calls.
     * @param defaultApiKey   the defaultApiKey
     */
    public DefaultUserSecurityManager(ApiCallExecutor apiCallExecutor, BushfireApiKey defaultApiKey, BushfireApi api) {
        this.apiCallExecutor = apiCallExecutor;
        this.defaultApiKey = defaultApiKey;
        this.bushfireApi = api;
    }

    @Override
    public User getExistingUser(String username) {
        if (NumberUtils.isNumber(username)) {
            Long accountId = Long.parseLong(username);
            Account account = bushfireApi.accountApi().getAccount(accountId);
            if (account != null) {
                if (account.getUsernames().size() > 0 && account.isPro()) {
                    username = account.getUsernames().get(0);
                }
            }
        }

        ApiCallResponse<User> response = apiCallExecutor.call(new GetUserApiCall(username));

        if (!response.isErrorResponse()) {
            return response.getResponseObject();
        }

        throw new UserNotRecognisedException(username);
    }

    @Override
    public List<Account> getUserAccounts(String username, ApiKeyProvider apiKeyProvider) {
        try {
            Long accountId = Long.parseLong(username);
            Account account = bushfireApi.accountApi().getAccount(accountId);
            return Arrays.asList(account);
        } catch (Exception e) {
            return apiCallExecutor.call(new GetAccountsApiCall(username, apiKeyProvider)).getResponseObject();
        }
    }

    @Override
    public Account getAccount(Long accountId, ApiKeyProvider apiKeyProvider) {
        return apiCallExecutor.call(new GetAccountCommand(accountId, apiKeyProvider)).getResponseObject();
    }

    @Override
    public ApiCallResponse<User> registerNewUser(RegisterUserBean registerUserBean) {
        return apiCallExecutor.call(new RegisterUserApiCall(registerUserBean));
    }

    @Override
    public ApiCallResponse<Void> validateNewUser(RegisterUserBean registerUserBean) {
        return apiCallExecutor.call(new ValidateRegistrationFormApiCall(registerUserBean));
    }

    @Override
    public BushfireApiKey getDefaultApiKey() {
        return defaultApiKey;
    }
}
