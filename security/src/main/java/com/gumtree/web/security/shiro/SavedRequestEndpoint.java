package com.gumtree.web.security.shiro;

import org.apache.shiro.web.util.SavedRequest;

/**
 * Represents saved request endpoints for use in "new user" login flow.
 */
public enum SavedRequestEndpoint {

    NONE(false), BUSHFIRE_POST_AD(true), LEGACY_POST_AD(true), <PERSON><PERSON><PERSON>(false);

    /**
     * Convert the given {@link SavedRequest} into a {@link SavedRequestEndpoint}.
     *
     * @param savedRequest the source
     * @return the given {@link SavedRequest} as a {@link SavedRequestEndpoint}.
     */
    public static SavedRequestEndpoint savedRequestEndpoint(SavedRequest savedRequest) {
        if (savedRequest == null) {
            return NONE;
        } else if (savedRequest.getRequestURI().contains("add_posting")) {
            return LEGACY_POST_AD;
        } else if (savedRequest.getRequestURI().contains("postad")) {
            return BUSHFIRE_POST_AD;
        }
        return OTHER;
    }

    private boolean isPostAd;

    /**
     * Determines if is a post ad url (Bushfire or Legacy).
     *
     * @param postAd is a post ad url (Bushfire or Legacy).
     */
    SavedRequestEndpoint(boolean postAd) {
        isPostAd = postAd;
    }

    /**
     * @return if is a post ad url (Bushfire or Legacy).
     */
    public boolean isPostAd() {
        return isPostAd;
    }
}