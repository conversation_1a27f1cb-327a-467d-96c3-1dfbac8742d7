package com.gumtree.web.security.config;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Private class for building filter chain definitions.
 */
public class FilterDefinitionBuilder {

    private String url;

    private List<String> filters = Lists.newArrayList();

    public FilterDefinitionBuilder forUrl(String url) {
        this.url = url;
        return this;
    }

    public FilterDefinitionBuilder withFilter(String filter) {
        filters.add(filter);
        return this;
    }

    public String build() {
        StringBuilder builder = new StringBuilder();
        builder.append(url);
        builder.append(" = ");
        builder.append(Joiner.on(", ").join(filters));
        return builder.toString();
    }
}
