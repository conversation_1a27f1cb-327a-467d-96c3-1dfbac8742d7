package com.gumtree.web.security.shiro;

import com.gumtree.util.http.HttpRequestUtils;
import com.gumtree.web.security.SecurityHelper;
import org.apache.shiro.web.util.WebUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.HttpMethod;
import java.io.IOException;
import java.util.Collections;
import java.util.Map;

/**
 * Filter for soft login.
 */
public final class NonAjaxSoftLoginWithCallbackUrlFilter extends GumtreeSoftLoginFilter {

    private final String host;

    public NonAjaxSoftLoginWithCallbackUrlFilter(String host, String loginUrl, SecurityHelper securityHelper) {
        super(securityHelper, true);
        this.host = host;
        setLoginUrl(loginUrl);
    }

    @Override
    protected boolean internalIsAccessAllowed(ServletRequest request, ServletResponse response) {
        return HttpRequestUtils.isAjaxRequest(request) || super.internalIsAccessAllowed(request, response);
    }

    @Override
    protected void redirectToLogin(ServletRequest request, ServletResponse response) throws IOException {
        // if we decided to logout the make sure the user is logged out
        logout(request, response);

        HttpServletRequest httpRequest = WebUtils.toHttp(request);
        StringBuilder absoluteUrl = new StringBuilder(host).append(httpRequest.getRequestURI());
        if(httpRequest.getMethod().equals(HttpMethod.POST)){
            absoluteUrl.append(buildQueryString(httpRequest.getParameterMap()));
        }
        Map<String, String> paramMap = Collections.singletonMap(RedirectUtils.CALLBACK_PARAM_NAME, absoluteUrl.toString());
        WebUtils.issueRedirect(request, response, getLoginUrl(), paramMap, false, false);
    }

    private String buildQueryString(Map<String, String[]> requestParameters){
        StringBuilder queryString = new StringBuilder("?");
        for (Map.Entry<String, String[]> entry : requestParameters.entrySet()) {
            queryString.append(entry.getKey())
                       .append("=")
                       .append(entry.getValue()[0])
                       .append("&");
        }
        queryString.deleteCharAt(queryString.length() - 1); // remove the last &
        return queryString.toString();
    }
}
