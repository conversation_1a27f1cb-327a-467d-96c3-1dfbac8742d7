package com.gumtree.web.security.shiro;

import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.security.login.LoginUtils;
import org.apache.shiro.web.util.SavedRequest;
import org.springframework.util.StringUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Context that represents "new user" login - i.e. user enters email address and says "No, I'm a new user".
 */
public final class NewUserLoginFlowContext {

    private static final Pattern LEGACY_CATEGORY_ID_PATTERN = Pattern.compile(".*category_id=(0|[1-9][0-9]*).*");
    private static final Pattern BUSHFIRE_CATEGORY_ID_PATTERN = Pattern.compile(".*categoryId=(0|[1-9][0-9]*).*");

    private LoginUtils loginUtils;

    private ServletRequest request;

    private ServletResponse response;

    private UserSession userSession;

    private UrlScheme urlScheme;

    private SavedRequest savedRequest;

    private SavedRequestEndpoint savedRequestEndpoint;

    private String username;

    /**
     * Constructor.
     *
     * @param username      the username entered as part of the flow
     * @param request       the request
     * @param response      the response
     * @param loginUtils    the login utils
     * @param urlScheme     the url scheme
     * @throws IOException if error occurs on construction
     */
    public NewUserLoginFlowContext(
            String username,
            ServletRequest request,
            ServletResponse response,
            LoginUtils loginUtils,
            UrlScheme urlScheme,
            UserSession userSession
    ) throws IOException {

        this.username = username;
        this.request = request;
        this.response = response;
        this.loginUtils = loginUtils;
        this.userSession = userSession;
        this.savedRequest = loginUtils.getSavedRequest(request);
        this.savedRequestEndpoint = SavedRequestEndpoint.savedRequestEndpoint(savedRequest);
        this.urlScheme = urlScheme;
    }

    /**
     * Redirect contained request to bushfire post ad.
     *
     * @throws IOException if error occurs redirecting
     */
    public void redirectToBushfirePostAd() throws IOException {
        Long categoryId = extractCategoryIdFromLegacySavedRequest();
        loginUtils.clearSavedRequest(request);
        ((HttpServletResponse) response).sendRedirect(urlScheme.bushfirePostAdUrlForCategoryId(categoryId));
    }

    /**
     * Redirect to create account
     *
     * @throws IOException if error occurs redirecting
     */
    public void redirectToCreateAccount() throws IOException {
        loginUtils.clearSavedRequest(request);
        ((HttpServletResponse) response).sendRedirect(urlScheme.urlFor(Actions.CREATE_ACCOUNT));
    }

    /**
     * @return the login utils
     */
    public LoginUtils getLoginUtils() {
        return loginUtils;
    }

    /**
     * @return the username
     */
    public String getUsername() {
        return username;
    }

    public UserSession getUserSession() {
        return userSession;
    }

    private Long extractCategoryIdFromLegacySavedRequest() {
        Long categoryId = null;
        if (StringUtils.hasLength(savedRequest.getQueryString())) {
            Pattern categoryIdPattern;
            switch (savedRequestEndpoint) {
                case LEGACY_POST_AD:
                    categoryIdPattern = LEGACY_CATEGORY_ID_PATTERN;
                    break;
                case BUSHFIRE_POST_AD:
                default:
                    categoryIdPattern = BUSHFIRE_CATEGORY_ID_PATTERN;
                    break;
            }

            Matcher matcher = categoryIdPattern.matcher(savedRequest.getQueryString());
            if (matcher.matches()) {
                return new Long(matcher.group(1));
            }

        }
        return categoryId;
    }

    /**
     * @return the {@link SavedRequestEndpoint} for the current request
     */
    public SavedRequestEndpoint getSavedRequestEndpoint() {
        return savedRequestEndpoint;
    }
}
