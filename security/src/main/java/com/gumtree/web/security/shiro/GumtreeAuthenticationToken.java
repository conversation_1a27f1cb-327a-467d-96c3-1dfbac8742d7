package com.gumtree.web.security.shiro;

import com.gumtree.user.service.model.AuthenticationProvider;
import org.apache.shiro.authc.HostAuthenticationToken;
import org.apache.shiro.authc.RememberMeAuthenticationToken;

import java.util.Optional;

public class GumtreeAuthenticationToken implements HostAuthenticationToken, RememberMeAuthenticationToken {
    private static final long serialVersionUID = 1L;

    private String username;
    private String password;
    private String recaptchaResponse;
    private AuthenticationProvider authenticationProvider;
    private String host;
    private String ipAddress;
    private Boolean optInMarketing;
    private String threatmetrixSessionId;

    public GumtreeAuthenticationToken() {

    }

    @Override
    public Object getPrincipal() {
        return getUsername();
    }

    @Override
    public Object getCredentials() {
        return null; // credentials handled by facebook - we don't need them
    }

    @Override
    public String getHost() {
        return host;
    }

    @Override
    public boolean isRememberMe() {
        return true;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public Boolean isOptInMarketing() {
        return optInMarketing;
    }

    public AuthenticationProvider getAuthenticationProvider() {
        return authenticationProvider;
    }

    public String getRecaptchaResponse() {
        return recaptchaResponse;
    }

    public Optional<String> getIpAddress() {
        return Optional.ofNullable(ipAddress);
    }

    public Optional<String> getThreatmetrixSessionId() {
        return Optional.ofNullable(threatmetrixSessionId);
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private String username;
        private String password;
        private String recaptchaResponse;
        private AuthenticationProvider authenticationProvider;
        private String host;
        private String ipAddress;
        private Boolean optInMarketing;
        private String threatmetrixSessionId;

        public Builder withUsername(String username) {
            this.username = username;
            return this;
        }

        public Builder withPassword(String password) {
            this.password = password;
            return this;
        }

        public Builder withRecaptchaResponse(String recaptchaResponse) {
            this.recaptchaResponse = recaptchaResponse;
            return this;
        }

        public Builder withAuthenticationProvider(AuthenticationProvider authenticationProvider) {
            this.authenticationProvider = authenticationProvider;
            return this;
        }

        public Builder withHost(String host) {
            this.host = host;
            return this;
        }

        public Builder withIpAddress(Optional<String> ipAddress) {
            this.ipAddress = ipAddress.orElse(null);
            return this;
        }

        public Builder withOptInMarketing(Boolean optInMarketing) {
            this.optInMarketing = optInMarketing;
            return this;
        }

        public Builder withthreatmetrixSessionId(Optional<String> threatmetrixSessionId) {
            this.threatmetrixSessionId = threatmetrixSessionId.orElse(null);
            return this;
        }

        public GumtreeAuthenticationToken build() {
            GumtreeAuthenticationToken gumtreeAuthenticationToken = new GumtreeAuthenticationToken();
            gumtreeAuthenticationToken.threatmetrixSessionId = threatmetrixSessionId;
            gumtreeAuthenticationToken.authenticationProvider = authenticationProvider;
            gumtreeAuthenticationToken.host = host;
            gumtreeAuthenticationToken.ipAddress = ipAddress;
            gumtreeAuthenticationToken.optInMarketing = optInMarketing;
            gumtreeAuthenticationToken.password = password;
            gumtreeAuthenticationToken.recaptchaResponse = recaptchaResponse;
            gumtreeAuthenticationToken.username = username;
            return gumtreeAuthenticationToken;
        }

    }

}
