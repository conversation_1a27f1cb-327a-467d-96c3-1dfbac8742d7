package com.gumtree.userapi.model;


import java.io.Serializable;

/**
 * Required to decrypt rememberMe cookies. Can't use contract generated class. JSON serializer will fix this.
 */
@Deprecated
public class GumtreeAccessToken implements Serializable {
    private String value;

    public static GumtreeAccessToken createFromString(String value) {
        GumtreeAccessToken code = new GumtreeAccessToken();
        if (value == null) {
            code.value = "";
        } else {
            code.value = value;
        }

        return code;
    }

    public static GumtreeAccessToken create(Long value) {
        GumtreeAccessToken code = new GumtreeAccessToken();
        if (value == null) {
            code.value = "";
        } else {
            code.value = value.toString();
        }

        return code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String asString() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        GumtreeAccessToken that = (GumtreeAccessToken) o;

        if (value != null ? !value.equals(that.value) : that.value != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return value != null ? value.hashCode() : 0;
    }
}
