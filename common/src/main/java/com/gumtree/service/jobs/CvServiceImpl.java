package com.gumtree.service.jobs;

import com.gumtree.cvstore.client.command.DeleteCvCommand;
import com.gumtree.cvstore.client.command.GetCvContentCommand;
import com.gumtree.cvstore.client.command.GetCvMetadataCommand;
import com.gumtree.cvstore.client.command.UploadCvCommand;
import com.gumtree.cvstore.model.ApiResponse;
import com.gumtree.cvstore.model.CvContentResponse;
import com.gumtree.cvstore.model.CvMetadataResponse;
import com.gumtree.cvstore.model.IdResponse;
import com.gumtree.cvstore.spec.CvStoreClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

public class CvServiceImpl implements CvService {

    private Logger logger = LoggerFactory.getLogger(CvServiceImpl.class);

    private final CvStoreClient cvStoreClient;

    @Autowired
    public CvServiceImpl(CvStoreClient cvStoreClient) {
        this.cvStoreClient = cvStoreClient;
    }

    @Override
    public void upload(CvData requestCvData) {
        ApiResponse<IdResponse> response = cvStoreClient.execute(new UploadCvCommand(
                requestCvData.getUserId(),
                requestCvData.getOriginalFilename(),
                requestCvData.getContentType(),
                requestCvData.getBytes()));

        handleErrors(response);
    }

    @Override
    public Optional<CvData> getMetadata(Long userId) {
        ApiResponse<Optional<CvMetadataResponse>> response =  cvStoreClient.execute(new GetCvMetadataCommand(userId));
        Optional<CvMetadataResponse> metadataResponse = handleErrors(response);
        return metadataResponse.map(metadata -> CvData.builder().
                    withUserId(userId).
                    withContentType(metadata.getContentType()).
                    withOriginalFilename(metadata.getFileName()).
                    withUploadDate(metadata.getUploadTimestamp()).build());
    }

    @Override
    public Optional<CvData> getContent(Long userId) {
        ApiResponse<Optional<CvContentResponse>> response = cvStoreClient.execute(new GetCvContentCommand(userId));
        Optional<CvContentResponse> contentResponse = handleErrors(response);
        return contentResponse.map(content -> CvData.builder().
                    withUserId(userId).
                    withContentType(content.getContentType()).
                    withOriginalFilename(content.getFileName()).
                    withBytes(content.getContent()).
                    build());
    }

    @Override
    public void delete(Long userId) {
        ApiResponse<Boolean> response = cvStoreClient.execute(new DeleteCvCommand(userId));
        handleErrors(response);
    }

    private <T> T handleErrors(ApiResponse<T> response) {
        if (!response.isDefined()) {
            String message = "Error occurred when communicating with CV service: " + response.getError().getErrorCode();
            if (response.getError().getMessage() != null) {
                message = message + ": " + response.getError().getMessage();
            }
            logger.error(message);
            throw new RuntimeException(message);
        } else {
            return response.get();
        }
    }

}
