package com.gumtree.service.category;

import com.google.common.base.Optional;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.Category;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * A service for querying available categories.
 *
 * <AUTHOR>
 */
public interface CategoryService {

    /**
     * Determine if a category exists by name.
     *
     * @param name the name of the category
     * @return true or false
     */
    boolean exists(String name);

    /**
     * Determine if a category exists by id.
     *
     * @param id the id of the category
     * @return true or false
     */
    boolean exists(Long id);

    /**
     * Obtains a category by name
     *
     *
     * @param name The name of the category
     * @return The category with the specific name
     */
    Optional<Category> getByUniqueName(String name);

    /**
     * Obtains a category by id
     *
     *
     * @param id The id of the category
     * @return The category with the matching id
     */
    Optional<Category> getById(Long id);

    /**
     * Get the given category's parent.
     *
     * @param category the child category
     * @return the given category's parent.
     */
    Optional<Category> getParent(Category category);

    /**
     * Obtains an l1 category for a given category by id
     *
     *
     * @param id The id of the category to retrieve an l1 for
     * @return The l1 category for the given category id
     */
    Optional<Category> getL1Category(Long id);

    /**
     * Retrieve the category level hierarchy for the given category.
     *
     *
     * @param category the category
     * @return the category level hierarchy for the given category.
     */
    Map<Integer, Category> getLevelHierarchy(Category category);

    Map<Integer, Category> getLevelHierarchy(Long categoryId);

    /**
     * Retrieves the category level for the given id
     *
     * @param id id of the category
     * @return category level = l0, l1, l2, l3... etc
     */
    Integer getCategoryLevel(Long id);

    /**
     * Retrieves the list of child categories for the given category
     *
     * @param category category to retreive child categories for
     * @return category list of children
     */
    List<Category> getChildCategories(Category category);

    /**
     * Check whether a category is a leaf category - ie, it has no children.
     *
     * @param category category to retrieve child categories for
     * @return whether the category is a leaf category
     */
    boolean isLeaf(Category category);

    /**
     * Find a category by a display name.
     *
     * @param displayName to search for
     * @return null if not found
     */
    Collection<Category> findByDisplayName(String displayName);

    /**
     * @param potentialChild to check
     * @param category       potential parent
     * @return true if direct or indirect child
     */
    boolean isChild(Category potentialChild, Category category);

    /**
     * @return all categories.
     */
    Collection<Category> getAll();

    List<Category> getEnabledAndNotVirtual();

    /**
     * @param category to check
     * @return true if category is hidden.
     */
    boolean isHidden(Category category);

    /**
     * Get a list of attribute types for a given category.
     *
     * @param categoryId the category id
     * @return list of attribute types
     */
    List<String> attributeTypes(Long categoryId);

    /**
     * Get the display label for a given attribute type for the specified category.
     *
     *
     * @param categoryId    the category id
     * @param attributeType the attribute type
     * @return the display label
     */
    Optional<String> attributeDisplayLabel(Long categoryId, String attributeType);

    /**
     * Get the display label for a given attribute value for a given category
     *
     * @param categoryId    the category id
     * @param attributeName the attribute name
     * @param value         the value to look up
     * @return the display label
     */
    Optional<String> attributeValueDisplayLabel(Long categoryId, String attributeName, String value);

    /**
     * Test if a category supports a specific attribute type.
     *
     * @param categoryId    the category id
     * @param attributeName the attribute type
     * @return true if the category supports that attribute
     */
    boolean supportsAttribute(Long categoryId, String attributeName);

    /**
     * Returns all attributes matching to provided addtibute name.
     *
     * @param name  attribute name
     */
    List<AttributeMetadata> attributesByName(String name);

    /**
     * Get the branch of the category tree with the current category
     * as final leaf.
     *
     * @param category the leaf category
     * @return the full category branch
     */
    List<Category> getCategoriesList(Category category);

    /**
     * Get the branch of the category tree with the current category and, optionally, it's direct children.
     *
     * @param category              the category
     * @param includeDirectChildren whether to also fetch direct children
     * @param includeRoot           whether to include root 'All' category
     * @return the branch of the category tree with the current category and, optionally, it's direct children.
     */
    List<Category> getBranch(Category category, boolean includeDirectChildren, boolean includeRoot);

    Category getRootCategory();

    Optional<Collection<AttributeMetadata>> getCategoryAttributes();

    CategoryModel getCategoryModel();
}