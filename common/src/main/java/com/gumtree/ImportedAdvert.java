package com.gumtree;

import com.gumtree.util.json.JsonSerializer;

import java.io.Serializable;


/**
 * A simple advert representation.
 *
 * <AUTHOR>
 */
public final class ImportedAdvert implements Serializable {

    private Long id;
    private String creationDate;
    private String lastModification;
    private String deleteDate;
    private String liveDate;
    private Integer approvedBy;
    private String status;
    private String title;
    private String description;
    private String date;
    private String location;
    private String contactEmail;
    private String otherContactable;
    private String firstName;
    private String lastName;
    private String userEmail;
    private String password;
    private String userPhone;
    private String originalLiveDate;
    private String antiSpam;
    private String remoteHost;
    private Integer price;
    private String priceFrequency;
    private String postcode;
    private String anonymousEmail;
    private String couples;
    private Integer age;
    private String agency;
    private String skypeId;
    private String lastModifiedDate;
    private Integer currentStatus;
    private Integer lastAction;
    private Integer autoscreenScore;
    private String hasImages;
    private Double longitude;
    private Double latitude;
    private String lonlattype;
    private String smsMe;
    private Integer isIndexed;
    private Integer roomType;
    private Integer houseType;
    private Integer numBedrooms;
    private Integer contractType;
    private Integer userId;

    /**
     * @return the advert id
     */
    public Long getId() {
        return id;
    }

    /**
     * @return the advert title
     */
    public String getTitle() {
        return title;
    }

    /**
     * @return the advert description
     */
    public String getDescription() {
        return description;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(String creationDate) {
        this.creationDate = creationDate;
    }

    public String getLastModification() {
        return lastModification;
    }

    public void setLastModification(String lastModification) {
        this.lastModification = lastModification;
    }

    public String getDeleteDate() {
        return deleteDate;
    }

    public void setDeleteDate(String deleteDate) {
        this.deleteDate = deleteDate;
    }

    public String getLiveDate() {
        return liveDate;
    }

    public void setLiveDate(String liveDate) {
        this.liveDate = liveDate;
    }

    public Integer getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(Integer approvedBy) {
        this.approvedBy = approvedBy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getOtherContactable() {
        return otherContactable;
    }

    public void setOtherContactable(String otherContactable) {
        this.otherContactable = otherContactable;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getOriginalLiveDate() {
        return originalLiveDate;
    }

    public void setOriginalLiveDate(String originalLiveDate) {
        this.originalLiveDate = originalLiveDate;
    }

    public String getAntiSpam() {
        return antiSpam;
    }

    public void setAntiSpam(String antiSpam) {
        this.antiSpam = antiSpam;
    }

    public String getRemoteHost() {
        return remoteHost;
    }

    public void setRemoteHost(String remoteHost) {
        this.remoteHost = remoteHost;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public String getPriceFrequency() {
        return priceFrequency;
    }

    public void setPriceFrequency(String priceFrequency) {
        this.priceFrequency = priceFrequency;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getAnonymousEmail() {
        return anonymousEmail;
    }

    public void setAnonymousEmail(String anonymousEmail) {
        this.anonymousEmail = anonymousEmail;
    }

    public String getCouples() {
        return couples;
    }

    public void setCouples(String couples) {
        this.couples = couples;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getAgency() {
        return agency;
    }

    public void setAgency(String agency) {
        this.agency = agency;
    }

    public String getSkypeId() {
        return skypeId;
    }

    public void setSkypeId(String skypeId) {
        this.skypeId = skypeId;
    }

    public String getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(String lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public Integer getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(Integer currentStatus) {
        this.currentStatus = currentStatus;
    }

    public Integer getLastAction() {
        return lastAction;
    }

    public void setLastAction(Integer lastAction) {
        this.lastAction = lastAction;
    }

    public Integer getAutoscreenScore() {
        return autoscreenScore;
    }

    public void setAutoscreenScore(Integer autoscreenScore) {
        this.autoscreenScore = autoscreenScore;
    }

    public String getHasImages() {
        return hasImages;
    }

    public void setHasImages(String hasImages) {
        this.hasImages = hasImages;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getLonlattype() {
        return lonlattype;
    }

    public void setLonlattype(String lonlattype) {
        this.lonlattype = lonlattype;
    }

    public String getSmsMe() {
        return smsMe;
    }

    public void setSmsMe(String smsMe) {
        this.smsMe = smsMe;
    }

    public Integer getIsIndexed() {
        return isIndexed;
    }

    public void setIsIndexed(Integer indexed) {
        isIndexed = indexed;
    }

    public Integer getRoomType() {
        return roomType;
    }

    public void setRoomType(Integer roomType) {
        this.roomType = roomType;
    }

    public Integer getHouseType() {
        return houseType;
    }

    public void setHouseType(Integer houseType) {
        this.houseType = houseType;
    }

    public Integer getNumBedrooms() {
        return numBedrooms;
    }

    public void setNumBedrooms(Integer numBedrooms) {
        this.numBedrooms = numBedrooms;
    }

    public Integer getContractType() {
        return contractType;
    }

    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object o) {

        if (this == o) {
            return true;
        }
        if (!(o instanceof ImportedAdvert)) {
            return false;
        }

        ImportedAdvert importedAdvert = (ImportedAdvert) o;

        if (description != null ? !description.equals(importedAdvert.description)
                : importedAdvert.description != null) {
            return false;
        }
        if (id != null ? !id.equals(importedAdvert.id) : importedAdvert.id != null) {
            return false;
        }
        if (title != null ? !title.equals(importedAdvert.title) : importedAdvert.title != null) {
            return false;
        }

        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (title != null ? title.hashCode() : 0);
        result = 31 * result + (description != null ? description.hashCode() : 0);
        return result;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return JsonSerializer.toJsonString(this);
    }
}
