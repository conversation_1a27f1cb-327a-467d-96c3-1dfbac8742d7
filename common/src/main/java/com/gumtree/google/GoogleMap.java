package com.gumtree.google;

import com.gumtree.domain.location.LocationCentroid;
import org.springframework.util.Assert;


public final class GoogleMap {

    private static final String PROTOCOL = "http://";
    private static final String GOOGLE_HOST = "maps.googleapis.com";
    private static final String GOOGLE_PATH = "/maps/api/staticmap";
    private static final String ZOOM = "zoom=15";
    private static final String MAP_TYPE = "maptype=roadmap";
    private static final String SIZE = "size=320x240";
    private static final String HEIGHT = "240";
    private static final String WIDTH = "320";
    private static final String STYLE = "cursor: pointer; ";    //seems an odd place to put this
    private static final String SENSOR = "sensor=false";
    private static final String CLIENT = "client=gme-marktplaats";
    private String latitude;
    private String longitude;

    private GoogleMapsUrlSigner urlSigner;

    /**
     * Constructor, the asserts are an extra check, a GoogleMap is called from the vipController
     * and should never attempt to be created without valid criteria
     *
     * @param locationPoint the location centroid
     */
    public GoogleMap(GoogleMapsUrlSigner urlSigner, LocationCentroid locationPoint) {
        Assert.notNull(urlSigner);
        Assert.notNull(locationPoint);
        Assert.notNull(locationPoint.getLatitude());
        Assert.notNull(locationPoint.getLongitude());
        this.urlSigner = urlSigner;
        this.latitude = String.valueOf(locationPoint.getLatitude());
        this.longitude = String.valueOf(locationPoint.getLongitude());
    }

    public String getHeight() {
        return HEIGHT;
    }

    public String getWidth() {
        return WIDTH;
    }

    public String getStyle() {
        return STYLE;
    }
    public String getLongitude() {
        return this.longitude;
    }
    public String getLatitude() {
        return this.latitude;
    }
    /**
     * Return the googleMap url
     *
     * @return sb.toString() representing the googlemap url
     */
    public String getUrl() {
        StringBuilder sb = new StringBuilder();

        appendUrlFragment(sb, ZOOM);
        appendUrlFragment(sb, SIZE);
        appendUrlFragment(sb, MAP_TYPE);

        sb.append("center");
        sb.append("=");
        sb.append(this.latitude);
        //"escape" comma lat/long separator
        sb.append("%2C");
        sb.append(this.longitude);
        sb.append("&");

        appendUrlFragment(sb, SENSOR);
        sb.append(CLIENT);

        String params = sb.toString();

        try {
            return PROTOCOL + GOOGLE_HOST + urlSigner.signRequest(GOOGLE_PATH, params);
        } catch (UrlSigningException e) {
            //TODO: add logging
            throw new RuntimeException(e);
        }
    }

    /*
   This method appends a url fragment in the form of pair&;
    */
    private void appendUrlFragment(StringBuilder sb, String pair) {
        sb.append(pair);
        sb.append("&");
    }

    /**
     * Equals method
     *
     * @param o method to compare
     * @return return if strings are equal
     */
    @Override
    public boolean equals(Object o) {
        if (o != null && o instanceof GoogleMap) {
            GoogleMap compare = (GoogleMap) o;
            if (getUrl().equals(compare.getUrl())) {
                return true;
            }
        }

        return false;
    }

    @Override
    public int hashCode() {
        return toString().hashCode();
    }
}
