package com.gumtree.domain.media;

/**
 */
public enum ImageSize {

    MINITHUMB(77, 72, 54, "moreadsthumb"),
    THUMB(78, 75, 56, "thumb"),
    MAIN(79, 320, 240, "big"),
    FULL(80, 600, 450, "extrabig"),
    PREVIEW(81, 96, 72, "preview"),
    MAXITHUMB(5, 100, 75, "moreadsthumb");

    private final int id;
    private final int width;
    private final int height;
    private final String name;

    private ImageSize(int id, int width, int height, String name) {
        this.id = id;
        this.width = width;
        this.height = height;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public String getSize() {
        return width + "x" + height;
    }

    public String getName() {
        return name;
    }
}