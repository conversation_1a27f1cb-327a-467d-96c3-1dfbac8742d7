package com.gumtree.domain.media.entity;

import com.gumtree.domain.media.Video;
import org.codehaus.jackson.map.annotate.JsonDeserialize;

/**
 * Default implementation of {@link Video}
 */
public class VideoEntity implements Video {

    @JsonDeserialize
    private String url;

    public VideoEntity() {    }

    public VideoEntity(String url) {
        this.url = url;
    }

    @Override
    public final String getUrl() {
        return url;
    }

    public final void setUrl(String url) {
        this.url = url;
    }
}
