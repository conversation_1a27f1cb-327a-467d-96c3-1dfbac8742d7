package com.gumtree.domain.media.entity;

import com.gumtree.domain.media.Image;
import org.codehaus.jackson.map.annotate.JsonDeserialize;

/**
 * Default implementation of {@link Image}
 */
public class ImageEntity implements Image {

    @JsonDeserialize
    private Long id;

    private String baseUrl;

    public ImageEntity() {}

    public ImageEntity(Long id, String baseUrl) {
        this.id = id;
        this.baseUrl = baseUrl;
    }

    @Override
    public final Long getId() {
        return id;
    }

    public final void setId(Long id) {
        this.id = id;
    }

    public final void setBaseUrl(String url) {
        this.baseUrl = url;
    }

    @Override
    public final String getBaseUrl() {
        return baseUrl;
    }
}
