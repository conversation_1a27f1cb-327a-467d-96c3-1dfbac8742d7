package com.gumtree.domain.category;


import com.google.common.base.Optional;
import com.gumtree.api.category.domain.Category;

public enum Categories {
    ALL(1L, "all"),
    
    // l1
    MOTORS(2551L, "cars-vans-motorbikes"),
    FOR_SALE(2549L, "for-sale"),
    FLATS_AND_HOUSES(10201L, "flats-houses"), // on website is 'Property'
    JOBS(2553L, "jobs"),
    SERVICES(2554L, "business-services"),
    COMMUNITY(2550L, "community"),
    PETS(2526L, "pets"),
    
    // l2 and the rest
    
    PROPERTY_TO_RENT(12181L, "property-to-rent"),
    PROPERTY_TO_SHARE(12183L, "property-to-share"),
    PROPERTY_FOR_SALE(12182L, "property-for-sale"),

    CARS(9311L, "cars"),
    TICKETS(2521L, "tickets"),
    MOTORBIKES(10442L, "motorbikes-scooters"),
    HOUSE_CLEARANCE(153L, "house-clearance"),
    HOME_GARDEN(2514L,"home-garden"),
    OFFICE_FURNITURE(4626L, "office-furniture-equipment"),
    OTHER_GOODS(2524L, "miscellaneous-goods"),
    STUFF_WANTED(2525L, "stuff-wanted"),
    SWAP_SHOP(1039L, "swap-shop"),
    VANS(94L, "vans"),
    OTHER_VEHICLES(11983L, "other-vehicles"),
    IPHONE(10205L, "iphone");



    private Long id;
    private String seoName;

    Categories(Long id, String seoName) {
        this.id = id;
        this.seoName = seoName;
    }

    public Long getId() {
        return id;
    }

    public String getSeoName() {
        return seoName;
    }

    public boolean is(Optional<Category> cat) {
        return cat != null && cat.isPresent() && this.is(cat.get());
    }

    public boolean is(Category cat) {
        return cat != null && cat.getSeoName() != null && cat.getSeoName().equals(this.getSeoName());
    }
}
