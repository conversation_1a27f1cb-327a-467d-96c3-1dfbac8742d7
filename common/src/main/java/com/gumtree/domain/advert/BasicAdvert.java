package com.gumtree.domain.advert;

import com.gumtree.domain.Identifiable;

import java.util.Date;
import java.util.List;

/**
 * A model to represent a basic classified advert, containing only core properties. Most of the adverts, including
 * expired and archived adverts will contain these core properties.
 */
public interface BasicAdvert extends Identifiable<Long> {
    /**
     * @return the title of the advert
     */
    String getTitle();

    /**
     * @return the description for the advert
     */
    String getDescription();

    /**
     * @return the id of the category this advert is bound to
     */

    Long getCategoryId();

    /**
     * @return the id of the location this advert is bound to
     */
    Integer getLocationId();

    /**
     *
     * @return the ids of the polygons associated with the advert
     */
    List<Integer> getLocationIds();

    /**
     * The date this advert was originally put live to site.
     * @return null has never been live yet
     */
    Date getLiveDate();

    /**
     * The date this advert was archived
     * @return null has not need archived yet
     */
    Date getArchivedDate();

    /**
     * @return the status of this advert
     */
    AdvertStatus getStatus();

}
