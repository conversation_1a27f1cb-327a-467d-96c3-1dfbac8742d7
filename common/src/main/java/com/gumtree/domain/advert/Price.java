package com.gumtree.domain.advert;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Currency;

/**
 * Model to represent the price of an {@link Advert}
 */
public interface Price extends Serializable {

    /**
     * @return the currency for this {@link Price}
     */
    Currency getCurrency();

    /**
     * @return the monetary amount of this {@link Price}
     */
    BigDecimal getAmount();
}
