package com.gumtree.domain.user.entity;

import com.gumtree.domain.user.User;
import com.gumtree.domain.user.UserType;
import com.gumtree.util.json.jackson.deserializer.DateStringToDateDeserializer;
import org.codehaus.jackson.map.annotate.JsonDeserialize;

import java.util.Date;

/**
 * Default implementation of {@link User}
 */
public class UserEntity implements User {

    @JsonDeserialize
    private String id;

    @JsonDeserialize
    private UserType type;

    @JsonDeserialize(using = DateStringToDateDeserializer.class)
    private Date firstPostingDate;

    @JsonDeserialize
    private String contactUrl;

    @JsonDeserialize
    private String contactTelephone;

    @JsonDeserialize
    private String emailAddress;

    @JsonDeserialize
    private String contactName;

    @Override
    public final String getId() {
        // TODO: Could we should hash the email address here?
        return id;
    }

    @Override
    public final UserType getType() {
        return type;
    }

    @Override
    public final Date getFirstPostingDate() {
        return firstPostingDate;
    }

    @Override
    public final String getEmailAddress() {
        return emailAddress;
    }

    @Override
    public final String getContactUrl() {
        return contactUrl;
    }

    @Override
    public final String getContactTelephone() {
        return contactTelephone;
    }

    public final void setId(String id) {
        this.id = id;
    }

    public final void setType(UserType type) {
        this.type = type;
    }

    public final void setFirstPostingDate(Date firstPostingDate) {
        this.firstPostingDate = firstPostingDate;
    }

    public final void setContactUrl(String contactUrl) {
        this.contactUrl = contactUrl;
    }

    public final void setContactTelephone(String contactTelephone) {
        this.contactTelephone = contactTelephone;
    }

    public final void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public static final class Builder {
        private UserEntity userEntity;

        private Builder() {
            userEntity = new UserEntity();
        }

        public Builder withId(String id) {
            userEntity.id = id;
            return this;
        }

        public Builder withType(UserType type) {
            userEntity.type = type;
            return this;
        }

        public Builder withFirstPostingDate(Date firstPostingDate) {
            userEntity.firstPostingDate = firstPostingDate;
            return this;
        }

        public Builder withContactUrl(String contactUrl) {
            userEntity.contactUrl = contactUrl;
            return this;
        }

        public Builder withContactTelephone(String contactTelephone) {
            userEntity.contactTelephone = contactTelephone;
            return this;
        }

        public Builder withEmailAddress(String emailAddress) {
            userEntity.emailAddress = emailAddress;
            return this;
        }

        public Builder withContactName(String contactName) {
            userEntity.contactName = contactName;
            return this;
        }

        public static Builder entity() {
            return new Builder();
        }

        public UserEntity build() {
            return userEntity;
        }
    }
}
