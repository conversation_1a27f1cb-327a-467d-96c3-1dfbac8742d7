package com.gumtree.domain.location.entity;

import com.gumtree.domain.location.LocationCentroid;
import org.codehaus.jackson.map.annotate.JsonDeserialize;

import java.io.Serializable;

/**
 * Default implementation of {@link LocationCentroid}
 */
public class LocationCentroidEntity implements LocationCentroid, Serializable {
    private static final long serialVersionUID = 1L;

    @JsonDeserialize
    private Double latitude;

    @JsonDeserialize
    private Double longitude;

    @Override
    public final Double getLatitude() {
        return latitude;
    }

    public final void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    @Override
    public final Double getLongitude() {
        return longitude;
    }

    public final void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public static final class Builder {
        private LocationCentroidEntity locationCentroidEntity;

        private Builder() {
            locationCentroidEntity = new LocationCentroidEntity();
        }

        public Builder withLatitude(Double latitude) {
            locationCentroidEntity.latitude = latitude;
            return this;
        }

        public Builder withLongitude(Double longitude) {
            locationCentroidEntity.longitude = longitude;
            return this;
        }

        public static Builder entity() {
            return new Builder();
        }

        public LocationCentroidEntity build() {
            return locationCentroidEntity;
        }
    }
}
