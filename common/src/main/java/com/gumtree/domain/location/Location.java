package com.gumtree.domain.location;

import com.gumtree.domain.Identifiable;

import java.math.BigDecimal;

/**
 * Represents a location.
 *
 * <AUTHOR> apeglow
 */
public interface Location extends Identifiable<Integer>, Comparable<Location> {

    /**
     * name of the location "United Kingdom"
     */
    String UK = "uk";

    Long UK_ID = 10000392L;

    /**
     * name of the location "United Kingdom"
     */
    String UK_DISPLAY_NAME = "United Kingdom";

    /**
     * Get the unique name for this location.
     *
     * @return the unique name for this location.
     */

    String getName();

    /**
     * Get the display name for this location.
     *
     * @return the display name for this location.
     */
    String getDisplayName();

    /**
     * Specifies whether this is a landing location or not.
     *
     * @return whether this is a landing location or not.
     */
    boolean isLanding();

    BigDecimal getLatitude();

    BigDecimal getLongitude();

    BigDecimal getRadius();
}
