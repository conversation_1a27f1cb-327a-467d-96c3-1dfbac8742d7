package com.gumtree.domain.newattribute.value;


import java.io.Serializable;

/**
 * This represents a range from a min to a max value.
 * Currently we are in the lucky position that we have only
 * integer ranges. That's why min and max are of type integer.
 *
 * If this changes once the best approach is to change the datatype
 * of min and max to {@link com.gumtree.domain.newattribute.AttributeValue}.
 *
 * <AUTHOR>
 *
 */
public final class Range implements Serializable {
    private final Long min;
    private final Long max;

    /**
     * Will sort both values automatically.
     * @param v1 maybe null
     * @param v2 maybe null
     */
    public Range(Long v1, Long v2) {
        if (v1 != null && v2 != null) {
            min = Math.min(v1, v2);
            max = Math.max(v1, v2);
        } else {
            min = v1;
            max = v2;
        }
    }

    public Long getMin() {
        return min;
    }

    public Long getMax() {
        return max;
    }

    /**
     * Is the range given as parameter contained in the this range?
     * @param range to be checked
     * @return true or false
     */
    public boolean containes(Range range) {
        Long myMin = min != null ? min : 0;
        Long theirMin = range.getMin() != null ? range.getMin() : 0;

        if (theirMin < myMin) {
            return false;
        }

        if (max == null) {
            return true;
        }

        if (range.getMax() == null) {
            return false;
        }

        return max >= range.getMax();
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((max == null) ? 0 : max.hashCode());
        result = prime * result + ((min == null) ? 0 : min.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof Range)) {
            return false;
        }
        Range other = (Range) obj;
        if (max == null) {
            if (other.max != null) {
                return false;
            }
        } else if (!max.equals(other.max)) {
            return false;
        }
        if (min == null) {
            if (other.min != null) {
                return false;
            }
        } else if (!min.equals(other.min)) {
            return false;
        }
        return true;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "Range [min=" + min + ", max=" + max + "]";
    }

}
