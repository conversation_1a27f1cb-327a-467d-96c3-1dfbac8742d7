package com.gumtree.domain.newattribute;

import com.gumtree.api.category.domain.SearchAttribute;
import com.gumtree.domain.newattribute.value.Range;

import java.util.List;

/**
 * Display representation of an atribute type.
 * <AUTHOR>
 *
 */
public interface DisplayAttributeType {

    /**
     * @return the name (serves as identifier) of this attribute type
     */
    String getName();

    /**
     * @return the display name
     */
    String getDisplayName();

    /**
     * @return all attribute of this type
     *         which are available in the current context
     */
    List<DisplayAttribute> getAttributes();

    /**
     * Get the first attribute in the list.
     * @return null if no attributes are available
     */
    DisplayAttribute getFirstAttribute();

    /**
     * The currently selected value of this attribute type.
     * @return maybe null
     */
    String getSelectedValue();

    /**
     * The currently selected range of this attribute type.
     * @return maybe null
     */
    Range getSelectedRange();

    /**
     * @return the search type of this atribute type.
     */
    SearchAttribute.Style getSearchStyle();
}
