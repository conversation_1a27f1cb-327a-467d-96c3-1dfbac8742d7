package com.gumtree.domain.newattribute;

import java.io.Serializable;

/**
 * This represents a concrete attribute. Do not confuse
 * attribute with attribute type (and this with data type).
 *
 * An attribute is e.g. "Mileage = 15000".
 *
 * "Mileage" is not an attribute. "Mileage" is an attribute type.
 *
 * <AUTHOR>
 *
 */
public interface Attribute extends Serializable {

    /**
     * @return the type of this attribute
     */
    String getType();

    /**
     * @return the value
     */
    AttributeValue getValue();

}
