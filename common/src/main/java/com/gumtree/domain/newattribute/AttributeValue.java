package com.gumtree.domain.newattribute;

import java.io.Serializable;

/**
 * Represents a value of an attribute.
 *
 * There is an implementation for every {@link AttributeValueRange}.
 *
 * Please note, that *equals and hashCode* must be overwritten in
 * every implementation.
 *
 * <AUTHOR>
 *
 */
public interface AttributeValue extends Serializable {

    /**
     * The name serves as identifier for this value.
     * @return the name
     */
    String getName();

    /**
     * Converts the value to any desired data type.
     * @param clazz to convert the value to
     * @return null if value does not make any valid value of the desired type.
     * @param <T> the desired type
     */
    <T> T as(Class<T> clazz);
}
