package com.gumtree.domain.newattribute.internal.value;

import com.gumtree.domain.newattribute.AttributeValue;
import com.gumtree.domain.newattribute.internal.InvalidAttributeValueException;

import java.util.List;

/**
 * Implements a boolean value.
 *
 * This will understand "yes" and "true" as true,
 * "no" and "false" as false
 * "all" as (true or false)
 *
 * If you change something don't forget to overwrite equals and hashcode.
 *
 * <AUTHOR>
 *
 */
public final class BoolValue implements AttributeValue {

    private static Boolean createFromString(String str) {
        if (str.equalsIgnoreCase("yes") || str.equalsIgnoreCase("y") || str.equalsIgnoreCase("true")) {
            return true;
        }

        if (str.equals("all")) {
            return null;
        }
        return false;
    }

    private static Boolean createFromList(List<Object> list) {
        if (!list.isEmpty()) {
            return createFromString(list.get(0).toString());
        }
        return null;
    }

    private final Boolean value;

    /**
     * Can be a boolean, a string "yes", "no", "true", "false", "all"
     * an string array or list containing an entry with such value.
     * @param value to be interpreted
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    private BoolValue(Boolean value) {
        this.value = value;
    }

    @Override
    public String getName() {
        if (value == null) {
            return "all";
        }
        if (value) {
            return "yes";
        }
        return "no";
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T as(Class<T> clazz) {
        if (Boolean.class.isAssignableFrom(clazz)) {
            return (T) value;
        } else if (String.class.isAssignableFrom(clazz)) {
            return (T) getName();
        }

        return null;
    }

    @Override
    public String toString() {
        if (value == null) {
            return "";
        }
        if (value) {
            return "Yes";
        }
        return "No";
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((value == null) ? 0 : value.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof BoolValue)) {
            return false;
        }
        BoolValue other = (BoolValue) obj;
        if (value == null) {
            if (other.value != null) {
                return false;
            }
        } else if (!value.equals(other.value)) {
            return false;
        }
        return true;
    }

    /**
     * Creates instance of Bool Attribute Value
     *
     * @param o - The object
     * @return - instance of BoolValue
     * @throws InvalidAttributeValueException
     *             - If Attribute value cannot be created
     */
    public static AttributeValue create(Object o) throws InvalidAttributeValueException {
        Boolean value;
        if (o instanceof Boolean) {
            value = (Boolean) o;
        } else if (o instanceof String) {
            value = createFromString((String) o);
        } else if (o instanceof String[]) {
            Boolean v = null;
            String[] strArray = (String[]) o;
            for (String str : strArray) {
                Boolean singleValue = createFromString(str);
                if (singleValue == null) {
                    v = null;
                    break;
                }
                if (v == null) {
                    v = singleValue;
                }

                if (!v.equals(singleValue)) {
                    v = null;
                    break;
                }
            }

            value = v;
        } else if (o instanceof List) {
            value = createFromList((List) o);
        } else {
            throw new InvalidAttributeValueException("Object " + o + " could not be converted to bool value.");
        }

        if (value == null) {
            throw new InvalidAttributeValueException("Object " + o + " could not be converted to bool value.");
        }
        return new BoolValue(value);
    }
}
