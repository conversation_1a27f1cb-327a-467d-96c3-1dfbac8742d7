package com.gumtree.util.url;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.search.UserSearchRefinement;
import com.gumtree.search.sorting.Sorting;
import com.gumtree.util.model.UserSearch;
import com.gumtree.util.http.UrlParameter;

import java.util.List;

/**
 * Interface for a URL scheme for search results pages.
 */
public interface SRPUrlScheme {

    String SORT_BY = "sort_by";
    String SORT_ORDER = "sort_order";
    String SORT_ORDER_INCREASING = "increasing";
    String MIN_PRICE = "min_price";
    String MAX_PRICE = "max_price";
    String WITH_IMAGES_ONLY = "photos_filter";
    String URGENT_ADS_ONLY = "urgent_filter";
    String FEATURED = "featured_filter";
    String DISTANCE = "distance";
    String CURRENT_DISTANCE = "current_distance";
    String MAX_ADVERT_AGE = "age";
    String SEARCH_SCOPE = "search_scope";
    String SEARCH_SCOPE_ALL = "all";
    String SEARCH_TERMS = "q";

    /**
     * Get a url for viewing a category listing page
     *
     * @param category the category to view
     * @param location the location selected alongside the category
     * @return a url for viewing a category listing page.
     */
    String getFor(Category category, Location location);

    /**
     * Get a url for viewing a listing page for
     * all categories.
     *
     * @param location must not be null
     * @return the url
     */
    String getFor(Location location);

    /**
     * @param userSearch executed
     * @param sorting    required
     * @return the url
     */
    String getFor(UserSearch userSearch, Sorting sorting);

    /**
     * @param userSearch not null
     * @return the url
     */
    String getForNoAttributes(UserSearch userSearch);

    /**
     * Returns a URL based on the <code>UserSearch</code> and page number to
     * append
     *
     * @param userSearch the UserSearch
     * @param pageNumber the page number to go to
     * @param prefix     prefix to use
     * @return the generated Url.
     */
    String getFor(UserSearch userSearch, int pageNumber, String prefix);

    /**
     * @param userSearchRefinement not null
     * @return list of parameters
     */
    List<UrlParameter> getUrlParametersFor(UserSearchRefinement userSearchRefinement);

    /**
     * @param userSearch to create url for
     * @return the url
     */
    String getFor(UserSearch userSearch);

    String getForMobile(UserSearch userSearch);

    /**
     * @param userSearch not null
     * @param location   new location
     * @return url
     */
    String getFor(UserSearch userSearch, Location location);

    /**
     * @param userSearch not null
     * @param category   new category
     * @return url
     */
    String getFor(UserSearch userSearch, Category category);

    /**
     * Get the matching url without the query part.
     *
     * @param userSearch to represent
     * @return the url
     */
    String getForWithoutQuery(UserSearch userSearch);

    /**
     * Get the matching url without the query part for a specific page number.
     *
     * @param userSearch to represent
     * @param pageNumber page number
     * @return the url
     */
    String getForWithoutQuery(UserSearch userSearch, Integer pageNumber);

    /**
     * Get the matching url without the query part
     *
     * @param userSearch to represent
     * @param category location
     * @return the url
     */
//    String getForWithoutQuery(UserSearch userSearch, Category category);

    /**
     * Get the matching url without the query part
     *
     * @param userSearch to represent
     * @param location location
     * @return the url
     */
    //String getForWithoutQuery(UserSearch userSearch, Location location);

    /**
     * Get the URL for the RSS feed for the given search.
     *
     * @param userSearch to represent
     * @param pageNumber page number
     * @return the url
     */
    String getRSSUrl(UserSearch userSearch, Integer pageNumber);
}