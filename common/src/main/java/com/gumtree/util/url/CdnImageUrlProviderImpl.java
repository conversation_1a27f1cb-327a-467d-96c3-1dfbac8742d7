package com.gumtree.util.url;

import com.ebay.ecg.eps.client.EpsImageUrl;
import com.gumtree.domain.media.ImageSize;
import com.gumtree.web.service.images.secure.SecureImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CdnImageUrlProviderImpl implements CdnImageUrlProvider{

    public static final String CLOUDFLARE_URL_PREFIX = "imagedelivery.net";
    public static final String FWD_SLASH_CHAR = "/";

    private final SecureImageService secureImageService;

    @Autowired
    public CdnImageUrlProviderImpl(SecureImageService secureImageService) {
        this.secureImageService = secureImageService;
    }

    public String getImageUrl(String imageUrl, ImageSize size) {
        if (isCloudFlareUrl(imageUrl)) {
            return buildCFImageUrl(imageUrl, size.getId());
        } else {
            return new EpsImageUrl(imageUrl).getImageUrl(size.getId());
        }
    }

    public String getSecureImageUrl(String imageUrl, int sizeId){
        if (isCloudFlareUrl(imageUrl)) {
            return buildCFImageUrl(imageUrl, sizeId);
        } else {
            String epsImageUrl = new EpsImageUrl(imageUrl).getImageUrl(sizeId);
            return secureImageService.getSecureUrl(epsImageUrl);
        }
    }

    public boolean isCloudFlareUrl(String imageUrl){
        return imageUrl.contains(CLOUDFLARE_URL_PREFIX);
    }

    public String buildCFImageUrl(String imageUrl, int sizeId){
        return imageUrl.substring(0, imageUrl.lastIndexOf(FWD_SLASH_CHAR) + 1) + sizeId;
    }
}
