package com.gumtree.util.url.impl;

import com.google.common.base.Joiner;
import com.google.common.base.Optional;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.common.util.StringUtils;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.search.UserGeoLocation;
import com.gumtree.search.UserSearchLocation;
import com.gumtree.search.UserSearchRefinement;
import com.gumtree.search.sorting.Sorting;
import com.gumtree.service.category.CategoryService;
import com.gumtree.util.http.UrlParameter;
import com.gumtree.util.model.UserSearch;
import com.gumtree.util.url.SRPUrlScheme;
import com.gumtree.util.url.UrlSchemeBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_MAKE;
import static com.gumtree.api.category.domain.CategoryConstants.Attribute.VEHICLE_MODEL;
import static com.gumtree.common.util.url.UrlUtils.enc;
import static com.gumtree.domain.category.Categories.CARS;
import static org.apache.commons.lang3.StringUtils.replaceChars;

@Service
public class DefaultSRPUrlScheme extends UrlSchemeBase implements SRPUrlScheme {
    protected static final String RSS_PATH = "rssfeed";
    private static final List<CategoryConstants.Attribute> MAKE_MODEL_ATTRIBUTES =
            ImmutableList.of(VEHICLE_MAKE, VEHICLE_MODEL);

    @Autowired
    private CategoryService categoryService;

    @Override
    public final String getFor(Category category, Location location) {
        Assert.notNull(category);
        Assert.notNull(location);
        if (category.getUrl() != null) {
            return UrlSchemeImpl.getFixedCategoryUrl(category, location);
        }
        UserSearch userSearch = new UserSearch.Builder()
                .category(category)
                .locationSearch(new UserSearchLocation(location))
                .build();
        return getForInternal(userSearch, 1, null);
    }

    @Override
    public final String getFor(Location location) {
        UserSearch userSearch = new UserSearch.Builder()
                .category(categoryService.getByUniqueName(Categories.ALL.getSeoName()).get())
                .locationSearch(new UserSearchLocation(location))
                .build();
        return getForInternal(userSearch, 1, null);
    }

    @Override
    public final String getForNoAttributes(UserSearch userSearch) {
        UserSearchRefinement currentRefinement = userSearch.getRefinement();
        UserSearchRefinement newRefinement = new UserSearchRefinement.Builder()
                .sorting(currentRefinement.getSorting())
                .build();
        UserSearch newUserSearch = new UserSearch.Builder(userSearch)
                .refinement(newRefinement)
                .build();
        return getFor(newUserSearch);
    }

    @Override
    public final String getFor(UserSearch userSearch, Sorting sorting) {
        UserSearchRefinement currentRefinement = userSearch.getRefinement();
        UserSearchRefinement newRefinement = new UserSearchRefinement.Builder(currentRefinement)
                .sorting(sorting)
                .build();
        UserSearch newUserSearch = new UserSearch.Builder(userSearch)
                .refinement(newRefinement)
                .build();
        return getFor(newUserSearch, 1, null);
    }

    @Override
    public final String getFor(UserSearch userSearch, Category category) {
        UserSearch.Builder searchForNewCategory = new UserSearch.Builder(userSearch)
                .category(category);
        Category oldCategory = userSearch.getCategory();
        // GTMOT-85 If old-category is root, don't remove refinements
        if (oldCategory.getParentId() != null && differentL1s(oldCategory, category)) {
            // cleanup attributes
            UserSearchRefinement oldRefinement = userSearch.getRefinement();
            UserSearchRefinement newRefinment = new UserSearchRefinement.Builder()
                    .sorting(oldRefinement.getSorting())
                    .urgentAdsOnly(oldRefinement.isUrgentAdsOnly())
                    .featuredAdsOnly(oldRefinement.getFeaturedAdsOnly())
                    .adsWithImagesOnly(oldRefinement.isAdsWithImagesOnly())
                    .build();
            searchForNewCategory.refinement(newRefinment);
        }
        return getFor(searchForNewCategory.build());
    }

    private boolean differentL1s(Category oldCategory, Category newCategory) {
        if (oldCategory.equals(newCategory)) {
            return false;
        }
        Optional<Category> oldL1 = categoryService.getL1Category(oldCategory.getId());
        if (!oldL1.isPresent()) {
            return true;
        }
        Optional<Category> newL1 = categoryService.getL1Category(newCategory.getId());
        if (!newL1.isPresent()) {
            return true;
        }

        return !oldL1.get().equals(newL1.get());
    }

    @Override
    public final String getFor(UserSearch userSearch, Location location) {
        UserSearch userSearchForNewLocation = new UserSearch.Builder(userSearch)
                .geoLocationSearch(Optional.<UserGeoLocation>absent())
                .locationSearch(new UserSearchLocation(location))
                .build();
        return getFor(userSearchForNewLocation, 1, null);
    }

    @Override
    public final String getFor(UserSearch userSearch, int pageNumber, String prefix) {
        return getForInternal(userSearch, pageNumber, prefix);
    }

    @Override
    public final String getFor(UserSearch userSearch) {
        return getFor(userSearch, 1, null);
    }

    @Override
    public String getForMobile(UserSearch userSearch) {

        StringBuilder builder = new StringBuilder()
                .append(absoluteUrlBuilder().toString().replace("www.", "m."));

        String categoryName = "all";
        if (userSearch.getCategory() != null) {
            categoryName = userSearch.getCategory().getSeoName();
        }
        builder.append("/").append(categoryName);

        if(CARS.is(userSearch.getCategory())) {
            addCarMakeModelDirectories(userSearch, builder);
        }

        String locationSearchTerm = getLocationSearchTerm(userSearch.getLocationSearch());
        if (!StringUtils.hasText(locationSearchTerm)) {
            locationSearchTerm = userSearch.getLocationSearch().getLocation().getName();
        }

        builder.append("/").append(enc(locationSearchTerm));

        String searchTerms = userSearch.getUserSearchKeywords().getUserInput();
        if (StringUtils.hasText(searchTerms)) {
            builder.append("/").append(enc(searchTerms));
        }

        if (userSearch.getRefinement().getAttributes().size() != 0
                || (userSearch.getRefinement().getDistance().isPresent()
                && userSearch.getRefinement().getDistance().get() != 0)) {
            addUrlParameters(builder, userSearch.getRefinement());
        }

        return builder.toString();
    }

    @Override
    public final List<UrlParameter> getUrlParametersFor(UserSearchRefinement r) {
        List<UrlParameter> parameters = new ArrayList<>();
        Sorting sorting = r.getSorting();
        Collection<Attribute> attributes = r.getAttributes();

        if (sorting != null) {
            if (!sorting.getSortProperty().equals(Sorting.DEFAULT.getSortProperty())) {
                parameters.add(new UrlParameter(SRPUrlScheme.SORT_BY, sorting.getSortProperty().toString()));
            }
            if (sorting.isAscending()) {
                parameters.add(new UrlParameter(SRPUrlScheme.SORT_ORDER, SRPUrlScheme.SORT_ORDER_INCREASING));
            }
        }

        for (Attribute attribute : attributes) {
            String valueName = attribute.getValue().getName();
            if (valueName != null) {
                String key = attribute.getType();
                parameters.add(new UrlParameter(key, valueName));
            }
        }

        if (r.isAdsWithImagesOnly()) {
            parameters.add(new UrlParameter(WITH_IMAGES_ONLY, "Y"));
        }

        if (r.isUrgentAdsOnly()) {
            parameters.add(new UrlParameter(URGENT_ADS_ONLY, "Y"));
        }
        if (r.isFeaturedAdsOnly()) {
            parameters.add(new UrlParameter(FEATURED, "Y"));
        }

        if (r.getSearchInDescription()) {
            parameters.add(new UrlParameter(SRPUrlScheme.SEARCH_SCOPE, SRPUrlScheme.SEARCH_SCOPE_ALL));
        }

        if (r.getDistance().isPresent() && r.getDistance().get() != 0) {
            parameters.add(new UrlParameter(SRPUrlScheme.DISTANCE, String.valueOf(r.getDistance().get())));
        }

        if (r.getMaxAdAge().isPresent()) {
            parameters.add(new UrlParameter(SRPUrlScheme.MAX_ADVERT_AGE, String.valueOf(r.getMaxAdAge().get())));
        }

        Collections.sort(parameters);
        return parameters;
    }

    @Override
    public final String getForWithoutQuery(UserSearch userSearch) {
        return getForInternal(userSearch, 1, false, null);
    }

    @Override
    public final String getForWithoutQuery(UserSearch userSearch, Integer pageNumber) {
        return getForInternal(userSearch, pageNumber, false, null);
    }

    @Override
    public final String getRSSUrl(UserSearch userSearch, Integer pageNumber) {
        if (pageNumber == null) {
            pageNumber = 1;
        }
        return getFor(userSearch, pageNumber, RSS_PATH);
    }

    private String getForInternal(UserSearch userSearch, int pageNumber, String prefix) {
        return getForInternal(userSearch, pageNumber, true, prefix);
    }

    private String getForInternal(UserSearch userSearch, int pageNumber, boolean withQuery, String prefix) {

        String searchTerms = userSearch.getUserSearchKeywords().getUserInput();
        StringBuilder url = absoluteUrlBuilder();
        if (prefix != null) {
            url.append("/").append(prefix);
        }
        url.append("/");
        final Category category = userSearch.getCategory();
        url.append(category.getSeoName());

        final Attribute modelAttribute = userSearch.getRefinement().getAttribute(VEHICLE_MODEL.getName());
        if(modelAttribute != null && CARS.is(categoryService.getParent(category))) {
            url.insert(url.indexOf(category.getSeoName()), CARS.getSeoName() + "/");
            url.append("/").append(replaceChars(modelAttribute.getValue().as(String.class), '_', '-'));
        }

        String locationSearchTerm = getLocation(userSearch.getGeoLocationSearch(), userSearch.getLocationSearch());
        if (!StringUtils.hasText(locationSearchTerm)) {
            locationSearchTerm = userSearch.getLocationSearch().getLocation().getName();
        }
        if (!locationSearchTerm.equals("uk") || StringUtils.hasText(searchTerms) || pageNumber > 1) {
            url.append("/");
            url.append(enc(locationSearchTerm));
        }

        if (StringUtils.hasText(searchTerms)) {
            url.append("/").append(enc(searchTerms));
        }

        if (pageNumber > 1) {
            url.append("/page").append(pageNumber);
        }

        if (withQuery) {
            addUrlParameters(url, userSearch.getRefinement());
        }

        return url.toString();
    }

    private void addCarMakeModelDirectories(UserSearch userSearch, StringBuilder url) {
        for(CategoryConstants.Attribute attr: MAKE_MODEL_ATTRIBUTES) {
            final Attribute attribute = userSearch.getRefinement().getAttribute(attr.getName());
            if(attribute != null) {
                url.append('/').append(attribute.getValue().as(String.class).replace('_', '-'));
            } else {
                break;
            }
        }
    }

    private String getLocation(Optional<UserGeoLocation> geoLocation, UserSearchLocation userLocation) {
        if (geoLocation != null && geoLocation.isPresent()) {
            return geoLocation.get().getPostcode();
        } else {
            return getLocationSearchTerm(userLocation);
        }
    }

    private String getLocationSearchTerm(UserSearchLocation locationSearch) {
        Collection<String> searchTerms = locationSearch.getLocationSearchTerms();
        Collection<String> zipcodes = locationSearch.getZipOutCodes();
        if (searchTerms.isEmpty() && zipcodes.isEmpty()) {
            if (locationSearch.getLocation() != null) {
                return locationSearch.getLocation().getName();
            }
            return "uk";
        }
        return locationSearch.getUserInput();
    }

    private void addUrlParameters(StringBuilder url, UserSearchRefinement refinement) {
        List<String> displayedParameters = Lists.newArrayList();
        for (UrlParameter urlParameter : getUrlParametersFor(refinement)) {
            if (!VEHICLE_MAKE.getName().equals(urlParameter.getName()) &&
                    !VEHICLE_MODEL.getName().equals(urlParameter.getName())) {
                displayedParameters.add(urlParameter.getName() + "=" + urlParameter.getValue());
            }
        }
        if (!displayedParameters.isEmpty()) {
            url.append('?');
            Joiner.on('&').appendTo(url, displayedParameters);
        }
    }

}
