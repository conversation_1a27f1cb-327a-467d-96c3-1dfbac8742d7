package com.gumtree.util;

import com.gumtree.domain.category.Categories;

import java.util.HashMap;
import java.util.Map;

/**
 * Provides mapping of entity ids to and from symbolic names.
 * TODO: I'm really not sure this is right (I don't like it much) - Probably need an injectable bean
 */
public final class SymbolicNameMapper {

    private static Map<String, Long> symbolicCategoryNameToIdMap = new HashMap<>();

    private static Map<Long, String> categoryIdToSymbolicNameMap = new HashMap<>();

    static {
        addCategoryMapping(Categories.MOTORS.getId(), "cat_cars_L1");
        addCategoryMapping(Categories.FOR_SALE.getId(), "cat_for-sale_L1");
        addCategoryMapping(Categories.SERVICES.getId(), "cat_services_L1");
        addCategoryMapping(Categories.JOBS.getId(), "cat_jobs_L1");
        addCategoryMapping(Categories.FLATS_AND_HOUSES.getId(), "cat_housing_L1");
        addCategoryMapping(Categories.COMMUNITY.getId(), "cat_community_L1");
        addCategoryMapping(Categories.PETS.getId(), "cat_pets_L1");
        addCategoryMapping(1000003L, "cat_competitions_L1");
        addCategoryMapping(9395L, "cat_pets-for-sale_L2");
    }

    /**
     * Private constructor.
     */
    private SymbolicNameMapper() {

    }

    /**
     * Map from symbolic category name to category id.
     *
     * @param symbolicName the category symbolic name
     * @return the category id for the given symbolic name
     */
    public static Long getCategoryId(String symbolicName) {
        return symbolicCategoryNameToIdMap.get(symbolicName);
    }

    /**
     * Map from category id to category symbolic name.
     *
     * @param categoryId the category id
     * @return the symbolic category name
     */
    public static String getSymbolicNameForCategory(Long categoryId) {
        return categoryIdToSymbolicNameMap.get(categoryId);
    }

    private static void addCategoryMapping(Long id, String symbolicName) {
        symbolicCategoryNameToIdMap.put(symbolicName, id);
        categoryIdToSymbolicNameMap.put(id, symbolicName);
    }
}
