package com.gumtree.util.cache;

/**
 * Created with IntelliJ IDEA.
 * User: asalvadore
 * Date: 6/17/13
 * Time: 6:19 PM
 * To change this template use File | Settings | File Templates.
 */
public interface TypedCacheService<K, T> {
    /**
     * Get the value associated with 'key', cached or freshly acquired
     *
     * @param namespace the namespace
     * @param key the key for the desired value
     * @return the value for the specified key
     */
    T get(CacheNamespace namespace, K key);
}
