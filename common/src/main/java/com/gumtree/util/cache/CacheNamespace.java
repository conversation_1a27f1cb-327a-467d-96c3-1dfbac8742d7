package com.gumtree.util.cache;

/**
 * Represents cache namespaces.
 * <AUTHOR>
 *
 */
public enum CacheNamespace {

    LOCATION_PAGE_ABUNDANCE_COUNTS("ac"),
    CATEGORY_PAGE_ABUNDANCE_COUNTS("cac"),
    HOMEPAGE_ABUNDANCE_COUNTS("hp"),
    CATEGORY_LANDING_PAGE_MODEL("lp"),
    VIP_PAGE_MODEL("vp"),
    STATS_SERVICE_POPULAR_SEARCHES("stps"),
    VIP_XML_SITEMAP_HISTOGRAM_PAGINATOR("vxhp");

    private final String prefix;

    CacheNamespace(String prefix) {
        this.prefix = prefix;
    }

    public String getPrefix() {
        return prefix;
    }


}
