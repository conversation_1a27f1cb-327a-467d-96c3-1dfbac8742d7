package com.gumtree.util.cache.memcached;

import com.gumtree.common.util.cache.remote.GumtreeCacheable;
import com.gumtree.util.cache.CacheNamespace;
import com.gumtree.util.cache.TypedCacheService;
import com.gumtree.util.cache.TypedCacheValueFactory;
import net.spy.memcached.CASValue;

import java.util.Date;

/**
 * User: rajisingh
 * Date: 19/04/11
 * Time: 16:55
 *
 * @param <T> the type of the object that this service caches
 */
public class TypedMemcachedCacheService<K extends GumtreeCacheable,T> extends
        AbstractMemcachedCacheService<T> implements TypedCacheService<K,T>{

    // TODO: Use async memcached calls and handle failure scenarios

    private TypedCacheValueFactory<K,T> valueFactory;

    /**
     * Set the value factory for this service to generate values with
     *
     * @param factory the factory to use
     */
    public final void setValueFactory(TypedCacheValueFactory<K, T> factory) {
        valueFactory = factory;
    }

    /**
     * Get the value for the specified key.  If the cache has a fresh value, use that.
     * If the cache has a non-stale value that needs refreshing, acquire a lock and refresh it
     * or if the lock fails, use the old value.  If the cache is completely stale synchronously
     * get a new value
     *
     * @param namespace must not be null
     * @param key the key for the desired value
     * @return the value for the specified key
     */
    @Override
    public final T get(CacheNamespace namespace, K key) {
        CASValue<T> casValue = getFromCache(namespace, key.getCacheKey());

        if (casValue == null) {
            // TODO: Negotiate a single updater for initialisation as well, with more limited timeouts
            // TODO: to prevent clients from blocking for too long
            // TODO: This is to prevent update storms at initialisation
            LOGGER.debug("No cache value available, fetching a new one");
            return updateEntry(namespace, key).getValue();
        }

        // TODO: Handle incompatible data exceptions
        MemcachedEntry<T> cacheEntry = (MemcachedEntry<T>) casValue.getValue();

        if (cacheEntry == null) {
            cacheEntry = new MemcachedEntry<T>(key.getCacheKey());
        }

        if (needsUpdating(cacheEntry)) {
            if (becomeEntryUpdater(namespace, casValue)) {
                LOGGER.debug("Acquired update 'lock', updating entry: " + cacheEntry.getKey());
                return updateEntry(namespace, key).getValue();
            }

            LOGGER.debug("Couldn't acquire update 'lock', using old value: " + cacheEntry.getKey());
        }

        // Fall back on return the existing value
        return cacheEntry.getValue();
    }

    private MemcachedEntry<T> updateEntry(CacheNamespace namespace, K key) {
        MemcachedEntry<T> cacheEntry = new MemcachedEntry(key.getCacheKey());
        cacheEntry.setValue(valueFactory.create(key));
        cacheEntry.setUpdated(new Date());
        memcachedClient.set(makeCacheKey(namespace, cacheEntry.getKey()), cacheExpiry, cacheEntry);

        return cacheEntry;
    }

}
