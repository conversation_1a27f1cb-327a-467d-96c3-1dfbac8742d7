package com.gumtree.util.cache.memcached;

import com.gumtree.util.cache.CacheNamespace;
import net.spy.memcached.CASResponse;
import net.spy.memcached.CASValue;
import net.spy.memcached.MemcachedClientIF;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Created with IntelliJ IDEA.
 * User: asalvadore
 * Date: 6/17/13
 * Time: 6:38 PM
 * To change this template use File | Settings | File Templates.
 */
public class AbstractMemcachedCacheService<T> {
    private static final int SHUTDOWN_TIMEOUT = 10; // Seconds
    protected static final Logger LOGGER = LoggerFactory.getLogger(MemcachedCacheService.class);
    protected MemcachedClientIF memcachedClient;
    protected int cacheExpiry;
    private int updaterExpiry;
    private int refreshExpiry;
    protected String projectVersion;

    /**
     * Set the memcached client for this service
     *
     * @param client the new client to use
     */
    public final void setMemcachedClient(MemcachedClientIF client) {
        memcachedClient = client;
    }

    protected String makeCacheKey(CacheNamespace namespace, String key) {
        return namespace.getPrefix() + ':' + projectVersion + ':' + key;
    }

    @SuppressWarnings("unchecked") protected CASValue<T> getFromCache(CacheNamespace namespace, String key) {
        try {
            return (CASValue<T>) memcachedClient.gets(makeCacheKey(namespace, key));
        } catch (RuntimeException e) {
            LOGGER.debug("Memcached communication error. {}", e.getMessage());
        }
        return null;
    }

    protected boolean needsUpdating(MemcachedEntry<T> entry) {
        if (entry == null) {
            return true;
        }

        Date now = new Date();

        return cacheHasExpired(now, entry)
                || (cacheNeedsToBeRefreshed(now, entry) && newUpdaterNeeded(now, entry));
    }

    private boolean cacheNeedsToBeRefreshed(Date now, MemcachedEntry<T> entry) {
        return entry.getUpdated() == null || hasExpired(entry.getUpdated(), now, refreshExpiry);
    }

    private boolean newUpdaterNeeded(Date now, MemcachedEntry<T> entry) {
        return entry.getUpdaterSetAt() == null || hasExpired(entry.getUpdaterSetAt(), now, updaterExpiry);
    }

    private boolean cacheHasExpired(Date now, MemcachedEntry<T> entry) {
        return entry.getUpdated() == null || hasExpired(entry.getUpdated(), now, cacheExpiry);
    }

    private boolean hasExpired(Date timestamp, Date now, Integer expiry) {
        return timeDifference(timestamp, now) >= expiry;
    }

    private int timeDifference(Date timestamp, Date now) {
        Long difference = (now.getTime() - timestamp.getTime()) / 1000;
        return difference.intValue();
    }

    protected boolean becomeEntryUpdater(CacheNamespace namespace, CASValue<T> casValue) {
        MemcachedEntry<T> cacheEntry = (MemcachedEntry<T>) casValue.getValue();

        if (cacheEntry == null) {
            return true; // No existing value, so may as well get one
        }

        String me = UUID.randomUUID().toString();
        cacheEntry.setUpdater(me);

        CASResponse response = memcachedClient.cas(
                makeCacheKey(namespace, cacheEntry.getKey()), casValue.getCas(), cacheEntry);

        if (response == null || response.equals(CASResponse.EXISTS)) {
            // EXISTS means someone else got the lock
            // null probably means we failed the memcached call, or something like that
            return false;
        } else if (response.equals(CASResponse.OK)) {
            return true;
        } else { // response.equals(CASResponse.NOT_FOUND)
            // Unusual response, given that we VERY recently checked and had found an entry!
            // If it's not found, we need to set it
            // In this context a few clients may end up simultaneously becoming the updater
            LOGGER.warn("Memcached entry NOT FOUND when trying to become updater: " + cacheEntry.getKey());
            return true;
        }
    }

    /**
     * Call this to indicate that the service is no longer needed.  Usually called at shutdown
     * to insure memcachedClient's dedicated IO thread gets cleaned up suitably
     */
    public final void finish() {
        // TODO: Set IO thread to be a daemon thread so JVM doesn't wait for it
        memcachedClient.shutdown(SHUTDOWN_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * Set the time after which a value should be considered fully expired
     *
     * @param cacheExpiry the new cache expiry in seconds
     */
    public final void setCacheExpiry(int cacheExpiry) {
        this.cacheExpiry = cacheExpiry;
    }

    /**
     * Set the time period after which an updater lock should be considered invalid and we
     * should try to grab a new one
     *
     * @param updaterExpiry the new update expiry in seconds
     */
    public final void setUpdaterExpiry(int updaterExpiry) {
        this.updaterExpiry = updaterExpiry;
    }

    /**
     * Set the time period after which we should try to refresh a value but can still
     * use the old value
     *
     * @param refreshExpiry the new update expiry in seconds
     */
    public final void setRefreshExpiry(int refreshExpiry) {
        this.refreshExpiry = refreshExpiry;
    }

    /**
     * Set the project version for this service, which is used to create version-specific cache keys
     *
     * @param projectVersion the project version
     */
    public final void setProjectVersion(String projectVersion) {
        this.projectVersion = projectVersion;
    }
}
