package com.gumtree.util.cache;

import com.gumtree.common.util.cache.remote.GumtreeCacheable;

/**
 * User: <PERSON><PERSON>
 * Date: 22/04/2011
 * Time: 19:13
 */
public interface CacheServiceFactory {
    /**
     * Return a new CacheService for the specified valueFactory
     *
     * @param valueFactory the valuefactory to use for generating cache values
     * @param <T> the type to be stored in the cache
     * @return the new cache service
     */
    <T> CacheService<T> getService(CacheValueFactory<T> valueFactory);

    <K extends GumtreeCacheable, T> TypedCacheService<K, T> getService(TypedCacheValueFactory<K, T> valueFactory);
}
