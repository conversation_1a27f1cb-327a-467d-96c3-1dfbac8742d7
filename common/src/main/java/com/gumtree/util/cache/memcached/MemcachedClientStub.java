package com.gumtree.util.cache.memcached;

import net.spy.memcached.CASResponse;
import net.spy.memcached.CASValue;
import net.spy.memcached.MemcachedClientIF;
import net.spy.memcached.internal.BulkFuture;
import net.spy.memcached.transcoders.Transcoder;

import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;

/**
 * User: rajisingh
 * Date: 21/04/11
 * Time: 07:47
 */
public class MemcachedClientStub implements MemcachedClientIF {
    /**
     * {@inheritDoc}
     */
    public final java.util.Collection<java.net.SocketAddress> getAvailableServers() {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.Collection<java.net.SocketAddress> getUnavailableServers() {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final net.spy.memcached.transcoders.Transcoder<java.lang.Object> getTranscoder() {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final net.spy.memcached.NodeLocator getNodeLocator() {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Boolean> append(
            long l, java.lang.String s, java.lang.Object o) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> java.util.concurrent.Future<java.lang.Boolean> append(
            long l, java.lang.String s, T t, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Boolean> prepend(
            long l, java.lang.String s, java.lang.Object o) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> java.util.concurrent.Future<java.lang.Boolean> prepend(
            long l, java.lang.String s, T t, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> java.util.concurrent.Future<net.spy.memcached.CASResponse> asyncCAS(
            java.lang.String s, long l, T t, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<net.spy.memcached.CASResponse> asyncCAS(
            java.lang.String s, long l, java.lang.Object o) {
        return null;
    }

    @Override
    public <T> CASResponse cas(String s, long l, int i, T t, Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> net.spy.memcached.CASResponse cas(
            java.lang.String s, long l, T t, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final net.spy.memcached.CASResponse cas(
            java.lang.String s, long l, java.lang.Object o) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> java.util.concurrent.Future<java.lang.Boolean> add(
            java.lang.String s, int i, T t, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Boolean> add(
            java.lang.String s, int i, java.lang.Object o) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> java.util.concurrent.Future<java.lang.Boolean> set(
            java.lang.String s, int i, T t, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Boolean> set(
            java.lang.String s, int i, java.lang.Object o) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> java.util.concurrent.Future<java.lang.Boolean> replace(
            java.lang.String s, int i, T t, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Boolean> replace(
            java.lang.String s, int i, java.lang.Object o) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> java.util.concurrent.Future<T> asyncGet(
            java.lang.String s, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Object> asyncGet(java.lang.String s) {
        return null;
    }

    @Override
    public Future<CASValue<Object>> asyncGetAndTouch(String s, int i) {
        return null;
    }

    @Override
    public <T> Future<CASValue<T>> asyncGetAndTouch(String s, int i, Transcoder<T> tTranscoder) {
        return null;
    }

    @Override
    public CASValue<Object> getAndTouch(String s, int i) {
        return null;
    }

    @Override
    public <T> CASValue<T> getAndTouch(String s, int i, Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> java.util.concurrent.Future<net.spy.memcached.CASValue<T>> asyncGets(
            java.lang.String s, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<net.spy.memcached.CASValue<java.lang.Object>> asyncGets(
            java.lang.String s) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> net.spy.memcached.CASValue<T> gets(
            java.lang.String s, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final net.spy.memcached.CASValue<java.lang.Object> gets(java.lang.String s) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> T get(java.lang.String s, net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.lang.Object get(java.lang.String s) {
        return null;
    }

    @Override
    public <T> BulkFuture<Map<String, T>> asyncGetBulk(Iterator<String> stringIterator, Iterator<Transcoder<T>> transcoderIterator) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> BulkFuture<java.util.Map<java.lang.String, T>> asyncGetBulk(
            java.util.Collection<java.lang.String> strings,
            net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    @Override
    public BulkFuture<Map<String, Object>> asyncGetBulk(Iterator<String> stringIterator) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final BulkFuture<java.util.Map<java.lang.String, java.lang.Object>> asyncGetBulk(
            java.util.Collection<java.lang.String> strings) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> BulkFuture<java.util.Map<java.lang.String, T>> asyncGetBulk(
            net.spy.memcached.transcoders.Transcoder<T> tTranscoder, java.lang.String... strings) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final BulkFuture<java.util.Map<java.lang.String, java.lang.Object>> asyncGetBulk(
            java.lang.String... strings) {
        return null;
    }

    @Override
    public <T> Map<String, T> getBulk(Iterator<String> stringIterator, Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> java.util.Map<java.lang.String, T> getBulk(
            java.util.Collection<java.lang.String> strings,
            net.spy.memcached.transcoders.Transcoder<T> tTranscoder) {
        return null;
    }

    @Override
    public Map<String, Object> getBulk(Iterator<String> stringIterator) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.Map<java.lang.String, java.lang.Object> getBulk(
            java.util.Collection<java.lang.String> strings) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> java.util.Map<java.lang.String, T> getBulk(
            net.spy.memcached.transcoders.Transcoder<T> tTranscoder, java.lang.String... strings) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.Map<java.lang.String, java.lang.Object> getBulk(
            java.lang.String... strings) {
        return null;
    }

    @Override
    public <T> Future<Boolean> touch(String s, int i, Transcoder<T> tTranscoder) {
        return null;
    }

    @Override
    public <T> Future<Boolean> touch(String s, int i) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.Map<java.net.SocketAddress, java.lang.String> getVersions() {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.Map<java.net.SocketAddress, java.util.Map<java.lang.String, java.lang.String>> getStats() {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.Map<java.net.SocketAddress, java.util.Map<java.lang.String, java.lang.String>> getStats(
            java.lang.String s) {
        return null;
    }

    @Override
    public long incr(String s, long l) {
        return 0;
    }

    /**
     * {@inheritDoc}
     */
    public final long incr(java.lang.String s, int i) {
        return 0L;
    }

    @Override
    public long decr(String s, long l) {
        return 0;
    }

    /**
     * {@inheritDoc}
     */
    public final long decr(java.lang.String s, int i) {
        return 0L;
    }

    @Override
    public long incr(String s, long l, long l2, int i) {
        return 0;
    }

    /**
     * {@inheritDoc}
     */
    public final long incr(java.lang.String s, int i, long l, int i1) {
        return 0L;
    }

    @Override
    public long decr(String s, long l, long l2, int i) {
        return 0;
    }

    /**
     * {@inheritDoc}
     */
    public final long decr(java.lang.String s, int i, long l, int i1) {
        return 0L;
    }

    @Override
    public Future<Long> asyncIncr(String s, long l) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Long> asyncIncr(java.lang.String s, int i) {
        return null;
    }

    @Override
    public Future<Long> asyncDecr(String s, long l) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Long> asyncDecr(java.lang.String s, int i) {
        return null;
    }

    @Override
    public long incr(String s, long l, long l2) {
        return 0;
    }

    /**
     * {@inheritDoc}
     */
    public final long incr(java.lang.String s, int i, long l) {
        return 0L;
    }

    @Override
    public long decr(String s, long l, long l2) {
        return 0;
    }

    /**
     * {@inheritDoc}
     */
    public final long decr(java.lang.String s, int i, long l) {
        return 0L;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Boolean> delete(java.lang.String s) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Boolean> flush(int i) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final java.util.concurrent.Future<java.lang.Boolean> flush() {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final void shutdown() {
    }

    /**
     * {@inheritDoc}
     */
    public final boolean shutdown(long l, java.util.concurrent.TimeUnit timeUnit) {
        return true;
    }

    /**
     * {@inheritDoc}
     */
    public final boolean waitForQueues(long l, java.util.concurrent.TimeUnit timeUnit) {
        return true;
    }

    /**
     * {@inheritDoc}
     */
    public final boolean addObserver(net.spy.memcached.ConnectionObserver connectionObserver) {
        return true;
    }

    /**
     * {@inheritDoc}
     */
    public final boolean removeObserver(net.spy.memcached.ConnectionObserver connectionObserver) {
        return true;
    }

    /**
     * {@inheritDoc}
     */
    public final <T> BulkFuture<Map<String, T>> asyncGetBulk(
            Collection<String> strings, Iterator<Transcoder<T>> transcoderIterator) {
        return null;
    }

    @Override
    public <T> BulkFuture<Map<String, T>> asyncGetBulk(Iterator<String> stringIterator, Transcoder<T> tTranscoder) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    public final Set<String> listSaslMechanisms() {
        return null;
    }
}
