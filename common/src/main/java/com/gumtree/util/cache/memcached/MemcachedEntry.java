package com.gumtree.util.cache.memcached;

import java.io.Serializable;
import java.util.Date;

/**
 * User: rajisingh
 * Date: 19/04/11
 * Time: 17:23
 *
 * @param <T> the type of the object that this cacheEntry represents
 */
public class MemcachedEntry<T> implements Serializable {
    private T value;
    private String key;
    private String updater;

    private Date updaterSetAt;
    private Date updated;

    /**
     * Construct a new MemcachedEntry object
     *
     * @param key the key this object is for
     */
    public MemcachedEntry(String key) {
        this.key = key;
    }

    /**
     * Get the value for this cache entry
     *
     * @return the value
     */
    public final T getValue() {
        return value;
    }

    /**
     * Get the key for this cache entry
     *
     * @return the key
     */
    public final String getKey() {
        return key;
    }

    /**
     * Get the updated timestamp for this cache entry
     *
     * @return the updated timestamp
     */
    public final Date getUpdated() {
        return updated;
    }

    /**
     * Get the timestamp the updater was set at
     *
     * @return the updater timestamp
     */
    public final Date getUpdaterSetAt() {
        return updaterSetAt;
    }

    /**
     * Set a new updater
     *
     * @param updater the updater
     */
    public final void setUpdater(String updater) {
        this.updater = updater;
        this.updaterSetAt = new Date();
    }

    /**
     * Set a new value
     *
     * @param value the value to set
     */
    public final void setValue(T value) {
        this.value = value;
    }

    /**
     * Set the updated timestamp
     *
     * @param updated the timestamp
     */
    public final void setUpdated(Date updated) {
        this.updated = updated;
    }
}
