package com.gumtree.util;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

public abstract class GtCollectors {
    private GtCollectors() {
    }

    /**
     * Collector which create a {@link java.util.LinkedHashMap}. Useful if you are converting from a list to a map and want to preserve
     * order of items.
     *
     * @param keyMapper
     * @param valueMapper
     *
     * @param <T>
     * @param <K>
     * @param <U>
     *
     * @return {@link java.util.LinkedHashMap}
     */
    public static <T, K, U> Collector<T, ?, Map<K,U>> toLinkedMap(Function<? super T, ? extends K> keyMapper,
                                                                  Function<? super T, ? extends U> valueMapper) {
        return Collectors.toMap(keyMapper, valueMapper,
                (u, v) -> {
                    throw new IllegalStateException(String.format("Duplicate key %s", u));
                },
                LinkedHashMap::new);
    }
}
