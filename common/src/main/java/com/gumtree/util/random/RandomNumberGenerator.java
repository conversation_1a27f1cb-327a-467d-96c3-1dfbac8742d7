package com.gumtree.util.random;

import org.springframework.stereotype.Component;

import java.util.Random;

@Component
public class RandomNumberGenerator {

    private Random random;

    public RandomNumberGenerator() {
        random = new Random(System.currentTimeMillis());
    }

    public int positiveIntegerUpToExcluding(int upToExcluding) {
        return random.nextInt(upToExcluding);
    }

}
