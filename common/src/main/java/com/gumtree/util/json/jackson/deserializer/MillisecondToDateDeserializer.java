package com.gumtree.util.json.jackson.deserializer;

import org.codehaus.jackson.JsonParser;
import org.codehaus.jackson.map.DeserializationContext;
import org.codehaus.jackson.map.JsonDeserializer;

import java.io.IOException;
import java.util.Date;

/**
 * Converts a long value, representing milliseconds, into a {@link Date}
 */
public class MillisecondToDateDeserializer extends JsonDeserializer<Date> {

    @Override
    public final Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
            throws IOException {
        return new Date(jsonParser.getLongValue());
    }
}
