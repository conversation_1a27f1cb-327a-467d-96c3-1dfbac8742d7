package com.gumtree.util.json.jackson.deserializer;

import org.codehaus.jackson.JsonParser;
import org.codehaus.jackson.map.DeserializationContext;
import org.codehaus.jackson.map.JsonDeserializer;

import java.io.IOException;
import java.util.Currency;

/**
 * Converts a string value, representing a currency code, into a {@link Currency} object
 */
public class StringToCurrencyDeserializer extends JsonDeserializer<Currency> {

    @Override
    public final Currency deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
            throws IOException {
        return Currency.getInstance(jsonParser.getText());
    }
}
