package com.gumtree.util.http;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public abstract class HttpRequestUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpRequestUtils.class);

    public static final String REQUEST_WITH_HEADER = "X-Requested-With";
    private static final String UTM_PARAM_PREFIX = "utm";

    private HttpRequestUtils() {
    }

    public static boolean isAjaxRequest(ServletRequest request) {
        if (request instanceof HttpServletRequest) {
            String header = ((HttpServletRequest) request).getHeader(REQUEST_WITH_HEADER);
            return "XMLHttpRequest".equals(header);
        }

        return false;
    }

    public static String getHeader(String name, HttpServletRequest request) {
        return request.getHeader(name);
    }

    public static String filterUtmParamsToQueryString(Map<String, String[]> parameterMap) {
        String utmParams = parameterMap.entrySet()
                .stream()
                .filter(param -> param.getKey().startsWith(UTM_PARAM_PREFIX))
                .map(param -> encode(param.getKey(), StandardCharsets.UTF_8) + "=" +
                        encode(Arrays.stream(param.getValue()).findFirst().orElse(""), StandardCharsets.UTF_8))
                .collect(Collectors.joining("&"));

        if (StringUtils.isNotBlank(utmParams)) {
            return "?" + utmParams;
        } else {
            return "";
        }
    }

    public static String encode(String queryParam, Charset standardCharset) {
        try {
            return URLEncoder.encode(queryParam, standardCharset.toString());
        } catch (UnsupportedEncodingException e) {
            LOGGER.warn("Couldn't encode parameters : {}", queryParam, e);
            return "";
        }
    }
}

