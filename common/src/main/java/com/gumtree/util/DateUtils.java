package com.gumtree.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 */
public final class DateUtils {

    private DateUtils() {
    }
    /**
     * Convert string into date.
     *
     * @param date   the date string
     * @param format the format of the string
     * @return the converted date
     */
    public static Date convertDateString(String date, String format) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            return simpleDateFormat.parse(date);
        } catch (ParseException pex) {
            return null;
        }
    }
}
