package com.gumtree.util;

import com.gumtree.util.mime.AcceptedMime;
import com.gumtree.util.mime.WrongMimeTypeException;
import org.apache.tika.Tika;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Optional;

public final class MimeTypeResolver {

    private MimeTypeResolver() {
    }

    public static String resolveCVContentType(MultipartFile file) throws IOException, WrongMimeTypeException {
        Tika tika = new Tika();
        String contentType = tika.detect(file.getInputStream(), file.getOriginalFilename());
        Optional<AcceptedMime> acceptedMime = AcceptedMime.fromContent(contentType);

        if (!acceptedMime.isPresent()){
            throw new WrongMimeTypeException("The CV should be a word document, a pdf or a rich text file");
        }

        return acceptedMime.get().getContentType();
    }
}
