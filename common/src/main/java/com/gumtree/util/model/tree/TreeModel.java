package com.gumtree.util.model.tree;

import com.gumtree.domain.Identifiable;

import java.util.List;

/**
 * Defines the model for a tree hierarchy. Supports the concept of multiple
 * root nodes.
 *
 * @param <T> the model type for the nodes in the tree.
 */
public interface TreeModel<T extends Identifiable> {

    /**
     * Get the root nodes of the tree model.
     *
     * @return the root nodes of the tree model.
     */
    List<TreeNode<T>> getRoots();
}
