package com.gumtree.util.model.tree;

import com.gumtree.domain.Identifiable;

import java.util.ArrayList;
import java.util.List;

/**
 * A simple implementation of {@link TreeNode}
 *
 * @param <T> the model type
 */
public class DefaultTreeNode<T extends Identifiable> implements TreeNode<T> {

    private T model;

    private String id;

    private TreeNode<T> parent;

    private List<TreeNode<T>> children = new ArrayList<TreeNode<T>>();

    /**
     * Constructor.
     *
     * @param parent this node's parent
     * @param model  the model object backing this node
     */
    public DefaultTreeNode(TreeNode<T> parent, T model) {
        this(null, parent, model);
    }

    /**
     * Constructor.
     *
     * @param parent this node's parent
     * @param model  the model object backing this node
     * @param id     the id of the node
     */
    public DefaultTreeNode(String id, TreeNode<T> parent, T model) {
        this.id = id;
        this.parent = parent;
        this.model = model;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final String getId() {
        return id;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final T getModel() {
        return model;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final TreeNode<T> getParent() {
        return parent;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final List<TreeNode<T>> getChildren() {
        return children;
    }

    /**
     * Adds a new child to this node
     *
     * @param child The new child
     */
    public final void addChild(TreeNode<T> child) {
        if (!children.contains(child)) {
            children.add(child);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DefaultTreeNode)) {
            return false;
        }

        DefaultTreeNode that = (DefaultTreeNode) o;

        if (model != null ? !model.equals(that.model) : that.model != null) {
            return false;
        }

        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final int hashCode() {
        return model != null ? model.hashCode() : super.hashCode();
    }
}
