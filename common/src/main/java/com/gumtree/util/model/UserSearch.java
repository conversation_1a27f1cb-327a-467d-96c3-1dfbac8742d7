package com.gumtree.util.model;

import com.google.common.base.Optional;
import com.gumtree.api.category.domain.Category;
import com.gumtree.common.util.cache.remote.GumtreeCacheable;
import com.gumtree.search.UserGeoLocation;
import com.gumtree.search.UserSearchKeywords;
import com.gumtree.search.UserSearchLocation;
import com.gumtree.search.UserSearchRefinement;
import com.gumtree.util.helper.DisplayAdsViewMode;
import org.springframework.util.Assert;

import java.util.Collections;

/**
 *
 */
public final class UserSearch implements GumtreeCacheable {

    private final Category category;
    private final UserSearchLocation locationSearch;
    private final Optional<UserGeoLocation> geoLocationSearch;
    private final UserSearchKeywords keywordsSearch;
    private final UserSearchRefinement refinement;
    private DisplayAdsViewMode displayAdsViewMode;
    private Optional<Long> lastModifiedDateLimit;

    private UserSearch(Builder builder) {
        this.locationSearch = builder.locationSearch;
        this.category = builder.category;
        this.keywordsSearch = builder.keywordsSearch;
        this.refinement = builder.refinement;
        this.displayAdsViewMode = builder.displayAdsViewMode;
        this.lastModifiedDateLimit = builder.lastModifiedDateLimit;
        this.geoLocationSearch = builder.geoLocationSearch;
    }

    public UserSearchLocation getLocationSearch() {
        return locationSearch;
    }

    /**
     * @return the category
     */
    public Category getCategory() {
        return category;
    }

    public UserSearchKeywords getUserSearchKeywords() {
        return keywordsSearch;
    }

    public UserSearchRefinement getRefinement() {
        return refinement;
    }

    public DisplayAdsViewMode getDisplayAdsViewMode() {
        return displayAdsViewMode;
    }

    public Optional<Long> getCategoryId() {
        if (getCategory() == null) {
            return Optional.absent();
        } else {
            return Optional.fromNullable(getCategory().getId());
        }
    }

    public Optional<Integer> getLocationId() {
        if (getLocationSearch() == null || getLocationSearch().getLocation() == null) {
            return Optional.absent();
        } else {
            return Optional.fromNullable(getLocationSearch().getLocation().getId());
        }
    }

    public Optional<String> getOutcodeSearch() {
        if (getLocationSearch() != null
                && getLocationSearch().getZipOutCodes() != null
                && getLocationSearch().getZipOutCodes().size() > 0) {
            return Optional.of(getLocationSearch().getZipOutCodes().get(0));
        } else {
            return Optional.absent();
        }
    }

    public Optional<UserGeoLocation> getGeoLocationSearch() {
        return geoLocationSearch;
    }

    public boolean isPostcodeSearch() {
        return geoLocationSearch.isPresent();
    }

    /* (non-Javadoc)
    * @see java.lang.Object#toString()
    */
    @Override
    public String toString() {
        return getCacheKey();
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */

    @Override
    public int hashCode() {
        int result = category != null ? category.hashCode() : 0;
        result = 31 * result + (locationSearch != null ? locationSearch.hashCode() : 0);
        result = 31 * result + (geoLocationSearch != null ? geoLocationSearch.hashCode() : 0);
        result = 31 * result + (keywordsSearch != null ? keywordsSearch.hashCode() : 0);
        result = 31 * result + (refinement != null ? refinement.hashCode() : 0);
        result = 31 * result + (displayAdsViewMode != null ? displayAdsViewMode.hashCode() : 0);
        result = 31 * result + (lastModifiedDateLimit != null ? lastModifiedDateLimit.hashCode() : 0);
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        UserSearch that = (UserSearch) o;

        if (category != null ? !category.equals(that.category) : that.category != null) {
            return false;
        }
        if (keywordsSearch != null ? !keywordsSearch.equals(that.keywordsSearch) : that.keywordsSearch != null) {
            return false;
        }
        if (locationSearch != null ? !locationSearch.equals(that.locationSearch) : that.locationSearch != null) {
            return false;
        }
        if (geoLocationSearch != null
                ? !geoLocationSearch.equals(that.geoLocationSearch) : that.geoLocationSearch != null) {
            return false;
        }
        if (refinement != null ? !refinement.equals(that.refinement) : that.refinement != null) {
            return false;
        }
        if (lastModifiedDateLimit != null
                ? !lastModifiedDateLimit.equals(that.lastModifiedDateLimit)
                : that.lastModifiedDateLimit != null) {
            return false;
        }
        if (displayAdsViewMode != null
                ? !displayAdsViewMode.equals(that.displayAdsViewMode) : that.displayAdsViewMode != null) {
            return false;
        }

        return true;
    }

    public Optional<Long> getLastModifiedDateLimit() {
        return lastModifiedDateLimit;
    }

    /**
     * Builder for creating new instances.
     *
     * <AUTHOR>
     */
    public static final class Builder {
        private UserSearchLocation locationSearch = UserSearchLocation.EMPTY;
        private UserSearchKeywords keywordsSearch = UserSearchKeywords.EMPTY;
        private Category category;
        private UserSearchRefinement refinement = UserSearchRefinement.EMPTY;
        private DisplayAdsViewMode displayAdsViewMode = DisplayAdsViewMode.TEXTLINK_ACTIVE;
        private Optional<Long> lastModifiedDateLimit = Optional.absent();
        private Optional<UserGeoLocation> geoLocationSearch = Optional.absent();

        /**
         * Construct a new instance.
         */
        public Builder() {
            // nothing to do
        }

        /**
         * Construct a copy.
         *
         * @param toCopy example
         */
        public Builder(UserSearch toCopy) {
            this.geoLocationSearch = toCopy.geoLocationSearch;
            this.locationSearch = toCopy.locationSearch;
            this.keywordsSearch = toCopy.keywordsSearch;
            this.category = toCopy.category;
            this.refinement = toCopy.refinement;
            this.displayAdsViewMode = toCopy.displayAdsViewMode;
            this.lastModifiedDateLimit = toCopy.lastModifiedDateLimit;
        }

        /**
         * @param value to set
         * @return same builder instance
         */
        public Builder category(Category value) {
            this.category = value;
            return this;
        }

        /**
         * @param value to set
         * @return same builder instance
         */
        public Builder keywordsSearch(UserSearchKeywords value) {
            this.keywordsSearch = value;
            return this;
        }

        /**
         * @param value to set
         * @return same builder instance
         */
        public Builder locationSearch(UserSearchLocation value) {
            if (!this.geoLocationSearch.isPresent()) {
                this.locationSearch = value;
            }
            return this;
        }

        /**
         * @param value to set
         * @return same builder instance
         */
        public Builder geoLocationSearch(Optional<UserGeoLocation> value) {
            Assert.notNull(value);
            this.geoLocationSearch = value;
            if (this.geoLocationSearch.isPresent()) {
                this.locationSearch = new UserSearchLocation(
                        geoLocationSearch.get().getLocation(),
                        Collections.<String>emptyList(),
                        Collections.<String>emptyList(),
                        geoLocationSearch.get().getPostcode());
            }

            return this;
        }

        /**
         * @param value to set
         * @return same instance
         */
        public Builder refinement(UserSearchRefinement value) {
            this.refinement = value;
            return this;
        }

        /**
         * @param value to set
         * @return same instance
         */
        public Builder displayAdsViewMode(DisplayAdsViewMode value) {
            this.displayAdsViewMode = value;
            return this;
        }

        public Builder modifiedSince(Long value) {
            this.lastModifiedDateLimit = Optional.fromNullable(value);
            return this;
        }

        /**
         * @return a new instance
         */
        public UserSearch build() {
            return new UserSearch(this);
        }
    }

    @Override
    public String getCacheKey() {
        return "UserSearch [category=" + category + ", locationSearch="
                + locationSearch + ", keywordsSearch=" + keywordsSearch + ", lastModifiedDateLimit="
                + lastModifiedDateLimit + ", refinement=" + refinement + ", displayAdsViewMode=" + displayAdsViewMode
                + ", geoLocationSearch=" + geoLocationSearch + "]";
    }

}
