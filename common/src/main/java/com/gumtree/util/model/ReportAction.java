package com.gumtree.util.model;

import com.gumtree.domain.advert.Advert;
import com.gumtree.util.url.UrlScheme;

/**
 * Custom ReportAction that builds a report-an-ad URL
 */
public class ReportAction implements Action {

    private ApplicationEndpoint endpoint = Actions.ApplicationEndpoint.BUYER;
    private UrlScheme urlScheme;
    private Advert advert;

    /**
     * Constructor a report-action
     *
     * @param urlScheme urlScheme to use to build advert URL
     * @param advert    advert to build URL for
     */
    public ReportAction(UrlScheme urlScheme, Advert advert) {
        this.urlScheme = urlScheme;
        this.advert = advert;
    }

    @Override
    public final String getUrl() {
        return urlScheme.urlFor(advert) + "/report";
    }

    @Override
    public final ApplicationEndpoint getEndpoint() {
        return endpoint;
    }

    @Override
    public final boolean isUrlAbsolute() {
        return true;
    }

    @Override
    public final boolean isMailLink() {
        return false;
    }
}
