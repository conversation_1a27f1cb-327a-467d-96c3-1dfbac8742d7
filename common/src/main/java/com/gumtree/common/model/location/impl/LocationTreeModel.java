package com.gumtree.common.model.location.impl;

import com.gumtree.domain.location.Location;
import com.gumtree.common.model.location.LocationModel;
import com.gumtree.util.model.tree.DefaultTreeNode;
import com.gumtree.util.model.tree.TreeModel;
import com.gumtree.util.model.tree.TreeNode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * A model that represents a tree of locations and can be built from {@link com.gumtree.domain.location.Location}
 * objects
 *
 * <AUTHOR> Hall
 */
public final class LocationTreeModel implements TreeModel<Location> {

    /**
     * A {@link ThreadLocal} that is used to hold the model while it is being built
     */
    private static ThreadLocal<LocationTreeModel> threadLocalModel = new ThreadLocal<LocationTreeModel>();

    /**
     * A {@link ThreadLocal} that is used to store a cache of previously created nodes so as not to duplicate routes
     */
    private static ThreadLocal<Map<Location, DefaultTreeNode<Location>>> threadLocalNodeCache =
            new ThreadLocal<Map<Location, DefaultTreeNode<Location>>>();

    /**
     * A root node used to represent the root of the tree
     */
    private DefaultTreeNode<Location> hiddenRoot =
            new DefaultTreeNode<Location>(null, null);

    /**
     * Private constructor preventing direct instantiation.
     */
    private LocationTreeModel() {
    }

    /**
     * Obtains a list of root nodes from the model
     *
     * @return the root nodes
     */
    public List<TreeNode<Location>> getRoots() {
        return hiddenRoot.getChildren();
    }

    /**
     * Builds a new {@link LocationTreeModel} from the specified location.
     * <p/>
     * The tree uses the zoom in, zoom out and near by relationships to create an effective
     * tree for displaying the specified location on a web page.
     *
     * @param locationModel   the source of location data
     * @param currentLocation The location to build the tree from
     * @return The build tree model
     */
    public static LocationTreeModel buildModel(LocationModel locationModel, Location currentLocation) {

        //Create the empty linkTreeModel and store it in the thread local
        LocationTreeModel locationTreeModel = new LocationTreeModel();
        threadLocalModel.set(locationTreeModel);

        //Reset the cache
        threadLocalNodeCache.set(new HashMap<Location, DefaultTreeNode<Location>>());

        //Build the first node, used to represent the current location
        DefaultTreeNode<Location> currentLocationNode = getNode(locationModel, currentLocation);

        //Build the upwards tree, resolving all the roots
        resolveRoots(locationModel, currentLocation);

        //Zoom in nodes should be added as children of the current node
        for (Location zoomInLocation : locationModel.getZoomIn(currentLocation)) {
            currentLocationNode.addChild(new DefaultTreeNode<Location>(currentLocationNode, zoomInLocation));
        }

        //Near by nodes are added as siblings of the current location
        //so we need to discover the node that will act as parent to the current
        //This could be the root or it could be the first zoom out
        DefaultTreeNode<Location> nearByRoot = getParent(locationModel, currentLocation);

        //Add the near by nodes
        for (Location nearByLocation : locationModel.getNearby(currentLocation)) {
            nearByRoot.addChild(new DefaultTreeNode<Location>(nearByRoot, nearByLocation));
        }

        //Return the linkTreeModel
        return locationTreeModel;
    }

    /**
     * Obtains the tree node for the specified location, using the cache if possible
     *
     * @param location The location
     * @return The node
     */
    private static DefaultTreeNode<Location> getNode(LocationModel locationModel, Location location) {
        if (threadLocalNodeCache.get().containsKey(location)) {
            return threadLocalNodeCache.get().get(location);
        }

        DefaultTreeNode<Location> node = new DefaultTreeNode<Location>(getParent(locationModel, location), location);
        threadLocalNodeCache.get().put(location, node);
        return node;
    }

    /**
     * Obtains the parent node for any given {@link com.gumtree.domain.location.Location}
     *
     * @param location The location to resolve
     * @return The parent node
     */
    private static DefaultTreeNode<Location> getParent(LocationModel locationModel, Location location) {
        if (locationModel.getZoomOut(location).isEmpty()) {
            return threadLocalModel.get().hiddenRoot;
        } else {
            return getNode(locationModel, new ArrayList<Location>(locationModel.getZoomOut(location)).get(0));
        }
    }

    /**
     * Recursively add roots of the specified location to the tree
     *
     * @param location The starting location
     */
    private static void resolveRoots(LocationModel locationModel, Location location) {

        //Add the location to it's parent node
        getParent(locationModel, location).addChild(getNode(locationModel, location));

        //Iterate all the zoom out locations adding children for those too
        for (Location zoomOut : locationModel.getZoomOut(location)) {
            resolveRoots(locationModel, zoomOut);
        }
    }
}
