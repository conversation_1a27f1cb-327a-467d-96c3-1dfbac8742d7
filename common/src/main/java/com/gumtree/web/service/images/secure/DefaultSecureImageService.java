package com.gumtree.web.service.images.secure;

import java.net.MalformedURLException;
import java.net.URL;

public class DefaultSecureImageService implements SecureImageService {

    private final String secureHost;

    public DefaultSecureImageService(String secureHost) {
        this.secureHost = secureHost;
    }

    @Override
    public String getSecureUrl(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            return secureHost + url.getPath();
        } catch (MalformedURLException e) {
            throw new IllegalArgumentException("Could not parse image url : " + imageUrl, e);
        }
    }
}
