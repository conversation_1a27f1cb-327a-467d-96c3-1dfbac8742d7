package com.gumtree.madgex;

public abstract class MadgexUtils {
    /**
     * Must match value defined in MadgexAdvertToFlatAdConverter in gumtree/madgex-advert-importer.git repository
     *
     * To avoid ID conflicts all Madgex ads IDs are offset by the below value, same applies to accountIds as those are Madgex AcccountIds
     * and not Gumtree's.
     */
    public static final Long MADGEX_ADS_OFFSET = 4000000000L;
    public static final Long MADGEX_ACCOUNT_OFFSET = 4000000000L;

    private MadgexUtils() {
    }

    public static boolean isMadgexAdvert(long advertId) {
        return advertId > MADGEX_ADS_OFFSET;
    }

    public static long getMadgexAdvertId(long advertId) {
        if (advertId > MADGEX_ADS_OFFSET) {
            return advertId - MADGEX_ADS_OFFSET;
        } else {
            throw new IllegalArgumentException(advertId + " is not valid madgex advert id");
        }
    }

    public static long getGumtreeAccountId(long accountId) {
        if (accountId > MADGEX_ACCOUNT_OFFSET) {
           return accountId - MADGEX_ACCOUNT_OFFSET;
        } else {
            return accountId;
        }
    }
}
