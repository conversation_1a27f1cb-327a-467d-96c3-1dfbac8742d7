package com.gumtree.search.sorting;

import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;

import java.util.HashSet;
import java.util.Set;

/**
 * Represents a actual sorting.
 */
public class Sorting {

    public static final Sorting DEFAULT = new Sorting(SortProperty.DATE, false);

    public static final Set<String> CATEGORIES_WITH_SPECIAL_DATE_FOR_SORTING = new HashSet<String>();
    private static final Set<Long> CATEGORY_IDS_WITH_SPECIAL_DATE_FOR_SORTING = new HashSet<Long>();

    static {
        CATEGORIES_WITH_SPECIAL_DATE_FOR_SORTING.add("events-gigs-nightlife");
        CATEGORIES_WITH_SPECIAL_DATE_FOR_SORTING.add("rideshare-car-pooling");
        CATEGORY_IDS_WITH_SPECIAL_DATE_FOR_SORTING.add(CategoryConstants.EVENTS_ID);
        CATEGORY_IDS_WITH_SPECIAL_DATE_FOR_SORTING.add(CategoryConstants.RIDESHARE_ID);
    }

    public static boolean supportsSpecialDateForSorting(Long categoryId) {
        return categoryId != null && CATEGORY_IDS_WITH_SPECIAL_DATE_FOR_SORTING.contains(categoryId);
    }

    /**
     * @param sortProperty to check
     * @param category     to check
     * @return yes or no
     */
    public static boolean supports(SortProperty sortProperty, Category category) {
        if (sortProperty == SortProperty.PRICE) {
            Boolean priceAvailable = category.getPriceAvailable();
            return priceAvailable != null && priceAvailable;
        }

        return true;
    }

    private final SortProperty sortProperty;
    private final boolean ascending;

    /**
     * Construct.
     *
     * @param sortProperty by which to order
     * @param ascending    or not
     */
    public Sorting(SortProperty sortProperty, boolean ascending) {
        this.sortProperty = sortProperty;
        this.ascending = ascending;
    }

    public final SortProperty getSortProperty() {
        return sortProperty;
    }

    public final boolean isAscending() {
        return ascending;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public final int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + (ascending ? 1231 : 1237);
        result = prime * result
                + ((sortProperty == null) ? 0 : sortProperty.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof Sorting)) {
            return false;
        }
        Sorting other = (Sorting) obj;
        if (ascending != other.ascending) {
            return false;
        }
        if (sortProperty != other.sortProperty) {
            return false;
        }
        return true;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public final String toString() {
        return "Sorting [sortProperty=" + sortProperty.name() + ", ascending="
                + ascending + "]";
    }
}
