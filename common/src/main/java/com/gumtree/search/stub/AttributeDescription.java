package com.gumtree.search.stub;

import java.io.Serializable;

import org.codehaus.jackson.map.annotate.JsonDeserialize;

/**
 * For reading from json.
 * <AUTHOR>
 *
 */
public final class AttributeDescription implements Serializable {

    @JsonDeserialize
    private String id;

    @JsonDeserialize
    private String value;

    public String getId() {
        return id;
    }

    public String getValue() {
        return value;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        result = prime * result + ((value == null) ? 0 : value.hashCode());
        return result;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof AttributeDescription)) {
            return false;
        }
        AttributeDescription other = (AttributeDescription) obj;
        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }
        if (value == null) {
            if (other.value != null) {
                return false;
            }
        } else if (!value.equals(other.value)) {
            return false;
        }
        return true;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "AttributeDescription [id=" + id + ", value=" + value + "]";
    }
}
