package com.gumtree.search.stub;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.codehaus.jackson.map.annotate.JsonDeserialize;

import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.advert.AdvertStatus;
import com.gumtree.domain.advert.Price;
import com.gumtree.domain.advert.entity.PriceEntity;
import com.gumtree.domain.feed.Feed;
import com.gumtree.domain.location.LocationCentroid;
import com.gumtree.domain.location.entity.LocationCentroidEntity;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.Video;
import com.gumtree.domain.media.entity.ImageEntity;
import com.gumtree.domain.media.entity.VideoEntity;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.user.User;
import com.gumtree.domain.user.entity.UserEntity;
import com.gumtree.util.json.jackson.deserializer.DateStringToDateDeserializer;

/**
 * For being read from json.
 * <AUTHOR>
 *
 */
public class StubAdvert implements Advert {

    @JsonDeserialize
    private Long id;

    @JsonDeserialize
    private String title;

    @JsonDeserialize
    private String description;

    @JsonDeserialize
    private Integer locationId;

    @JsonDeserialize
    private List<Integer> locationIds;

    @JsonDeserialize
    private Long categoryId;

    @JsonDeserialize
    private String locationText;

    @JsonDeserialize
    private String postcode;

    @JsonDeserialize
    private PriceEntity price;

    @JsonDeserialize
    private ImageEntity mainImage;

    @JsonDeserialize
    private List<AttributeDescription> attributes = new ArrayList<AttributeDescription>();
    private List<Attribute> realAttributes = new ArrayList<Attribute>();

    @JsonDeserialize(using = DateStringToDateDeserializer.class)
    private Date liveDate;

    @JsonDeserialize(using = DateStringToDateDeserializer.class)
    private Date archivedDate;

    @JsonDeserialize
    private UserEntity postingUser;

    @JsonDeserialize
    private boolean urgent = false;

    @JsonDeserialize
    private boolean featured = false;

    @JsonDeserialize
    private boolean spotlighted = false;

    @JsonDeserialize
    private AdvertStatus status;

    @JsonDeserialize
    private List<ImageEntity> images = new ArrayList<ImageEntity>();

    @JsonDeserialize
    private boolean visibleOnMap = false;

    @JsonDeserialize
    private LocationCentroidEntity point;

    @JsonDeserialize
    private List<VideoEntity> videos = new ArrayList<VideoEntity>();

    @JsonDeserialize
    private Feed feed;

    @JsonDeserialize
    private String websiteLink;

    @JsonDeserialize
    private boolean paidFor = false;

    @JsonDeserialize
    private boolean proAccountAd = false;

    @Override
    public final Long getId() {
        return id;
    }

    @Override
    public final String getTitle() {
        return title;
    }

    @Override
    public final String getDescription() {
        return description;
    }

    @Override
    public final Integer getLocationId() {
        return locationId;
    }

    @Override
    public final List<Integer> getLocationIds() {
        return locationIds;
    }

    @Override
    public final Long getCategoryId() {
        return categoryId;
    }

    @Override
    public final String getLocationText() {
        return locationText;
    }

    @Override
    public final String getPostcode() {
        return postcode;
    }

    @Override
    public final Price getPrice() {
        return price;
    }

    @Override
    public final Image getMainImage() {
        return mainImage;
    }
    public final Collection<AttributeDescription> getStubAttributes() {
        return attributes;
    }

    @Override
    public final Collection<Attribute> getAttributes() {
        return realAttributes;
    }

    public final void setRealAttributes(List<Attribute> attributes) {
        this.realAttributes = attributes;
    }

    @Override
    public final Date getLiveDate() {
        return liveDate;
    }

    @Override
    public final Date getArchivedDate() {
        return archivedDate;
    }

    @Override
    public final User getPostingUser() {
        return postingUser;
    }

    @Override
    public final boolean isUrgent() {
        return urgent;
    }

    @Override
    public boolean isSpotlighted() {
        return spotlighted;
    }

    @Override
    public final boolean isFeatured() {
        return featured;
    }

    @Override
    public final String getWebsiteLink() {
        return websiteLink;
    }

    @Override
    public final String toString() {
        return "AdvertEntity{"
                + "id=" + id
                + ", title='" + title + '\''
                + '}';
    }

    @Override
    public final Attribute getAttribute(String attributeType) {
        for (Attribute attribute : realAttributes) {
            if (attribute.getType().equals(attributeType)) {
                return attribute;
            }
        }
        return null;
    }

    @Override
    public final LocationCentroid getPoint() {
        return point;
    }

    @Override
    public final AdvertStatus getStatus() {
        return status;
    }

    @Override
    public final Collection<Image> getImages() {
        return new ArrayList<Image>(images);
    }

    @Override
    public final Collection<Video> getVideos() {
        return new ArrayList<Video>(videos);
    }

    @Override
    public final boolean isVisibleOnMap() {
        return visibleOnMap;
    }

    @Override
    public final Feed getFeed() {
        return feed;
    }

    @Override
    public Boolean isPaidFor() {
        return paidFor;
    }

    @Override
    public Boolean isProAccountAd() {
        return proAccountAd;
    }
}
