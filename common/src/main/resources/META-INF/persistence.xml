<persistence xmlns="http://java.sun.com/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd"
             version="2.0">
    <persistence-unit name="gumtree">
        <class>com.gumtree.api.category.domain.Category</class>
        <class>com.gumtree.domain.location.Location</class>
        <class>com.gumtree.LandingLocation</class>

        <!--<properties>
            <property name="hibernate.cache.provider_class" value="net.sf.ehcache.hibernate.SingletonEhCacheProvider"/>
            <property name="hibernate.cache.use_query_cache" value="true"/>
            <property name="hibernate.cache.use_second_level_cache" value="true"/>
            <property name="hibernate.generate_statistics" value="true"/>
        </properties>-->

    </persistence-unit>
</persistence>