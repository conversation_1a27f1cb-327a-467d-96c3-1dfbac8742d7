package com.gumtree.util.cache;

import java.util.HashSet;
import java.util.Set;

import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class CacheNamespaceTest {
    
    @Test
    public void doesNotContainDuplicateNamespacePrefixes() {
        Set<String> prefixes = new HashSet<String>();
        
        for (CacheNamespace namespace : CacheNamespace.values()) {
            String prefix = namespace.getPrefix();
            assertThat(prefixes.contains(prefix), equalTo(false));
            prefixes.add(prefix);
        }
    }

}
