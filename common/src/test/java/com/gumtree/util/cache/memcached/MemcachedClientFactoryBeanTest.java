package com.gumtree.util.cache.memcached;

import net.spy.memcached.MemcachedClientIF;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

/**
 * User: salamandr
 * Date: 23/04/2011
 * Time: 10:11
 */
public class MemcachedClientFactoryBeanTest {

    @Test
    public void createReturnsStub() throws Exception {
        MemcachedClientFactoryBean factory = new MemcachedClientFactoryBean();
        ReflectionTestUtils.setField(factory, "distributedCache", "stub");

        MemcachedClientIF client = factory.createInstance();
        assertThat(client.getClass().getName(), equalTo("com.gumtree.util.cache.memcached.MemcachedClientStub"));
    }

}
