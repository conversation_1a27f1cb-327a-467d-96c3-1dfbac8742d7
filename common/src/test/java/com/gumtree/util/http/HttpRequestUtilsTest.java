package com.gumtree.util.http;

import com.google.common.collect.ImmutableMap;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;

import static org.fest.assertions.api.Assertions.assertThat;

public class HttpRequestUtilsTest {

    @Test
    public void shouldIdentifyRequestAsAjaxRequest() {
        // given
        MockHttpServletRequest req = new MockHttpServletRequest();
        req.addHeader("X-Requested-With", "XMLHttpRequest");

        // when
        boolean isAjax = HttpRequestUtils.isAjaxRequest(req);

        // then
        assertThat(isAjax).isTrue();
    }

    @Test
    public void shouldNotIdentifyRequestAsAjaxRequestIfRequestWithHeaderIsNotSet() {
        // given
        MockHttpServletRequest req = new MockHttpServletRequest();

        // when
        boolean isAjax = HttpRequestUtils.isAjaxRequest(req);

        // then
        assertThat(isAjax).isFalse();
    }

    @Test
    public void shouldNotIdentifyRequestAsAjaxRequestIfRequestWithHeaderIsSetInvalidValue() {
        // given
        MockHttpServletRequest req = new MockHttpServletRequest();
        req.addHeader("X-Requested-With", "rubbish");

        // when
        boolean isAjax = HttpRequestUtils.isAjaxRequest(req);

        // then
        assertThat(isAjax).isFalse();
    }

    @Test
    public void shouldFilterUtmParameters() {
        // given
        MockHttpServletRequest req = new MockHttpServletRequest();
        ImmutableMap<String, Object[]> utmParams = ImmutableMap.of(
                "utm_source", new String[]{"google"},
                "utm_medium", new String[]{"cpc"},
                "randomQueryString", new String[]{"random"});
        req.addParameters(utmParams);

        // when
        String queryString = HttpRequestUtils.filterUtmParamsToQueryString(req.getParameterMap());

        // then
        assertThat(queryString).isEqualTo("?utm_source=google&utm_medium=cpc");
    }
}
