package com.gumtree.util;

import com.google.common.base.Optional;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

public class GuavaUtilsTest {

    @Test
    public void shouldReturnValueIfTransformationResultNotAbsent() {
        Optional<String> result = GuavaUtils.flatMap(Optional.of("test"), s -> Optional.of(s.toUpperCase()));
        assertEquals(result.get(), "TEST");
    }

    @Test
    public void shouldReturnAbsentIfInputAbsent() {
        Optional<String> result = GuavaUtils.flatMap(Optional.<String>absent(), s -> Optional.of(s.toUpperCase()));
        assertFalse(result.isPresent());
    }

    @Test
    public void shouldReturnValueIfTransformationResultAbsent() {
        Optional<String> result = GuavaUtils.flatMap(Optional.of("test"), s -> Optional.absent());
        assertFalse(result.isPresent());
    }

    @Test
    public void shouldProperlyCreateGuavaOptionalFromNonEmptyValue() {
        Optional<String> result = GuavaUtils.java8OptionalToGuava(java.util.Optional.of("test"));
        assertEquals(result.get(), "test");
    }

    @Test
    public void shouldProperlyCreateGuavaOptionalFromEmptyValue() {
        Optional<String> result = GuavaUtils.java8OptionalToGuava(java.util.Optional.empty());
        assertFalse(result.isPresent());
    }

}