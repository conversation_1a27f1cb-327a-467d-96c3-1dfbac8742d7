package com.gumtree.util.url;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.netflix.config.ConfigurationManager;
import com.netflix.config.DynamicProperty;
import org.apache.commons.configuration.AbstractConfiguration;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Properties;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 *
 */
public class PSUrlSchemeTest {

    private final PopularSearchUrlScheme defaultPSUrlScheme;
    private final Category mockCategory;
    private final Location mockLocation;

    public PSUrlSchemeTest() {
        Properties properties = new Properties();
        properties.setProperty("gumtree.url.buyer.base_uri", "");
        properties.setProperty("gumtree.url.seller.base_uri", "");
        properties.setProperty("gumtree.url.seller.secure.base_uri", "");
        properties.setProperty("gumtree.url.reply.base_uri", "");
        ConfigurationManager.loadProperties(properties);

        this.defaultPSUrlScheme = new PopularSearchUrlScheme();
        ReflectionTestUtils.setField(defaultPSUrlScheme, "buyerBaseUri", "http://test.com");
        this.mockCategory = mock(Category.class);
        this.mockLocation = mock(Location.class);
    }

    @Test
    public void testUrlForListingWithCategoryAndLocation() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("clapham");
        assertThat(defaultPSUrlScheme.getFor(mockCategory, mockLocation), equalTo("http://test.com/popular-search/cars/clapham"));
    }

    @Test
    public void testUrlForListingWithCategoryAndUKLocation() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        assertThat(defaultPSUrlScheme.getFor(mockCategory, mockLocation), equalTo("http://test.com/popular-search/cars/uk"));
    }

    @Test
    public void testUrlForListingWithCategoryAndNullLocation() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        assertThat(defaultPSUrlScheme.getFor(mockCategory, null), equalTo("http://test.com/popular-search/cars"));
    }

    @Test
    public void testUrlForListingWithOnlyLocation() {
        when(mockLocation.getName()).thenReturn("clapham");
        assertThat(defaultPSUrlScheme.getFor(mockLocation), equalTo("http://test.com/popular-search/all/clapham"));
    }

    @Test
    public void testUrlForListingWithLocationNullCategory() {
        when(mockLocation.getName()).thenReturn("clapham");
        assertThat(defaultPSUrlScheme.getFor((Category) null, mockLocation), equalTo("http://test.com/popular-search/all/clapham"));
    }

    @Test
    public void testUrlForListingWithLocationNullLocationNullCategory() {
        assertThat(defaultPSUrlScheme.getFor(null, null), equalTo("http://test.com/popular-search/all"));
    }


}
