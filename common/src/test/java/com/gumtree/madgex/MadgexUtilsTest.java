package com.gumtree.madgex;

import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class MadgexUtilsTest {

    @Test
    public void shouldIdentifyMadgexAdCorrectly() {
        assertThat(MadgexUtils.isMadgexAdvert(1L)).isFalse();
        assertThat(MadgexUtils.isMadgexAdvert(4000000000L)).isFalse();
        assertThat(MadgexUtils.isMadgexAdvert(4000000001L)).isTrue();
        assertThat(MadgexUtils.isMadgexAdvert(5000000001L)).isTrue();
    }

    @Test
    public void shouldGetGumtreeAccountId() {
        assertThat(MadgexUtils.getGumtreeAccountId(1L)).isEqualTo(1l);
        assertThat(MadgexUtils.getGumtreeAccountId(4000000000L)).isEqualTo(4000000000L);
        assertThat(MadgexUtils.getGumtreeAccountId(4000000001L)).isEqualTo(1l);
        assertThat(MadgexUtils.getGumtreeAccountId(5000000001L)).isEqualTo(1000000001L);
    }

    @Test
    public void shouldGetMadgexAdvertId() {
        assertThat(MadgexUtils.getMadgexAdvertId(4000000001L)).isEqualTo(1L);
        assertThat(MadgexUtils.getMadgexAdvertId(5000000001L)).isEqualTo(1000000001L);
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldFailToGetMadgexAdvertIdIfIdIsNotValidMadgexAdvertId_1() {
        MadgexUtils.getMadgexAdvertId(1L);
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldFailToGetMadgexAdvertIdIfIdIsNotValidMadgexAdvertId_2() {
        MadgexUtils.getMadgexAdvertId(4000000000L);
    }
}