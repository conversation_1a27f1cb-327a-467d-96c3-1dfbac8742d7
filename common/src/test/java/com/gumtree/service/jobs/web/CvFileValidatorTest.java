package com.gumtree.service.jobs.web;

import org.junit.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertThat;


public class CvFileValidatorTest {

    @Test
    public void shouldSucceedForValidFile() {
        MultipartFile file = new MockMultipartFile("cv", "test.pdf", "application/pdf", new byte[5]);
        assertThat(CvFileValidator.validate(file), is(empty()));
    }

    @Test
    public void shouldFailIfFileTooLarge() {
        MultipartFile file = new MockMultipartFile("cv", "test.pdf", "application/pdf", new byte[8 * 1024 * 1024 + 1]);
        assertThat(CvFileValidator.validate(file), contains("upload.cv.file.too.large"));
    }

    @Test
    public void shouldFailIfInvalidFileType() {
        MultipartFile file = new MockMultipartFile("cv", "test.exe", "application/x-msdownload", new byte[5]);
        assertThat(CvFileValidator.validate(file), contains("upload.cv.invalid.type"));
    }

    @Test
    public void shouldFailIfFileEmpty() {
        MultipartFile file = new MockMultipartFile("cv", "test.pdf", "application/pdf", new byte[0]);
        assertThat(CvFileValidator.validate(file), contains("upload.cv.empty.file"));
    }

}