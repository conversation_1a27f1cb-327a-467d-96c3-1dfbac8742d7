package com.gumtree.service.jobs;

import org.fest.assertions.core.Condition;
import org.junit.Test;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;

import static com.gumtree.service.jobs.CvData.builder;
import static org.fest.assertions.api.Assertions.assertThat;

public class CvDataTest {

    @Test
    public void shouldInitialiseUploadDateTime() {
        CvData cvData = builder().build();
        assertThat(cvData.getUploadDateTime()).is(justNow);
    }

    @Test
    public void shouldBuildCvDataOk() throws IOException {
        byte[] bytes = "Hello".getBytes();
        String contentType = "application/json";
        String prefix = "mycv";
        String suffix = "docx";
        String originalFilename = prefix + "." + suffix;
        Long user = 1L;
        CvData cvData = builder().withBytes(bytes)
                .withContentType(contentType)
                .withOriginalFilename(originalFilename)
                .withUserId(user)
                .build();

        assertThat(cvData.getBytes()).isEqualTo(bytes);
        assertThat(cvData.getContentType()).isEqualTo(contentType);
        assertThat(cvData.getFilePrefix()).isEqualTo(prefix);
        assertThat(cvData.getFileSuffix()).isEqualTo(suffix);
        assertThat(cvData.getOriginalFilename()).isEqualTo(originalFilename);
        assertThat(cvData.getUserId()).isEqualTo(user);

        CvData cvData2 = new CvData.Builder(cvData).build();
        assertThat(cvData2.getBytes()).isEqualTo(bytes);
        assertThat(cvData2.getContentType()).isEqualTo(contentType);
        assertThat(cvData2.getFilePrefix()).isEqualTo(prefix);
        assertThat(cvData2.getFileSuffix()).isEqualTo(suffix);
        assertThat(cvData2.getOriginalFilename()).isEqualTo(originalFilename);
        assertThat(cvData2.getUserId()).isEqualTo(user);
    }


    @Test
    public void shouldSetUploadTime1DaysAgo() {
        LocalDateTime yesterday = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).minusDays(1).withHour(23).withMinute(59);
        CvData cvData = builder().withUploadDate(yesterday.atZone(ZoneId.systemDefault()).toInstant()).build();
        assertThat(cvData.getPeriodSinceUploaded()).isEqualTo("1 day ago");
    }

    @Test
    public void shouldSetUploadTime2DaysAgo() {
        CvData cvData = builder().withUploadDate(LocalDateTime.now().minusDays(2).atZone(ZoneId.systemDefault()).toInstant()).build();
        assertThat(cvData.getPeriodSinceUploaded()).isEqualTo("2 days ago");
    }

    @Test
    public void shouldSetUploadTimeToday() {
        CvData cvData = builder().withUploadDate(LocalDateTime.now().minusMinutes(2).atZone(ZoneId.systemDefault()).toInstant()).build();
        assertThat(cvData.getPeriodSinceUploaded()).isEqualTo("today");
    }

    @Test
    public void shouldSetUploadTime1MonthsAgo() {
        CvData cvData = builder().withUploadDate(LocalDateTime.now().minusMonths(1).atZone(ZoneId.systemDefault()).toInstant()).build();
        assertThat(cvData.getPeriodSinceUploaded()).isEqualTo("1 month ago");
    }

    @Test
    public void shouldSetUploadTimeMonthsAgo() {
        CvData cvData = builder().withUploadDate(LocalDateTime.now().minusMonths(10).atZone(ZoneId.systemDefault()).toInstant()).build();
        assertThat(cvData.getPeriodSinceUploaded()).isEqualTo("10 months ago");
    }

    @Test
    public void shouldSetUploadTime1YearAgo() {
        CvData cvData = builder().withUploadDate(LocalDateTime.now().minusDays(27).minusMonths(11).minusYears(1).atZone(ZoneId.systemDefault()).toInstant()).build();
        assertThat(cvData.getPeriodSinceUploaded()).isEqualTo("1 year ago");
    }

    @Test
    public void shouldSetUploadTimeYearsAgo() {
        CvData cvData = builder().withUploadDate(LocalDateTime.now().minusYears(2).atZone(ZoneId.systemDefault()).toInstant()).build();
        assertThat(cvData.getPeriodSinceUploaded()).isEqualTo("2 years ago");
    }

    private final Condition<Instant> justNow = new Condition<Instant>("just Now") {
        @Override
        public boolean matches(Instant dateToCheck) {
            Instant now = Instant.now();
            return now.atZone(ZoneId.systemDefault()).isEqual(dateToCheck.atZone(ZoneId.systemDefault())) ||
                    (now.isAfter(dateToCheck) && now.minus(20, ChronoUnit.SECONDS).isBefore(dateToCheck));
        }
    };
}
