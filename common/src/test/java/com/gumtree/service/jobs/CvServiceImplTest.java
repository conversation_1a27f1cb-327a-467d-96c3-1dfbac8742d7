package com.gumtree.service.jobs;

import com.gumtree.cvstore.client.config.CvStoreClientFactory;
import com.gumtree.cvstore.spec.CvStoreClient;
import com.gumtree.cvstore.stub.CvStoreStub;
import com.gumtree.cvstore.stub.config.CvStoreStubServerFactory;
import org.hamcrest.Matchers;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Optional;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.fail;

public class CvServiceImplTest {

    CvStoreStub stubServer;

    CvService cvService;

    @Before
    public void setUp() {
            CvStoreStubServerFactory stubFactory = new CvStoreStubServerFactory();
            stubFactory.setEnabled(true);
            stubServer = stubFactory.create();
            try {
                stubServer.start();
            } catch (Exception e) {
                e.printStackTrace();
            }

            CvStoreClientFactory clientFactory = new CvStoreClientFactory();
            clientFactory.setHost("http://localhost");
            clientFactory.setPort(stubServer.getPort());
            clientFactory.setConnectionTimeout(1000);
            clientFactory.setSocketTimeout(1000);



            try {
                CvStoreClient cvStoreClient = clientFactory.create();
                cvService = new CvServiceImpl(cvStoreClient);
            } catch (URISyntaxException e) {
                throw new RuntimeException(e);
            }
    }

    @After
    public void tearDown() throws IOException {
        stubServer.stop();
    }


    @Test
    public void shouldProperlyUploadCv() throws IOException {
        //given
        CvData data = CvData.builder().
                withContentType("application/xml").
                withOriginalFilename("file.pdf").
                withUserId(2L).
                withBytes("Test".getBytes()).
                build();

        //when
        cvService.upload(data);

        //then
        assertThat(cvService.getMetadata(2L).isPresent(), equalTo(true));
    }

    @Test
    public void shouldProperlyGetMetadata() throws IOException {
        //given
        CvData storedData = CvData.builder().
                withContentType("application/xml").
                withOriginalFilename("file.pdf").
                withUserId(3L).
                withBytes("Test".getBytes()).
                build();
        cvService.upload(storedData);

        //when
        Optional<CvData> receviedData = cvService.getMetadata(3L);

        //then
        assertThat(receviedData.isPresent(), equalTo(true));
        assertThat(receviedData.get().getContentType(), equalTo(storedData.getContentType()));
        assertThat(receviedData.get().getOriginalFilename(), equalTo(storedData.getOriginalFilename()));
        assertThat(receviedData.get().getUserId(), equalTo(storedData.getUserId()));
    }

    @Test
    public void shouldReturnNothingIfNoMetadata() throws IOException {
        //when
        Optional<CvData> receviedData = cvService.getMetadata(4L);

        //then
        assertThat(receviedData.isPresent(), equalTo(false));
    }

    @Test
    public void shouldProperlyGetContent() throws IOException {
        //given
        CvData storedData = CvData.builder().
                withContentType("application/xml").
                withOriginalFilename("file.pdf").
                withUserId(3L).
                withBytes("Test".getBytes()).
                build();
        cvService.upload(storedData);

        //when
        Optional<CvData> receviedData = cvService.getContent(3L);

        //then
        assertThat(receviedData.isPresent(), equalTo(true));
        assertThat(receviedData.get().getContentType(), equalTo(storedData.getContentType()));
        assertThat(receviedData.get().getOriginalFilename(), equalTo(storedData.getOriginalFilename()));
        assertThat(receviedData.get().getUserId(), equalTo(storedData.getUserId()));
        assertThat(new String(receviedData.get().getBytes()), equalTo("Test"));
    }

    @Test
    public void shouldProperlyDeleteCv() throws IOException {
        //given
        CvData storedData = CvData.builder().
                withContentType("application/xml").
                withOriginalFilename("file.pdf").
                withUserId(3L).
                withBytes("Test".getBytes()).
                build();
        cvService.upload(storedData);


        //when
        cvService.upload(storedData);
        cvService.delete(storedData.getUserId());

        //then
        Optional<CvData> receviedData = cvService.getMetadata(3L);
        assertThat(receviedData.isPresent(), equalTo(false));
    }

    @Test
    public void shouldProperlyHandleErrors() throws IOException {
        //given
        CvData storedData = CvData.builder().
                withContentType("application/xml").
                withOriginalFilename("file.pdf").
                withUserId(1L).
                withBytes(new byte[9 * 1024 * 1024]).
                build();
        try {
            cvService.upload(storedData);
            fail("Expected exception");
        } catch (Exception e) {
            assertThat(e.getMessage(), Matchers.containsString("FILE_TOO_LARGE"));
        }

    }


}
