package com.gumtree.domain;

import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class NonZeroPageNumberTest {
    @Test
    public void shouldCreateNewInstanceOk() {
        assertThat(NonZeroPageNumber.of(1).getValue()).isEqualTo(1);
        assertThat(NonZeroPageNumber.of(10).getValue()).isEqualTo(10);
        assertThat(NonZeroPageNumber.of(9999).getValue()).isEqualTo(9999);
        assertThat(NonZeroPageNumber.of(9999)).isEqualTo(new NonZeroPageNumber(9999));
    }

    @Test(expected = IllegalArgumentException.class)
    public void shouldFailIfInputIsLessThanZero() {
        new NonZeroPageNumber(0);
    }
}