package com.gumtree.domain.attribute.internal.value;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import java.sql.Date;
import java.util.Calendar;

import org.junit.Test;

import com.gumtree.domain.newattribute.AttributeValue;
import com.gumtree.domain.newattribute.internal.InvalidAttributeValueException;
import com.gumtree.domain.newattribute.internal.value.DateValue;

public class DateValueTest {
    
    @Test
    public void convertsStringsToDate() {
        AttributeValue dateValue = null;
        try {
            dateValue = DateValue.create("20110504");
        }catch (InvalidAttributeValueException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        
        Calendar eventDate = Calendar.getInstance();
        eventDate.setTime(dateValue.as(Date.class));
        assertThat(eventDate.get(Calendar.DAY_OF_MONTH), equalTo(4));
        assertThat(eventDate.get(Calendar.MONTH), equalTo(4));
        assertThat(eventDate.get(Calendar.YEAR), equalTo(2011));
    }

}
