package com.gumtree.domain.newattribute.internal;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.SearchAttribute;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.DisplayAttribute;
import com.gumtree.domain.newattribute.internal.value.TextValue;
import com.gumtree.service.category.CategoryService;
import com.gumtree.util.model.tree.TreeModel;
import org.junit.Before;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.google.common.collect.Lists.newArrayList;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class DefaultAttributeServiceTest {
    private DefaultAttributeService attributeService;
    private CategoryService categoryService;
    private Category categoryCars = newCategory(211, "cars");
    private Attribute vehicleMakeAttr;
    private Attribute vehicleModelAttr;
    private Attribute vehicleSellerTypeAttr;
    private Attribute vehicleMileageAttr;


    @Before
    public void init() throws IOException {
        categoryService = mock(CategoryService.class);
        vehicleMakeAttr = initializeNewAttribute("vehicle_make", categoryCars, 1, "bmw");
        vehicleModelAttr = initializeNewAttribute("vehicle_model", categoryCars, 12, "");
        vehicleMileageAttr = initializeNewAttribute("vehicle_mileage", categoryCars, 4, "");
        vehicleSellerTypeAttr = initializeNewAttribute("seller_type", categoryCars, 3, "private");
        List<String> carsAttributes = newArrayList("vehicle_make", "vehicle_model", "vehicle_mileage", "seller_type");
        given(categoryService.attributeTypes(categoryCars.getId())).willReturn(carsAttributes);
        final TreeModel treeModel = mock(TreeModel.class);
        when(treeModel.getRoots()).thenReturn(Collections.emptyList());
        attributeService = new DefaultAttributeService(categoryService);
    }

    private Category newCategory(int id, String name) {
        return new Category((long) id, "seo-" + name, name);
    }

    private Attribute initializeNewAttribute(String attributeType, Category category, int vipOrder, String attributeValue) {
        given(categoryService.attributeDisplayLabel(category.getId(), attributeType))
                .willReturn(Optional.of(attributeType));
        addAttributeMetadataToCategory(attributeType, category, vipOrder);
        return new DefaultAttribute(attributeType, attributeValue != null ? TextValue.create(attributeValue, true) : null);
    }

    private void addAttributeMetadataToCategory(String attributeType, Category category, int vipOrder) {
        List<AttributeMetadata> categoryAttrMetadata = category.getAttributeMetadata();
        if (categoryAttrMetadata == null) {
            categoryAttrMetadata = new ArrayList<AttributeMetadata>();
        }
        ArrayList<AttributeMetadata> newCategoryAttrMetadata = Lists.newArrayList(categoryAttrMetadata);
        AttributeMetadata attributeMetadata = new AttributeMetadata();
        attributeMetadata.setName(attributeType);
        attributeMetadata.setLabel(attributeType);
        attributeMetadata.setSearchStyle(SearchAttribute.Style.EQ);
        attributeMetadata.setVipOrder(vipOrder);
        newCategoryAttrMetadata.add(attributeMetadata);
        category.setAttributeMetadata(newCategoryAttrMetadata);
    }

    private List<String> getAttributeNames(List<DisplayAttribute> attributes) {
        List<String> names = new ArrayList<String>(attributes.size());
        for (DisplayAttribute attr: attributes) {
            names.add(attr.getTypeDisplayName());
        }
        return names;
    }
}
