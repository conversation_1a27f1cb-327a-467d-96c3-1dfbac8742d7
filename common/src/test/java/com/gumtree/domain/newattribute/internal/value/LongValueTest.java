package com.gumtree.domain.newattribute.internal.value;

import com.gumtree.domain.newattribute.internal.InvalidAttributeValueException;
import com.gumtree.domain.newattribute.value.Range;
import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class LongValueTest {

    @Test
    public void shouldConvertToLong() throws InvalidAttributeValueException {
        assertThat(LongValue.create("92,000", null)).isEqualTo(new LongValue(92000L, null, null));
        assertThat(LongValue.create("over_50", null)).isEqualTo(new LongValue(null, new Range(51L, null), null));
        assertThat(LongValue.create("up_to_50", null)).isEqualTo(new LongValue(null, new Range(0L, 50L), null));
        assertThat(LongValue.create("30_to_50", null)).isEqualTo(new LongValue(null, new Range(30L, 50L), null));
        assertThat(LongValue.create("54.3", null)).isEqualTo(new LongValue(54L, null, null));
        assertThat(LongValue.create("54.9", null)).isEqualTo(new LongValue(54L, null, null));
        assertThat(LongValue.create("54.9", null)).isEqualTo(new LongValue(54L, null, null));
        assertThat(LongValue.create(Double.valueOf("54.9"), null)).isEqualTo(new LongValue(54L, new Range(54L,54L), null));
        assertThat(LongValue.create(54L, null)).isEqualTo(new LongValue(54L, new Range(54L,54L), null));
        assertThat(LongValue.create(54, null)).isEqualTo(new LongValue(54L, new Range(54L,54L), null));
        assertThat(LongValue.create("54", null)).isEqualTo(new LongValue(54L, null, null));
        assertThat(LongValue.create("54.99999", null)).isEqualTo(new LongValue(54L, null, null));
    }
}