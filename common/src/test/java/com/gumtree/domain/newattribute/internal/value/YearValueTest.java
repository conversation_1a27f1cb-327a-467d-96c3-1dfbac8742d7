package com.gumtree.domain.newattribute.internal.value;

import com.gumtree.domain.newattribute.internal.InvalidAttributeValueException;
import org.hamcrest.Matchers;
import org.junit.Test;

import static org.junit.Assert.assertThat;

public class YearValueTest {

    @Test
    public void shouldConvertLongToString() throws InvalidAttributeValueException {
        assertThat(YearValue.create(2004L).as(String.class), Matchers.equalTo("2004"));
    }

    @Test
    public void shouldConvertIntegerToString() throws InvalidAttributeValueException {
        assertThat(YearValue.create(1999).as(String.class), Matchers.equalTo("1999"));
    }

    @Test
    public void shouldConvertStringToString() throws InvalidAttributeValueException {
        assertThat(YearValue.create("2013").as(String.class), Matchers.equalTo("2013"));
    }
}
