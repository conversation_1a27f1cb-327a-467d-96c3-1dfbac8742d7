package com.gumtree.google;

import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;

/**
 * Unit tests for GoogleMapsUrlSigner class.
 */
public class GoogleMapsUrlSignerTest {

    @Test(expected = UrlSigningException.class)
    public void invalidKeyThrowsException() throws Exception {
        GoogleMapsUrlSigner urlSigner = createUrlSigner("");
        String url = urlSigner.signRequest("fdvdfv", "fsfdgfd");
    }

    @Test
    public void validKeySignsCorrectly() throws Exception {
        GoogleMapsUrlSigner urlSigner = createUrlSigner("HEfjRBVgKHdYoP6qXiuD1NRCCJM=");
        String url = urlSigner.signRequest("fdvdfv", "fsfdgfd");
        assertEquals("fdvdfv?fsfdgfd&signature=RZ4xohqB_Thvl-sXt-dLJTzP5xc=", url);
    }

    private GoogleMapsUrlSigner createUrlSigner(String key) {
        GoogleMapsUrlSigner urlSigner = new GoogleMapsUrlSigner();
        ReflectionTestUtils.setField(urlSigner, "encodedKey", key);
        urlSigner.afterPropertiesSet();
        return urlSigner;
    }
}
