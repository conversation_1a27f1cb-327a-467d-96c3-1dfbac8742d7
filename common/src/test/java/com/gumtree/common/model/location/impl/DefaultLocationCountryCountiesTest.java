package com.gumtree.common.model.location.impl;

import com.google.common.collect.Sets;
import com.gumtree.domain.location.Location;
import com.gumtree.service.location.LocationService;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class DefaultLocationCountryCountiesTest {

    private DefaultLocationCountryCounties countryCounties;
    private Location englandLocation;
    private Location scotlandLocation;

    @Before
    public void setUp() {
        LocationService locationService = mock(LocationService.class);
        when(locationService.getById(10000352)).thenReturn(englandLocation = mock(Location.class));
        when(locationService.getById(10000349)).thenReturn(scotlandLocation = mock(Location.class));
        when(locationService.getById(10000354)).thenReturn( mock(Location.class));
        when(locationService.getById(10000355)).thenReturn(mock(Location.class));
        countryCounties = new DefaultLocationCountryCounties(locationService);
        countryCounties.loadFromProperties(properties());
    }

    private Properties properties() {
        Properties properties = new Properties();
        properties.setProperty("10000352", "99999996");
        properties.setProperty("10000349", "99999997");
        properties.setProperty("10000354", "99999998");
        properties.setProperty("10000355", "99999999");
        return properties;
    }

    @Test
    public void testGetCounties() {
        Location englandCountry = countryCounties.getCountry(englandLocation);

        assertThat(englandCountry, equalTo(DefaultLocationCountryCounties.ENGLAND));
    }

    @Test
    public void testGetCountry() {
        List<Location> scotishCountries = countryCounties.getCounties(DefaultLocationCountryCounties.SCOTLAND);

        assertThat(scotishCountries, hasSize(1));
        assertThat(scotishCountries, contains(scotlandLocation));
    }
}
