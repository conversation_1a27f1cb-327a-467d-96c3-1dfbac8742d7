package com.gumtree.common.model.attribute;



import org.mockito.Matchers;
import org.mockito.Mockito;

import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.AttributeValue;

public class AttributeMockingUtils {

    public static Attribute mock(String attributeType, Object value) {
        Attribute mockedAttribute = Mockito.mock(Attribute.class);
        AttributeValue mockedAttributeValue = Mockito.mock(AttributeValue.class);

        Mockito.when(mockedAttribute.getType()).thenReturn(attributeType);
        Mockito.when(mockedAttribute.getValue()).thenReturn(mockedAttributeValue);
        Mockito.when(mockedAttributeValue.as(Matchers.any(Class.class))).thenReturn(value);

        return mockedAttribute;
    }

}
