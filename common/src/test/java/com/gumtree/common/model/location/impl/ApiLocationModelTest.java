package com.gumtree.common.model.location.impl;

import com.gumtree.api.Locations;
import com.gumtree.api.Outcodes;
import com.gumtree.api.client.spec.LocationApi;
import com.gumtree.domain.location.Location;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class ApiLocationModelTest {

    private List<com.gumtree.api.Location> testLocations;
    private List<String> testOutcodes;
    private ApiLocationModel sut;

    @Before
    public void setup() {
        testLocations = new ArrayList<com.gumtree.api.Location>();
        com.gumtree.api.Location australia = createLocation(0, "Australia", false);
        testLocations.add(australia);
        com.gumtree.api.Location queensland = createLocation(1, "Queensland", true);
        testLocations.add(queensland);
        com.gumtree.api.Location newSouthWales = createLocation(2, "New South Wales", true);
        testLocations.add(newSouthWales);
        com.gumtree.api.Location brisbane = createLocation(3, "Brisbane", false);
        testLocations.add(brisbane);
        com.gumtree.api.Location sunshineCoast = createLocation(4, "Sunshine Coast", false);
        testLocations.add(sunshineCoast);
        com.gumtree.api.Location goldCoast = createLocation(5, "Gold Coast", false);
        testLocations.add(goldCoast);
        com.gumtree.api.Location sydney = createLocation(6, "Sydney", false);
        testLocations.add(sydney);
        com.gumtree.api.Location newcastle = createLocation(7, "Newcastle", false);
        testLocations.add(newcastle);
        com.gumtree.api.Location wollongong = createLocation(8, "Wollongong", false);
        testLocations.add(wollongong);
        com.gumtree.api.Location victoria = createLocation(9, "Victoria", false);
        testLocations.add(victoria);

        addZoomIn(australia, queensland);
        addZoomIn(queensland, brisbane);
        addZoomIn(queensland, goldCoast);
        addZoomIn(queensland, sunshineCoast);
        addZoomIn(australia, newSouthWales);
        addZoomIn(newSouthWales, sydney);
        addZoomIn(newSouthWales, newcastle);
        addZoomIn(newSouthWales, wollongong);
        addZoomIn(australia, victoria);

        addZoomOut(queensland, australia);
        addZoomOut(brisbane, queensland);
        addZoomOut(goldCoast, queensland);
        addZoomOut(sunshineCoast, queensland);
        addZoomOut(newSouthWales, australia);
        addZoomOut(sydney, newSouthWales);
        addZoomOut(newcastle, newSouthWales);
        addZoomOut(wollongong, newSouthWales);
        addZoomOut(victoria, australia);

        addNearby(queensland, newSouthWales);
        addNearby(newSouthWales, queensland);
        addNearby(brisbane, goldCoast);
        addNearby(goldCoast, brisbane);
        addNearby(brisbane, sunshineCoast);
        addNearby(sunshineCoast, brisbane);
        addNearby(sydney, newcastle);
        addNearby(newcastle, sydney);
        addNearby(sydney, wollongong);
        addNearby(wollongong, sydney);
        addNearby(victoria, newSouthWales);
        addNearby(newSouthWales, victoria);

        testOutcodes = Arrays.asList("SW18", "CR2", "TW19");

        sut = createInMemoryLocationModel();
    }

    @Test
    public void getLandingLocation() {
        assertThat(sut.getById(7).isLanding(), equalTo(false));
    }

    @Test
    public void getNonLandingLocation() {
        assertThat(sut.getById(2).isLanding(), equalTo(true));
    }

    @Test
    public void getRootLocation() {
        Location root = sut.getRootLocation();
        assertThat(root.getId(), equalTo(0));
        assertThat(root.getName(), equalTo("Australia"));
    }

    @Test
    public void getAll() {

        // call method under test
        Collection<Location> locations = sut.getAll();

        // verify results
        assertThat(locations.size(), equalTo(testLocations.size()));

    }

    @Test
    public void getById() {

        // call method under test
        Location location = sut.getById(5);

        // verify results
        assertThat(location.getId(), equalTo(5));
        assertThat(location.getName(), equalTo("Gold Coast"));

    }

    @Test
    public void getByName() {

        // call method under test
        Location location = sut.getByName("Gold Coast");

        // verify results
        assertThat(location.getId(), equalTo(5));
        assertThat(location.getName(), equalTo("Gold Coast"));

    }

    @Test
    public void getZoomIn() {

        // get location to get zoom ins for
        Location location = sut.getByName("Australia");

        // call method under test
        List<Location> locations = new ArrayList<Location>(sut.getZoomIn(location));

        // verify results
        assertThat(locations.size(), equalTo(3));
        assertThat(locations.get(0).getName(), equalTo("New South Wales"));
        assertThat(locations.get(1).getName(), equalTo("Queensland"));
        assertThat(locations.get(2).getName(), equalTo("Victoria"));
    }

    @Test
    public void getZoomInWithNoZoomInsAvailable() {

        // get location to get zoom ins for
        Location location = sut.getByName("Brisbane");

        // call method under test
        Collection<Location> locations = sut.getZoomIn(location);

        // verify results
        assertThat(locations.size(), equalTo(0));

    }

    @Test
    public void getZoomOut() {

        // get location to get zoom outs for
        Location location = sut.getByName("Brisbane");

        // call method under test
        Collection<Location> locations = sut.getZoomOut(location);

        // verify results
        assertThat(locations.size(), equalTo(1));
        assertThat(contains(locations, "Queensland"), equalTo(true));

    }

    @Test
    public void getZoomOutWithNoZoomOutsAvailable() {

        // get location to get zoom outs for
        Location location = sut.getByName("Australia");

        // call method under test
        Collection<Location> locations = sut.getZoomOut(location);

        // verify results
        assertThat(locations.size(), equalTo(0));

    }

    @Test
    public void getLocationsHierarchy() {
        Location location = sut.getByName("Brisbane");

        Map<Integer, Location> locations = sut.getLocationHierarchy(location);

        assertThat(locations.size(), is(3));
        assertThat(locations.get(1), equalTo(sut.getByName("Australia")));
        assertThat(locations.get(2), equalTo(sut.getByName("Queensland")));
        assertThat(locations.get(3), equalTo(sut.getByName("Brisbane")));
    }

    @Test
    public void getNearby() {

        // get location to get nearbys for
        Location location = sut.getByName("Brisbane");

        // call method under test
        List<Location> locations = new ArrayList<Location>(sut.getNearby(location));

        // verify results
        assertThat(locations.size(), equalTo(2));
        assertThat(locations.get(0).getName(), equalTo("Gold Coast"));
        assertThat(locations.get(1).getName(), equalTo("Sunshine Coast"));

    }

    @Test
    public void getNearbyWithNoNearbysAvailable() {

        // get location to get nearbys for
        Location location = sut.getByName("Australia");

        // call method under test
        Collection<Location> locations = sut.getNearby(location);

        // verify results
        assertThat(locations.size(), equalTo(0));

    }

    @Test
    public void getPrimaryLocation() {
        Location location = sut.getPrimaryLocation(Arrays.asList(0, 2, 6));
        assertThat(location.getId(), equalTo(6));
    }

    @Test
    public void getSmallestLocation() {
        Integer locationId = sut.getSmallestLocation(Arrays.asList(0, 1, 3));
        assertThat(locationId, equalTo(3));
    }

    @Test
    public void getByNames() {
        List<Location> locations = sut.getByNames("Australia", "Brisbane");
        assertThat(locations.get(0).getName(), equalTo("Australia"));
        assertThat(locations.get(1).getName(), equalTo("Brisbane"));
    }

    @Test
    public void hasZoomInForParent() {
        Location location = mock(Location.class);
        when(location.getId()).thenReturn(1);
        assertThat(sut.hasZoomIn(location), equalTo(true));
    }

    @Test
    public void hasZoomInForLeaf() {
        Location location = mock(Location.class);
        when(location.getId()).thenReturn(3);
        assertThat(sut.hasZoomIn(location), equalTo(false));
    }

    @Test
    public void getCountyForNonCounty() {
        Location location = mock(Location.class);
        when(location.getId()).thenReturn(3);
        assertThat(sut.getCounty(location).getId(), equalTo(1));
    }

    @Test
    public void getCountyForCounty() {
        Location location = mock(Location.class);
        when(location.getId()).thenReturn(1);
        assertThat(sut.getCounty(location).getId(), equalTo(1));
    }

    @Test
    public void findByDisplayName() {
        // call method under test
        List<Location> locations = new ArrayList<Location>(sut.findByDisplayName("Gold Coast"));
        // verify results
        assertThat(locations.size(), equalTo(1));
        assertThat(locations.get(0).getId(), equalTo(5));
        assertThat(locations.get(0).getName(), equalTo("Gold Coast"));
    }

    @Test
    public void findByDisplayNameNoMatch() {
        // call method under test
        List<Location> locations = new ArrayList<Location>(sut.findByDisplayName("Unknown"));
        // verify results
        assertThat(locations.size(), equalTo(0));
    }

    private ApiLocationModel createInMemoryLocationModel() {

        // mock up collaborators
        LocationApi locationApi = mock(LocationApi.class);
        Locations locations = new Locations();
        locations.setLocations(testLocations);
        Outcodes outcodes = new Outcodes();
        outcodes.setOutcodes(testOutcodes);
        when(locationApi.locations()).thenReturn(locations);
        when(locationApi.outcodes()).thenReturn(outcodes);

        // create new instance of sut
        return new ApiLocationModel(locationApi);

    }

    private boolean contains(Collection<Location> locations, String locationName) {
        for (Location location : locations) {
            if (location.getName().equals(locationName))
                return true;
        }
        return false;
    }

    private com.gumtree.api.Location createLocation(long id, String name, boolean isLanding) {
        com.gumtree.api.Location location = new com.gumtree.api.Location();
        location.setId(id);
        location.setSeoName(name);
        location.setName(name);
        location.setLanding(isLanding);
        return location;
    }

    private void addZoomIn(com.gumtree.api.Location source, com.gumtree.api.Location target) {
        List<Long> zoomIns = source.getZoomIns();
        if (zoomIns == null) {
            zoomIns = new ArrayList<Long>();
            source.setZoomIns(zoomIns);
        }
        zoomIns.add(target.getId());
    }

    private void addZoomOut(com.gumtree.api.Location source, com.gumtree.api.Location target) {
        List<Long> zoomOuts = source.getZoomOuts();
        if (zoomOuts == null) {
            zoomOuts = new ArrayList<Long>();
            source.setZoomOuts(zoomOuts);
        }
        zoomOuts.add(target.getId());
    }

    private void addNearby(com.gumtree.api.Location source, com.gumtree.api.Location target) {
        List<Long> nearbys = source.getNearbys();
        if (nearbys == null) {
            nearbys = new ArrayList<Long>();
            source.setNearbys(nearbys);
        }
        nearbys.add(target.getId());
    }
}
