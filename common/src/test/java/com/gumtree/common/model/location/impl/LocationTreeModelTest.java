package com.gumtree.common.model.location.impl;

import com.gumtree.domain.location.Location;
import com.gumtree.common.model.location.LocationModel;
import com.gumtree.util.LocationTestUtils;
import com.gumtree.util.model.tree.TreeNode;
import org.junit.Test;

import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.Collections;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;

public class LocationTreeModelTest {

    @Test
    public void modelWithSingleLocationShouldHaveSingleRootAndNoChildren() throws MalformedURLException {

        Location location = buildLocation("test-single", "Test Single", true);

        LocationTreeModel locationTreeModel = LocationTreeModel.buildModel(
                LocationTestUtils.createNoAssociationsLocationModel(), location);

        assertThat(locationTreeModel.getRoots().size(), equalTo(1));
        assertLocationGraphNode(locationTreeModel.getRoots().get(0), location, 0);
    }

    @Test
    public void modelWithLocationWithZoominsShouldBuildSimpleTree() throws MalformedURLException {

        LocationModel locationModel = mock(LocationModel.class);

        Location zoom1 = buildLocation("zoom-1", "Zoom 1", true);
        Location zoom2 = buildLocation("zoom-2", "Zoom 2", true);
        Location zoom3 = buildLocation("zoom-3", "Zoom 3", true);

        Location current = buildLocation("current-location", "Current Location", true);

        LocationTestUtils.addAssociations(
                locationModel,
                current,
                Arrays.asList(zoom1, zoom2, zoom3),
                Collections.<Location>emptyList(),
                Collections.<Location>emptyList());

        LocationTreeModel locationTreeModel = LocationTreeModel.buildModel(locationModel, current);

        assertThat(locationTreeModel.getRoots().size(), equalTo(1));
        assertLocationGraphNode(locationTreeModel.getRoots().get(0), current, 3);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(0), zoom1, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(1), zoom2, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(2), zoom3, 0);
    }

    @Test
    public void modelWithNearBysButNoZoomOutsShouldShowAsBunchOfRoots() throws MalformedURLException {

        LocationModel locationModel = mock(LocationModel.class);

        Location nearby1 = buildLocation("nearby-1", "Near By 1", true);
        Location nearby2 = buildLocation("nearby-2", "Near By 2", true);
        Location nearby3 = buildLocation("nearby-3", "Near By 3", true);

        Location current = buildLocation("current-location", "Current Location", true);

        LocationTestUtils.addAssociations(
                locationModel,
                current,
                Collections.<Location>emptyList(),
                Collections.<Location>emptyList(),
                Arrays.asList(nearby1, nearby2, nearby3));

        LocationTreeModel locationTreeModel = LocationTreeModel.buildModel(locationModel, current);

        assertThat(locationTreeModel.getRoots().size(), equalTo(4));
        assertLocationGraphNode(locationTreeModel.getRoots().get(0), current, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(1), nearby1, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(2), nearby2, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(3), nearby3, 0);
    }

    @Test
    public void modelWithSingleZoomOutAndNearBysShouldShowTreeWithZoomOutAtRootAndNearBysAsSiblings() throws MalformedURLException {

        LocationModel locationModel = mock(LocationModel.class);

        Location nearby1 = buildLocation("nearby-1", "Near By 1", false);
        Location nearby2 = buildLocation("nearby-2", "Near By 2", false);
        Location nearby3 = buildLocation("nearby-3", "Near By 3", false);
        Location zoomOut = buildLocation("zoom-out", "Zoom Out", true);

        Location current = buildLocation("current-location", "Current Location", true);

        LocationTestUtils.addAssociations(
                locationModel,
                current,
                Collections.<Location>emptyList(),
                Arrays.asList(zoomOut),
                Arrays.asList(nearby1, nearby2, nearby3));

        LocationTreeModel locationTreeModel = LocationTreeModel.buildModel(locationModel, current);

        assertThat(locationTreeModel.getRoots().size(), equalTo(1));
        assertLocationGraphNode(locationTreeModel.getRoots().get(0), zoomOut, 4);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(0), current, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(1), nearby1, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(2), nearby2, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(3), nearby3, 0);
    }

    @Test
    public void modelWithMultipleZoomOutsShouldShowTheseZoomOutsAtTheRootButTheyShouldNotShowTheirZoomIns() throws MalformedURLException {

        LocationModel locationModel = mock(LocationModel.class);

        Location mainZoomOut = buildLocation("main-zoomout", "Main Zoom Out", true);
        Location shouldNotShow1 = buildLocation("should-not-show-1", "Should Not Show", true);
        Location altZoomOut1 = buildLocation("alt-zoomout-1", "Alt Zoom Out 1", true);
        Location shouldNotShow2 = buildLocation("should-not-show-2", "Should Not Show", true);
        Location altZoomOut2 = buildLocation("alt-zoomout-2", "Alt Zoom Out 2", true);

        Location current = buildLocation("current-location", "Current Location", true);

        LocationTestUtils.addAssociations(
                locationModel,
                current,
                Collections.<Location>emptyList(),
                Arrays.asList(mainZoomOut, altZoomOut1, altZoomOut2),
                Collections.<Location>emptyList());

        LocationTestUtils.addAssociations(
                locationModel,
                altZoomOut1,
                Arrays.asList(shouldNotShow1),
                Collections.<Location>emptyList(),
                Collections.<Location>emptyList());

        LocationTestUtils.addAssociations(
                locationModel,
                altZoomOut2,
                Arrays.asList(shouldNotShow2),
                Collections.<Location>emptyList(),
                Collections.<Location>emptyList());

        LocationTreeModel locationTreeModel = LocationTreeModel.buildModel(locationModel, current);

        assertThat(locationTreeModel.getRoots().size(), equalTo(3));
        assertLocationGraphNode(locationTreeModel.getRoots().get(0), mainZoomOut, 1);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(0), current, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(1), altZoomOut1, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(2), altZoomOut2, 0);
    }

    @Test
    public void modelWithMultipleLevelsOfZoomOUtShouldShowTheseLevelsCorrectly() throws MalformedURLException {

        LocationModel locationModel = mock(LocationModel.class);

        Location london = buildLocation("london", "London", true);

        Location centralLondon = buildLocation("london-central", "Central London", true);
        Location westLondon = buildLocation("london-west", "West London", true);
        Location hydePark = buildLocation("hyde-park", "Hyde Park", true);
        Location hydeParkCorner = buildLocation("hyde-park-corner", "Hyde Park Corner", true);

        LocationTestUtils.addAssociations(
                locationModel,
                hydeParkCorner,
                Collections.<Location>emptyList(),
                Arrays.asList(hydePark),
                Collections.<Location>emptyList());

        LocationTestUtils.addAssociations(
                locationModel,
                hydePark,
                Collections.<Location>emptyList(),
                Arrays.asList(centralLondon, westLondon),
                Collections.<Location>emptyList());

        LocationTestUtils.addAssociations(
                locationModel,
                westLondon,
                Collections.<Location>emptyList(),
                Arrays.asList(london),
                Collections.<Location>emptyList());

        LocationTestUtils.addAssociations(
                locationModel,
                centralLondon,
                Collections.<Location>emptyList(),
                Arrays.asList(london),
                Collections.<Location>emptyList());

        LocationTreeModel locationTreeModel = LocationTreeModel.buildModel(locationModel, hydeParkCorner);

        assertThat(locationTreeModel.getRoots().size(), equalTo(1));
        assertLocationGraphNode(locationTreeModel.getRoots().get(0), london, 2);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(0), centralLondon, 1);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(0).getChildren().get(0), hydePark, 1);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(0).getChildren().get(0).getChildren().get(0), hydeParkCorner, 0);
        assertLocationGraphNode(locationTreeModel.getRoots().get(0).getChildren().get(1), westLondon, 0);

    }

    private void assertLocationGraphNode(TreeNode<Location> node, Location location, int childCount) throws MalformedURLException {

        assertThat(node.getModel(), equalTo(location));
        assertThat(node.getChildren().size(), equalTo(childCount));
    }

    private static Location buildLocation(String name, String displayName, boolean landing) {
        return LocationTestUtils.createLocation(null, name, displayName, landing);
    }
}
