package com.gumtree.common.format;

import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class TelephoneNumberFormatterTest {


    @Test
    public void shouldMaskAllButLast4Digits() {
        assertThat(TelephoneNumberFormatter.mask("01632960012"))
                .isEqualTo("0163296XXXX");
    }

    @Test
    public void shouldMaskAllButLast4DigitsIntNumber() {
        assertThat(TelephoneNumberFormatter.mask("00441632960012"))
                .isEqualTo("0044163296XXXX");
    }

    @Test
    public void shouldCopeWithInvalidNumber() {
        assertThat(TelephoneNumberFormatter.mask("262"))
                .isEqualTo("XXXX");
    }

    @Test
    public void shouldMarkLast4DigitsDataMaskEqualLength() {
        assertThat(TelephoneNumberFormatter.mask("01632960012"))
                .isEqualTo("0163296XXXX");
    }


}
