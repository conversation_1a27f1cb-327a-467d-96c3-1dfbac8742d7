package com.gumtree.api;

import okhttp3.HttpUrl;
import org.junit.Test;

import static com.gumtree.api.OkHttpClientMetricsInterceptor.extractHost;
import static com.gumtree.api.OkHttpClientMetricsInterceptor.extractPath;
import static org.fest.assertions.api.Assertions.assertThat;

public class OkHttpClientMetricsInterceptorTest {

    @Test
    public void extractPathNoPath() {
        HttpUrl url = HttpUrl.parse("http://localhost:8080");
        String path = extractPath(url);
        assertThat(path).isEqualTo("/");
    }

    @Test
    public void extractPathApiOnly() {
        HttpUrl url = HttpUrl.parse("http://localhost:8080/api");
        String path = extractPath(url);
        assertThat(path).isEqualTo("/api");
    }

    @Test
    public void extractPathWithApiAndMoreSegments() {
        HttpUrl url = HttpUrl.parse("http://localhost:8080/api/other");
        String path = extractPath(url);
        assertThat(path).isEqualTo("/api/other");
    }

    @Test
    public void extractPathWithApiOnlyShowsTwoSegmentsToAvoidIdOrName() {
        HttpUrl url = HttpUrl.parse("http://localhost:8080/api/other/<EMAIL>");
        String path = extractPath(url);
        assertThat(path).isEqualTo("/api/other");
    }

    @Test
    public void extractPathWithoutApiOnlySingleSegment() {
        HttpUrl url = HttpUrl.parse("http://localhost:8080/other");
        String path = extractPath(url);
        assertThat(path).isEqualTo("/other");
    }

    @Test
    public void extractPathWithoutApiOnlyShowsOneSegmentsToAvoidIdOrName() {
        HttpUrl url = HttpUrl.parse("http://localhost:8080/other/<EMAIL>");
        String path = extractPath(url);
        assertThat(path).isEqualTo("/other");
    }

    @Test
    public void extractHostNoPort() {
        HttpUrl url = HttpUrl.parse("http://localhost");
        String host = extractHost(url);
        assertThat(host).isEqualTo("localhost");
    }

    @Test
    public void extractHostWithPort() {
        HttpUrl url = HttpUrl.parse("http://localhost:8080");
        String host = extractHost(url);
        assertThat(host).isEqualTo("localhost");
    }

    @Test
    public void extractHostMultipleSegments() {
        HttpUrl url = HttpUrl.parse("http://livead-search.europe-west4.gum-site-stage.gumtree.cloud:8080");
        String host = extractHost(url);
        assertThat(host).isEqualTo("livead-search");
    }
}
