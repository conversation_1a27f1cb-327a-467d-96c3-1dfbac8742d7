package com.gumtree.api.domain;

import com.google.common.base.Optional;
import com.gumtree.web.common.domain.location.ResolvedOutcodeLocation;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.fest.assertions.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class ResolvedOutcodeLocationTest {
    private static final BigDecimal LONGITUDE = new BigDecimal(0.01);
    private static final BigDecimal LATITUDE = new BigDecimal(51.0);

    @Test
    public void shouldOutcodeWithoutLocation() {
        // when
        ResolvedOutcodeLocation resolvedLocation = new ResolvedOutcodeLocation(" Tw9 ", LATITUDE, LONGITUDE, Optional.absent());

        // then
        assertThat(resolvedLocation.supportsNearbySearch()).isTrue();
        assertThat(resolvedLocation.supportsRadialSearch()).isTrue();
        assertThat(resolvedLocation.getIdName()).isEqualTo("tw9");
        assertThat(resolvedLocation.getSearchLocation()).isEqualTo("TW9");
        assertThat(resolvedLocation.getLatitude()).isEqualTo(java.util.Optional.of(LATITUDE));
        assertThat(resolvedLocation.getLongitude()).isEqualTo(java.util.Optional.of(LONGITUDE));
    }

    @Test
    public void shouldReturnTypeFlags() {
        // given
        ResolvedOutcodeLocation resolvedLocation = new ResolvedOutcodeLocation("TW9", LATITUDE, LONGITUDE, Optional.absent());

        // when , then
        assertThat(resolvedLocation.isPostcode()).isFalse();
        assertThat(resolvedLocation.isOutcode()).isTrue();
        assertThat(resolvedLocation.isLocation()).isFalse();
        assertThat(resolvedLocation.isUnresolved()).isFalse();
    }
}
