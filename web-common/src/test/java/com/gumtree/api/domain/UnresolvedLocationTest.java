package com.gumtree.api.domain;

import com.google.common.base.Optional;
import com.gumtree.web.common.domain.location.UnresolvedLocation;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import static org.fest.assertions.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class UnresolvedLocationTest {

    @Test
    public void unresolvedLocationWithNullLocation() {
        // when
        UnresolvedLocation unresolvedLocation = new UnresolvedLocation(null);

        // then
        assertThat(unresolvedLocation.getLocation()).isEqualTo(Optional.absent());
        assertThat(unresolvedLocation.getIdName()).isEmpty();
        assertThat(unresolvedLocation.getSearchLocation()).isNull();
        assertThat(unresolvedLocation.supportsNearbySearch()).isFalse();
        assertThat(unresolvedLocation.supportsRadialSearch()).isFalse();
    }

    @Test
    public void unresolvedLocationWithNonNullLocation() {
        // when
        UnresolvedLocation unresolvedLocation = new UnresolvedLocation(" Staines Bus station ");

        // then
        assertThat(unresolvedLocation.getLocation()).isEqualTo(Optional.absent());
        assertThat(unresolvedLocation.supportsNearbySearch()).isFalse();
        assertThat(unresolvedLocation.supportsRadialSearch()).isFalse();
        assertThat(unresolvedLocation.getIdName()).isEqualTo("Staines+Bus+station");
        assertThat(unresolvedLocation.getSearchLocation()).isEqualTo("Staines Bus station");
    }

    @Test
    public void shouldReturnTypeFlags() {
        // given
        UnresolvedLocation unresolvedLocation = new UnresolvedLocation("staines bus station");

        // when , then
        assertThat(unresolvedLocation.isPostcode()).isFalse();
        assertThat(unresolvedLocation.isOutcode()).isFalse();
        assertThat(unresolvedLocation.isLocation()).isFalse();
        assertThat(unresolvedLocation.isUnresolved()).isTrue();
    }
}
