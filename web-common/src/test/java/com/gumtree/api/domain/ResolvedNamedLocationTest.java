package com.gumtree.api.domain;

import com.gumtree.web.common.domain.location.GeoPointLocation;
import com.gumtree.web.common.domain.location.Location;
import com.gumtree.web.common.domain.location.ResolvedNamedLocation;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ResolvedNamedLocationTest {
    private static final BigDecimal LONGITUDE = new BigDecimal(0.01);
    private static final BigDecimal LATITUDE = new BigDecimal(51.0);

    private static final Long LOCATION_ID = 99L;
    @Mock
    private Location location;

    @Before
    public void before() {
        when(location.getId()).thenReturn(LOCATION_ID);
        when(location.getLatitude()).thenReturn(LATITUDE);
        when(location.getLongitude()).thenReturn(LONGITUDE);
        when(location.getRadius()).thenReturn(BigDecimal.TEN);
    }

    @Test
    public void shouldGetLocationIdName() {
        // given
        when(location.getSeoName()).thenReturn("staines-upon-thames");
        when(location.getName()).thenReturn("Staines-upon-Thames, Surrey");
        when(location.getLocality()).thenReturn("Staines-upon-Thames");
        when(location.isLocalityUnique()).thenReturn(true);

        // when + then
        assertThat(new ResolvedNamedLocation(location).getIdName()).isEqualTo("staines-upon-thames");
        assertThat(new ResolvedNamedLocation(location).getSearchLocation()).isEqualTo(location.getLocality());
    }

    @Test
    public void shouldGetSearchLocationOfNonUniqueLocation() {
        // given
        when(location.getName()).thenReturn("Staines-upon-Thames, Surrey");
        when(location.getLocality()).thenReturn("Staines-upon-Thames");
        when(location.isLocalityUnique()).thenReturn(false);

        // when + then
        assertThat(new ResolvedNamedLocation(location).getSearchLocation()).isEqualTo(location.getName());
    }

    @Test
    public void shouldBeInstanceOfGeoPointLocation() {
        assertThat(new ResolvedNamedLocation(mock(Location.class))).isInstanceOf(GeoPointLocation.class);
    }

    @Test
    public void shouldSupportDistanceSearchIfLocationHasLatitude() {
        // given
        Location location = mock(Location.class);
        when(location.getLatitude()).thenReturn(BigDecimal.ONE);
        when(location.getLongitude()).thenReturn(BigDecimal.TEN);
        ResolvedNamedLocation resolvedLocation = new ResolvedNamedLocation(location);

        // when
        boolean supportsDistance = resolvedLocation.supportsRadialSearch();

        // then
        assertThat(supportsDistance).isTrue();
    }

    @Test
    public void shouldNotSupportDistanceSearchIfLocationHasNoLatitude() {
        // given
        Location location = mock(Location.class);
        ResolvedNamedLocation resolvedLocation = new ResolvedNamedLocation(location);

        // when
        boolean supportsDistance = resolvedLocation.supportsRadialSearch();

        // then
        assertThat(supportsDistance).isFalse();
    }

    @Test
    public void shouldSupportNearbySearchIfLocationHasLatitude() {
        // given
        Location location = mock(Location.class);
        when(location.getLatitude()).thenReturn(BigDecimal.ONE);
        when(location.getLongitude()).thenReturn(BigDecimal.TEN);
        ResolvedNamedLocation resolvedLocation = new ResolvedNamedLocation(location);

        // when
        boolean supports = resolvedLocation.supportsNearbySearch();

        // then
        assertThat(supports).isTrue();
    }

    @Test
    public void shouldNotSupportNearbySearchIfLocationHasNoLatitude() {
        // given
        Location location = mock(Location.class);
        ResolvedNamedLocation resolvedLocation = new ResolvedNamedLocation(location);

        // when
        boolean supports = resolvedLocation.supportsNearbySearch();

        // then
        assertThat(supports).isFalse();
    }

    @Test
    public void shouldReturnTypeFlags() {
        // given
        ResolvedNamedLocation resolvedLocation = new ResolvedNamedLocation(location);

        // when , then
        assertThat(resolvedLocation.isPostcode()).isFalse();
        assertThat(resolvedLocation.isOutcode()).isFalse();
        assertThat(resolvedLocation.isLocation()).isTrue();
        assertThat(resolvedLocation.isUnresolved()).isFalse();
    }
}
