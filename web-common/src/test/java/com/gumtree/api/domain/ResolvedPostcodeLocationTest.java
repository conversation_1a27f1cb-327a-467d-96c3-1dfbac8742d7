package com.gumtree.api.domain;

import com.google.common.base.Optional;
import com.gumtree.web.common.domain.location.Location;
import com.gumtree.web.common.domain.location.ResolvedPostcodeLocation;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.fest.assertions.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class ResolvedPostcodeLocationTest {
    private static final BigDecimal LONGITUDE = new BigDecimal(0.01);
    private static final BigDecimal LATITUDE = new BigDecimal(51.0);

    @Mock
    private Location location;

    private ResolvedPostcodeLocation resolvedLocation;

    @Before
    public void before() {
        resolvedLocation = new ResolvedPostcodeLocation("tw209pz", LATITUDE, LONGITUDE, Optional.of(location));
    }

    @Test
    public void locationIsNotMandatory() {
        // when
        ResolvedPostcodeLocation postcodeLocation = new ResolvedPostcodeLocation(" tw209Pz ", BigDecimal.ONE, BigDecimal.TEN, Optional.absent());

        // then
        assertThat(postcodeLocation.getLocation()).isEqualTo(Optional.absent());
        assertThat(postcodeLocation.getIdName()).isEqualTo("tw209pz");
        assertThat(postcodeLocation.getSearchLocation()).isEqualTo("TW209PZ");
        assertThat(postcodeLocation.getLatitude()).isEqualTo(java.util.Optional.of(BigDecimal.ONE));
        assertThat(postcodeLocation.getLongitude()).isEqualTo(java.util.Optional.of(BigDecimal.TEN));
        assertThat(postcodeLocation.supportsNearbySearch()).isTrue();
        assertThat(postcodeLocation.supportsRadialSearch()).isTrue();
    }

    @Test
    public void postcodeWithLocation() {
        // when
        ResolvedPostcodeLocation postcodeLocation = new ResolvedPostcodeLocation("tw209pz", BigDecimal.ONE, BigDecimal.TEN, Optional.of(location));

        // then
        assertThat(postcodeLocation.getLocation()).isEqualTo(Optional.of(location));
        assertThat(postcodeLocation.getIdName()).isEqualTo("tw209pz");
        assertThat(postcodeLocation.getSearchLocation()).isEqualTo("TW209PZ");
        assertThat(postcodeLocation.getLatitude()).isEqualTo(java.util.Optional.of(BigDecimal.ONE));
        assertThat(postcodeLocation.getLongitude()).isEqualTo(java.util.Optional.of(BigDecimal.TEN));
        assertThat(postcodeLocation.supportsNearbySearch()).isTrue();
        assertThat(postcodeLocation.supportsRadialSearch()).isTrue();
    }

    @Test
    public void shouldReturnTypeFlags() {
        // given
        ResolvedPostcodeLocation postcodeLocation = new ResolvedPostcodeLocation("tw209pz", BigDecimal.ONE, BigDecimal.TEN, Optional.absent());

        // when , then
        assertThat(postcodeLocation.isPostcode()).isTrue();
        assertThat(postcodeLocation.isOutcode()).isFalse();
        assertThat(postcodeLocation.isLocation()).isFalse();
        assertThat(postcodeLocation.isUnresolved()).isFalse();
    }
}
