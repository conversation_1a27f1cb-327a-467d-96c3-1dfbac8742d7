package com.gumtree.api.command.contactemails;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.UserApi;
import org.junit.Before;
import org.junit.Test;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class SetPreferredContactEmailCommandTest {

    private BushfireApi api;
    private UserApi userApi;

    @Before
    public void setUp() {
        api = mock(BushfireApi.class);
        userApi = mock(UserApi.class);
        when(api.create(eq(UserApi.class), any(BushfireApiKey.class))).thenReturn(userApi);
    }

    @Test
    public void updatesContactEmail() throws Exception {
        User user = new User();
        user.setId(23L);
        user.setApiKey(new Bushfire<PERSON><PERSON><PERSON>ey());
        SetPreferredContactEmailCommand cmd = new SetPreferredContactEmailCommand(user, "<EMAIL>", api);

        cmd.run();

        verify(userApi).setPreferredContactEmail(23L, "<EMAIL>");
    }
}
