package com.gumtree.service.advert.impl.bapi;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.ApiAdBuilder;
import com.gumtree.domain.advert.AdvertStatus;
import com.gumtree.domain.advert.BasicAdvert;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;

import java.util.Date;

import static com.gumtree.api.ApiAdBuilder.advert;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.collection.IsIterableContainingInOrder.contains;

public class ApiAdToBasicAdvertConverterTest {

    private ApiAdToBasicAdvertConverter apiAdToAdvertConverter;

    @Before
    public void setup() {
        apiAdToAdvertConverter = new ApiAdToBasicAdvertConverter(new ApiAdvertStatusToAdvertStatusConverter());
    }

    @Test
    public void shouldConvertTitleAndDescription() {
        Ad apiAdvert = liveAdvert().with().description("expected description").title("expected title").build();
        BasicAdvert advert = apiAdToAdvertConverter.convert(apiAdvert);
        assertThat(advert.getDescription(), is("expected description"));
        assertThat(advert.getTitle(), is("expected title"));
    }

    @Test
    public void shouldConvertId() {
        Ad apiAdvert = liveAdvert().with().id(1256L).build();
        BasicAdvert advert = apiAdToAdvertConverter.convert(apiAdvert);
        assertThat(advert.getId(), is(1256L));
    }

    @Test
    public void shouldConvertAdvertStatus() {
        Ad apiAdvert = advert().with().status(AdStatus.LIVE).build();
        apiAdvert.setStatus(AdStatus.LIVE);
        BasicAdvert advert = apiAdToAdvertConverter.convert(apiAdvert);
        assertThat(advert.getStatus(), is(AdvertStatus.LIVE));
    }

    @Test
    public void shouldConvertLiveDateIfThereIsOne() {
        Ad apiAdvert = liveAdvert().with().liveDate(new DateTime(1234567890L)).build();
        BasicAdvert advert = apiAdToAdvertConverter.convert(apiAdvert);
        assertThat(advert.getLiveDate(), is(new Date(1234567890L)));
    }

    @Test
    public void shouldConvertLiveDateWhenItsNotSet() {
        Ad apiAdvert = liveAdvert().with().liveDate(null).build();
        BasicAdvert advert = apiAdToAdvertConverter.convert(apiAdvert);
        assertThat(advert.getLiveDate(), is(nullValue()));
    }

    @Test
    public void shouldConvertLocation() {
        Ad apiAdvert = liveAdvert().with().locationId(887766L).locations(12345L, 789012L).build();
        BasicAdvert advert = apiAdToAdvertConverter.convert(apiAdvert);
        assertThat(advert.getLocationId(), is(887766));
        assertThat(advert.getLocationIds(), contains(12345, 789012));
    }

    @Test
    public void shouldConvertCategory() {
        Ad apiAdvert = liveAdvert().with().categoryId(123456789L).build();
        BasicAdvert advert = apiAdToAdvertConverter.convert(apiAdvert);
        assertThat(advert.getCategoryId(), is(123456789L));
    }

    private ApiAdBuilder liveAdvert(){
        return advert().with().status(AdStatus.LIVE);
    }
}
