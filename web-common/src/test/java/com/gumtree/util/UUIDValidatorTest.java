package com.gumtree.util;

import org.junit.Test;

import java.util.UUID;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;


public class UUIDValidatorTest {

    @Test
    public void shouldBeValid() {

        String existingValue = UUID.randomUUID().toString();
        assertThat(UUIDValidator.isValid(existingValue), is(true));
    }

    @Test
    public void shouldBeInvalid() {
        String existingValue = "blah";
        assertThat(UUIDValidator.isValid(existingValue), is(false));
    }

}