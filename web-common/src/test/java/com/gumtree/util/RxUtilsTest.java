package com.gumtree.util;

import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.api.WebApiResponse;
import org.junit.After;
import org.junit.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import rx.Single;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;


public class RxUtilsTest {

    private static PrintStream ORIGINAL_STD_OUT_STREAM = System.out;

    @After
    public void cleanUp() {
        System.setOut(ORIGINAL_STD_OUT_STREAM);
    }

    @Test
    public void shouldWrapPassInValueInWepApiResponseEntity() {
        // when
        ResponseEntity result = RxUtils.singleToWebApiResponseEntity(Single.just(100L), "");

        // then
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(result.getBody()).isEqualTo(new WebApiResponse<>(100L));
    }

    @Test
    public void shouldWrapPassInValueInResponseEntity() {
        // when
        ResponseEntity result = RxUtils.singleToResponseEntity(Single.just(100L), "");

        // then
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(result.getBody()).isEqualTo(100L);
    }

    @Test
    public void shouldWrapErrorInResponseEntity() {
        // when
        ResponseEntity result = RxUtils.singleToResponseEntity(
                Single.error(new RuntimeException("fake exception")), "Custom error message 12345");

        // then
        assertThat(result.getStatusCode()).isEqualTo(INTERNAL_SERVER_ERROR);
        assertThat(result.getBody()).isEqualTo(
                new WebApiErrorResponse(INTERNAL_SERVER_ERROR, "internal-error", "Custom error message 12345"));
    }

    @Test
    public void shouldWrapApiErrorResponseInResponseEntity() {
        WebApiErrorResponse error = new WebApiErrorResponse(BAD_REQUEST, "client-error", "test error");
        WebApiErrorException exception = new WebApiErrorException(error);

        // when
        ResponseEntity result = RxUtils.singleToResponseEntity(Single.error(exception), "Custom error message 12345");

        // then
        assertThat(result.getStatusCode()).isEqualTo(BAD_REQUEST);
        assertThat(result.getBody()).isEqualTo(error);
    }

    @Test
    public void shouldWrapApiErrorResponseInResponseEntityWithDebugLog() {
        ByteArrayOutputStream stdOutContent = mockStdOut();

        WebApiErrorResponse error = new WebApiErrorResponse(BAD_REQUEST, "client-error", "test error");
        WebApiErrorException exception = new WebApiErrorException(error, WebApiErrorException.LogLevel.DEBUG);

        // when
        ResponseEntity result = RxUtils.singleToResponseEntity(Single.error(exception), "Custom error message 12345");

        // then
        assertThat(result.getStatusCode()).isEqualTo(BAD_REQUEST);
        assertThat(result.getBody()).isEqualTo(error);

        // and
        assertThat(stdOutContent.toString())
                .contains("DEBUG com.gumtree.util.RxUtils - WebApiErrorException: Code : client-error; Message : test error");
    }

    @Test
    public void shouldWrapApiErrorResponseInResponseEntityWithWarningLog() {
        ByteArrayOutputStream stdOutContent = mockStdOut();

        WebApiErrorResponse error = new WebApiErrorResponse(BAD_REQUEST, "client-error", "test error");
        WebApiErrorException exception = new WebApiErrorException(error, WebApiErrorException.LogLevel.WARN);

        // when
        ResponseEntity result = RxUtils.singleToResponseEntity(Single.error(exception), "Custom error message 12345");

        // then
        assertThat(result.getStatusCode()).isEqualTo(BAD_REQUEST);
        assertThat(result.getBody()).isEqualTo(error);

        // and
        assertThat(stdOutContent.toString())
                .contains("WARN  com.gumtree.util.RxUtils - WebApiErrorException: Code : client-error; Message : test error");
    }

    @Test
    public void shouldWrapApiErrorResponseInResponseEntityWithNoLog() {
        ByteArrayOutputStream stdOutContent = mockStdOut();

        WebApiErrorResponse error = new WebApiErrorResponse(BAD_REQUEST, "client-error", "test error");
        WebApiErrorException exception = new WebApiErrorException(error);

        // when
        ResponseEntity result = RxUtils.singleToResponseEntity(Single.error(exception), "Custom error message 12345");

        // then
        assertThat(result.getStatusCode()).isEqualTo(BAD_REQUEST);
        assertThat(result.getBody()).isEqualTo(error);

        // and
        assertThat(stdOutContent.toString())
                .doesNotContain("com.gumtree.util.RxUtils - WebApiErrorException: Code : client-error; Message : test error");
    }

    @Test
    public void shouldWrapApiErrorResponseInResponseEntityWithErrorLog() {
        ByteArrayOutputStream stdOutContent = mockStdOut();

        WebApiErrorResponse error = new WebApiErrorResponse(BAD_REQUEST, "client-error", "test error");
        WebApiErrorException exception = new WebApiErrorException(error, WebApiErrorException.LogLevel.ERROR);

        // when
        ResponseEntity result = RxUtils.singleToResponseEntity(Single.error(exception), "Custom error message 12345");

        // then
        assertThat(result.getStatusCode()).isEqualTo(BAD_REQUEST);
        assertThat(result.getBody()).isEqualTo(error);

        // and
        assertThat(stdOutContent.toString())
                .contains("ERROR com.gumtree.util.RxUtils - A WebApiErrorException occurred.");
        assertThat(stdOutContent.toString())
                .contains("WebApiErrorException: Code : client-error; Message : test error");
    }

    @Test
    public void shouldWrapRawValueInApiResponse() throws Exception {
        // when
        CompletableFuture<ResponseEntity> result = RxUtils.singleToWebApiResponseFuture(Single.just(100L));

        // then
        ResponseEntity responseEntity = result.get();
        assertThat(responseEntity.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(responseEntity.getBody()).isEqualTo(new WebApiResponse(100L));
    }

    @Test
    public void shouldReturnErrorResponseWrappedInsideException() throws ExecutionException, InterruptedException {
        // given
        WebApiErrorResponse error = new WebApiErrorResponse(BAD_REQUEST, "client-error", "test error");
        WebApiErrorException exception = new WebApiErrorException(error);

        // when
        CompletableFuture<ResponseEntity> response = RxUtils.singleToWebApiResponseFuture(Single.error(exception));

        // then
        ResponseEntity responseEntity = response.get();
        assertThat(responseEntity.getStatusCode()).isEqualTo(BAD_REQUEST);
        assertThat(responseEntity.getBody()).isEqualTo(error);
    }

    @Test
    public void shouldReturnGenericErrorResponseForUnknownException() throws ExecutionException, InterruptedException {
        // when
        CompletableFuture<ResponseEntity> response = RxUtils.singleToWebApiResponseFuture(Single.error(new RuntimeException("¯|_(ツ)_/¯")));

        // then
        ResponseEntity responseEntity = response.get();
        assertThat(responseEntity.getStatusCode()).isEqualTo(INTERNAL_SERVER_ERROR);
        assertThat(responseEntity.getBody()).isEqualTo(new WebApiErrorResponse(INTERNAL_SERVER_ERROR, "internal-error", "Something went wrong"));
    }

    @Test
    public void shouldWrapOptionalRawValueInResponseEntity() {
        // when
        ResponseEntity result = RxUtils.singleOfOptToResponseEntity(Single.just(Optional.of(100L)), "User not found");

        // then
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(result.getBody()).isEqualTo(100L);
    }

    @Test
    public void shouldWrapEmptyOptionalInApiErrorResponse() {
        // when
        ResponseEntity result = RxUtils.singleOfOptToResponseEntity(Single.just(Optional.empty()), "User not found");

        // then
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
        assertThat(result.getBody()).isEqualTo(new WebApiErrorResponse(HttpStatus.NOT_FOUND, "not-found", "User not found"));
    }

    @Test
    public void shouldWrapErrorInResponseEntityIfOptionalValueIsExpected() {
        // when
        ResponseEntity result = RxUtils.singleOfOptToResponseEntity(
                Single.error(new RuntimeException("fake exception")), "Custom error message 12345");

        // then
        assertThat(result.getStatusCode()).isEqualTo(INTERNAL_SERVER_ERROR);
        assertThat(result.getBody()).isEqualTo(
                new WebApiErrorResponse(INTERNAL_SERVER_ERROR, "internal-error", "Unexpected error"));
    }

    private ByteArrayOutputStream mockStdOut() {
        ByteArrayOutputStream outContent = new ByteArrayOutputStream();
        PrintStream printStream = new PrintStream(outContent);
        System.setOut(printStream);
        return outContent;
    }
}