package com.gumtree.mobile.web.seo.metadata;

import org.junit.Test;

import java.util.List;
import java.util.Set;

import static com.google.common.collect.Sets.newHashSet;
import static com.gumtree.mobile.web.seo.metadata.YamlCategorySeoConfigs.generatePageKeys;
import static com.gumtree.mobile.web.seo.metadata.YamlCategorySeoConfigs.isOptionalKey;
import static com.gumtree.mobile.web.seo.metadata.YamlCategorySeoConfigs.keyItemWithDirectiveRemoved;
import static org.fest.assertions.api.Assertions.assertThat;

public class YamlCategorySeoConfigsTest {

    @Test
    public void generatePageKeysFromPageKeyWithoutOptionalKeyItem() {
        // given

        // when
        List<Set<String>> pages = generatePageKeys(newHashSet("a", "b"), 0);

        // then
        assertThat(pages).containsExactly(newHashSet("a", "b"));
    }

    @Test
    public void generatePageKeysFromPageKeyWithOptionalKeyItem() {
        assertThat(generatePageKeys(newHashSet("OPT(a)", "b", "OPT(c)"), 0))
                .containsOnly(
                        newHashSet("a", "b", "c"),
                        newHashSet("a", "b"),
                        newHashSet("b", "c")
        );

        assertThat(generatePageKeys(newHashSet("OPT(a)", "b", "c"), 0))
                .containsOnly(newHashSet("a", "b", "c"));
    }

    @Test
    public void shouldCorrectlyIdentifyOptionalKeyItem() {
        assertThat(isOptionalKey("OPT(key)")).isTrue();
        assertThat(isOptionalKey("OPT(key")).isFalse();
        assertThat(isOptionalKey("key")).isFalse();
        assertThat(isOptionalKey("OPT(key]")).isFalse();
        assertThat(isOptionalKey("opt(key)")).isFalse();
    }

    @Test
    public void shouldGetOptionalKeyItemName() {
        assertThat(keyItemWithDirectiveRemoved("OPT(key)")).isEqualTo("key");
        assertThat(keyItemWithDirectiveRemoved("key")).isEqualTo("key");
        assertThat(keyItemWithDirectiveRemoved("OPT(key")).isEqualTo("OPT(key");
        assertThat(keyItemWithDirectiveRemoved("OPT(key]")).isEqualTo("OPT(key]");
        assertThat(keyItemWithDirectiveRemoved("opt(key)")).isEqualTo("opt(key)");
    }
}