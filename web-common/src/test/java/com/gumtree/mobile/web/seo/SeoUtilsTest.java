package com.gumtree.mobile.web.seo;

import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class SeoUtilsTest {

    @Test
    public void convertTextToSeoUrlFriendlyValue() {
        assertThat(SeoUtils.getSeoUrlFriendlyValueOf("Cafe Crêperie")).isEqualTo("cafe-cr-perie");
        assertThat(SeoUtils.getSeoUrlFriendlyValueOf("*** 200£ per/day +20£. ***")).isEqualTo("-200£-per-day-20£.-");
    }
}