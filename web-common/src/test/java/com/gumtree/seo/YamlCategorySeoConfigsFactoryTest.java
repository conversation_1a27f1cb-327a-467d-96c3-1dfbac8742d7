package com.gumtree.seo;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class YamlCategorySeoConfigsFactoryTest {

    @Test(expected = IllegalArgumentException.class)
    public void shouldThrownExceptionIfConfigFileIsNotSet() throws IOException {
        new YamlCategorySeoConfigFactory(null).getNewInstance();
    }

    @Test
    public void shouldGetPageConfigForRootCategory() throws IOException {
        // given
        CategorySeoConfig configs = factory("category-configs/single-category.yaml").getNewInstance();

        // when
        Map<String, String> pageConfig = configs.getPageConfig(Lists.newArrayList("all"), Sets.newHashSet("category", "search"));

        // then
        assertThat(pageConfig, is(
                pageConfig("title for search in all categories", "description for search in category", "h1 for search in category")));
    }

    @Test
    public void shouldGetPageConfigForConfiguredLevel1Category() throws IOException {
        // given
        CategorySeoConfig configs = factory("category-configs/single-category.yaml").getNewInstance();

        // when
        Map<String, String> pageConfig = configs.getPageConfig(Lists.newArrayList("all", "for-sale"), Sets.newHashSet("category", "search"));

        // then
        assertThat(pageConfig, is(
                pageConfig("title for search in for sale", "description for search in for sale", "h1 for search in for sale")));
    }

    @Test
    public void shouldGetPageConfigForNonConfiguredLevel1CategoryPage() throws IOException {
        // given
        CategorySeoConfig configs = factory("category-configs/single-category.yaml").getNewInstance();

        // when
        Map<String, String> pageConfig = configs.getPageConfig(Lists.newArrayList("all", "for-sale"), new HashSet<String>());

        // then
        assertThat(pageConfig, is(pageConfig("title", "description", "h1")));
    }

    @Test
    public void shouldGetPageConfigForNotConfiguredLevel2Category() throws IOException {
        // given
        CategorySeoConfig configs = factory("category-configs/single-category.yaml").getNewInstance();

        // when
        Map<String, String> pageConfig = configs.getPageConfig(Lists.newArrayList("all", "for-sale", "tickets"),
                Sets.newHashSet("category", "search"));

        // then
        assertThat(pageConfig, is(
                pageConfig("title for search in for sale", "description for search in for sale", "h1 for search in for sale")));
    }

    @Test
    public void shouldUseFallbackConfigIfConfigDoesNotExist() throws IOException {
        // given
        YamlCategorySeoConfigFactory factory = factory("non-existing.yaml", "category-configs/single-category.yaml");

        // when
        CategorySeoConfig config = factory.getNewInstance();

        // then
        assertThat(config.getPageConfig(Lists.newArrayList("all"), new HashSet<String>()).size(), is(3));
    }

    @Test(expected = FileNotFoundException.class)
    public void shouldThrowExceptionIfConfigIsNotFound() throws IOException {
        factory("non-existing.yaml").getNewInstance();
    }

    @Test(expected = FileNotFoundException.class)
    public void shouldThrowExceptionIfBothConfigAreNotFound() throws IOException {
        factory("non-existing.yaml", "other-non-existing.yaml").getNewInstance();
    }

    private YamlCategorySeoConfigFactory factory(String config) {
        return new YamlCategorySeoConfigFactory(new ClassPathResource(config));
    }

    private YamlCategorySeoConfigFactory factory(String configFile, String fallbackConfig) {
        return new YamlCategorySeoConfigFactory(new ClassPathResource(configFile), new ClassPathResource(fallbackConfig));
    }

    private Map<String, String> pageConfig(String title, String description, String h1) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("seoTitle", title);
        map.put("seoDescription", description);
        if (h1 != null) {
            map.put("seoH1", h1);
        }

        return map;
    }
}
