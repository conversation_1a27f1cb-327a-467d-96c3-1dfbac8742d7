package com.gumtree.seo;

import com.google.common.collect.Lists;
import org.junit.Test;
import org.mockito.internal.util.collections.Sets;

import java.util.ArrayList;
import java.util.Set;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;


public class SEOPagePropertyTest {

    @Test
    public void shouldGetPageIdProperties() {
        ArrayList<SEOPageProperty> expected = Lists.newArrayList(
                SEOPageProperty.CATEGORY,
                SEOPageProperty.LOCATION,
                SEOPageProperty.PAGINATION,
                SEOPageProperty.SEARCH_TERM,
                SEOPageProperty.BLANK_SEARCH_TERM,
                SEOPageProperty.VIP,
                SEOPageProperty.EMPLOYER,
                SEOPageProperty.POPULAR_SEARCH,
                SEOPageProperty.CAR_MAKE,
                SEOPageProperty.CAR_MODEL,
                SEOPageProperty.BIKE_MAKE);
        Set<SEOPageProperty> set = Sets.newSet(SEOPageProperty.PAGE_ID_PROPERTIES.toArray(new SEOPageProperty[0]));

        assertThat(set.size(), is(expected.size()));
        for (SEOPageProperty prop : expected) {
            assertThat(set.contains(prop), is(true));
        }
    }
}
