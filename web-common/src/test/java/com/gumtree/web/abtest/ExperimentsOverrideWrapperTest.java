package com.gumtree.web.abtest;

import com.gumtree.web.cookie.cutters.abtest.ExperimentsOverrideCookie;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.Map;

import static org.fest.assertions.api.Assertions.assertThat;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ExperimentsOverrideWrapperTest {

    @Mock
    private ExperimentsProvider baseExperimentsProvider;

    @Mock
    private HttpServletRequest request;

    @Before
    public void setup() {
        Map<String, String> baseExperimentsMap = new HashMap<>();
        baseExperimentsMap.put("LAB-TEST-01", "A");
        baseExperimentsMap.put("LAB-TEST-02", "B");
        when(baseExperimentsProvider.get()).thenReturn(new Experiments(baseExperimentsMap));
    }

    @Test
    public void shouldReturnBaseResponseIfNoCookie() {
        // when
        Experiments experiments = getTestInstance().get();

        // then
        assertThat(experiments.getExperiments()).isEqualTo(new HashMap<String, String>(){
            {
                put("LAB-TEST-01", "A");
                put("LAB-TEST-02", "B");
            }
        });
    }

    @Test
    public void shouldOverrideExistingValue() {
        // given
        Cookie cookie = new Cookie(ExperimentsOverrideCookie.NAME, "LAB-TEST-01:Wg==");
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});

        // when
        Experiments experiments = getTestInstance().get();


        // then
        assertThat(experiments.getExperiments()).isEqualTo(new HashMap<String, String>(){
            {
                put("LAB-TEST-01", "Z");
                put("LAB-TEST-02", "B");
            }
        });
    }

    @Test
    public void shouldOverrideExistingValueByRemoving() {
        // given
        Cookie cookie = new Cookie(ExperimentsOverrideCookie.NAME, "LAB-TEST-01:Tk9ORQ==|LAB-TEST-02:Wg==");
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});

        // when
        Experiments experiments = getTestInstance().get();


        // then
        assertThat(experiments.getExperiments()).isEqualTo(new HashMap<String, String>(){
            {
                put("LAB-TEST-02", "Z");
            }
        });
    }

    private ExperimentsOverrideWrapper getTestInstance() {
        return new ExperimentsOverrideWrapper("", baseExperimentsProvider, request);
    }
}