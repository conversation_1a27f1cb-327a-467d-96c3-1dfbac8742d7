package com.gumtree.web.filter;

import com.gumtree.common.properties.GtPropManager;
import com.gumtree.config.MwebProperty;
import java.io.IOException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class ClickJackingFilterTest {

    private final static String CONTENT_SECURITY_POLICY_HEADER_NAME = "Content-Security-Policy";
    private final static String CONTENT_SECURITY_POLICY_TEST_VALUE = "frame-ancestors https://test.gumtree.com/";

    @Mock
    private HttpServletRequest request;

    @Mock
    private ServletResponse response;

    @Mock
    private FilterChain chain;

    private ClickjackingFilter filter;

    @Before
    public void setUp() throws Exception {
        GtPropManager.setProperty(MwebProperty.CONTENT_SECURITY_POLICY.getPropertyName(),
                CONTENT_SECURITY_POLICY_TEST_VALUE);

        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        filter = new ClickjackingFilter();
    }

    @Test
    public void doFilterAddsContentSecurityPolicyHeader() throws ServletException, IOException {
        filter.doFilter(request, response, chain);
        HttpServletResponse httpResponse = (HttpServletResponse)response;

        assert(httpResponse.getHeader(CONTENT_SECURITY_POLICY_HEADER_NAME)).equals(CONTENT_SECURITY_POLICY_TEST_VALUE);
    }

}
