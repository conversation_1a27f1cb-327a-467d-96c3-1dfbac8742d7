package com.gumtree.web.common.page.context;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.helper.DisplayAdsViewMode;
import com.gumtree.web.abtest.AbTestType;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.common.page.handler.PageHandler;
import com.gumtree.web.common.page.model.FooterModel;
import com.gumtree.web.common.page.model.HeaderModel;
import com.gumtree.web.common.page.model.PageModelFactory;
import com.gumtree.web.common.util.ExecutorServiceFactory;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.mobile.device.Device;
import org.springframework.mobile.device.DeviceResolver;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

import static com.gumtree.domain.category.Categories.ALL;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class GumtreeCommonPageContextTest {

    private GumtreeCommonPageContext pageContext;

    private HttpServletRequest request;

    private HttpServletResponse response;

    private PageHandler pageHandler;

    private GumtreePage pageAnnotation;

    private LocationService locationService;

    private CategoryService categoryService;

    private PageModelFactory pageModelFactory;

    private Map<String, Object> model;

    private ExecutorServiceFactory executorServiceFactory;

    private ExecutorService executorService;

    private HeaderModel headerModel;

    private FooterModel footerModel;

    private Category allCategory;

    private DeviceResolver deviceResolver;

    private Device device;


    @Before
    public void init() {
        pageContext = mock(GumtreeCommonPageContext.class, Mockito.CALLS_REAL_METHODS);

        request = mock(HttpServletRequest.class);
        response = mock(HttpServletResponse.class);
        pageHandler = mock(PageHandler.class);
        locationService = mock(LocationService.class);
        categoryService = mock(CategoryService.class);
        allCategory = mock(Category.class);
        when(categoryService.getByUniqueName(ALL.getSeoName())).thenReturn(Optional.of(allCategory));
        pageModelFactory = mock(PageModelFactory.class);
        model = mock(Map.class);
        executorServiceFactory = mock(ExecutorServiceFactory.class);
        executorService = mock(ExecutorService.class);
        deviceResolver = mock(DeviceResolver.class);
        device = mock(Device.class);

        pageAnnotation = mock(GumtreePage.class);
        when(pageAnnotation.value()).thenReturn(PageType.VIP);
        when(pageAnnotation.parallelisation()).thenReturn(3);
        when(pageHandler.getPageAnnotation()).thenReturn(pageAnnotation);
        when(executorServiceFactory.newFixedThreadPool(3)).thenReturn(executorService);

        when(request.getScheme()).thenReturn("http");
        when(request.getServerName()).thenReturn("www.gumtree.com");
        when(request.getServerPort()).thenReturn(80);
        when(request.getRequestURI()).thenReturn("/a/b/c");
        when(request.getHeader("Referer")).thenReturn("pageReferrer");
        when(request.getHeader("user-agent")).thenReturn("unknown-device");
        when(deviceResolver.resolveDevice(any(HttpServletRequest.class))).thenReturn(device);
        when(device.isMobile()).thenReturn(false);
        when(device.isTablet()).thenReturn(false);

        headerModel = mock(HeaderModel.class);
        footerModel = mock(FooterModel.class);
        when(pageModelFactory.createHeaderModel(pageContext, pageAnnotation)).thenReturn(headerModel);
        when(pageModelFactory.createFooterModel(pageContext, pageAnnotation)).thenReturn(footerModel);

        ReflectionTestUtils.setField(pageContext, "locationService", locationService);
        ReflectionTestUtils.setField(pageContext, "categoryService", categoryService);
        ReflectionTestUtils.setField(pageContext, "pageModelFactory", pageModelFactory);
        ReflectionTestUtils.setField(pageContext, "executorServiceFactory", executorServiceFactory);
        ReflectionTestUtils.setField(pageContext, "deviceResolver", deviceResolver);
    }

    @Test
    public void executorServiceCreatedWithCorrectNumberOfThreads() {
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getExecutorService(), equalTo(executorService));
    }

    @Test
    public void executorServiceShutdownOnDestroy() throws Exception {
        pageContext.init(request, response, pageHandler);
        pageContext.destroy();
        verify(executorService).shutdown();
    }

    @Test
    public void destroyDoesNotFailWhenContextHasNotBeenInitialised() throws Exception {
        pageContext.destroy();
    }

    @Test(expected = IllegalArgumentException.class)
    public void throwsExceptionIfPageAnnotationIsNull() {
        when(pageHandler.getPageAnnotation()).thenReturn(null);
        pageContext.init(request, response, pageHandler);
    }

    @Test
    public void isInitialisedIsFalseBeforeInitIsCalled() {
        assertThat(pageContext.isInitialised(), equalTo(false));
    }

    @Test
    public void isInitialisedIsTrueAfterInitIsCalled() {
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.isInitialised(), equalTo(true));
    }

    @Test(expected = IllegalStateException.class)
    public void onceInitialisedCallingInitThrowsException() {
        pageContext.init(request, response, pageHandler);
        pageContext.init(request, response, pageHandler);
    }

    @Test
    public void pageTypeIsSetCorrectly() {
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getPageType(), equalTo(PageType.VIP));
    }

    @Test
    public void countyIsNullByDefault() {
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getCounty(), nullValue());
    }

    @Test
    public void getLocationHierarchy() {
        Location location = mock(Location.class);
        Map locationHierarchy = mock(Map.class);
        when(locationService.getLocationHierarchy(location)).thenReturn(locationHierarchy);
        pageContext.init(request, response, pageHandler);
        pageContext.setLocation(location);
        assertThat(pageContext.getLocationHierarchy(), equalTo(locationHierarchy));
    }

    @Test
    public void l1CategoryIsNullByDefault() {
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getL1Category(), nullValue());
    }

    @Test
    public void categoryHierarchyIsEmptyByDefault() {
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getCategoryLevelHierarchy().size(), equalTo(0));
    }

    @Test
    public void absoluteUrlGeneratedCorrectly() {
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getAbsoluteRequestUrl().toString(), equalTo("http://www.gumtree.com/a/b/c"));
    }

    @Test
    public void absoluteUrlGeneratedCorrectlyWithNonDefaultPort() {
        when(request.getServerPort()).thenReturn(8080);
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getAbsoluteRequestUrl().toString(), equalTo("http://www.gumtree.com:8080/a/b/c"));
    }

    @Test
    public void absoluteUrlGeneratedCorrectlyWithAQueryString() {
        when(request.getQueryString()).thenReturn("a=b&c=d");
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getAbsoluteRequestUrl().toString(), equalTo("http://www.gumtree.com/a/b/c?a=b&c=d"));
    }

    @Test
    public void absoluteUrlGeneratedCorrectlyWithAQueryStringWithNonDefaultPort() {
        when(request.getServerPort()).thenReturn(8080);
        when(request.getQueryString()).thenReturn("a=b&c=d");
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getAbsoluteRequestUrl().toString(), equalTo("http://www.gumtree.com:8080/a/b/c?a=b&c=d"));
    }

    @Test
    public void referrerGeneratedCorrectly() {
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getReferrer(), equalTo("pageReferrer"));
    }

    @Test
    public void defaultCategoryIsAll() {
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getCategory(), equalTo(allCategory));
    }

    @Test
    public void deviceTypeDefaultsToDesktopComputer() {
        pageContext.init(request, response, pageHandler);
        assertThat(pageContext.getDeviceType(), equalTo("desktop-computer"));
    }

    @Test
    public void headerModelIsCreatedAndAddedToViewModelCorrectly() {
        pageContext.init(request, response, pageHandler);
        pageContext.populateModel(model, pageHandler);
        verify(model).put("headerModel", headerModel);
    }

    @Test
    public void footerModelIsCreatedAndAddedToViewModelCorrectly() {
        pageContext.init(request, response, pageHandler);
        pageContext.populateModel(model, pageHandler);
        verify(model).put("footerModel", footerModel);
    }

    @Test
    public void escapesKeyword() {
        pageContext.setSearchTerm(";\"\"></iframe></div><script>prompt(1)</script>");
        assertThat(pageContext.getSearchTerm(),
                equalTo(";&quot;&quot;&gt;&lt;/iframe&gt;&lt;/div&gt;&lt;script&gt;prompt(1)&lt;/script&gt;"));
    }

    @Test
    public void setDisplayAdsViewMode() {
        pageContext.setDisplayAdsViewMode(DisplayAdsViewMode.TEXTLINK_ACTIVE);
        assertThat(pageContext.getDisplayAdsViewMode(), equalTo(DisplayAdsViewMode.TEXTLINK_ACTIVE));
    }

    @Test
    public void setPageNumber() {
        pageContext.setPageNumber(23);
        assertThat(pageContext.getPageNumber(), equalTo(23));
    }

    @Test
    public void setAbTestTypes() {
        List<AbTestType> abTestTypes = Lists.newArrayList();
        pageContext.setAbTestTypes(abTestTypes);
        assertThat(pageContext.getAbTestTypes().equals(abTestTypes), is(true));
    }
}
