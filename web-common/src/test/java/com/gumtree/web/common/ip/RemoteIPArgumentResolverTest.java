package com.gumtree.web.common.ip;

import org.junit.Before;
import org.junit.Test;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebArgumentResolver;
import org.springframework.web.context.request.NativeWebRequest;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class RemoteIPArgumentResolverTest {

    private RemoteIPArgumentResolver argumentResolver;

    private NativeWebRequest nativeWebRequest;

    private HttpServletRequest httpServletRequest;

    @Before
    public void init() {
        nativeWebRequest = mock(NativeWebRequest.class);
        httpServletRequest = mock(HttpServletRequest.class);
        argumentResolver = new RemoteIPArgumentResolver();
        when(nativeWebRequest.getNativeRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getRemoteAddr()).thenReturn("***********");
    }

    @Test
    public void supportsRemoteIPMethodParameter() {
        MethodParameter parameter = mock(MethodParameter.class);
        when(parameter.getParameterType()).thenReturn((Class) RemoteIP.class);
        assertThat(argumentResolver.supportsParameter(parameter), equalTo(true));
    }

    @Test
    public void doesNotSupportMethodParametersThatAreNotOfTypeGumtreePageContext() {
        MethodParameter parameter = mock(MethodParameter.class);
        when(parameter.getParameterType()).thenReturn((Class) Valid.class);
        assertThat(argumentResolver.supportsParameter(parameter), equalTo(false));
    }

    @Test
    public void returnsRemoteIPFromHttpServletRequest() throws Exception {
        RemoteIP remoteIP = (RemoteIP) argumentResolver.resolveArgument(null, null, nativeWebRequest, null);
        assertThat(remoteIP.getIpAddress(), equalTo("***********"));
    }

    @Test
    public void returnsRemoteIPFromTrueGumIp() throws Exception {
        when(httpServletRequest.getHeader("true-gum-ip")).thenReturn("***********");
        RemoteIP remoteIP = (RemoteIP) argumentResolver.resolveArgument(null, null, nativeWebRequest, null);
        assertThat(remoteIP.getIpAddress(), equalTo("***********"));
    }

    @Test
    public void returnsRemoteIPFromXForwardedFor() throws Exception {
        when(httpServletRequest.getHeader("x-forwarded-for")).thenReturn("***********, ********");
        RemoteIP remoteIP = (RemoteIP) argumentResolver.resolveArgument(null, null, nativeWebRequest, null);
        assertThat(remoteIP.getIpAddress(), equalTo("***********"));
    }

    @Test
    public void returnsRemoteIPWhenInvalidHeaders() throws Exception {
        when(httpServletRequest.getHeader("true-gum-ip")).thenReturn("");
        when(httpServletRequest.getHeader("x-forwarded-for")).thenReturn("");
        RemoteIP remoteIP = (RemoteIP) argumentResolver.resolveArgument(null, null, nativeWebRequest, null);
        assertThat(remoteIP.getIpAddress(), equalTo("***********"));
    }
}
