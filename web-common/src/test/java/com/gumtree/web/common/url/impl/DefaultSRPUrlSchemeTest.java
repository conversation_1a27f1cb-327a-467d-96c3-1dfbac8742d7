package com.gumtree.web.common.url.impl;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableList;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.newattribute.Attribute;
import com.gumtree.domain.newattribute.internal.DefaultAttribute;
import com.gumtree.domain.newattribute.internal.value.TextValue;
import com.gumtree.search.UserGeoLocation;
import com.gumtree.search.UserSearchKeywords;
import com.gumtree.search.UserSearchLocation;
import com.gumtree.search.UserSearchRefinement;
import com.gumtree.search.sorting.SortProperty;
import com.gumtree.search.sorting.Sorting;
import com.gumtree.service.category.CategoryService;
import com.gumtree.util.http.UrlParameter;
import com.gumtree.util.model.UserSearch;
import com.gumtree.util.url.SRPUrlScheme;
import com.gumtree.util.url.impl.DefaultSRPUrlScheme;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;

import static com.gumtree.domain.category.Categories.ALL;
import static com.gumtree.domain.category.Categories.CARS;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class DefaultSRPUrlSchemeTest extends AbstractSRPUrlSchemeTest {

    @InjectMocks
    private DefaultSRPUrlScheme urlScheme;

    @Mock
    private CategoryService categoryService;

    @Mock
    private Location location;

    @Mock
    Category categoryRoot;


    @Before
    public void setUp() {
        super.setUp();
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(urlScheme, "buyerBaseUri", "http://www.gumtree.com");
        when(categoryService.getByUniqueName(ALL.getSeoName())).thenReturn(Optional.of(categoryRoot));
        when(location.getName()).thenReturn("uk");
        when(location.getDisplayName()).thenReturn("United Kingdom");
        when(categoryRoot.getSeoName()).thenReturn("all");
    }

    @Test
    public void shouldAddDistanceToUrlParamsIfPresent() {
        // given
        UserSearchRefinement refinement = new UserSearchRefinement.Builder().distance(Optional.of(10D)).build();

        // when
        List<UrlParameter> urlParams = urlScheme.getUrlParametersFor(refinement);

        // then
        assertThat(urlParams, hasSize(1));
        assertThat(urlParams.contains(new UrlParameter(SRPUrlScheme.DISTANCE, "10.0")), is(true));
    }

    @Test
    public void shouldNotAddDistanceToUrlParamsIfNotPresent() {
        // given
        UserSearchRefinement refinement =
                new UserSearchRefinement.Builder().distance(Optional.<Double>absent()).build();

        // when
        List<UrlParameter> urlParams = urlScheme.getUrlParametersFor(refinement);

        // then
        assertThat(urlParams, hasSize(0));
    }

    @Test
    public void shouldAddMaxAdvertAgeToUrlParamsIfPresent() {
        // given
        UserSearchRefinement refinement = new UserSearchRefinement.Builder().maxAdvertAge(Optional.of(10)).build();

        // when
        List<UrlParameter> urlParams = urlScheme.getUrlParametersFor(refinement);

        // then
        assertThat(urlParams, hasSize(1));
        assertThat(urlParams.contains(new UrlParameter(SRPUrlScheme.MAX_ADVERT_AGE, "10")), is(true));
    }

    @Test
    public void shouldAddParamsIfPresent() {
        // given
        UserSearchRefinement refinement = new UserSearchRefinement.Builder()
                .maxAdvertAge(Optional.of(10))
                .adsWithImagesOnly(true)
                .urgentAdsOnly(true)
                .searchInDescription(true)
                .featuredAdsOnly(true)
                .build();

        // when
        List<UrlParameter> urlParams = urlScheme.getUrlParametersFor(refinement);

        // then
        assertThat(urlParams, hasSize(5));
        assertThat(urlParams.contains(new UrlParameter(SRPUrlScheme.MAX_ADVERT_AGE, "10")), is(true));
        assertThat(urlParams.contains(new UrlParameter(SRPUrlScheme.URGENT_ADS_ONLY, "Y")), is(true));
        assertThat(urlParams.contains(new UrlParameter(SRPUrlScheme.WITH_IMAGES_ONLY, "Y")), is(true));
        assertThat(urlParams.contains(new UrlParameter(SRPUrlScheme.SEARCH_SCOPE, "all")), is(true));
        assertThat(urlParams.contains(new UrlParameter(SRPUrlScheme.FEATURED, "Y")), is(true));
    }

    @Test
    public void shouldNotMaxAdvertAgeToUrlParamsIfNotPresent() {
        // given
        UserSearchRefinement refinement =
                new UserSearchRefinement.Builder().maxAdvertAge(Optional.<Integer>absent()).build();

        // when
        List<UrlParameter> urlParams = urlScheme.getUrlParametersFor(refinement);

        // then
        assertThat(urlParams, hasSize(0));
    }

    @Test
    public void shouldReturnMobileUrlWithLocation() {
        UserSearch userSearch = new UserSearch.Builder()
                .locationSearch(new UserSearchLocation(location))
                .build();

        assertEquals("http://m.gumtree.com/all/uk",
                urlScheme.getForMobile(userSearch));

    }

    @Test
    public void shouldReturnMobileUrlWithCategory() {
        UserSearch userSearch = new UserSearch.Builder()
                .category(categoryService.getByUniqueName(ALL.getSeoName()).get())
                .build();

        assertEquals("http://m.gumtree.com/all/uk",
                urlScheme.getForMobile(userSearch));

    }

    @Test
    public void shouldReturnMobileUrlWithCategoryAndLocation() {
        UserSearch userSearch = new UserSearch.Builder()
                .locationSearch(new UserSearchLocation(location))
                .category(categoryService.getByUniqueName(ALL.getSeoName()).get())
                .build();

        assertEquals("http://m.gumtree.com/all/uk",
                urlScheme.getForMobile(userSearch));

    }

    @Test
    public void shouldReturnMobileUrlWithSearchQuery() {
        UserSearch userSearch = new UserSearch.Builder()
                .keywordsSearch(new UserSearchKeywords("red", Collections.EMPTY_SET, Collections.EMPTY_SET))
                .locationSearch(new UserSearchLocation(location))
                .category(categoryService.getByUniqueName(ALL.getSeoName()).get())
                .build();

        assertEquals("http://m.gumtree.com/all/uk/red",
                urlScheme.getForMobile(userSearch));
    }

    @Test
    public void shouldReturnPaginatedURLForRadialSearch() {
        // given
        UserSearch userSearch = new UserSearch.Builder()
                .geoLocationSearch(Optional.of(UserGeoLocation.builder().postcode("TW209PZ").build()))
                .keywordsSearch(new UserSearchKeywords("bwm", Collections.EMPTY_SET, Collections.EMPTY_SET))
                .locationSearch(new UserSearchLocation(location))
                .category(newAllCategory())
                .refinement(new UserSearchRefinement.Builder().distance(Optional.of(50.0)).build())
                .build();

        // when
        String url = urlScheme.getFor(userSearch, 2, null);

        // then
        assertEquals("http://www.gumtree.com/all/TW209PZ/bwm/page2?distance=50.0", url);
    }

    @Test
     public void shouldReturnCarMakeModelURL() {
        // given
        final Category landRoverCategory = Category.newCategory().withSeoName("land-rover").build();
        UserSearch userSearch = new UserSearch.Builder()
                .category(landRoverCategory)
                .refinement(new UserSearchRefinement.Builder()
                        .attributes(ImmutableList.<Attribute>of(
                                new DefaultAttribute(CategoryConstants.Attribute.VEHICLE_MAKE.getName(), TextValue.create("land_rover", true)),
                                new DefaultAttribute(CategoryConstants.Attribute.VEHICLE_MODEL.getName(), TextValue.create("range_rover", true))))
                        .build())
                .build();

        // when
        when(categoryService.getParent(landRoverCategory)).thenReturn(Optional.of(newCarsCategory()));
        String url = urlScheme.getFor(userSearch, 1, null);

        // then
        assertEquals("http://www.gumtree.com/cars/land-rover/range-rover", url);
    }

    @Test
    public void shouldReturnCarMakeModelURLForMobile() {
        // given
        final Category landRoverCategory = Category.newCategory().withSeoName("cars").build();
        UserSearch userSearch = new UserSearch.Builder()
                .category(landRoverCategory)
                .refinement(new UserSearchRefinement.Builder()
                        .attributes(ImmutableList.<Attribute>of(
                                new DefaultAttribute(CategoryConstants.Attribute.VEHICLE_MAKE.getName(), TextValue.create("land_rover", true)),
                                new DefaultAttribute(CategoryConstants.Attribute.VEHICLE_MODEL.getName(), TextValue.create("range_rover", true))))
                        .build())
                .build();

        // when
        when(categoryService.getParent(landRoverCategory)).thenReturn(Optional.of(newCarsCategory()));
        String url = urlScheme.getForMobile(userSearch);

        // then
        assertEquals("http://m.gumtree.com/cars/land-rover/range-rover/uk", url);
    }

    @Test
    public void shouldNotMakeModelUrlIfMakeIsMissing() {
        // given
        UserSearch userSearch = new UserSearch.Builder()
                .category(newCarsCategory())
                .refinement(new UserSearchRefinement.Builder()
                        .attributes(ImmutableList.<Attribute>of(
                                new DefaultAttribute(CategoryConstants.Attribute.VEHICLE_MODEL.getName(), TextValue.create("range_rover", true))))
                        .build())
                .build();

        // when
        String url = urlScheme.getFor(userSearch, 1, null);

        // then
        assertEquals("http://www.gumtree.com/cars", url);
    }

    @Test
    public void shouldReturnPaginatedURLForLocationSearch() {
        // given
        UserSearch userSearch = new UserSearch.Builder()
                .keywordsSearch(new UserSearchKeywords("bwm", Collections.EMPTY_SET, Collections.EMPTY_SET))
                .locationSearch(new UserSearchLocation(location))
                .category(newAllCategory())
                .refinement(new UserSearchRefinement.Builder().distance(Optional.of(50.0)).build())
                .build();

        // when
        String url = urlScheme.getFor(userSearch, 7, null);

        // then
        assertEquals("http://www.gumtree.com/all/uk/bwm/page7?distance=50.0", url);
    }

    @Test
    public void shouldReturnPaginatedURLWithNoDistanceIfNotDistance() {
        // given
        UserSearch userSearch = new UserSearch.Builder()
                .geoLocationSearch(Optional.of(UserGeoLocation.builder().postcode("TW209PZ").build()))
                .keywordsSearch(new UserSearchKeywords("bwm", Collections.EMPTY_SET, Collections.EMPTY_SET))
                .locationSearch(new UserSearchLocation(location))
                .category(newAllCategory())
                .build();

        // when
        String url = urlScheme.getFor(userSearch, 2, null);

        // then
        assertEquals("http://www.gumtree.com/all/TW209PZ/bwm/page2", url);
    }

    @Test
    public void shouldReturnPaginatedURLWithNoDistanceIfDistanceIsCero() {
        // given
        UserSearch userSearch = new UserSearch.Builder()
                .geoLocationSearch(Optional.of(UserGeoLocation.builder().postcode("TW209PZ").build()))
                .keywordsSearch(new UserSearchKeywords("bwm", Collections.EMPTY_SET, Collections.EMPTY_SET))
                .locationSearch(new UserSearchLocation(location))
                .category(newAllCategory())
                .refinement(new UserSearchRefinement.Builder().distance(Optional.of(0.0)).build())
                .build();

        // when
        String url = urlScheme.getFor(userSearch, 2, null);

        // then
        assertEquals("http://www.gumtree.com/all/TW209PZ/bwm/page2", url);
    }

    @Test
    public void shouldReturnCorrectUrlForLocationTreeNode() {
        // given
        UserSearch userSearch = new UserSearch.Builder()
                .geoLocationSearch(Optional.of(UserGeoLocation.builder().postcode("TW209PZ").build()))
                .keywordsSearch(new UserSearchKeywords("bwm", Collections.EMPTY_SET, Collections.EMPTY_SET))
                .locationSearch(new UserSearchLocation(location("london")))
                .category(newAllCategory())
                .build();

        // when
        String url = urlScheme.getFor(userSearch, location("london"));

        // then
        assertEquals("http://www.gumtree.com/all/london/bwm", url);
    }

    @Test
    public void shouldReturnCorrectUrlForLocationTreeNodeWithSorting() {
        // given
        UserSearch userSearch = new UserSearch.Builder()
                .keywordsSearch(new UserSearchKeywords("bwm", Collections.EMPTY_SET, Collections.EMPTY_SET))
                .locationSearch(new UserSearchLocation(location("london")))
                .category(newAllCategory())
                .build();

        // when
        String url = urlScheme.getFor(userSearch, new Sorting(SortProperty.PRICE, true));

        // then
        assertEquals("http://www.gumtree.com/all/london/bwm?sort_by=price&sort_order=increasing", url);
    }

    @Test
    public void shouldReturnCorrectUrlForLocationTreeNodeWithDistance() {
        // given
        UserSearch userSearch = new UserSearch.Builder()
                .geoLocationSearch(Optional.of(UserGeoLocation.builder().postcode("TW209PZ").build()))
                .keywordsSearch(new UserSearchKeywords("bwm", Collections.EMPTY_SET, Collections.EMPTY_SET))
                .locationSearch(new UserSearchLocation(location("london")))
                .refinement(new UserSearchRefinement.Builder().distance(Optional.of(50.0)).build())
                .category(newAllCategory())
                .build();

        // when
        String url = urlScheme.getFor(userSearch, location("london"));

        // then
        assertEquals("http://www.gumtree.com/all/london/bwm?distance=50.0", url);
    }

    private Location location(String name) {
        Location location = mock(Location.class);
        when(location.getName()).thenReturn("london");
        return location;
    }

    private Category newAllCategory() {
        Category category = new Category();
        category.setSeoName(ALL.getSeoName());
        return category;
    }

    private Category newCarsCategory() {
        Category category = new Category();
        category.setSeoName(CARS.getSeoName());
        return category;
    }

    @Test
    public void shouldNotIncludeDistanceParameterIfNotDistance() {
        //given
        UserSearch userSearch = new UserSearch.Builder()
                .category(newAllCategory())
                .build();

        //when
        String url = urlScheme.getFor(userSearch);

        //Then
        assertEquals("http://www.gumtree.com/all", url);
    }

    @Test
    public void shouldNotIncludeDistanceParameterIfDistanceIsCero() {
        //given
        UserSearch userSearch = new UserSearch.Builder()
                .refinement(new UserSearchRefinement.Builder().distance(Optional.of(0.0)).build())
                .category(newAllCategory())
                .build();

        //when
        String url = urlScheme.getFor(userSearch);

        //Then
        assertEquals("http://www.gumtree.com/all", url);
    }

    @Test
    public void shouldIncludeDistanceParameterIfDistance() {
        //given
        UserSearch userSearch = new UserSearch.Builder()
                .refinement(new UserSearchRefinement.Builder().distance(Optional.of(50.0)).build())
                .category(newAllCategory())
                .build();

        //when
        String url = urlScheme.getFor(userSearch);

        //Then
        assertEquals("http://www.gumtree.com/all?distance=50.0", url);
    }

    @Test
    public void shouldGetFixedUrlForCategory() {
        Category category = mock(Category.class);
        when(category.getUrl()).thenReturn("some_url");

        assertThat(urlScheme.getFor(category, location), is("some_url"));
    }

    @Test
    public void shouldGetUrlForCategoryAndLocation() {
        assertThat(urlScheme.getFor(categoryRoot, location), is("http://www.gumtree.com/all"));
    }

    @Test
    public void shouldGetUrlForLocation() {
        assertThat(urlScheme.getFor(location), is("http://www.gumtree.com/all"));
    }
}
