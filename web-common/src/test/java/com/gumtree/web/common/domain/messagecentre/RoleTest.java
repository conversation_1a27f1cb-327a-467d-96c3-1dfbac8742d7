package com.gumtree.web.common.domain.messagecentre;

import org.junit.Test;

import java.util.Optional;

import static org.fest.assertions.api.Assertions.assertThat;

public class RoleTest {

    @Test
    public void shouldGetRoleFromString() {
        assertThat(Role.fromStr("buyer")).isEqualTo(Optional.of(Role.Buyer));
        assertThat(Role.fromStr("Buyer")).isEqualTo(Optional.of(Role.Buyer));
        assertThat(Role.fromStr("BUYER")).isEqualTo(Optional.of(Role.Buyer));
        assertThat(Role.fromStr("seller")).isEqualTo(Optional.of(Role.Seller));
        assertThat(Role.fromStr(null)).isEqualTo(Optional.empty());
        assertThat(Role.fromStr("abc")).isEqualTo(Optional.empty());
        assertThat(Role.fromStr("buyer1")).isEqualTo(Optional.empty());
        assertThat(Role.fromStr("sellerX")).isEqualTo(Optional.empty());
    }
}