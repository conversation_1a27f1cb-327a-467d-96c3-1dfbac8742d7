package com.gumtree.web.common.url.impl;

import com.google.common.base.Optional;
import com.gumtree.api.Ad;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.advert.entity.AdvertEntity;
import com.gumtree.domain.advert.entity.PriceEntity;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.ImageSize;
import com.gumtree.domain.media.entity.ImageEntity;
import com.gumtree.domain.user.User;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.CdnImageUrlProvider;
import com.gumtree.util.url.SRPUrlScheme;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.util.url.impl.UrlSchemeImpl;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Currency;
import java.util.HashMap;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class UrlSchemeImplTest extends AbstractSRPUrlSchemeTest {

    private final LocationService mockLocationService = mock(LocationService.class);

    private final SRPUrlScheme mockedSRPUrlScheme = mock(SRPUrlScheme.class);

    private final Location mockedLocation = mock(Location.class);

    private CategoryService categoryService;

    private  CdnImageUrlProvider cdnImageUrlProvider ;
    @Before
    public void setup() {
        super.setUp();
        categoryService = mock(CategoryService.class);
        cdnImageUrlProvider=mock(CdnImageUrlProvider.class);
        when(categoryService.getById(anyLong())).thenReturn(Optional.<Category>absent());
    }

    @Test
    public void testBushfirePostAdUrlForNullCategory() {
        String url = getUrlScheme().bushfirePostAdUrlFor(null);
        assertThat(url, equalTo("https://secure.seller.test.com/postad"));
    }

    @Test
    public void testBushfirePostAdUrlForCategoryIdWithNullCategory() {
        String url = getUrlScheme().bushfirePostAdUrlForCategoryId(null);
        assertThat(url, equalTo("https://secure.seller.test.com/postad"));
    }

    @Test
    public void testBushfirePostAdUrlForCategoryIdWithUnknownCategory() {
        when(categoryService.getById(1L)).thenReturn(Optional.<Category>absent());
        String url = getUrlScheme().bushfirePostAdUrlForCategoryId(1L);
        assertThat(url, equalTo("https://secure.seller.test.com/postad"));
    }

    @Test
    public void testBushfirePostAdUrlForAllCategory() {
        Category cat = createCategory(1, Categories.ALL.getSeoName(), "All", null);
        String url = getUrlScheme().bushfirePostAdUrlFor(cat);
        assertThat(url, equalTo("https://secure.seller.test.com/postad"));
    }

    @Test
    public void testBushfirePostAdUrlForNonNullCategory() {
        Category cat = createCategory(20, "test-cat", "Test Cat", null);
        String url = getUrlScheme().bushfirePostAdUrlFor(cat);
        assertThat(url, equalTo("https://secure.seller.test.com/postad?categoryId=20"));
    }

    @Test
    public void testBushfirePostEventUrl() {
        String url = getUrlScheme().bushfirePostEventUrl();
        assertThat(url, equalTo("https://secure.seller.test.com/postad?categoryId=21"));
    }

    @Test
    public void testEditAdUrlGeneration() {
        Ad ad = new Ad();
        ad.setId(10L);
        String url = getUrlScheme().editUrlFor(ad);
        assertThat(url, equalTo("https://secure.seller.test.com/postad?advertId=10"));
    }

    @Test
    public void testPostAdUrlForCategoryAndLocation() {
        Location location = createLocation(1, "clapham", "Clapham", false);
        Category category = createCategory(1, "events-nightlife", "Events & Nightlife", "");
        assertThat(getUrlScheme().postAdUrlFor(location, category),
                equalTo("http://seller.test.com/postad"));
    }

    @Test
    public void testPlainPostAdUrl() {
        assertThat(getUrlScheme().postAdUrl(), equalTo("http://seller.test.com/postad"));
    }

    @Test
    public void testPostAdUrlForCategory() {
        Category category = createCategory(1, "events-nightlife", "Events & Nightlife", "");
        assertThat(getUrlScheme().postAdUrlFor(category), equalTo("http://seller.test.com/postad"));
    }

    @Test
    public void testPostAdUrlForLocation() {
        assertThat(getUrlScheme().postAdUrlFor(createLocation(1, "clapham", "Clapham", false)),
                equalTo("http://seller.test.com/postad"));
    }

    @Test
    public void testUrlForLocation() {
        assertThat(getUrlScheme().urlFor(createLocation(1, "clapham", "Clapham", false)), equalTo("http://buyer.test.com/all/clapham"));
    }

    @Test
    public void testUrlForLocationForUK() {
        assertThat(getUrlScheme().urlFor(createLocation(1, "uk", "United Kingdom", false)), equalTo("http://buyer.test.com/uk"));
    }

    @Test
    public void testUrlForLandingLocation() {
        assertThat(getUrlScheme().urlFor(createLocation(1, "clapham", "Clapham", true)), equalTo("http://buyer.test.com/clapham"));
    }

    @Test
    public void testUrlForCategoryWithLocation() {
        assertThat(getUrlScheme().urlForCategory(createCategory(1, "cars", "Cars", null),
                createLocation(1, "clapham", "Clapham", true)), equalTo("http://buyer.test.com/c/cars/clapham"));
    }

    @Test
    public void testUrlForCategoryWithUKLocation() {
        assertThat(getUrlScheme().urlForCategory(createCategory(1, "cars", "Cars", null),
                createLocation(1, "uk", "United Kingdom", true)), equalTo("http://buyer.test.com/c/cars"));
    }

    @Test
    public void testUrlForCategoryWithNonEnabledCategory() {
        when(mockedSRPUrlScheme.getFor(any(Category.class), any(Location.class))).thenReturn("http://buyer.test.com/pets");

        assertThat(getUrlScheme().urlForCategory(createCategory(1, "pets", "Pets", null),
                createLocation(1, "uk", "United Kingdom", true)), equalTo("http://buyer.test.com/pets"));
    }

    @Test
    public void testUrlForCategoryWithFixedUrl() {
        assertThat(getUrlScheme().urlForCategory(createCategory(1, "cars", "Cars", "fixedUrl"),
                createLocation(1, "clapham", "Clapham", true)), equalTo("fixedUrl"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUrlForLocationNullLocation() {
        getUrlScheme().urlFor((Location) null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUrlForCategoryWithLocationNullCategory() {
        getUrlScheme().urlForCategory(null, mock(Location.class));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUrlForCategoryWithLocationNullLocation() {
        getUrlScheme().urlForCategory(mock(Category.class), null);
    }

    @Test
    public void testUrlForAdvert() {
        int golfCatId = 1;

        // given
        Advert advert = createAdvert(1l, "Volkswagen Golf", "London", golfCatId, 1l);
        Category golfCat = Category.newCategory().withId((long) golfCatId).withSeoName("golf-seo-name").build();
        when(categoryService.getById((long)golfCatId)).thenReturn(Optional.of(golfCat));

        // when + then
        assertThat(getUrlScheme().urlFor(advert), equalTo("http://buyer.test.com/p/golf-seo-name/volkswagen-golf/1"));
    }

    public void testUrlForAdvertWithBogusCategory() {
        Advert advert = createAdvert(1l, "Volkswagen Golf", "London", 999999, 1l);

        String url = getUrlSchemeWithNullCategory().urlFor(advert);
        assertThat(url, equalTo("http://buyer.test.com/p/all/volkswagen-golf/1"));
    }

    @Test
    public void testUrlForAdvertWithHTML() {
        int golfCatId = 1;

        // given
        Advert advert = createAdvert(1l, "Volkswagen <b>Golf</b>", "London", golfCatId, 1l);
        Category golfCat = Category.newCategory().withId((long) golfCatId).withSeoName("golf-seo-name").build();
        when(categoryService.getById((long)golfCatId)).thenReturn(Optional.of(golfCat));

        // when + then
        assertThat(getUrlScheme().urlFor(advert), equalTo("http://buyer.test.com/p/golf-seo-name/volkswagen-bgolfb/1"));
    }

    @Test
    public void testUrlForAdvertWithNonAlphanumericCharacters() {
        int golfCatId = 1;

        // given
        Advert advert = createAdvert(1l, "$$Volkswagen Golf!", "London", golfCatId, 1l);
        Category golfCat = Category.newCategory().withId((long) golfCatId).withSeoName("golf-seo-name").build();
        when(categoryService.getById((long)golfCatId)).thenReturn(Optional.of(golfCat));

        // when + then
        assertThat(getUrlScheme().urlFor(advert), equalTo("http://buyer.test.com/p/golf-seo-name/volkswagen-golf/1"));
    }

    @Test
    public void testUrlForAdvertWithNonAlphanumericWords() {
        int golfCatId = 1;

        // given
        Advert advert = createAdvert(1l, "$$ Volkswagen * Golf!", "London", golfCatId, 1l);
        Category golfCat = Category.newCategory().withId((long) golfCatId).withSeoName("golf-seo-name").build();
        when(categoryService.getById((long)golfCatId)).thenReturn(Optional.of(golfCat));

        // when + then
        assertThat(getUrlScheme().urlFor(advert), equalTo("http://buyer.test.com/p/golf-seo-name/volkswagen-golf/1"));
    }

    @Test
    public void testThumbnailUrlForAdvertSingleDomain() {
        Advert advert = createAdvert(1l, "Volkswagen Golf", "London", 1, 1l);
        assertThat(getUrlScheme().thumbnailUrlFor(advert.getMainImage()), equalTo("http://is.gumtree.com/image/moreadsthumb/1.jpg"));
    }

    @Test
    public void testThumbnailUrlForAdvertMultipleDomain() {
        Advert advert = createAdvert(1l, "Volkswagen Golf", "London", 1, 1000002l);
        UrlSchemeImpl urlScheme = getUrlScheme();
        assertThat(urlScheme.thumbnailUrlFor(advert.getMainImage()),
                equalTo("http://is02.gumtree.com/image/moreadsthumb/1000002.jpg"));
    }

    @Test
    public void testThumbnailUrlForAdvertNoImage() {
        Advert advert = createAdvert(1l, "Volkswagen Golf", "London", 1, null);
        assertThat(getUrlScheme().thumbnailUrlFor(advert.getMainImage()), equalTo(""));
    }

    @Test
    public void testThumbnailUrlFromImageUrl() {
        when(cdnImageUrlProvider.getImageUrl("https://imagedelivery.net/ePR8PyKf84wPHx7_RYmEag/aac25f17-4d19-4fc8-0e28-500c14949d00/1",ImageSize.MAXITHUMB)).thenReturn("https://imagedelivery.net/ePR8PyKf84wPHx7_RYmEag/aac25f17-4d19-4fc8-0e28-500c14949d00/5");
        assertThat(getUrlScheme().thumbnailUrlFor("https://imagedelivery.net/ePR8PyKf84wPHx7_RYmEag/aac25f17-4d19-4fc8-0e28-500c14949d00/1"),
                equalTo("https://imagedelivery.net/ePR8PyKf84wPHx7_RYmEag/aac25f17-4d19-4fc8-0e28-500c14949d00/5"));

    }
    @Test
    public void testPreviewUrlFromImageUrl() {
        when(cdnImageUrlProvider.getImageUrl("http://i.ebayimg.com/00/s/MTUwWDIwMA==/$T2eC16h,!)UE9s3wEgrtBRHLvmINeg~~48_1.JPG",ImageSize.PREVIEW)).thenReturn("http://i.ebayimg.com/00/s/MTUwWDIwMA==/$T2eC16h,!)UE9s3wEgrtBRHLvmINeg~~48_81.JPG");
        assertThat(getUrlScheme().previewUrlFor("http://i.ebayimg.com/00/s/MTUwWDIwMA==/$T2eC16h,!)UE9s3wEgrtBRHLvmINeg~~48_1.JPG"),
                equalTo("http://i.ebayimg.com/00/s/MTUwWDIwMA==/$T2eC16h,!)UE9s3wEgrtBRHLvmINeg~~48_81.JPG"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUrlForAdvertNullLocation() {
        getUrlScheme().urlFor((Advert) null);
    }

    @Test
    public void testPostEventUrl() {
        Location location = createLocation(1, "brisbane", "Brisbane", false);
        assertThat(getUrlScheme().postEventUrlFor(location),
                equalTo("http://seller.test.com/postad"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testViewEventsUrlForNullLocation() {
        getUrlScheme().viewEventsUrlFor(null);
    }

    @Test
    public void testViewEventsUrl() {
        Location location = createLocation(1, "brisbane", "Brisbane", false);
        Category eventsCategory = Category.newCategory().withId(CategoryConstants.EVENTS_ID).build();
        when(categoryService.getById(CategoryConstants.EVENTS_ID)).thenReturn(Optional.of(eventsCategory));
        when(mockedSRPUrlScheme.getFor(eventsCategory, location)).thenReturn("blablabla-link");

        String url = getUrlScheme().viewEventsUrlFor(location);

        assertThat(url, equalTo("blablabla-link"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRssEventsUrlForNullLocation() {
        getUrlScheme().rssEventsUrlFor(null);
    }

    @Test
    public void testRssEventsUrl() {
        Location location = createLocation(1, "brisbane", "Brisbane", false);
        assertThat(
                getUrlScheme().rssEventsUrlFor(location),
                equalTo("http://buyer.test.com/rssfeed/all/brisbane"));
    }
    @Test
    public void testRssEventsLondon() {
        Location location = createLocation(1, "london", "London", true);
        assertThat(
                getUrlScheme().rssEventsUrlFor(location),
                equalTo("http://buyer.test.com/rssfeed/all/london"));
    }

    @Test
    public void testPostAdUrl() {
        Location location = createLocation(1, "brisbane", "Brisbane", false);
        Category category = createCategory(1, "cheese-rolling", "Cheese rolling", null);
        assertThat(getUrlScheme().postAdUrlFor(location, category),
                equalTo("http://seller.test.com/postad"));
    }

    @Test
    public void testUrlsForImageNull() {
        Image image = null;
        Map<ImageSize, String> imageUrls = getUrlScheme().urlsFor(image);
        assertThat(imageUrls.isEmpty(), equalTo(true));
    }

    @Test
    public void testUrlsForImagesIdNull() {
        Image image = mock(Image.class);
        when(image.getId()).thenReturn(null);
        Map<ImageSize, String> imageUrls = getUrlScheme().urlsFor(image);
        assertThat(imageUrls.isEmpty(), equalTo(true));
    }

    @Test
    public void testUrlsForImages() {
        Image image = mock(Image.class);
        when(image.getId()).thenReturn(2l);
        Map<ImageSize, String> imageUrls = getUrlScheme().urlsFor(image);
        assertThat(imageUrls.get(ImageSize.THUMB), equalTo("http://is.gumtree.com/image/thumb/2.jpg"));
        assertThat(imageUrls.get(ImageSize.MAIN), equalTo("http://is.gumtree.com/image/big/2.jpg"));
        assertThat(imageUrls.get(ImageSize.FULL), equalTo("http://is.gumtree.com/image/extrabig/2.jpg"));
    }

    /**
     * Tests that category id and location name are part of
     * the url.
     */
    @Test
    public final void emailAlertLinkContainsUbercatAndLocationname() {
        Long categoryId = 42L;
        String locationname = "London";

        Advert mockAdvert = mock(Advert.class);
        Location mockAdLocation = mock(Location.class);
        Location mockLandingLocation = mock(Location.class);

        when(mockAdvert.getCategoryId()).thenReturn(categoryId);
        when(mockLocationService.getById(anyInt())).thenReturn(mockAdLocation);
        when(mockLocationService.getLanding(mockAdLocation)).thenReturn(mockLandingLocation);
        when(mockLandingLocation.getDisplayName()).thenReturn("London");

        String url = getUrlScheme().emailAlertsUrlFor(categoryId, mockAdLocation);
        assertThat(url, containsString(categoryId.toString()));
        assertThat(url, containsString(locationname));
    }

    @Test
    public final void generatesNotNullSRPAlertEmailURL() {
        String url = getUrlScheme().emailAlertsUrlFor(null, null);
        assertThat(url != null, equalTo(true));

    }


    @Test
    public final void generatesAlertEmailURLWithNoCategory() {
        when(mockLocationService.getLanding(mockedLocation)).thenReturn(mockedLocation);
        when(mockedLocation.getDisplayName()).thenReturn("London");

        String url = getUrlScheme().emailAlertsUrlFor(null, mockedLocation);
        assertThat(url, equalTo("http://alerts.gumtree.com/?search_location=London"));
    }

    @Test
    public final void generatesSRPAlertEmailURLWithLocationAndCategory() {
        when(mockLocationService.getLanding(mockedLocation)).thenReturn(mockedLocation);
        when(mockedLocation.getDisplayName()).thenReturn("London");
        String url = getUrlScheme().emailAlertsUrlFor(130L, mockedLocation);
        assertThat(url, equalTo("http://alerts.gumtree.com/?search_location=London#cat-130"));
    }

    @Test
    public final void testBushfireReplyUrlFor() {

        Category mockCategory1 = mock(Category.class);
        Category mockCategory2 = mock(Category.class);
        Category mockCategory3 = mock(Category.class);
        when(mockCategory1.getId()).thenReturn(100L);
        when(mockCategory2.getId()).thenReturn(200L);
        when(mockCategory3.getId()).thenReturn(300L);
        Map<Integer, Category> mockCategoryMap = new HashMap<Integer, Category>();
        mockCategoryMap.put(1, mockCategory1);
        mockCategoryMap.put(2, mockCategory2);
        mockCategoryMap.put(3, mockCategory3);

        Advert mockAdvert = mock(Advert.class);
        when(mockAdvert.getId()).thenReturn(2000000L);
        when(mockAdvert.getCategoryId()).thenReturn(300L);
        when(categoryService.getById(300L)).thenReturn(Optional.of(mockCategory3));
        when(categoryService.getLevelHierarchy(mockCategory3)).thenReturn(mockCategoryMap);

        String url = getUrlScheme().replyUrlFor(mockAdvert);
        assertThat(url, equalTo("http://reply.test.com/reply?advertId=2000000"));
    }

    @Test
    public final void testReplyUrlForNoEmailAddress() {
        Advert mockAdvert = mock(Advert.class);
        User user = mock(User.class);
        when(user.getEmailAddress()).thenReturn(null);
        when(mockAdvert.getPostingUser()).thenReturn(user);
        when(mockAdvert.getId()).thenReturn(2000000L);
        String url = getUrlScheme().replyUrlFor(mockAdvert);
        assertThat(url, equalTo("http://reply.test.com/reply?advertId=2000000"));
    }

    @Test
    public final void testReplyUrlForNullUser() {
        Advert mockAdvert = mock(Advert.class);
        when(mockAdvert.getPostingUser()).thenReturn(null);
        when(mockAdvert.getId()).thenReturn(2000000L);
        String url = getUrlScheme().replyUrlFor(mockAdvert);
        assertThat(url, equalTo("http://reply.test.com/reply?advertId=2000000"));
    }

    @Test
    public final void generatesHttpsUrlForSecureSellerAction() {
        assertThat(getUrlScheme().urlFor(Actions.BUSHFIRE_LOGIN), equalTo("https://secure.seller.test.com/login"));
    }

    @Test
    public void shouldCreateUrlForReportingAdvert() {
        assertThat(getUrlScheme().urlToReportAd(123456L), equalTo("http://buyer.test.com/report-ad/123456"));
    }

    private Category createCategory(Integer id, String name, String displayName, String fixedUrl) {
        Category category = mock(Category.class);
        when(category.getId()).thenReturn((long) id);
        when(category.getSeoName()).thenReturn(name);
        when(category.getName()).thenReturn(displayName);
        when(category.getUrl()).thenReturn(fixedUrl);
        return category;
    }

    private Location createLocation(Integer id, String name, String displayName, boolean isLanding) {
        Location location = mock(Location.class);
        when(location.getId()).thenReturn(id);
        when(location.getName()).thenReturn(name);
        when(location.getDisplayName()).thenReturn(displayName);
        when(location.isLanding()).thenReturn(isLanding);
        return location;
    }

    private Advert createAdvert(Long id, String title, String location, int categoryId, Long imageId) {
        AdvertEntity advert = new AdvertEntity();
        advert.setId(id);
        advert.setTitle(title);
        advert.setLocationText(location);
        advert.setCategoryId((long) categoryId);

        ImageEntity image = new ImageEntity();
        image.setId(imageId);
        advert.setMainImage(image);

        PriceEntity price = new PriceEntity();
        price.setCurrency(Currency.getInstance("GBP"));
        price.setAmount(new BigDecimal(0));
        advert.setPrice(price);

        return advert;
    }

    private UrlSchemeImpl getUrlScheme() {
        UrlSchemeImpl urlScheme = new UrlSchemeImpl();
        ReflectionTestUtils.setField(urlScheme, "numberOfImageDomains", 4);
        ReflectionTestUtils.setField(urlScheme, "imageThreshold", 1000000);
        ReflectionTestUtils.setField(urlScheme, "imageDomain", "http://is.gumtree.com");
        ReflectionTestUtils.setField(urlScheme, "categoryLandingPagesEnabled", Arrays.asList("cars", "jobs"));
        ReflectionTestUtils.setField(urlScheme, "locationService", mockLocationService);
        ReflectionTestUtils.setField(urlScheme, "srpUrlScheme", mockedSRPUrlScheme);
        Category category = createCategory(1, "cars-vans-motorbikes", "Cars, Vans & Motorbikes", null);
        Category rootCategory = createCategory(1, "all", "All Categories", null);
        when(categoryService.getRootCategory()).thenReturn(rootCategory);
        when(categoryService.getL1Category(anyLong())).thenReturn(Optional.of(category));
        Category eventsCategory = createCategory(2, "events-gigs-nightlife", "Events, Gigs & Nightlife", null);
        when(categoryService.getByUniqueName("events-gigs-nightlife")).thenReturn(Optional.of(eventsCategory));
        ReflectionTestUtils.setField(urlScheme, "categoryService", categoryService);
        ReflectionTestUtils.setField(urlScheme, "buyerBaseUri", "http://buyer.test.com");
        ReflectionTestUtils.setField(urlScheme, "sellerBaseUri", "http://seller.test.com");
        ReflectionTestUtils.setField(urlScheme, "sellerSecureBaseUri", "https://secure.seller.test.com");
        ReflectionTestUtils.setField(urlScheme, "replyBaseUri", "http://reply.test.com");
        ReflectionTestUtils.setField(urlScheme, "cdnImageUrlProvider", cdnImageUrlProvider);
        return urlScheme;
    }

    private UrlScheme getUrlSchemeWithNullCategory() {
        UrlScheme urlScheme = new UrlSchemeImpl();
        ReflectionTestUtils.setField(urlScheme, "numberOfImageDomains", 4);
        ReflectionTestUtils.setField(urlScheme, "imageThreshold", 1000000);
        ReflectionTestUtils.setField(urlScheme, "imageDomain", "http://is.thegumtree.com");
        CategoryService categoryService = mock(CategoryService.class);
        Category rootCategory = createCategory(1, "all", "All Categories", null);
        when(categoryService.getRootCategory()).thenReturn(rootCategory);

        when(categoryService.getById(anyLong())).thenReturn(Optional.<Category>absent());
        when(categoryService.getL1Category(anyLong())).thenReturn(Optional.<Category>absent());
        Category eventsCategory = createCategory(2, "events-gigs-nightlife", "Events, Gigs & Nightlife", null);
        when(categoryService.getByUniqueName("events-gigs-nightlife")).thenReturn(Optional.of(eventsCategory));
        ReflectionTestUtils.setField(urlScheme, "categoryService", categoryService);
        ReflectionTestUtils.setField(urlScheme, "buyerBaseUri", "http://buyer.test.com");
        return urlScheme;
    }

    @Test
    public void testEmbedVideoUrl() {
        UrlScheme urlScheme = new UrlSchemeImpl();
        String embedUrl = urlScheme.urlForYoutubeEmbed("http://www.youtube.com/watch?v=dLpqIw0Wjoo");
        assertThat(embedUrl, equalTo("http://www.youtube.com/embed/dLpqIw0Wjoo?rel=0&modestbranding=1&controls=0&wmode=transparent"));
    }

    @Test
    public void testVideoThumbnailUrl() {
        UrlScheme urlScheme = new UrlSchemeImpl();
        String embedUrl = urlScheme.urlForYoutubeThumbnail("http://www.youtube.com/watch?v=dLpqIw0Wjoo");
        assertThat(embedUrl, equalTo("http://img.youtube.com/vi/dLpqIw0Wjoo/2.jpg"));
    }

    @Test
    public void testPackageUsageUrl() {
        assertThat(getUrlScheme().urlForPackageUsageHistory(110), equalTo("https://secure.seller.test.com/manage/company/packages/110"));
    }
}
