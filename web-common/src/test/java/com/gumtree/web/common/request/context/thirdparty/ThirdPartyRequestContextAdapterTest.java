package com.gumtree.web.common.request.context.thirdparty;

import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.util.helper.DisplayAdsViewMode;
import com.gumtree.web.abtest.AbTestType;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContextAdapter;
import com.gumtree.zeno.core.domain.PageType;
import org.hamcrest.CoreMatchers;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Test;

import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gumtree.domain.category.Categories.FLATS_AND_HOUSES;
import static com.gumtree.api.category.domain.CategoryConstants.FLATS_AND_HOUSES_ID;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ThirdPartyRequestContextAdapterTest {

    private GumtreePageContext pageContext;

    @Before
    public void init() {
        pageContext = mock(GumtreePageContext.class);

        when(pageContext.getCategory()).thenReturn(mock(Category.class));
    }

    @Test
    public void returnsCorrectPageType() {
        when(pageContext.getPageType()).thenReturn(PageType.VIP);
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getPageType(), equalTo(PageType.VIP));
    }

    @Test
    public void returnsCorrectLocation() {
        Location location = mock(Location.class);
        when(pageContext.getLocation()).thenReturn(location);
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getLocation(), equalTo(location));
    }

    @Test
    public void returnsCorrectCounty() {
        Location location = mock(Location.class);
        when(pageContext.getCounty()).thenReturn(location);
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCounty(), equalTo(location));
    }

    @Test
    public void returnsCorrectCategory() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        Category category = createCategory(2, "test-category");
        when(pageContext.getCategory()).thenReturn(category);
        when(pageContext.getCategoryLevelHierarchy()).thenReturn(createHierarchy(root, category));
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCategory(), equalTo(category));
    }

    @Test
    public void returnsAbsoluteRequestUrl() throws Exception {
        URL url = new URL("http://www.test.com");
        when(pageContext.getAbsoluteRequestUrl()).thenReturn(url);
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getAbsoluteRequestUrl(), equalTo(url));
    }

    @Test
    public void testLevelsWhenCategoryIsRoot() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        when(pageContext.getCategory()).thenReturn(root);
        when(pageContext.getCategoryLevelHierarchy()).thenReturn(createHierarchy(root));
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCategory(0), equalTo(root));
        assertThat(cut.getCategory(1), Matchers.nullValue());
        assertThat(cut.getCategory(2), Matchers.nullValue());
        assertThat(cut.getCategory(3), Matchers.nullValue());
        assertThat(cut.getCategory(4), Matchers.nullValue());
    }

    @Test
    public void testLevelsWhenCategoryIsL1() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        Category l1Category = createCategory(2, "test-l1-category");
        when(pageContext.getCategory()).thenReturn(l1Category);
        when(pageContext.getCategoryLevelHierarchy()).thenReturn(createHierarchy(root, l1Category));
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCategory(0), equalTo(root));
        assertThat(cut.getCategory(1), equalTo(l1Category));
        assertThat(cut.getCategory(2), Matchers.nullValue());
        assertThat(cut.getCategory(3), Matchers.nullValue());
        assertThat(cut.getCategory(4), Matchers.nullValue());
    }

    @Test
    public void testLevelsWhenCategoryIsL2() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        Category l1Category = createCategory(2, "test-l1-category");
        Category l2Category = createCategory(3, "test-l2-category");
        when(pageContext.getCategory()).thenReturn(l2Category);
        when(pageContext.getCategoryLevelHierarchy()).thenReturn(createHierarchy(root, l1Category, l2Category));
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCategory(0), equalTo(root));
        assertThat(cut.getCategory(1), equalTo(l1Category));
        assertThat(cut.getCategory(2), equalTo(l2Category));
        assertThat(cut.getCategory(3), Matchers.nullValue());
        assertThat(cut.getCategory(4), Matchers.nullValue());
    }

    @Test
    public void testLevelsWhenCategoryIsL3() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        Category l1Category = createCategory(2, "test-l1-category");
        Category l2Category = createCategory(3, "test-l2-category");
        Category l3Category = createCategory(4, "test-l3-category");
        when(pageContext.getCategory()).thenReturn(l3Category);
        when(pageContext.getCategoryLevelHierarchy()).thenReturn(createHierarchy(root, l1Category, l2Category, l3Category));
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCategory(0), equalTo(root));
        assertThat(cut.getCategory(1), equalTo(l1Category));
        assertThat(cut.getCategory(2), equalTo(l2Category));
        assertThat(cut.getCategory(3), equalTo(l3Category));
        assertThat(cut.getCategory(4), Matchers.nullValue());
    }

    @Test
    public void testLevelsWhenCategoryIsL4() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        Category l1Category = createCategory(2, "test-l1-category");
        Category l2Category = createCategory(3, "test-l2-category");
        Category l3Category = createCategory(4, "test-l3-category");
        Category l4Category = createCategory(5, "test-l4-category");
        when(pageContext.getCategory()).thenReturn(l3Category);
        when(pageContext.getCategoryLevelHierarchy()).thenReturn(createHierarchy(root, l1Category, l2Category, l3Category, l4Category));
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCategory(0), equalTo(root));
        assertThat(cut.getCategory(1), equalTo(l1Category));
        assertThat(cut.getCategory(2), equalTo(l2Category));
        assertThat(cut.getCategory(3), equalTo(l3Category));
        assertThat(cut.getCategory(4), equalTo(l4Category));
    }

    /**
     * TEST SPECIFICALLY TO ACCOUNT FOR NEED TO DEAL WITH FLATS & HOUSES LEVELS DIFFERENTLY. Basically, due
     * to legacy commitments, we need to "bump up" the l2 flats and houses categories to appear as l1s within
     * the level hierarchy.
     */

    @Test
    public void testLevelsWhenCategoryIsFlatsAndHouses() {
        Category root = createCategory(1L, Categories.ALL.getSeoName());
        Category flatsHouses = createCategory(FLATS_AND_HOUSES_ID, FLATS_AND_HOUSES.getSeoName());
        when(pageContext.getCategory()).thenReturn(flatsHouses);
        when(pageContext.getCategoryLevelHierarchy()).thenReturn(createHierarchy(root, flatsHouses));
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCategory(0), equalTo(flatsHouses));
        assertThat(cut.getCategory(1), Matchers.nullValue());
        assertThat(cut.getCategory(2), Matchers.nullValue());
        assertThat(cut.getCategory(3), Matchers.nullValue());
        assertThat(cut.getCategory(4), Matchers.nullValue());
    }

    @Test
    public void testLevelsWhereCategoryIsL2BelowFlatsAndHouses() {
        Category all = createCategory(1L, Categories.ALL.getSeoName());
        Category flatsHouses = createCategory(FLATS_AND_HOUSES_ID, FLATS_AND_HOUSES.getSeoName());
        Category l2Category = createCategory(2L, "test-l2-category");
        when(pageContext.getCategory()).thenReturn(l2Category);
        when(pageContext.getCategoryLevelHierarchy()).thenReturn(createHierarchy(all, flatsHouses, l2Category));
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCategory(0), equalTo(flatsHouses));
        assertThat(cut.getCategory(1), equalTo(l2Category));
        assertThat(cut.getCategory(2), Matchers.nullValue());
        assertThat(cut.getCategory(3), Matchers.nullValue());
        assertThat(cut.getCategory(4), Matchers.nullValue());
    }

    @Test
    public void testLevelsWhereCategoryIsL3BelowFlatsAndHouses() {
        Category all = createCategory(1L, Categories.ALL.getSeoName());
        Category flatsHouses = createCategory(FLATS_AND_HOUSES_ID, FLATS_AND_HOUSES.getSeoName());
        Category l2Category = createCategory(2L, "test-l2-category");
        Category l3Category = createCategory(3L, "test-l3-category");
        when(pageContext.getCategory()).thenReturn(l3Category);
        when(pageContext.getCategoryLevelHierarchy()).thenReturn(createHierarchy(all, flatsHouses, l2Category, l3Category));
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCategory(0), equalTo(flatsHouses));
        assertThat(cut.getCategory(1), equalTo(l2Category));
        assertThat(cut.getCategory(2), equalTo(l3Category));
        assertThat(cut.getCategory(3), Matchers.nullValue());
        assertThat(cut.getCategory(4), Matchers.nullValue());
    }

    @Test
    public void testAndLevelsWhereCategoryIsL4BelowFlatsAndHouses() {
        Category all = createCategory(1, Categories.ALL.getSeoName());
        Category flatsHouses = createCategory(FLATS_AND_HOUSES_ID, FLATS_AND_HOUSES.getSeoName());
        Category l2Category = createCategory(2, "test-l2-category");
        Category l3Category = createCategory(3, "test-l3-category");
        Category l4Category = createCategory(4, "test-l4-category");
        when(pageContext.getCategory()).thenReturn(l4Category);
        when(pageContext.getCategoryLevelHierarchy()).thenReturn(createHierarchy(all, flatsHouses, l2Category, l3Category, l4Category));
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getCategory(0), equalTo(flatsHouses));
        assertThat(cut.getCategory(1), equalTo(l2Category));
        assertThat(cut.getCategory(2), equalTo(l3Category));
        assertThat(cut.getCategory(3), equalTo(l4Category));
        assertThat(cut.getCategory(4), Matchers.nullValue());
    }

    private ThirdPartyRequestContextAdapter classUnderTest() {
        return new ThirdPartyRequestContextAdapter(pageContext);
    }

    private Map<Integer, Category> createHierarchy(Category... categories) {
        Map<Integer, Category> levels = new HashMap<Integer, Category>();
        for (int i = 0; i < categories.length; i++) {
            levels.put(i, categories[i]);
        }
        return levels;
    }

    private Category createCategory(long id, String name) {
        Category category = mock(Category.class);
        when(category.getId()).thenReturn((long)id);
        when(category.getSeoName()).thenReturn(name);
        return category;
    }

    @Test
    public void getDisplayAdsViewMode(){
        when(pageContext.getDisplayAdsViewMode()).thenReturn(DisplayAdsViewMode.TEXTLINK_ACTIVE);
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getDisplayAdsViewMode(), CoreMatchers.equalTo(DisplayAdsViewMode.TEXTLINK_ACTIVE));
    }

    @Test
    public void getPageNumber(){
        when(pageContext.getPageNumber()).thenReturn(23);
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getPageNumber(), equalTo(23));
    }

    @Test
    public void getAbTestTypes(){
        List<AbTestType> abTestTypes = Lists.newArrayList();
        when(pageContext.getAbTestTypes()).thenReturn(abTestTypes);
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getAbTestTypes().equals(abTestTypes), is(true));
    }

    @Test
    public void getReferrer(){
        when(pageContext.getReferrer()).thenReturn("referrer");
        ThirdPartyRequestContextAdapter cut = classUnderTest();
        assertThat(cut.getReferrer(), equalTo("referrer"));
    }

    @Test
    public void getLocationHierarchy() {
        Map hierarchyMap = mock(Map.class);
        when(pageContext.getLocationHierarchy()).thenReturn(hierarchyMap);
        Location mockLocation = mock(Location.class);
        when(hierarchyMap.get(1)).thenReturn(mockLocation);
        Location location = classUnderTest().getLocation(1);
        assertThat(location, equalTo(mockLocation));
    }

}
