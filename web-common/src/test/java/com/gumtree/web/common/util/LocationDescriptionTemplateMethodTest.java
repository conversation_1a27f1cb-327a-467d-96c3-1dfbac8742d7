package com.gumtree.web.common.util;

import com.gumtree.domain.location.Location;
import com.gumtree.service.location.LocationService;
import com.gumtree.web.common.util.LocationDescriptionTemplateMethod;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class LocationDescriptionTemplateMethodTest {
    private LocationService locationService;
    private String currentFreeTextLocation = null;
    private Location location;
    private Long category;
    private LocationDescriptionTemplateMethod locationDescriptionCreator;


    @Before
    public void setUp() {
        locationService = mock(LocationService.class);
        location = mock(Location.class);
        category = 1L;
        setUpLocationDescriptorCreator();
    }

    @Test
    public void descriptionShouldBeEmptyWhenProvidedLocationIsNull() {
        //having
        location = null;

        //when
        String descriptionParts = locationDescriptionCreator.createLocationDescription();

        //then
        assertThat(descriptionParts, equalTo(""));
    }

    @Test
    public void descriptionShouldContainFreeTextLocationAndProvidedLocationWhenFreeTextLocationIsProvidedAndProvidedLocationIsLanding() {
        //having
        String freeTextLocation = "Devon";
        String locationName = "Cornwall";
        String expectedDescription = "Devon, Cornwall";
        location = new LocationMocker().setFreeTextLocation(freeTextLocation).setDisplayName(locationName).isLanding(true).build();

        //when
        String description = locationDescriptionCreator.createLocationDescription();

        //then
        assertThat(description, equalTo(expectedDescription));
    }

    @Test
    public void descriptionShouldContainFreeTextLocationAndClosestLandingLocationWhenFreeTextLocationIsProvidedAndProvidedLocationIsNotLanding() {
        //having
        String freeTextLocation = "Windmill Road";
        String locationName = "Ealing";
        String landingLocationName = "London";
        String expectedDescription = "Windmill Road, London";
        Location landingLocation = new LocationMocker().setDisplayName(landingLocationName).isLanding(true).build();
        location = new LocationMocker().setFreeTextLocation(freeTextLocation).
                setDisplayName(locationName).isLanding(false).setLandingLocation(landingLocation).
                build();

        //when
        String description = locationDescriptionCreator.createLocationDescription();

        //then
        assertThat(description, equalTo(expectedDescription));
    }

    @Test
    public void descriptionShouldContainLocationNameWhenProvidedLocationIsLandingLocationAndThereIsNoFreeTextLocation() {
        //having
        String locationName = "London";
        location = new LocationMocker().setDisplayName(locationName).isLanding(true).build();

        //when
        String description = locationDescriptionCreator.createLocationDescription();

        //then
        assertThat(description, equalTo(locationName));
    }

    @Test
    public void descriptionShouldContainLocationNameWhenProvidedLocationIsLandingLocationAndThereIsEmptyFreeTextLocation() {
        //having
        String locationName = "London";
        location = new LocationMocker().setFreeTextLocation("").setDisplayName(locationName).isLanding(true).build();

        //when
        String description = locationDescriptionCreator.createLocationDescription();

        //then
        assertThat(description, equalTo(locationName));
    }

    @Test
    public void descriptionShouldContainLocationNameAndClosestLandingLocationWhenProvidedLocationIsNotLandingAndThereIsNoFreeTextLocation() {
        //having
        String locationName = "Aldgate";
        String landingLocationName = "London";
        String expectedDescription = "Aldgate, London";
        Location landingLocation = new LocationMocker().
                setDisplayName(landingLocationName).
                isLanding(true).build();
        location = new LocationMocker().
                setDisplayName(locationName).
                isLanding(false).
                setLandingLocation(landingLocation).
                build();

        //when
        String description = locationDescriptionCreator.createLocationDescription();

        //then
        assertThat(description, equalTo(expectedDescription));
    }

    @Test
    public void descriptionShouldNotContainLandingLocationWhenLandingLocationNameIsPartOfFreeTextLocation() {
        //having
        String locationName = "London";
        String freeTextLocation = "Windmill Road, London, NW";
        location = new LocationMocker().
                setFreeTextLocation(freeTextLocation).
                setDisplayName(locationName).
                isLanding(true).
                build();

        //when
        String description = locationDescriptionCreator.createLocationDescription();

        //then
        assertThat(description, equalTo(freeTextLocation));
    }

    @Test
    public void descriptionShouldNotContainLandingLocationWhenLandingLocationNameIsPartOfPrimaryLocation() {
        //having
        String locationName = "East London";
        String landingLocationName = "London";
        Location landingLocation = new LocationMocker().
                setDisplayName(landingLocationName).
                isLanding(true).build();
        location = new LocationMocker().
                setDisplayName(locationName).
                isLanding(false).
                setLandingLocation(landingLocation).
                build();

        //when
        String description = locationDescriptionCreator.createLocationDescription();

        //then
        assertThat(description, equalTo(locationName));
    }

    @Test
    public void descriptionShouldContainLandingLocationWhenLandingLocationNameIsPartOfPrimaryLocationUnwillingly() {
        //having
        String freeTextLocation = "Cornwallis Street";
        String locationName = "Cornwall";
        String expectedLocationDescription = "Cornwallis Street, Cornwall";
        location = new LocationMocker().
                setFreeTextLocation(freeTextLocation).
                setDisplayName(locationName).
                isLanding(true).
                build();

        //when
        String description = locationDescriptionCreator.createLocationDescription();

        //then
        assertThat(description, equalTo(expectedLocationDescription));
    }

    @Test
    public void descriptionShouldBeCreatedWhenCategoryIsNull() {
        //having
        String locationName = "London";
        category = null;
        location = new LocationMocker().setDisplayName(locationName).isLanding(true).build();

        //when
        String description = locationDescriptionCreator.createLocationDescription();

        //then
        assertThat(description, equalTo(locationName));
    }

    private void setUpLocationDescriptorCreator() {
        locationDescriptionCreator = new LocationDescriptionTemplateMethod(locationService) {
            @Override
            protected String getFreeTextLocation() {
                return getCurrentFreeTextLocation();
            }

            @Override
            protected Location getLocation() {
                return location;
            }

            @Override
            protected Long getCategoryId() {
                return category;
            }
        };
    }

    private String getCurrentFreeTextLocation() {
        return currentFreeTextLocation;
    }

    private class LocationMocker {
        private Location myLocation = mock(Location.class);

        private LocationMocker setDisplayName(String displayName) {
            when(myLocation.getDisplayName()).thenReturn(displayName);
            return this;
        }

        private LocationMocker isLanding(boolean is) {
            when(myLocation.isLanding()).thenReturn(is);
            if (is) {
                when(locationService.getLanding(myLocation)).thenReturn(myLocation);
            }
            return this;
        }

        private LocationMocker setLandingLocation(Location landingLocation) {
            when(locationService.getLanding(myLocation)).thenReturn(landingLocation);
            return this;
        }

        private LocationMocker setFreeTextLocation(String textLocation) {
            currentFreeTextLocation = textLocation;
            return this;
        }

        private Location build() {
            return myLocation;
        }
    }
}
