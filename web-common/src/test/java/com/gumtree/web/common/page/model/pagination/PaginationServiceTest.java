package com.gumtree.web.common.page.model.pagination;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.util.model.Link;
import com.gumtree.web.common.page.model.pagination.decadicpagination.DecadicPagination;
import com.gumtree.web.common.page.model.pagination.impl.DefaultPaginationService;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThat;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PaginationServiceTest {

    private int PAGE_SIZE = 50;
    private int PAGINATION_WINDOW_SIZE = 9;
    
	private DefaultPaginationService service;
    private Category mockCategory;
    private Location mockLocation;
    private PaginationUrlGenerator paginationUrlGenerator;

    @Before
    public void setup() {
        service = new DefaultPaginationService();
        mockCategory = mock(Category.class);
        mockLocation = mock(Location.class);
        paginationUrlGenerator = mock(PaginationUrlGenerator.class);
        when(paginationUrlGenerator.forPage(anyInt())).thenReturn("http://www.gumtree.com/category/location/pageN");
    }
    
    @Test
    public void returnsNotNullSRPPagination(){
        
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 130, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertNotNull(pagination);
    }
    
    @Test
    public void containsDecadicPagination(){
        
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 130, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        assertNotNull(pagination.getDecadicPagination());
    }
    
  //50000 results should return 1000 pages
    @Test
    public void returnsDecadicTwelveLinks() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 50000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        DecadicPagination decadicPagination = pagination.getDecadicPagination();
        assertThat(decadicPagination.getLinks().size(), equalTo(12));
    }

    @Test
    public void firstItemInPagelinkOnFirstPageListIsTwenty() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 50000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        DecadicPagination decadicPagination = pagination.getDecadicPagination();
        assertThat(decadicPagination.getLinks().get(0).getText(), equalTo("20"));
    }

    @Test
    public void firstItemInPagelinkOnPage11PageListIsNextFullDecaNumber() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        Pagination pagination = service.getPagination(paginationUrlGenerator, 11, 50000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        DecadicPagination decadicPagination = pagination.getDecadicPagination();
        assertThat(decadicPagination.getLinks().get(0).getText(), equalTo("20"));
    }

    @Test
    public void secondItemInPagelinkListIsNextFullDecaNumber() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 50000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        DecadicPagination decadicPagination = pagination.getDecadicPagination();
        assertThat(decadicPagination.getLinks().get(1).getText(), equalTo("30"));
      
    }

    @Test
    public void eleventhItemInPagelinkListIsFirstCentaNumber() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 50000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        DecadicPagination decadicPagination = pagination.getDecadicPagination();
        
        assertThat(decadicPagination.getLinks().get(9).getText(), equalTo("110"));
        assertThat(decadicPagination.getLinks().get(10).getText(), equalTo("200"));
    }

    //50000 results should return 1000 pages
    @Test
    public void eighteenthItemInPagelinkListIsNextCentaNumber() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        Pagination pagination = service.getPagination(paginationUrlGenerator, 18, 50000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        DecadicPagination decadicPagination = pagination.getDecadicPagination();
        assertThat(decadicPagination.getLinks().get(9).getText(), equalTo("120"));
        assertThat(decadicPagination.getLinks().get(10).getText(), equalTo("200"));
    }

    @Test
    public void threehundredAndFirstItemInPagelinkListIsNextCentaNumber() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        Pagination pagination = service.getPagination(paginationUrlGenerator, 301, 50000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        DecadicPagination decadicPagination = pagination.getDecadicPagination();
        assertThat(decadicPagination.getLinks().get(10).getText(), equalTo("500"));
        assertThat(decadicPagination.getLinks().get(11).getText(), equalTo("600"));
    }

    @Test
    public void doesNotDisplayMoreValuesThanSearchResults() {
        Integer searchResult = 105;
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        Pagination pagination = service.getPagination(paginationUrlGenerator, 10, searchResult, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        DecadicPagination decadicPagination = pagination.getDecadicPagination();
        List<Link> links = decadicPagination.getLinks();
        for (Link link : links) {
            int pageNumber = Integer.parseInt(link.getText());
            assertThat((pageNumber <= searchResult), equalTo(true));
        }
    }

    @Test    //the idea here is that we can hide this on the front end if empty
    public void returnsAnEmptyListIfThereAreNoValidEntries() {
        Integer searchResult = 105;
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        Pagination pagination = service.getPagination(paginationUrlGenerator, 100, searchResult, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        DecadicPagination decadicPagination = pagination.getDecadicPagination();
        List<Link> links = decadicPagination.getLinks();
        assertThat(links.isEmpty(), equalTo(true));
    }


    @Test
    public void displaysValidSRPPagination(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 51, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(true, pagination.isDisplay());
    	pagination = service.getPagination(paginationUrlGenerator, 1, 100, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(true, pagination.isDisplay());
    	pagination = service.getPagination(paginationUrlGenerator, 1, 152, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(true, pagination.isDisplay());
    }
    
    @Test
    public void doesNotDisplayUnnecessarySRPPagination(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 0, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(false, pagination.isDisplay());
    	pagination = service.getPagination(paginationUrlGenerator, 1, 37, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(false, pagination.isDisplay());
    	pagination = service.getPagination(paginationUrlGenerator, 1, 12, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(false, pagination.isDisplay());
        
    }

    @Test
    public void firstEntryIsAtLeastFivePagesAboveCurrentPageNumber(){
        Pagination pagination = service.getPagination(paginationUrlGenerator, 16, 2000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        assertThat(pagination.getDecadicPagination().getLinks().get(0).getText(), equalTo("30"));
    }

    @Test
    public void whenYouAreOnPageThirteenTheFirstNumberShouldBeTwenty(){
        Pagination pagination = service.getPagination(paginationUrlGenerator, 13, 2000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        assertThat(pagination.getDecadicPagination().getLinks().get(0).getText(), equalTo("20"));
    }
    
    @Test
    public void showsCorrectNumberOfSectionsWhenCurrentIndexIs1AndOnly2ResultPages(){
        Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 60, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        assertEquals(1, pagination.getPaginationSections().size());
    }
    
    @Test
    public void showsCorrectNumberOfSectionsWhenCurrentIndexIs1AndLowNumberOfResultPages(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 450, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(1, pagination.getPaginationSections().size());

    }
    
    @Test
    public void showsCorrectNumberOfSectionsWhenCurrentIndexIsMiddleAndLowMaximumNumberOfPages(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 9, 450, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(1, pagination.getPaginationSections().size());

    }
    
    @Test
    public void showsCorrectNumberOfSectionsWhenCurrentIndexIsHighAndLowMaximumNumberOfPages(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 20, 1000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(2, pagination.getPaginationSections().size());

    }
    
    @Test
    public void showsCorrectNumberOfSectionsWhenCurrentIndexIs1AndHighNumberOfResultPages(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 1000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(2, pagination.getPaginationSections().size());

    }
    
    @Test
    public void showsCorrectNumberOfSectionsWhenCurrentIndexIsMiddleAndLowNumberOfResultPages(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 5, 450, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(1, pagination.getPaginationSections().size());

    }
    
    @Test
    public void showsCorrectNumberOfSectionsWhenCurrentIndexIsMiddleAndHighNumberOfResultPages(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 12, 2000, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	assertEquals(3, pagination.getPaginationSections().size());

    }

    @Test
    public void shouldCreateValidPaginationForBasePathUrl() {
        // when
        Pagination pagination = service.getPagination("/sellerads", 1, 25);

        // then
        assertThat(pagination.getCurrentPageNumber(), is(1));
        assertThat(pagination.getTotalNumberOfPages(), is(3));
        assertThat(pagination.getPaginationSections().size(), is(1));
        assertThat(pagination.getDecadicPagination().getHasContent(), is(false));
        assertThat(pagination.isDisplay(), is(true));
    }
    
    @Test
    public void hidesInactiveButtonsAtStartOfPaginationPanel(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 1, 450, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	PaginationSection paginationSection = pagination.getPaginationSections().get(0);
    	List<PaginationLink> pageLinks = paginationSection.getPaginationLinks();
    	assertEquals(false, pageLinks.get(0).isActive());
    	assertEquals(false, pageLinks.get(1).isActive());
    	assertEquals(true, pageLinks.get(2).isActive());
    	assertEquals(true, pageLinks.get(3).isActive());
    	assertEquals(true, pageLinks.get(4).isActive());
    	assertEquals(true, pageLinks.get(5).isActive());
    	assertEquals(true, pageLinks.get(6).isActive());
    	assertEquals(true, pageLinks.get(7).isActive());
    	assertEquals(true, pageLinks.get(8).isActive());
    	assertEquals(true, pageLinks.get(9).isActive());
    	assertEquals(true, pageLinks.get(10).isActive());

    }
    
    @Test
    public void hidesInactiveButtonsAtEndOfPaginationPanel(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 9, 450, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	PaginationSection paginationSection = pagination.getPaginationSections().get(0);
    	List<PaginationLink> pageLinks = paginationSection.getPaginationLinks();
    	assertEquals(true, pageLinks.get(0).isActive());
    	assertEquals(true, pageLinks.get(1).isActive());
    	assertEquals(true, pageLinks.get(2).isActive());
    	assertEquals(true, pageLinks.get(3).isActive());
    	assertEquals(true, pageLinks.get(4).isActive());
    	assertEquals(true, pageLinks.get(5).isActive());
    	assertEquals(true, pageLinks.get(6).isActive());
    	assertEquals(true, pageLinks.get(7).isActive());
    	assertEquals(true, pageLinks.get(8).isActive());
    	assertEquals(false, pageLinks.get(9).isActive());
    	assertEquals(false, pageLinks.get(10).isActive());

    }
    
    @Test
    public void showsInactiveButtonsInMiddleOfPaginationPanel(){
    	Pagination pagination = service.getPagination(paginationUrlGenerator, 5, 450, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
    	PaginationSection paginationSection = pagination.getPaginationSections().get(0);
    	List<PaginationLink> pageLinks = paginationSection.getPaginationLinks();
    	assertEquals(true, pageLinks.get(0).isActive());
    	assertEquals(true, pageLinks.get(1).isActive());
    	assertEquals(true, pageLinks.get(2).isActive());
    	assertEquals(true, pageLinks.get(3).isActive());
    	assertEquals(true, pageLinks.get(4).isActive());
    	assertEquals(false, pageLinks.get(5).isActive());
    	assertEquals(true, pageLinks.get(6).isActive());
    	assertEquals(true, pageLinks.get(7).isActive());
    	assertEquals(true, pageLinks.get(8).isActive());
    	assertEquals(true, pageLinks.get(9).isActive());
    	assertEquals(true, pageLinks.get(10).isActive());

    }
    
    
    @Test
    public void testIsPreviousLink(){
        Pagination pagination = service.getPagination(paginationUrlGenerator, 5, 450, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        PaginationSection paginationSection = pagination.getPaginationSections().get(0);
        List<PaginationLink> pageLinks = paginationSection.getPaginationLinks();
        assertEquals(true, pageLinks.get(0).isPreviousLink());
        assertEquals(false, pageLinks.get(1).isPreviousLink());
        assertEquals(false, pageLinks.get(2).isPreviousLink());
        assertEquals(false, pageLinks.get(3).isPreviousLink());
        assertEquals(false, pageLinks.get(4).isPreviousLink());
        assertEquals(false, pageLinks.get(5).isPreviousLink());
        assertEquals(false, pageLinks.get(6).isPreviousLink());
        assertEquals(false, pageLinks.get(7).isPreviousLink());
        assertEquals(false, pageLinks.get(8).isPreviousLink());
        assertEquals(false, pageLinks.get(9).isPreviousLink());
        assertEquals(false, pageLinks.get(10).isPreviousLink());

    }
    
    @Test
    public void testIsNextLink(){
        Pagination pagination = service.getPagination(paginationUrlGenerator, 5, 450, 0, PAGE_SIZE, PAGINATION_WINDOW_SIZE);
        PaginationSection paginationSection = pagination.getPaginationSections().get(0);
        List<PaginationLink> pageLinks = paginationSection.getPaginationLinks();
        assertEquals(false, pageLinks.get(0).isNextLink());
        assertEquals(false, pageLinks.get(1).isNextLink());
        assertEquals(false, pageLinks.get(2).isNextLink());
        assertEquals(false, pageLinks.get(3).isNextLink());
        assertEquals(false, pageLinks.get(4).isNextLink());
        assertEquals(false, pageLinks.get(5).isNextLink());
        assertEquals(false, pageLinks.get(6).isNextLink());
        assertEquals(false, pageLinks.get(7).isNextLink());
        assertEquals(false, pageLinks.get(8).isNextLink());
        assertEquals(false, pageLinks.get(9).isNextLink());
        assertEquals(true, pageLinks.get(10).isNextLink());

    }
}
