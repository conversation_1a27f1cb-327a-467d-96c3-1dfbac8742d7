package com.gumtree.web.common.interceptor;

import org.junit.Test;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;

public class CustomResponseHeadersInterceptorTest {

    @Test(expected = IllegalArgumentException.class)
    public void testNullMap() {
        new CustomResponseHeadersInterceptor(null);
    }

    @Test
    public void testPostHandleInteractionsWithHeaders() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("key1", "value1");
        headers.put("key2", "value2");
        headers.put("key3", "value3");
        new CustomResponseHeadersInterceptor(headers).postHandle(request, response, null, null);
        verify(response).setHeader("key1", "value1");
        verify(response).setHeader("key2", "value2");
        verify(response).setHeader("key3", "value3");
        verifyNoMoreInteractions(response);
        verifyZeroInteractions(request);
    }

    @Test
    public void testPostHandleInteractionsWithNoHeaders() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        new CustomResponseHeadersInterceptor(new HashMap<String, String>()).postHandle(request, response, null, null);
        verifyZeroInteractions(response);
        verifyZeroInteractions(request);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testPostHandleNullResponse() throws Exception {
        new CustomResponseHeadersInterceptor(new HashMap<String, String>()).postHandle(null, null, null, null);
    }

    @Test
    public void testPreHandleNoInteractionsReturnTrue() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        assertThat(new CustomResponseHeadersInterceptor(
                new HashMap<String, String>()).preHandle(request, response, null), equalTo(true));
        verifyZeroInteractions(request);
        verifyZeroInteractions(response);
    }

    @Test
    public void testAfterCompletionNoInteractions() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        new CustomResponseHeadersInterceptor(new HashMap<String, String>()).afterCompletion(request, response, null, null);
        verifyZeroInteractions(request);
        verifyZeroInteractions(response);
    }
}
