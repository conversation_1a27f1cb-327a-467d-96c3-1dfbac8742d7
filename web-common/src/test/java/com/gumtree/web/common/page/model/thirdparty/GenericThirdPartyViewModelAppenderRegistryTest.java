package com.gumtree.web.common.page.model.thirdparty;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import org.junit.Before;
import org.junit.Test;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.lang.annotation.Annotation;
import java.util.Collection;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

/**
 */
public class GenericThirdPartyViewModelAppenderRegistryTest {

    private GenericThirdPartyViewModelAppenderRegistry appenderRegistry;

    @Before
    public void init() {
        appenderRegistry = new GenericThirdPartyViewModelAppenderRegistry();
    }

    @Test
    public void getAppendersReturnsEmptyListWhenNoAppendersHaveBeenRegistered() {
        Collection<ThirdPartyViewModelAppender> appenders = appenderRegistry.getAppenders(Valid.class);
        assertThat(appenders.size(), equalTo(0));
    }

    @Test
    public void supportsAppendersThatAreTriggeredByTheSameAnnotation() {
        TestAppenderOne appenderOne = new TestAppenderOne();
        TestAppenderOne appenderTwo = new TestAppenderOne();
        appenderRegistry.registerAppender(appenderOne);
        appenderRegistry.registerAppender(appenderTwo);
        Collection<ThirdPartyViewModelAppender> appenders = appenderRegistry.getAppenders(Valid.class);
        assertThat(appenders.size(), equalTo(2));
        assertThat(appenders.contains(appenderOne), equalTo(true));
        assertThat(appenders.contains(appenderTwo), equalTo(true));
    }

    @Test
    public void supportsAppendersThatAreTriggeredByTheDifferentAnnotations() {
        TestAppenderOne appenderOne = new TestAppenderOne();
        TestAppenderTwo appenderTwo = new TestAppenderTwo();
        appenderRegistry.registerAppender(appenderOne);
        appenderRegistry.registerAppender(appenderTwo);

        Collection<ThirdPartyViewModelAppender> appenders = appenderRegistry.getAppenders(Valid.class);
        assertThat(appenders.size(), equalTo(1));
        assertThat(appenders.contains(appenderOne), equalTo(true));

        appenders = appenderRegistry.getAppenders(NotNull.class);
        assertThat(appenders.size(), equalTo(1));
        assertThat(appenders.contains(appenderTwo), equalTo(true));
    }

    @Test
    public void ignoresNullAppender() {
        appenderRegistry.registerAppender(null);
    }

    private class TestAppenderOne implements ThirdPartyViewModelAppender<Valid> {
        @Override
        public Class<? extends Annotation> getSupportedAnnotationType() {
            return Valid.class;
        }

        @Override
        public void append(Map<String, Object> model, Valid annotation, ThirdPartyRequestContext requestContext) {

        }
    }

    private class TestAppenderTwo implements ThirdPartyViewModelAppender<NotNull> {
        @Override
        public Class<? extends Annotation> getSupportedAnnotationType() {
            return NotNull.class;
        }

        @Override
        public void append(Map<String, Object> model, NotNull annotation, ThirdPartyRequestContext requestContext) {

        }
    }
}
