package com.gumtree.web.common.util;

import com.google.common.base.Optional;
import com.google.common.collect.ImmutableSet;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;

import static org.hamcrest.core.IsEqual.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CategoryHierarchyCheckerTest {
    @Mock
    private CategoryModel categoryModel;

    private CategoryHierarchyChecker categoryHierarchyChecker;

    @Before
    public void setUp() throws Exception {
        this.categoryModel = mock(CategoryModel.class);
        this.categoryHierarchyChecker = new CategoryHierarchyChecker(categoryModel, ImmutableSet.of(10L, 20L, 30L));
    }

    @Test
    public void categoryMatched() throws Exception {
        when(categoryModel.getParentOf(10L))
                .thenReturn(Optional.of(new Category(1L, "categoryRoot", "testParentCategoryRoot")));
        assertThat(categoryHierarchyChecker.isAdvertCategoryWhiteListed(10L), equalTo(true));
    }

    @Test
    public void childCategoryMatched() throws Exception {
        when(categoryModel.getParentOf(50L))
                .thenReturn(Optional.of(new Category(20L, "categoryRoot", "testParentCategoryRoot")));
        assertThat(categoryHierarchyChecker.isAdvertCategoryWhiteListed(50L), equalTo(true));
    }

    @Test
    public void categoryNotInTree() throws Exception {
        when(categoryModel.getParentOf(60L))
                .thenReturn(Optional.of(new Category(90L, "caegoryParent", "testParentCategory")));
        when(categoryModel.getParentOf(90L))
                .thenReturn(Optional.of(new Category(1L, "categoryRoot", "testParentCategoryRoot")));
        assertThat(categoryHierarchyChecker.isAdvertCategoryWhiteListed(60L), equalTo(false));
    }

    @Test
    public void categorySubHierarchyMatch() throws Exception {
        when(categoryModel.getParentOf(80L))
                .thenReturn(Optional.of(new Category(100L, "caegoryParent", "testParentCategory")));
        when(categoryModel.getParentOf(100L))
                .thenReturn(Optional.of(new Category(30L, "caegoryGrandparent", "testGrandparentCategoryRoot")));
        when(categoryModel.getParentOf(30L))
                .thenReturn(Optional.of(new Category(1L, "categoryRoot", "testParentCategoryRoot")));
        assertThat(categoryHierarchyChecker.isAdvertCategoryWhiteListed(80L), equalTo(true));
    }

}