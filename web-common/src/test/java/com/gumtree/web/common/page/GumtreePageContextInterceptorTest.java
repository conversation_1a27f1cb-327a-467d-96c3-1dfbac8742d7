package com.gumtree.web.common.page;

import com.gumtree.common.properties.GtPropManager;
import com.gumtree.config.CommonProperty;
import com.gumtree.web.common.page.context.GumtreePageContextInterceptor;
import com.gumtree.web.common.page.context.RequestScopedGumtreePageContext;
import com.gumtree.web.common.page.handler.PageHandler;
import com.gumtree.web.common.page.handler.PageHandlerFactory;
import com.gumtree.web.common.page.model.thirdparty.ThirdPartyViewModelAppender;
import com.gumtree.web.common.page.model.thirdparty.ThirdPartyViewModelAppenderFactory;
import com.gumtree.web.common.page.util.RedirectViewWithRequiredModelKeys;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContextFactory;
import com.gumtree.web.common.util.RequestUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.ui.ModelMap;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.Arrays;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.eq;
import static org.mockito.Matchers.isA;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

/**
 */
public class GumtreePageContextInterceptorTest {

    private RequestScopedGumtreePageContext pageContext;

    private ThirdPartyViewModelAppenderFactory thirdPartyViewModelAppenderFactory;

    private ThirdPartyRequestContextFactory thirdPartyRequestContextFactory;

    private ThirdPartyRequestContext thirdPartyRequestContext;

    private PageHandlerFactory pageHandlerFactory;

    private PageHandler pageHandler;

    private HandlerMethod handlerMethod;

    private HttpServletRequest httpServletRequest;

    private HttpServletResponse httpServletResponse;

    private GumtreePageContextInterceptor interceptor;

    private ModelAndView modelAndView;

    private ModelMap modelMap;

    @Before
    public void init() {
        pageContext = mock(RequestScopedGumtreePageContext.class);
        thirdPartyViewModelAppenderFactory = mock(ThirdPartyViewModelAppenderFactory.class);
        thirdPartyRequestContextFactory = mock(ThirdPartyRequestContextFactory.class);
        handlerMethod = mock(HandlerMethod.class);
        httpServletRequest = mock(HttpServletRequest.class);
        httpServletResponse = mock(HttpServletResponse.class);
        modelAndView = mock(ModelAndView.class);
        modelMap = mock(ModelMap.class);
        thirdPartyRequestContext = mock(ThirdPartyRequestContext.class);
        pageHandlerFactory = mock(PageHandlerFactory.class);
        pageHandler = mock(PageHandler.class);

        interceptor = new GumtreePageContextInterceptor();
        ReflectionTestUtils.setField(interceptor, "pageContext", pageContext);
        ReflectionTestUtils.setField(interceptor, "thirdPartyViewModelAppenderFactory", thirdPartyViewModelAppenderFactory);
        ReflectionTestUtils.setField(interceptor, "thirdPartyRequestContextFactory", thirdPartyRequestContextFactory);
        ReflectionTestUtils.setField(interceptor, "pageHandlerFactory", pageHandlerFactory);

        when(modelAndView.getModelMap()).thenReturn(modelMap);
        when(thirdPartyRequestContextFactory.create(pageContext)).thenReturn(thirdPartyRequestContext);

        when(pageHandlerFactory.create(handlerMethod)).thenReturn(pageHandler);
        GumtreePage pageAnnotation = mock(GumtreePage.class);
        when(pageHandler.getPageAnnotation()).thenReturn(pageAnnotation);
    }

    @Test
    public void initialisesPageContextIfIsGumtreePage() throws Exception {
        boolean result = interceptor.preHandle(httpServletRequest, httpServletResponse, handlerMethod);
        verify(pageContext).init(httpServletRequest, httpServletResponse, pageHandler);
        assertThat(result, equalTo(true));
    }

    @Test
    public void doesNotInitialisesPageContextIfNotGumtreePage() throws Exception {
        when(pageHandlerFactory.create(handlerMethod)).thenReturn(null);
        boolean result = interceptor.preHandle(httpServletRequest, httpServletResponse, handlerMethod);
        verifyZeroInteractions(pageContext);
        assertThat(result, equalTo(true));
    }

    @Test
    public void postProcessesPageContextIfIsGumtreePage() throws Exception {
        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);
        verify(pageContext).populateModel(modelMap, pageHandler);
    }

    @Test
    public void doesNotPostProcessPageContextIfNotGumtreePage() throws Exception {
        when(pageHandlerFactory.create(handlerMethod)).thenReturn(null);
        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);
        verifyZeroInteractions(pageContext);
        verifyNoMoreInteractions(modelAndView);
    }

    @Test
    public void doesNotPostProcessButClearsModelMapIfIsGumtreePageButViewNameContainsRedirect() throws Exception {
        whenGettingViewName("redirect:/someredirectpage");

        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);

        verify(modelMap).clear();
        verifyZeroInteractions(pageContext);
    }

    @Test
    public void doesNotPostProcessButClearsModelMapIfIsGumtreePageButViewIsARedirectView() throws Exception {
        whenGettingRedirectView(new RedirectView());

        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);

        verify(modelMap).clear();
        verifyZeroInteractions(pageContext);
    }

    @Test
    public void doesNotRemoveModelMapElementsFromUrlIfIsGumtreePageButViewIsARedirectView() throws Exception {
        whenGettingRedirectView(new RedirectViewWithRequiredModelKeys("/checkout/{checkoutKey}/abc/{xKey}", "checkoutKey", "xKey"));
        ModelMap myModelMap = new ModelMap();
        when(modelAndView.getModelMap()).thenReturn(myModelMap);
        myModelMap.put("bla", "bli");
        myModelMap.put("bli", "bla");
        myModelMap.put("checkoutKey", "1234567890");
        myModelMap.put("xKey", "xxx");

        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);

        verifyZeroInteractions(pageContext);
        assertThat(myModelMap.size(), equalTo(2));
        assertThat((String) myModelMap.get("checkoutKey"), equalTo("1234567890"));
        assertThat((String) myModelMap.get("xKey"), equalTo("xxx"));
    }

    @Test
    public void doesNotInvokeAnyThirdPartyProcessingIfNotGumtreePage() throws Exception {
        when(pageHandlerFactory.create(handlerMethod)).thenReturn(null);
        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);
        verifyZeroInteractions(pageContext);
        verifyZeroInteractions(thirdPartyRequestContextFactory);
        verifyZeroInteractions(thirdPartyViewModelAppenderFactory);
        verifyZeroInteractions(modelAndView);
    }

    @Test
    public void doesNotInvokeAnyThirdPartyProcessingIfNoAdditionalAnnotations() throws Exception {
        GumtreePage pageAnnotation = mock(GumtreePage.class);
        when(pageHandlerFactory.create(handlerMethod)).thenReturn(pageHandler);
        when(pageHandler.getPageAnnotation()).thenReturn(pageAnnotation);
        when(pageHandler.getAdditionalAnnotations()).thenReturn(new ArrayList<Annotation>());
        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);
        verifyZeroInteractions(thirdPartyRequestContextFactory);
        verifyZeroInteractions(thirdPartyViewModelAppenderFactory);
    }

    @Test
    public void doesNotInvokeAnyThirdPartyProcessingIfNullAppendersForAdditionalAnnotations() throws Exception {
        Valid annotation1 = mock(Valid.class);
        when(annotation1.annotationType()).thenReturn((Class) Valid.class);
        NotNull annotation2 = mock(NotNull.class);
        when(annotation2.annotationType()).thenReturn((Class) NotNull.class);
        GumtreePage pageAnnotation = mock(GumtreePage.class);
        when(pageHandlerFactory.create(handlerMethod)).thenReturn(pageHandler);
        when(pageHandler.getPageAnnotation()).thenReturn(pageAnnotation);
        when(pageHandler.getAdditionalAnnotations()).thenReturn(Arrays.asList(annotation1, annotation2));
        when(thirdPartyViewModelAppenderFactory.getAppenders(Valid.class)).thenReturn(null);
        when(thirdPartyViewModelAppenderFactory.getAppenders(NotNull.class)).thenReturn(null);
        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);
        verify(thirdPartyViewModelAppenderFactory).getAppenders(Valid.class);
        verify(thirdPartyViewModelAppenderFactory).getAppenders(NotNull.class);
        verifyZeroInteractions(thirdPartyRequestContextFactory);
    }

    @Test
    public void invokeThirdPartyProcessingWhenAppendersAvailableForAnnotationsOnMethod() throws Exception {
        ThirdPartyViewModelAppender appender1 = mock(ThirdPartyViewModelAppender.class);
        ThirdPartyViewModelAppender appender2 = mock(ThirdPartyViewModelAppender.class);
        ThirdPartyViewModelAppender appender3 = mock(ThirdPartyViewModelAppender.class);
        ThirdPartyViewModelAppender appender4 = mock(ThirdPartyViewModelAppender.class);
        Valid annotation1 = mock(Valid.class);
        when(annotation1.annotationType()).thenReturn((Class) Valid.class);
        NotNull annotation2 = mock(NotNull.class);
        when(annotation2.annotationType()).thenReturn((Class) NotNull.class);
        GumtreePage pageAnnotation = mock(GumtreePage.class);
        when(pageHandlerFactory.create(handlerMethod)).thenReturn(pageHandler);
        when(pageHandler.getPageAnnotation()).thenReturn(pageAnnotation);
        when(pageHandler.getAdditionalAnnotations()).thenReturn(Arrays.asList(annotation1, annotation2));
        when(thirdPartyViewModelAppenderFactory.getAppenders(Valid.class)).thenReturn(Arrays.asList(appender1, appender2));
        when(thirdPartyViewModelAppenderFactory.getAppenders(NotNull.class)).thenReturn(Arrays.asList(appender3, appender4));
        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);
        verify(thirdPartyViewModelAppenderFactory).getAppenders(Valid.class);
        verify(thirdPartyViewModelAppenderFactory).getAppenders(NotNull.class);
        verify(thirdPartyRequestContextFactory, times(1)).create(pageContext);
        verify(appender1).append(eq(modelMap), isA(Valid.class), eq(thirdPartyRequestContext));
        verify(appender2).append(eq(modelMap), isA(Valid.class), eq(thirdPartyRequestContext));
        verify(appender3).append(eq(modelMap), isA(NotNull.class), eq(thirdPartyRequestContext));
        verify(appender4).append(eq(modelMap), isA(NotNull.class), eq(thirdPartyRequestContext));
    }

    @Test
    public void doesNotContinueProcessingOnPostHandleWhenModelAndViewIsNull() throws Exception {
        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, null);
        verifyZeroInteractions(pageHandlerFactory);
        verifyZeroInteractions(pageContext);
        verifyZeroInteractions(thirdPartyRequestContextFactory);
        verifyZeroInteractions(thirdPartyViewModelAppenderFactory);
        verifyZeroInteractions(modelAndView);
    }

    @Test
    public void ifIsNotGumtreePageAndIsRedirectViewThenDoesNotClearModelMap() throws Exception {
        whenGettingRedirectView(new RedirectView());
        when(pageHandlerFactory.create(handlerMethod)).thenReturn(null);
        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);
        verify(modelMap, never()).clear();
    }

    @Test
    public void ifIsNotGumtreePageAndViewNameContainsRedirectThenDoesNotClearModelMap() throws Exception {
        whenGettingViewName("redirect:/someredirectpage");
        when(pageHandlerFactory.create(handlerMethod)).thenReturn(null);
        interceptor.postHandle(httpServletRequest, httpServletResponse, handlerMethod, modelAndView);
        verify(modelMap, never()).clear();
    }

    private void whenGettingViewName(String viewName) {
        when(modelAndView.getViewName()).thenReturn(viewName);
        when(modelAndView.getView()).thenReturn(null);
    }

    private void whenGettingRedirectView(RedirectView redirectView) {
        when(modelAndView.getViewName()).thenReturn(null);
        when(modelAndView.getView()).thenReturn(redirectView);
    }
}
