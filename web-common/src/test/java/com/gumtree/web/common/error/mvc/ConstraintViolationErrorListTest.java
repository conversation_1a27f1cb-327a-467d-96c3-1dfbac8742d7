package com.gumtree.web.common.error.mvc;

import org.junit.Test;

import javax.validation.ConstraintViolation;
import javax.validation.Path;
import java.util.Arrays;
import java.util.HashSet;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class ConstraintViolationErrorListTest {

    @Test
    public void fieldErrorIsPopulatedCorrectly() {
        ConstraintViolation violation = mock(ConstraintViolation.class);
        Path path = mock(Path.class);
        when(violation.getMessage()).thenReturn("fieldErrorCode");
        when(violation.getPropertyPath()).thenReturn(path);
        when(path.toString()).thenReturn("fieldPath");

        ConstraintViolationErrorList errorList = new ConstraintViolationErrorList(
                new HashSet<ConstraintViolation>(Arrays.asList(violation)));

        assertThat(errorList.size(), equalTo(1));
        assertThat(errorList.get(0).getField(), equalTo("fieldPath"));
        assertThat(errorList.get(0).getDefaultMessage(), equalTo("fieldErrorCode"));
        assertThat(errorList.get(0).getMessageCode(), equalTo("fieldErrorCode"));
    }

    @Test
    public void globalErrorIsPopulatedCorrectly() {
        ConstraintViolation violation = mock(ConstraintViolation.class);
        when(violation.getMessage()).thenReturn("fieldErrorCode");
        when(violation.getPropertyPath()).thenReturn(null);

        ConstraintViolationErrorList errorList = new ConstraintViolationErrorList(
                new HashSet<ConstraintViolation>(Arrays.asList(violation)));

        assertThat(errorList.size(), equalTo(1));
        assertThat(errorList.get(0).getField(), equalTo(null));
        assertThat(errorList.get(0).getDefaultMessage(), equalTo("fieldErrorCode"));
        assertThat(errorList.get(0).getMessageCode(), equalTo("fieldErrorCode"));
    }
}
