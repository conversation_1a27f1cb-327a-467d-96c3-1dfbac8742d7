package com.gumtree.web.common.error.json;

import com.gumtree.web.common.error.ErrorMessageResolver;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class JsonValidationErrorReporterTest {

    private ErrorMessageResolver messageResolver;

    private JsonValidationErrorReporter errorReporter;

    @Before
    public void init() {
        messageResolver = mock(ErrorMessageResolver.class);
        errorReporter = new JsonValidationErrorReporter(messageResolver);
    }

    @Test
    public void simpleFieldErrorsAddedToModelCorrectly() {
        when(messageResolver.getMessage("some.message.code.1", null)).thenReturn("message 1");
        when(messageResolver.getMessage("some.message.code.2", null)).thenReturn("message 2");
        errorReporter.fieldError("field1", "some.message.code.1");
        errorReporter.fieldError("field2", "some.message.code.2");
        JsonValidationResponse response = errorReporter.getValidationResponse();
        JsonComponentError componentError1 = response.getValidation().getComponents().get(0);
        JsonComponentError componentError2 = response.getValidation().getComponents().get(1);
        assertThat(componentError1.getComponent(), equalTo("field1"));
        assertThat(componentError1.getMessages(), contains("<p>message 1</p>"));
        assertThat(componentError2.getComponent(), equalTo("field2"));
        assertThat(componentError2.getMessages(), contains("<p>message 2</p>"));
    }

    @Test
    public void extendedFieldErrorsAddedToModelCorrectly() {
        when(messageResolver.getMessage("some.message.code.1", "default message 1", "arg1", "arg2")).thenReturn("message 1");
        when(messageResolver.getMessage("some.message.code.2", "default message 2", "arg3", "arg4")).thenReturn("message 2");
        errorReporter.fieldError("field1", "some.message.code.1", "default message 1", "arg1", "arg2");
        errorReporter.fieldError("field2", "some.message.code.2", "default message 2", "arg3", "arg4");
        JsonValidationResponse response = errorReporter.getValidationResponse();
        JsonComponentError componentError1 = response.getValidation().getComponents().get(0);
        JsonComponentError componentError2 = response.getValidation().getComponents().get(1);
        assertThat(componentError1.getComponent(), equalTo("field1"));
        assertThat(componentError1.getMessages(), contains("<p>message 1</p>"));
        assertThat(componentError2.getComponent(), equalTo("field2"));
        assertThat(componentError2.getMessages(), contains("<p>message 2</p>"));
    }

    @Test
    public void simpleGlobalErrorAddedToModelCorrectly() {
        when(messageResolver.getMessage("some.message.code.1", null)).thenReturn("message 1");
        when(messageResolver.getMessage("some.message.code.2", null)).thenReturn("message 2");
        errorReporter.globalError("some.message.code.1");
        errorReporter.globalError("some.message.code.2");
        JsonValidationResponse response = errorReporter.getValidationResponse();
        assertThat(response.getValidation().getGlobalMessages(), containsInAnyOrder("<p>message 1</p>", "<p>message 2</p>"));
    }

    @Test
    public void extendedGlobalErrorAddedToModelCorrectly() {
        when(messageResolver.getMessage("some.message.code.1", "default message 1", "arg1", "arg2")).thenReturn("message 1");
        when(messageResolver.getMessage("some.message.code.2", "default message 2", "arg3", "arg4")).thenReturn("message 2");
        errorReporter.globalError("some.message.code.1", "default message 1", "arg1", "arg2");
        errorReporter.globalError("some.message.code.2", "default message 2", "arg3", "arg4");
        JsonValidationResponse response = errorReporter.getValidationResponse();
        assertThat(response.getValidation().getGlobalMessages(), containsInAnyOrder("<p>message 1</p>", "<p>message 2</p>"));
    }

    @Test
    public void modelIsEmptyWhenNoErrorsAdded() {
        JsonValidationResponse response = errorReporter.getValidationResponse();
        assertThat(response.getValidation().getGlobalMessages().size(), equalTo(0));
        assertThat(response.getValidation().getComponents().size(), equalTo(0));
    }
}
