package com.gumtree.web.common.thirdparty.sitezoneresolver;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.web.common.thirdparty.LegacySiteNameResolver;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import org.junit.Before;
import org.junit.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class DefaultSiteZoneResolverTest {

    private DefaultSiteZoneResolver resolver;
    private ThirdPartyRequestContext<?> requestContext;

    @Before
    public void setUp() {
        LegacySiteNameResolver legacySiteNameResolver = mock(LegacySiteNameResolver.class);
        resolver = new DefaultSiteZoneResolver(legacySiteNameResolver);
        requestContext = mock(ThirdPartyRequestContext.class);
        when(legacySiteNameResolver.resolveSiteName(anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                return invocation.getArguments()[0];
            }
        });
    }

    @Test
    public void testSiteAndZoneWhenCategoryIsRoot() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        when(requestContext.getCategory()).thenReturn(root);
        when(requestContext.getCategory(0)).thenReturn(root);
        assertThat(resolver.resolveSiteName(requestContext), equalTo("toplevel"));
        assertThat(resolver.resolveZoneName(requestContext), equalTo("toplevel"));
    }

    @Test
    public void testSiteAndZoneWhenCategoryIsL1() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        Category l1Category = createCategory(2, "test-l1-category");
        when(requestContext.getCategory()).thenReturn(l1Category);
        when(requestContext.getCategory(0)).thenReturn(root);
        when(requestContext.getCategory(1)).thenReturn(l1Category);
        assertThat(resolver.resolveSiteName(requestContext), equalTo("test-l1-category"));
        assertThat(resolver.resolveZoneName(requestContext), equalTo("toplevel"));
    }

    @Test
    public void testSiteAndZoneWhenCategoryIsL2() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        Category l1Category = createCategory(2, "test-l1-category");
        Category l2Category = createCategory(3, "test-l2-category");
        when(requestContext.getCategory()).thenReturn(l2Category);
        when(requestContext.getCategory(0)).thenReturn(root);
        when(requestContext.getCategory(1)).thenReturn(l1Category);
        when(requestContext.getCategory(2)).thenReturn(l2Category);
        assertThat(resolver.resolveSiteName(requestContext), equalTo("test-l1-category"));
        assertThat(resolver.resolveZoneName(requestContext), equalTo("test-l2-category"));
    }

    @Test
    public void testSiteAndZoneWhenCategoryIsL3() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        Category l1Category = createCategory(2, "test-l1-category");
        Category l2Category = createCategory(3, "test-l2-category");
        Category l3Category = createCategory(4, "test-l3-category");
        when(requestContext.getCategory()).thenReturn(l3Category);
        when(requestContext.getCategory(0)).thenReturn(root);
        when(requestContext.getCategory(1)).thenReturn(l1Category);
        when(requestContext.getCategory(2)).thenReturn(l2Category);
        when(requestContext.getCategory(3)).thenReturn(l3Category);
        assertThat(resolver.resolveSiteName(requestContext), equalTo("test-l1-category"));
        assertThat(resolver.resolveZoneName(requestContext), equalTo("test-l2-category"));
    }

    @Test
    public void testSiteAndZoneWhenCategoryIsL4() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        Category l1Category = createCategory(2, "test-l1-category");
        Category l2Category = createCategory(3, "test-l2-category");
        Category l3Category = createCategory(4, "test-l3-category");
        Category l4Category = createCategory(5, "test-l4-category");
        when(requestContext.getCategory()).thenReturn(l3Category);
        when(requestContext.getCategory(0)).thenReturn(root);
        when(requestContext.getCategory(1)).thenReturn(l1Category);
        when(requestContext.getCategory(2)).thenReturn(l2Category);
        when(requestContext.getCategory(3)).thenReturn(l3Category);
        when(requestContext.getCategory(4)).thenReturn(l4Category);
        assertThat(resolver.resolveSiteName(requestContext), equalTo("test-l1-category"));
        assertThat(resolver.resolveZoneName(requestContext), equalTo("test-l2-category"));
    }

    /**
     * TEST SPECIFICALLY TO ACCOUNT FOR NEED TO DEAL WITH FLATS & HOUSES LEVELS DIFFERENTLY. Basically, due
     * to legacy commitments, we need to "bump up" the l2 flats and houses categories to appear as l1s within
     * the level hierarchy.
     */

    @Test
    public void testSiteAndZoneWhenCategoryIsFlatsAndHouses() {
        Category root = createCategory(1, Categories.ALL.getSeoName());
        Category flatsHouses = createCategory(10201, Categories.FLATS_AND_HOUSES.getSeoName());
        when(requestContext.getCategory()).thenReturn(flatsHouses);
        when(requestContext.getCategory(0)).thenReturn(root);
        when(requestContext.getCategory(1)).thenReturn(flatsHouses);
        assertThat(resolver.resolveSiteName(requestContext), equalTo("toplevel"));
        assertThat(resolver.resolveZoneName(requestContext), equalTo(Categories.FLATS_AND_HOUSES.getSeoName()));
    }

    @Test
    public void testSiteAndZoneWhereCategoryIsL2BelowFlatsAndHouses() {
        Category flatsHouses = createCategory(10201, Categories.FLATS_AND_HOUSES.getSeoName());
        Category l2Category = createCategory(2, "test-l2-category");
        when(requestContext.getCategory()).thenReturn(l2Category);
        when(requestContext.getCategory(0)).thenReturn(flatsHouses);
        when(requestContext.getCategory(1)).thenReturn(l2Category);
        assertThat(resolver.resolveSiteName(requestContext), equalTo("test-l2-category"));
        assertThat(resolver.resolveZoneName(requestContext), equalTo("toplevel"));
    }

    @Test
    public void testSiteAndZoneWhereCategoryIsL3BelowFlatsAndHouses() {
        Category flatsHouses = createCategory(10201, Categories.FLATS_AND_HOUSES.getSeoName());
        Category l2Category = createCategory(2, "test-l2-category");
        Category l3Category = createCategory(3, "test-l3-category");
        when(requestContext.getCategory()).thenReturn(l3Category);
        when(requestContext.getCategory(0)).thenReturn(flatsHouses);
        when(requestContext.getCategory(1)).thenReturn(l2Category);
        when(requestContext.getCategory(2)).thenReturn(l3Category);
        assertThat(resolver.resolveSiteName(requestContext), equalTo("test-l2-category"));
        assertThat(resolver.resolveZoneName(requestContext), equalTo("test-l3-category"));
    }

    @Test
    public void testSiteAndZoneWhereCategoryIsL4BelowFlatsAndHouses() {
        Category flatsHouses = createCategory(10201, Categories.FLATS_AND_HOUSES.getSeoName());
        Category l2Category = createCategory(2, "test-l2-category");
        Category l3Category = createCategory(3, "test-l3-category");
        Category l4Category = createCategory(4, "test-l4-category");
        when(requestContext.getCategory()).thenReturn(l4Category);
        when(requestContext.getCategory(0)).thenReturn(flatsHouses);
        when(requestContext.getCategory(1)).thenReturn(l2Category);
        when(requestContext.getCategory(2)).thenReturn(l3Category);
        when(requestContext.getCategory(3)).thenReturn(l4Category);
        assertThat(resolver.resolveSiteName(requestContext), equalTo("test-l2-category"));
        assertThat(resolver.resolveZoneName(requestContext), equalTo("test-l3-category"));
    }

    private Category createCategory(int id, String name) {
        Category category = mock(Category.class);
        when(category.getId()).thenReturn((long)id);
        when(category.getSeoName()).thenReturn(name);
        return category;
    }
}
