package com.gumtree.web.common.page.model.impl;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.Link;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.security.UserContextPageLinks;
import com.gumtree.web.common.security.UserContextPageLinksFactory;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Matchers;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Matchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CommonHeaderModelTest {

    private UrlScheme urlScheme;

    private CommonHeaderModel headerModel;

    private Location homepageLocation;

    private Category category;

    private UserContextPageLinksFactory pageLinksFactory;

    private UserContextPageLinks pageLinks;

    @Before
    public void init() {
        urlScheme = mock(UrlScheme.class);
        pageLinksFactory = mock(UserContextPageLinksFactory.class);
        headerModel = null;
        homepageLocation = mock(Location.class);

        pageLinks = mock(UserContextPageLinks.class);
        when(pageLinks.getPostAdLink()).thenReturn(new SimpleLink("Post Ad", "postad.url"));
        when(pageLinks.getPostEventLink()).thenReturn(new SimpleLink("Post Event", "postEvent.url"));
        when(pageLinks.getManageAdsLink()).thenReturn(new SimpleLink("Manage Ads", "manageads.url"));
        when(pageLinks.getEditAccountLink()).thenReturn(new SimpleLink("Edit Account", "editaccount.url"));
        when(pageLinks.getLogoutLink()).thenReturn(new SimpleLink("Logout", "logout.url"));
        when(pageLinks.getCreateAccountLink()).thenReturn(new SimpleLink("Create account", "createaccount.url"));
        when(pageLinks.getLoginLink()).thenReturn(new SimpleLink("Login", "login.url"));

        when(pageLinksFactory.createPageLinks(homepageLocation, category)).thenReturn(pageLinks);
    }

    @Test
    public void getHomePageLinkTest() {
        when(urlScheme.urlFor(Matchers.<Location>any())).thenReturn("marks.house.url");
        when(homepageLocation.getDisplayName()).thenReturn("Mark's");
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, false, false, false);
        Link result = headerModel.getHomePageLink();
        assertThat(result.getText(), equalTo("Mark's"));
        assertThat(result.getUrl(), equalTo("marks.house.url"));
        assertThat(headerModel.getHomepageLocationDisplayName(), equalTo("Mark's"));
    }

    @Test
    public void getUKHomePageLinkTest() {
        when(urlScheme.urlFor(Actions.UK_HOME)).thenReturn("uk.url");
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, false, false, false);
        Link result = headerModel.getUKHomePageLink();
        assertThat(result.getText(), equalTo("United Kingdom"));
        assertThat(result.getUrl(), equalTo("uk.url"));
    }

    @Test
    public void getHomePageLinkTestForUnitedKingdom() {
        when(homepageLocation.getName()).thenReturn(Location.UK);
        when(urlScheme.urlFor(Actions.UK_HOME)).thenReturn("uk.url");
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, false, false, false);
        Link result = headerModel.getHomePageLink();
        assertThat(result.getText(), equalTo("United Kingdom"));
        assertThat(result.getUrl(), equalTo("uk.url"));
        assertThat(headerModel.getHomepageLocationDisplayName(), equalTo("United Kingdom"));
    }

    @Test
    public void whenHomepageLocationIsNullLinkIsDefaultedToUK() {
        when(homepageLocation.getName()).thenReturn(Location.UK);
        when(urlScheme.urlFor(Actions.UK_HOME)).thenReturn("uk.url");
        headerModel = new CommonHeaderModel(urlScheme, null, category, pageLinksFactory, false, false, false);
        Link result = headerModel.getHomePageLink();
        assertThat(result.getText(), equalTo("United Kingdom"));
        assertThat(result.getUrl(), equalTo("uk.url"));
        assertThat(headerModel.getHomepageLocationDisplayName(), equalTo("United Kingdom"));
    }

    @Test
    public void postAdLinkCorrect() {
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, false, false, false);
        assertThat(headerModel.getPostAdLink().getText(), equalTo("Post Ad"));
        assertThat(headerModel.getPostAdLink().getUrl(), equalTo("postad.url"));
    }

    @Test
    public void postEventLinkCorrect() {
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, false, false, false);
        assertThat(headerModel.getPostEventLink().getText(), equalTo("Post Event"));
        assertThat(headerModel.getPostEventLink().getUrl(), equalTo("postEvent.url"));
    }

    @Test
    public void manageAdsLinkCorrect() {
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, false, false, false);
        assertThat(headerModel.getManageAdsLink().getText(), equalTo("Manage Ads"));
        assertThat(headerModel.getManageAdsLink().getUrl(), equalTo("manageads.url"));
    }

    @Test
    public void editAccountLinkCorrect() {
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, false, false, false);
        assertThat(headerModel.getEditAccountLink().getText(), equalTo("Edit Account"));
        assertThat(headerModel.getEditAccountLink().getUrl(), equalTo("editaccount.url"));
    }

    @Test
    public void logoutLinkCorrect() {
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, false, false, false);
        assertThat(headerModel.getLogoutLink().getText(), equalTo("Logout"));
        assertThat(headerModel.getLogoutLink().getUrl(), equalTo("logout.url"));
    }

    @Test
    public void loginLinkCorrect() {
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, false, false, false);
        assertThat(headerModel.getLoginLink().getText(), equalTo("Login"));
        assertThat(headerModel.getLoginLink().getUrl(), equalTo("login.url"));
    }

    @Test
    public void createAccountLinkCorrect() {
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, false, false, false);
        assertThat(headerModel.getCreateAccountLink().getText(), equalTo("Create account"));
        assertThat(headerModel.getCreateAccountLink().getUrl(), equalTo("createaccount.url"));
    }

    @Test
    public void showCreateAccountLinkSetCorrectly() {
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, true, false, false);
        assertThat(headerModel.isShowCreateAccountLink(), equalTo(true));
    }

    @Test
    public void messagesLinkReturnedOnlyWhenMessageCentreEnabled(){
        headerModel = new CommonHeaderModel(urlScheme, homepageLocation, category, pageLinksFactory, true, false, false);
        String url = "test";
        headerModel.setMessagesLink(new SimpleLink("text", url));
        headerModel.setMessageCentreEnabled(false);
        assertThat(headerModel.getMessagesLink(), equalTo(null));
        headerModel.setMessageCentreEnabled(true);
        assertThat(headerModel.getMessagesLink().getUrl(), equalTo(url));
    }
}
