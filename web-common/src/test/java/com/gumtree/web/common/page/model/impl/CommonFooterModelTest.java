package com.gumtree.web.common.page.model.impl;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.Link;
import com.gumtree.util.url.UrlScheme;
import org.apache.commons.lang.WordUtils;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


/**
 */
public class CommonFooterModelTest {

    private LocationService locationService;

    private UrlScheme urlScheme;

    private Category category;

    private Category allCategory;

    private Location location;

    private Location ukLocation;

    private Location rootLocation;

    private List<Location> topTenCities;

    @Before
    public void init() {
        locationService = mock(LocationService.class);
        urlScheme = mock(UrlScheme.class);
        location = mock(Location.class);
        ukLocation = mock(Location.class);
        rootLocation = mock(Location.class);
        category = mock(Category.class);
        allCategory = mock(Category.class);
        when(category.getSeoDisplayName()).thenReturn("Seo Category");
        when(category.getName()).thenReturn("Test Category");
        when(allCategory.getSeoName()).thenReturn(Categories.ALL.getSeoName());
        when(allCategory.getSeoDisplayName()).thenReturn("All Classifieds");
        when(locationService.getRootLocation()).thenReturn(rootLocation);
        when(location.getName()).thenReturn("not-uk");
        when(location.getDisplayName()).thenReturn("Test Location");
        when(ukLocation.getName()).thenReturn(Location.UK);
        when(ukLocation.getDisplayName()).thenReturn("United Kingdom");
        when(rootLocation.getName()).thenReturn(Location.UK);
        when(rootLocation.getDisplayName()).thenReturn("United Kingdom");

        topTenCities = new ArrayList<>();
        for (String city : CommonFooterModel.TOP_10_CITIES) {
            Location location = mock(Location.class);
            when(location.getName()).thenReturn(city);
            when(location.getDisplayName()).thenReturn(WordUtils.capitalize(city));
            topTenCities.add(location);
            when(urlScheme.urlFor(location)).thenReturn("/url/for/" + city);
            when(urlScheme.urlForCategory(category, location)).thenReturn("/url/for/category/in/" + city);
            when(urlScheme.urlForCategory(allCategory, location)).thenReturn("/url/for/category/in/" + city);
        }
        when(locationService.getByNames(CommonFooterModel.TOP_10_CITIES)).thenReturn(topTenCities);

    }

    @Test
    public void popularCitiesPopulatedCorrectlyWhenCategoryIsNull() {
        CommonFooterModel model = new CommonFooterModel(locationService, urlScheme, location, true);
        List<Link> topCityLinks = model.getTopCityLinks();

        int index = 0;
        for (String city : CommonFooterModel.TOP_10_CITIES) {
            Link link = topCityLinks.get(index);
            assertThat(link.getText(), equalTo(WordUtils.capitalize(city)));
            assertThat(link.getUrl(), equalTo("/url/for/" + city));
            index++;
        }
    }

    @Test
    public void popularCitiesPopulatedCorrectlyWhenCategoryIsNotNull() {
        CommonFooterModel model = new CommonFooterModel(locationService, urlScheme, location, category, true);
        List<Link> topCityLinks = model.getTopCityLinks();

        int index = 0;
        for (String city : CommonFooterModel.TOP_10_CITIES) {
            Link link = topCityLinks.get(index);
            assertThat(link.getText(), equalTo("Seo Category in " + WordUtils.capitalize(city)));
            assertThat(link.getUrl(), equalTo("/url/for/category/in/" + city));
            index++;
        }
    }

    @Test
    public void popularCitiesPopulatedCorrectlyWhenCategoryIsAll() {

        CommonFooterModel model = new CommonFooterModel(locationService, urlScheme, location, allCategory, true);
        List<Link> topCityLinks = model.getTopCityLinks();

        int index = 0;
        for (String city : CommonFooterModel.TOP_10_CITIES) {
            Link link = topCityLinks.get(index);
            assertThat(link.getText(), equalTo("All Classifieds in " + WordUtils.capitalize(city)));
            assertThat(link.getUrl(), equalTo("/url/for/category/in/" + city));
            index++;
        }
    }

    @Test
    public void testGetPopularSearchesLinkWhenEnabled() throws Exception {
        when(urlScheme.urlFor(Actions.POPULAR_SEARCHES)).thenReturn("/expected/url");
        CommonFooterModel model = new CommonFooterModel(locationService, urlScheme, location, category, true);
        Link link = model.getPopularSearchesLink();
        assertThat(link.getUrl(), equalTo("/expected/url"));
        assertThat(link.getText(), equalTo("Popular searches"));
    }

    @Test
    public void shouldPopulateModelWithAndroidAppLink() {
        when(urlScheme.urlFor(Actions.ANDROID_APP)).thenReturn("http://androidlink");
        CommonFooterModel model = new CommonFooterModel(locationService, urlScheme, location, category, true);
        Link link = model.getAndroidAppLink();
        assertThat(link.getUrl(), equalTo("http://androidlink"));
        assertThat(link.getText(), equalTo("Android app"));
    }


}
