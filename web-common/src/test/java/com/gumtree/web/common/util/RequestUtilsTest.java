package com.gumtree.web.common.util;

import org.junit.Test;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.AbstractUrlBasedView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Optional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class RequestUtilsTest {

    @Test
    public void testIsRedirectViewNameForAbsoluteUrl() {
        ModelAndView modelAndView = mock(ModelAndView.class);
        when(modelAndView.getViewName()).thenReturn("redirect:http://www.test.com");
        assertThat(RequestUtils.isRedirect(modelAndView), equalTo(true));
    }

    @Test
    public void testIsRedirectViewName() {
        ModelAndView modelAndView = mock(ModelAndView.class);
        when(modelAndView.getViewName()).thenReturn("redirect:/test");
        assertThat(RequestUtils.isRedirect(modelAndView), equalTo(true));
    }

    @Test
    public void testNotRedirectViewName() {
        ModelAndView modelAndView = mock(ModelAndView.class);
        when(modelAndView.getViewName()).thenReturn("test");
        assertThat(RequestUtils.isRedirect(modelAndView), equalTo(false));
    }

    @Test
    public void testIsRedirectGetView() {
        ModelAndView modelAndView = mock(ModelAndView.class);
        when(modelAndView.getView()).thenReturn(new testViewClassInstanceOfRedirectView());
        assertThat(RequestUtils.isRedirect(modelAndView), equalTo(true));
    }

    @Test
    public void testIsNotARedirectGetView() {
        ModelAndView modelAndView = mock(ModelAndView.class);
        when(modelAndView.getView()).thenReturn(new testViewClassNotAnInstanceOfRedirectView());
        assertThat(RequestUtils.isRedirect(modelAndView), equalTo(false));
    }

    @Test
    public void shouldResolveRemoteIP() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        Optional<String> ip = Optional.of("*********");
        when(request.getHeader("x-forwarded-for")).thenReturn(ip.get());
        assertThat(RequestUtils.resolveRemoteIp(request), equalTo(ip));
    }

    private class testViewClassInstanceOfRedirectView extends RedirectView {
    }

    private class testViewClassNotAnInstanceOfRedirectView extends AbstractUrlBasedView {
        @Override
        protected void renderMergedOutputModel(Map<String, Object> model, HttpServletRequest request, HttpServletResponse response) throws Exception {
        }
    }
}
