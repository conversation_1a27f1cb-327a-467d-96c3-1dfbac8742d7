package com.gumtree.web.common.request.context.thirdparty;

import com.gumtree.web.common.thirdparty.LegacySiteNameResolver;
import junit.framework.TestCase;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

public class LegacySiteNameResolverTest extends TestCase {

    private LegacySiteNameResolver siteNameResolver = new LegacySiteNameResolver();

    @Test
    public void testSiteNameReturnedWhenNoResolutionConfigured() {
        assertThat(siteNameResolver.resolveSiteName("any-old-site"), equalTo("any-old-site"));
    }

    @Test
    public void testCarsVansMotorbikesResolvedToCars() {
        assertThat(siteNameResolver.resolveSiteName("cars-vans-motorbikes"), equalTo("cars"));
    }

    @Test
    public void testForSaleResolvedToForSale() {
        assertThat(siteNameResolver.resolveSiteName("for-sale"), equalTo("forsale"));
    }

    @Test
    public void testPropertyForSaleResolvedToFaltHouseForSale() {
        assertThat(siteNameResolver.resolveSiteName("property-for-sale"), equalTo("propertyforsale"));
    }

    @Test
    public void testFlatsAndHousesForRentResolvedToRentals() {
        assertThat(siteNameResolver.resolveSiteName("flats-and-houses-for-rent"), equalTo("rentals"));
    }

    @Test
    public void testBusinessServicesResolvedToServices() {
        assertThat(siteNameResolver.resolveSiteName("business-services"), equalTo("services"));
    }

}
