package com.gumtree.web.common.pagination;

import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class AppendingPaginationUrlGeneratorTest {
    private static final String BASE_URL = "/baseurl";

    private AppendingPaginationUrlGenerator urlGenerator = new AppendingPaginationUrlGenerator(BASE_URL);

    @Test
    public void shouldBuildCorrectUrlForVariousPages() {
        assertThat(urlGenerator.forPage(-1), is(BASE_URL));
        assertThat(urlGenerator.forPage(0), is(BASE_URL));
        assertThat(urlGenerator.forPage(1), is(BASE_URL));
        assertThat(urlGenerator.forPage(9), is(BASE_URL + "/page9" ));
        assertThat(urlGenerator.forPage(18), is(BASE_URL + "/page18" ));
    }
}
