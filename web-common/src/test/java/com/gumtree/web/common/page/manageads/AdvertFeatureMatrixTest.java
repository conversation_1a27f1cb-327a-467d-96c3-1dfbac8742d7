package com.gumtree.web.common.page.manageads;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.Ad;
import com.gumtree.api.AdFeature;
import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.api.domain.order.CreateOrderItemBean;
import com.gumtree.seller.domain.product.entity.ProductName;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class AdvertFeatureMatrixTest {

    private DateTime now;

    private DateTime today;

    private DateTime nextWeek;

    @Before
    public void setup() {
        now = new DateTime();
        today = now.toDateMidnight().toDateTime();
        nextWeek = today.plusDays(7);
    }

    @Test
    public void singleAdvertWithoutExistingFeaturesCreatesEmptyOrder() {
        Long accountId = 42L;

        Ad advert = createAdvert(1L);

        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advertFeatureMatrix.add(advert);

        CreateOrderBean createOrderBean = advertFeatureMatrix.createOrderBean(
                accountId, Lists.<Ad>newArrayList(advert));

        assertThat(createOrderBean.getAccountId(), equalTo(accountId));
        assertThat(createOrderBean.getItems().size(), equalTo(0));
    }

    @Test
    public void singleAdvertWithMultipleExistingFeaturesCreatesEmptyOrder() {
        Long accountId = 42L;

        Ad advert = createAdvert(1L);
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advert.getFeatures().add(createAdFeature(ProductName.FEATURE_14_DAY, nextWeek));
        advert.getFeatures().add(createAdFeature(ProductName.URGENT, nextWeek));
        advert.getFeatures().add(createAdFeature(ProductName.HOMEPAGE_SPOTLIGHT, nextWeek));
        advertFeatureMatrix.add(advert);

        CreateOrderBean createOrderBean = advertFeatureMatrix.createOrderBean(
                accountId, Lists.<Ad>newArrayList(advert));

        assertThat(createOrderBean.getAccountId(), equalTo(accountId));
        assertThat(createOrderBean.getItems().size(), equalTo(0));
    }

    @Test
    public void singleAdvertWithoutExistingFeaturesAndWithSelectedFeaturesCreatesCorrectOrder() {
        Long accountId = 42L;

        Ad advert = createAdvert(1L);
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advertFeatureMatrix.add(advert);
        Map<String, Boolean> features = Maps.newHashMap();
        features.put("BUMP_UP", true);
        features.put("URGENT", true);
        features.put("FEATURE_3_DAY", true);

        advertFeatureMatrix.getMatrix().put("1", features);

        CreateOrderBean createOrderBean = advertFeatureMatrix.createOrderBean(
                accountId, Lists.<Ad>newArrayList(advert));

        assertThat(createOrderBean.getAccountId(), equalTo(accountId));
        assertThat(createOrderBean.getItems().size(), equalTo(3));

        assertTrue(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.BUMP_UP));
        assertTrue(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.URGENT));
        assertTrue(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.FEATURE_3_DAY));

        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.FEATURE_7_DAY));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.FEATURE_14_DAY));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.HOMEPAGE_SPOTLIGHT));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.INSERTION));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.APPVAULT_RESPONSE_MANAGER));
    }

    @Test
    public void singleAdvertWithMultipleExistingFeaturesAndWithSelectedFeaturesCreatesCorrectOrder() {
        Long accountId = 42L;

        Ad advert = createAdvert(1L);
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advertFeatureMatrix.add(advert);
        advert.getFeatures().add(createAdFeature(ProductName.URGENT, nextWeek));
        advert.getFeatures().add(createAdFeature(ProductName.HOMEPAGE_SPOTLIGHT, nextWeek));

        Map<String, Boolean> features = Maps.newHashMap();
        features.put("BUMP_UP", true);
        features.put("URGENT", true);
        features.put("FEATURE_3_DAY", true);

        advertFeatureMatrix.getMatrix().put("1", features);

        CreateOrderBean createOrderBean = advertFeatureMatrix.createOrderBean(
                accountId, Lists.<Ad>newArrayList(advert));

        assertThat(createOrderBean.getAccountId(), equalTo(accountId));
        assertThat(createOrderBean.getItems().size(), equalTo(2));

        assertTrue(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.BUMP_UP));
        assertTrue(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.FEATURE_3_DAY));

        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.FEATURE_7_DAY));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.FEATURE_14_DAY));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.URGENT));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.HOMEPAGE_SPOTLIGHT));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.INSERTION));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.APPVAULT_RESPONSE_MANAGER));
    }

    @Test
    public void multipleAdvertsWithMultipleExistingFeaturesAndWithSelectedFeaturesCreatesCorrectOrder() {
        Long accountId = 42L;

        List<Ad> adverts = Lists.newArrayList();
        Ad advert = null;
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advert = createAdvert(1L);
        advert.getFeatures().add(createAdFeature(ProductName.URGENT, nextWeek));
        advert.getFeatures().add(createAdFeature(ProductName.HOMEPAGE_SPOTLIGHT, nextWeek));
        advertFeatureMatrix.add(advert);
        adverts.add(advert);

        advert = createAdvert(2L);
        advert.getFeatures().add(createAdFeature(ProductName.FEATURE_14_DAY, nextWeek));
        advertFeatureMatrix.add(advert);
        adverts.add(advert);


        advertFeatureMatrix.getMatrix().get("1").put("BUMP_UP", true);
        advertFeatureMatrix.getMatrix().get("1").put("URGENT", true);
        advertFeatureMatrix.getMatrix().get("1").put("FEATURE_3_DAY", true);

        advertFeatureMatrix.getMatrix().get("2").put("BUMP_UP", true);
        advertFeatureMatrix.getMatrix().get("2").put("URGENT", true);
        advertFeatureMatrix.getMatrix().get("2").put("FEATURE_3_DAY", true);

        CreateOrderBean createOrderBean = advertFeatureMatrix.createOrderBean(
                accountId, adverts);

        assertThat(createOrderBean.getAccountId(), equalTo(accountId));
        assertThat(createOrderBean.getItems().size(), equalTo(5));

        assertTrue(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.BUMP_UP));
        assertTrue(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.FEATURE_3_DAY));

        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.FEATURE_7_DAY));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.FEATURE_14_DAY));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.URGENT));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.HOMEPAGE_SPOTLIGHT));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.INSERTION));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 1L, ProductName.APPVAULT_RESPONSE_MANAGER));

        assertTrue(orderContainsAdvertFeature(createOrderBean, 2L, ProductName.BUMP_UP));
        assertTrue(orderContainsAdvertFeature(createOrderBean, 2L, ProductName.URGENT));
        assertTrue(orderContainsAdvertFeature(createOrderBean, 2L, ProductName.FEATURE_3_DAY));

        assertFalse(orderContainsAdvertFeature(createOrderBean, 2L, ProductName.FEATURE_7_DAY));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 2L, ProductName.FEATURE_14_DAY));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 2L, ProductName.HOMEPAGE_SPOTLIGHT));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 2L, ProductName.INSERTION));
        assertFalse(orderContainsAdvertFeature(createOrderBean, 2L, ProductName.APPVAULT_RESPONSE_MANAGER));
    }

    @Test
    public void singleAdvertWithNoFeaturesCanDisplayFeatureAll() {
        Ad advert = createAdvert(1L);
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advertFeatureMatrix.add(advert);

        assertThat(advertFeatureMatrix.isFeatureAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isUrgentAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isSpotlightAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isBumpUpAllEnabled(), equalTo(true));
    }

    @Test
    public void singleAdvertWith3DayFeatureCannotDisplayFeatureAll() {
        Ad advert = createAdvert(1L);
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advert.getFeatures().add(createAdFeature(ProductName.FEATURE_3_DAY, nextWeek));
        advertFeatureMatrix.add(advert);

        assertThat(advertFeatureMatrix.isFeatureAllEnabled(), equalTo(false));
        assertThat(advertFeatureMatrix.isUrgentAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isSpotlightAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isBumpUpAllEnabled(), equalTo(true));
    }

    @Test
    public void multipleAdvertsWhenOneHas3DayFeatureCanDisplayFeatureAll() {
        Ad advert = null;
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advert = createAdvert(1L);
        advert.getFeatures().add(createAdFeature(ProductName.FEATURE_3_DAY, nextWeek));
        advertFeatureMatrix.add(advert);

        advert = createAdvert(2L);
        advertFeatureMatrix.add(advert);

        assertThat(advertFeatureMatrix.isFeatureAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isUrgentAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isSpotlightAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isBumpUpAllEnabled(), equalTo(true));
    }

    @Test
    public void singleAdvertWithUrgentFeatureCannotDisplayUrgentAll() {
        Ad advert = createAdvert(1L);
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advert.getFeatures().add(createAdFeature(ProductName.URGENT, nextWeek));
        advertFeatureMatrix.add(advert);

        assertThat(advertFeatureMatrix.isFeatureAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isUrgentAllEnabled(), equalTo(false));
        assertThat(advertFeatureMatrix.isSpotlightAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isBumpUpAllEnabled(), equalTo(true));
    }

    @Test
    public void singleAdvertWithSpotlightFeatureCannotDisplaySpotlightAll() {
        Ad advert = createAdvert(1L);
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advert.getFeatures().add(createAdFeature(ProductName.HOMEPAGE_SPOTLIGHT, nextWeek));
        advertFeatureMatrix.add(advert);

        assertThat(advertFeatureMatrix.isFeatureAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isUrgentAllEnabled(), equalTo(true));
        assertThat(advertFeatureMatrix.isSpotlightAllEnabled(), equalTo(false));
        assertThat(advertFeatureMatrix.isBumpUpAllEnabled(), equalTo(true));
    }

    @Test
    public void singleAdvertWithAllFeaturesCanDisplayBumpUpAll() {
        Ad advert = createAdvert(1L);
        AdvertFeatureMatrix advertFeatureMatrix = new AdvertFeatureMatrix();

        advert.getFeatures().add(createAdFeature(ProductName.FEATURE_14_DAY, nextWeek));
        advert.getFeatures().add(createAdFeature(ProductName.URGENT, nextWeek));
        advert.getFeatures().add(createAdFeature(ProductName.HOMEPAGE_SPOTLIGHT, nextWeek));
        advertFeatureMatrix.add(advert);

        assertThat(advertFeatureMatrix.isBumpUpAllEnabled(), equalTo(true));
    }

    private boolean orderContainsAdvertFeature(CreateOrderBean createOrderBean, Long id, ProductName productName) {
        for (CreateOrderItemBean createOrderItemBean : createOrderBean.getItems()) {
            if (createOrderItemBean.getAdvertId().equals(id)
                    && createOrderItemBean.getProductName().equals(productName)) {
                return true;
            }
        }
        return false;
    }

    private Ad createAdvert(Long id) {
        Ad advert = new Ad();
        advert.setId(id);
        advert.setFeatures(Lists.<AdFeature>newArrayList());
        return advert;
    }

    private AdFeature createAdFeature(ProductName productName, DateTime expiry) {
        AdFeature adFeature = new AdFeature();
        adFeature.setProductName(productName);
        adFeature.setEndDate(expiry);
        return adFeature;
    }
}
