package com.gumtree.web.common.image;

import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.advert.entity.AdvertEntity;
import com.gumtree.domain.media.Image;
import com.gumtree.domain.media.ImageSize;
import com.gumtree.domain.media.Video;
import com.gumtree.domain.media.entity.ImageEntity;
import com.gumtree.domain.media.entity.VideoEntity;
import com.gumtree.service.advert.AdvertService;
import com.gumtree.util.url.UrlScheme;
import org.junit.Test;
import org.mockito.Matchers;

import java.util.*;

import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Test the MediaGallery Logic
 */
public class MediaGalleryTest {

    @Test
    public void testNullAdvert() {
        try {
            new MediaGallery(null, createUrlScheme(true));
        } catch (IllegalArgumentException ex) {
            org.junit.Assert.assertNotNull(ex.getMessage());
        }
    }

    @Test
    public void testNullUrlSchema() {
        try {
            new MediaGallery(createAdvertService(true, true).getAdvert(5l), null);
        } catch (IllegalArgumentException ex) {
            org.junit.Assert.assertNotNull(ex.getMessage());
        }
    }

    @Test
    public void testMaxImages() {
        MediaGallery mediaGallery = new MediaGallery(createAdvertService(true, true).getAdvert(5l), createUrlScheme(true));
        assertThat(mediaGallery.getMediaComponentList().size(), equalTo(8));
    }

    @Test
    public void testAltText() {
        Advert advert = createAdvertService(true, true).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        assertThat(mediaGallery.getAltText(), equalTo(advert.getTitle() + " " + advert.getLocationText() + " Picture"));
    }

    //has main image
    @Test
    public void testHasImagesTrue() {
        Advert advert = createAdvertService(true, false).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        assertThat(mediaGallery.getHasMedia(), equalTo(true));
    }

    //no images
    @Test
    public void testHasImagesFalse() {
        Advert advert = createAdvertService(false, false).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(false));
        assertThat(mediaGallery.getHasMedia(), equalTo(false));
    }

    //main image exits and list > 0
    @Test
    public void testSingleImageFalse() {
        Advert advert = createAdvertService(true, true).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        assertThat(mediaGallery.getSingleImage(), equalTo(false));
    }

    //main image exits and list = 0
    @Test
    public void testSingleImageTrue() {
        Advert advert = createAdvertService(true, false).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        assertThat(mediaGallery.getSingleImage(), equalTo(true));
    }

    @Test
    public void testMainImageUrls() {
        Advert advert = createAdvertService(true, true).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        assertThat(mediaGallery.getMainImage().getThumbUrl(), equalTo("/image/thumb"));
        assertThat(mediaGallery.getMainImage().getMainUrl(), equalTo("/image/mid"));
        assertThat(mediaGallery.getMainImage().getFullUrl(), equalTo("/image/full"));
    }

    @Test
    public void testOtherImageUrls() {
        Advert advert = createAdvertService(true, true).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));

        assertThat(mediaGallery.getMediaComponentList().size(), equalTo(8));

        for (MediaComponent imageComponent : mediaGallery.getMediaComponentList()) {
            assertThat(imageComponent.getThumbUrl(), equalTo("/image/thumb"));
            assertThat(imageComponent.getMainUrl(), equalTo("/image/mid"));
            assertThat(imageComponent.getFullUrl(), equalTo("/image/full"));
        }
    }

    @Test
    public void testVipUrl() {
        Advert advert = createAdvertService(true, true).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        assertThat(mediaGallery.getVipUrl(), equalTo("/test/advert"));
    }

    @Test
    public void testImageUrl() {
        Advert advert = createAdvertService(true, true).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        List<MediaComponent> imageComponentList = mediaGallery.getMediaComponentList();
        assertThat(imageComponentList.isEmpty(), equalTo(false));
        MediaComponent imageComponent = imageComponentList.get(0);
        assertThat(imageComponent.getThumbUrl(), equalTo("/image/thumb"));
        assertThat(imageComponent.getMainUrl(), equalTo("/image/mid"));
        assertThat(imageComponent.getFullUrl(), equalTo("/image/full"));
    }

    @Test
    public void tryAndAddAnImageWithNoId() {
        AdvertEntity advert = new AdvertEntity();
        advert.setId(1l);
        advert.setTitle("Advert 1");
        advert.setDescription("Advert description");
        advert.setLocationText("Advert location");
        advert.setCategoryId(1L);
        advert.setLocationId(1);
        advert.setMainImage(createImage(53769399l));
        List<Image> imageEntities = new ArrayList<Image>();
        ImageEntity imageEntity = new ImageEntity();
        imageEntity.setId(null);
        imageEntities.add(imageEntity);
        imageEntities.add(createImage(53769389l));
        imageEntities.add(createImage(53769379l));
        imageEntities.add(createImage(53769369l));
        advert.setImages(imageEntities);

        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        List<MediaComponent> imageComponentList = mediaGallery.getMediaComponentList();
        assertThat(imageComponentList.isEmpty(), equalTo(false));
        assertThat(imageComponentList.size(), equalTo(3));

    }

    @Test
    public void testVideoUrlWithMainImage() {
        Advert advert = createAdvertServiceWithVideo(true, false, true, 13).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        List<MediaComponent> imageComponentList = mediaGallery.getMediaComponentList();
        assertThat(imageComponentList.isEmpty(), equalTo(false));
        MediaComponent imageComponent = imageComponentList.get(0);
        assertThat(imageComponent.getThumbUrl(), equalTo("thumb-url"));
        assertThat(imageComponent.getMainUrl(), equalTo("embed-url"));
        assertThat(imageComponent.getFullUrl(), equalTo("embed-url"));
    }

    @Test
    public void testVideoNoMainImage() {
        Advert advert = createAdvertServiceWithVideo(false, false, true, 13).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(false));
        assertThat(mediaGallery.getMainImage().getThumbUrl(), equalTo("thumb-url"));
        assertThat(mediaGallery.getMainImage().getMainUrl(), equalTo("embed-url"));
        assertThat(mediaGallery.getMainImage().getFullUrl(), equalTo("embed-url"));
    }

    @Test
    public void testVideoUrlWithMaxImages() {    //video not added because the list is full
        Advert advert = createAdvertServiceWithVideo(true, true, true, 13).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        List<MediaComponent> imageComponentList = mediaGallery.getMediaComponentList();
        assertThat(imageComponentList.isEmpty(), equalTo(false));
        for (MediaComponent mediaComponent : imageComponentList) {
            assertThat(mediaComponent.getThumbUrl(), equalTo("/image/thumb"));
            assertThat(mediaComponent.getMainUrl(), equalTo("/image/mid"));
            assertThat(mediaComponent.getFullUrl(), equalTo("/image/full"));
        }

    }

    @Test
    public void testVideoUrlWithOtherImages() {    //video added to a list of 5 images
        Advert advert = createAdvertServiceWithVideo(true, true, true, 5).getAdvert(5l);
        MediaGallery mediaGallery = new MediaGallery(advert, createUrlScheme(true));
        List<MediaComponent> imageComponentList = mediaGallery.getMediaComponentList();
        assertThat(imageComponentList.isEmpty(), equalTo(false));
        for (MediaComponent mediaComponent : imageComponentList) {
            if (mediaComponent.getMediaType().equals(MediaType.IMAGE)) {
                assertThat(mediaComponent.getThumbUrl(), equalTo("/image/thumb"));
                assertThat(mediaComponent.getMainUrl(), equalTo("/image/mid"));
                assertThat(mediaComponent.getFullUrl(), equalTo("/image/full"));
            } else {
                assertThat(mediaComponent.getThumbUrl(), equalTo("thumb-url"));
                assertThat(mediaComponent.getMainUrl(), equalTo("embed-url"));
                assertThat(mediaComponent.getFullUrl(), equalTo("embed-url"));
            }
        }
    }

    private AdvertService createAdvertService(Boolean mainImage, Boolean addImages) {
        AdvertService advertService = mock(AdvertService.class);
        AdvertEntity advert = new AdvertEntity();
        advert.setId(1l);
        advert.setTitle("Advert 1");
        advert.setDescription("Advert description");
        advert.setLocationText("Advert location");
        advert.setCategoryId(1L);
        advert.setLocationId(1);
        if (addImages) {
            advert.setImages(createImages(13));
        }
        if (mainImage) {
            advert.setMainImage(createImage(53769399l));
        }

        when(advertService.getAdvert(anyLong())).thenReturn(advert);
        when(advertService.getAdverts(Matchers.<Set<Long>>anyObject())).thenReturn(createYourGumtreeAdsList());
        return advertService;
    }

    private AdvertService createAdvertServiceWithVideo(Boolean mainImage, Boolean addImages, Boolean addVideo, int images) {
        AdvertService advertService = mock(AdvertService.class);
        AdvertEntity advert = new AdvertEntity();
        advert.setId(1l);
        advert.setTitle("Advert 1");
        advert.setDescription("Advert description");
        advert.setLocationText("Advert location");
        advert.setCategoryId(1L);
        advert.setLocationId(1);
        if (addImages) {
            advert.setImages(createImages(images));
        }
        if (mainImage) {
            advert.setMainImage(createImage(53769399l));
        }
        if (addVideo) {
            advert.setVideos(createVideos());
        }

        when(advertService.getAdvert(anyLong())).thenReturn(advert);
        when(advertService.getAdverts(Matchers.<Set<Long>>anyObject())).thenReturn(createYourGumtreeAdsList());
        return advertService;
    }

    //TODO: change to UrlSchema.class
    private UrlScheme createUrlScheme(Boolean urls) {
        return createUrlScheme(urls);
    }

    private UrlScheme createUrlScheme(boolean returnImageUrls) {
        UrlScheme urlScheme = mock(UrlScheme.class);

        Map<ImageSize, String> imageUrls = new HashMap<ImageSize, String>();
        imageUrls.put(ImageSize.THUMB, "/image/thumb");
        imageUrls.put(ImageSize.MAIN, "/image/mid");
        imageUrls.put(ImageSize.FULL, "/image/full");

        when(urlScheme.urlFor(Matchers.<Advert>anyObject())).thenReturn("/test/advert");
        // when(urlScheme.urlForMediaGallery(Matchers.<Advert>anyObject())).thenReturn("/test/advert/gallery");

        if (returnImageUrls) {
            when(urlScheme.urlsFor(Matchers.<Image>anyObject())).thenReturn(imageUrls);
        }

        when(urlScheme.urlForYoutubeEmbed(anyString())).thenReturn("embed-url");
        when(urlScheme.urlForYoutubeThumbnail(anyString())).thenReturn("thumb-url");

        return urlScheme;
    }

    private Collection<Advert> createYourGumtreeAdsList() {

        long[] ids = { 42266371, 42266099, 77513355, 77472317, 77334207, 77258843, 43266371, 43266099 };

        List<Advert> adverts = new ArrayList<Advert>();
        for (int i = 0; i < ids.length; i++) {
            AdvertEntity advert = new AdvertEntity();
            advert.setId(ids[i]);
            advert.setTitle(i + ". recently view ad");
            advert.setLocationText("Location #" + i);
            advert.setMainImage(createImage(53769399l));
            adverts.add(advert);
        }
        return adverts;
    }

    private List<Image> createImages(int num) {
        List<Image> images = new ArrayList<Image>();
        for (int i = 0; i < num; i++) {
            images.add(createImage(i + 1000000));
        }
        return images;
    }

    private ImageEntity createImage(long id) {
        ImageEntity image = new ImageEntity();
        image.setId(id);
        return image;
    }

    private List<Video> createVideos() {
        List<Video> videos = new ArrayList<Video>();
        String[] videoUrls = { "http://www.youtube.com/watch?v=n5jpVbEL0jc",
                "http://www.youtube.com/watch?v=n5jpVbEL0jc",
                "http://www.youtube.com/watch?v=m6_VEYMb4HI&feature=relmfu" };
        for (String url : videoUrls) {
            videos.add(createVideo(url));
        }
        return videos;
    }

    private VideoEntity createVideo(String url) {
        VideoEntity video = new VideoEntity();
        video.setUrl(url);
        return video;
    }
}
