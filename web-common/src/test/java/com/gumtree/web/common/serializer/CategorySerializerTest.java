package com.gumtree.web.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.gumtree.api.category.domain.Category;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.io.IOException;

import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.mock;


@RunWith(MockitoJUnitRunner.class)
public class CategorySerializerTest {

    @Mock
    private JsonGenerator jgen;

    private CategorySerializer categorySerializer = new CategorySerializer();

    @Before

    @Test
    public void shouldSerializeCategory() throws IOException {
        // given
        Category category = Category.newCategory()
                .withId(11L)
                .withSeoName("some_category")
                .withName("Some Category")
                .withParentId(1L)
                .withDepth(1)
                .build();

        // when
        categorySerializer.serialize(category, jgen, mock(SerializerProvider.class));


        // then
        InOrder verifyInOrder = inOrder(jgen);
        verifyInOrder.verify(jgen).writeStartObject();
        verifyInOrder.verify(jgen).writeFieldName("id");
        verifyInOrder.verify(jgen).writeNumber(category.getId());
        verifyInOrder.verify(jgen).writeFieldName("parentId");
        verifyInOrder.verify(jgen).writeNumber(category.getParentId());
        verifyInOrder.verify(jgen).writeFieldName("depth");
        verifyInOrder.verify(jgen).writeNumber(category.getDepth());
        verifyInOrder.verify(jgen).writeFieldName("seoName");
        verifyInOrder.verify(jgen).writeString(category.getSeoName());
        verifyInOrder.verify(jgen).writeFieldName("name");
        verifyInOrder.verify(jgen).writeString(category.getName());
        verifyInOrder.verify(jgen).writeEndObject();
    }

    @Test
    public void shouldSerializeCategoryWithMissingValues() throws IOException {
        // given
        Category category = Category.newCategory()
                .withId(11L)
                .withName("Some Category")
                .withParentId(1L)
                .build();

        // when
        categorySerializer.serialize(category, jgen, mock(SerializerProvider.class));

        // then
        InOrder verifyInOrder = inOrder(jgen);
        verifyInOrder.verify(jgen).writeStartObject();
        verifyInOrder.verify(jgen).writeFieldName("id");
        verifyInOrder.verify(jgen).writeNumber(category.getId());
        verifyInOrder.verify(jgen).writeFieldName("parentId");
        verifyInOrder.verify(jgen).writeNumber(category.getParentId());
        verifyInOrder.verify(jgen).writeFieldName("name");
        verifyInOrder.verify(jgen).writeString(category.getName());
        verifyInOrder.verify(jgen).writeEndObject();
    }

}