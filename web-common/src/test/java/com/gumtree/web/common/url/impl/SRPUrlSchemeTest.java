package com.gumtree.web.common.url.impl;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.service.category.CategoryService;
import com.gumtree.util.url.impl.DefaultSRPUrlScheme;
import com.gumtree.util.model.UserSearch;
import com.gumtree.util.model.UserSearch.Builder;
import com.gumtree.search.UserSearchKeywords;
import com.gumtree.search.UserSearchLocation;
import com.netflix.config.ConfigurationManager;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Properties;

import static junit.framework.Assert.assertEquals;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.startsWith;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit tests for DefaultSRPUrlScheme.
 */
public class SRPUrlSchemeTest extends AbstractSRPUrlSchemeTest {

    private final DefaultSRPUrlScheme defaultSRPUrlScheme;
    private final Category mockCategory;
    private final Location mockLocation;

    public SRPUrlSchemeTest() {
        super.setUp();
        this.defaultSRPUrlScheme = new DefaultSRPUrlScheme();
        ReflectionTestUtils.setField(defaultSRPUrlScheme, "buyerBaseUri", "http://test.com");
        ReflectionTestUtils.setField(defaultSRPUrlScheme, "categoryService", mock(CategoryService.class));
        this.mockCategory = mock(Category.class);
        this.mockLocation = mock(Location.class);
    }


    @Test
    public void testUrlForListingWithLocation() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("clapham");
        assertThat(defaultSRPUrlScheme.getFor(mockCategory, mockLocation), equalTo("http://test.com/cars/clapham"));
    }

    @Test
    public void testUrlForListingWithUKLocation() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");
        assertThat(defaultSRPUrlScheme.getFor(mockCategory, mockLocation), equalTo("http://test.com/cars"));
    }

    @Test
    public void testUrlForListingWithFixedUrl() {
        when(mockCategory.getUrl()).thenReturn("fixedUrl");
        when(mockLocation.getName()).thenReturn("clapham");
        when(mockLocation.getDisplayName()).thenReturn("Clapham");
        assertThat(defaultSRPUrlScheme.getFor(mockCategory, mockLocation), equalTo("fixedUrl"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUrlForListingWithLocationNullCategory() {
        defaultSRPUrlScheme.getFor((Category) null, mock(Location.class));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUrlForListingWithLocationNullLocation() {
        defaultSRPUrlScheme.getFor(mock(Category.class), null);
    }

    @Test
    public final void generatesURLForPageNumber1() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");

        UserSearchLocation locationSearch = new UserSearchLocation(mockLocation, new ArrayList<String>(), new ArrayList<String>(), "clapham");
        UserSearchKeywords keywords = new UserSearchKeywords("bmw", Collections.<String>emptySet(), Collections.<Category>emptySet());
        Builder builder = new UserSearch.Builder().category(mockCategory).locationSearch(locationSearch).keywordsSearch(keywords);
        UserSearch userSearch = builder.build();

        String url = defaultSRPUrlScheme.getFor(userSearch, 1, null);
        assertThat(url, startsWith("http://test.com"));
        assertThat(url, containsString("/cars/uk/bmw"));
    }

    @Test
    public final void generatesURLForPagesGreaterThan1() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");

        UserSearchLocation locationSearch = new UserSearchLocation(mockLocation, new ArrayList<String>(), new ArrayList<String>(), "clapham");
        UserSearchKeywords keywords = new UserSearchKeywords("bmw", Collections.<String>emptySet(), Collections.<Category>emptySet());
        Builder builder = new UserSearch.Builder().category(mockCategory).locationSearch(locationSearch).keywordsSearch(keywords);
        UserSearch userSearch = builder.build();

        String url = defaultSRPUrlScheme.getFor(userSearch, 9, null);
        assertThat(url, startsWith("http://test.com"));
        assertThat(url, containsString("/cars/uk/bmw/page9"));
    }

    @Test
    public final void generatesURLFollowingURLConvention() {

        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");

        UserSearchLocation locationSearch = new UserSearchLocation(mockLocation, new ArrayList<String>(), new ArrayList<String>(), "clapham");
        UserSearchKeywords keywords = new UserSearchKeywords("bmw", Collections.<String>emptySet(), Collections.<Category>emptySet());
        Builder builder = new UserSearch.Builder().category(mockCategory).locationSearch(locationSearch).keywordsSearch(keywords);
        UserSearch userSearch = builder.build();

        String url = defaultSRPUrlScheme.getFor(userSearch, 9, null);
        assertThat(url, startsWith("http://test.com"));
        assertThat(url, containsString("/cars/uk/bmw/page9"));
    }

    @Test
    public final void generatesURLFollowingURLConventionWithNoSpecifiedSearchTerm() {

        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");


        UserSearchLocation locationSearch = new UserSearchLocation(mockLocation, new ArrayList<String>(), new ArrayList<String>(), "clapham");
        Builder builder = new UserSearch.Builder().category(mockCategory).locationSearch(locationSearch);
        UserSearch userSearch = builder.build();

        String url = defaultSRPUrlScheme.getFor(userSearch, 9, null);
        assertThat(url, startsWith("http://test.com"));
        assertThat(url, containsString("/cars/uk/page9"));
    }

    @Test
    public final void generateRSSUrl() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");

        UserSearch userSearch = buildUserSearch("bmw");

        String url = defaultSRPUrlScheme.getRSSUrl(userSearch, null);

        assertThat(url, equalTo("http://test.com/rssfeed/cars/uk/bmw"));

    }

    @Test
    public final void generateRSSUrlWithPageNumber() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");

        UserSearch userSearch = buildUserSearch("bmw");

        String url = defaultSRPUrlScheme.getRSSUrl(userSearch, 11);

        assertThat(url, equalTo("http://test.com/rssfeed/cars/uk/bmw/page11"));

    }
    @Test
    public final void testGetForWithoutQuery() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");

        UserSearch userSearch = buildUserSearch("bmw");

        String url = defaultSRPUrlScheme.getForWithoutQuery(userSearch);
        
        assertEquals("http://test.com/cars/uk/bmw", url);
    }

    @Test
    public final void testGetForWithoutQueryPageTwo() {
        when(mockCategory.getSeoName()).thenReturn("cars");
        when(mockLocation.getName()).thenReturn("uk");

        UserSearch userSearch = buildUserSearch("bmw");

        String url = defaultSRPUrlScheme.getForWithoutQuery(userSearch, 2);

        assertEquals("http://test.com/cars/uk/bmw/page2", url);
    }
    
    private UserSearch buildUserSearch(String keyword) {
        UserSearchLocation locationSearch = new UserSearchLocation(mockLocation, new ArrayList<String>(), new ArrayList<String>(), "clapham");
        UserSearchKeywords keywords = new UserSearchKeywords(keyword, Collections.<String>emptySet(), Collections.<Category>emptySet());
        Builder builder = new UserSearch.Builder().category(mockCategory).locationSearch(locationSearch).keywordsSearch(keywords);
        return builder.build();        
    }
}
