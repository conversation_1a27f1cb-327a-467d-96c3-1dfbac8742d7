package com.gumtree.web.common.page.handler;

import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Test;
import org.springframework.web.method.HandlerMethod;

import static org.hamcrest.CoreMatchers.instanceOf;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 */
public class DefaultPageHandlerFactoryTest {

    @Test
    public void returnsNullWhenGumtreePageAnnotationNotPresentOnMethodOrClass() throws Exception {
        DefaultPageHandlerFactory factory = new DefaultPageHandlerFactory();
        HandlerMethod handlerMethod = mock(HandlerMethod.class);
        when(handlerMethod.getMethod()).thenReturn(TestClassOne.class.getMethod("noGumtreePageAnnotation"));
        assertThat(factory.create(handlerMethod), nullValue());
    }

    @Test
    public void returnsDefaultPageHandlerWhenPageAnnotationIsPresentOnMethod() throws Exception {
        DefaultPageHandlerFactory factory = new DefaultPageHandlerFactory();
        HandlerMethod handlerMethod = mock(HandlerMethod.class);
        when(handlerMethod.getMethod()).thenReturn(TestClassTwo.class.getMethod("withGumtreePageAnnotation"));
        assertThat(factory.create(handlerMethod), instanceOf(DefaultPageHandler.class));
    }

    @Test
    public void returnsDefaultPageHandlerWhenPageAnnotationIsPresentOnClass() throws Exception {
        DefaultPageHandlerFactory factory = new DefaultPageHandlerFactory();
        HandlerMethod handlerMethod = mock(HandlerMethod.class);
        when(handlerMethod.getMethod()).thenReturn(TestClassThree.class.getMethod("noGumtreePageAnnotation"));
        assertThat(factory.create(handlerMethod), instanceOf(DefaultPageHandler.class));
    }

    @Test
    public void returnsDefaultPageHandlerWhenPageAnnotationIsPresentOnMethodAndClass() throws Exception {
        DefaultPageHandlerFactory factory = new DefaultPageHandlerFactory();
        HandlerMethod handlerMethod = mock(HandlerMethod.class);
        when(handlerMethod.getMethod()).thenReturn(TestClassFour.class.getMethod("withGumtreePageAnnotation"));
        assertThat(factory.create(handlerMethod), instanceOf(DefaultPageHandler.class));
    }

    public class TestClassOne {
        public void noGumtreePageAnnotation() {

        }
    }

    public class TestClassTwo {
        @GumtreePage(value = PageType.VIP)
        public void withGumtreePageAnnotation() {

        }
    }

    @GumtreePage(value = PageType.VIP)
    public class TestClassThree {
        public void noGumtreePageAnnotation() {

        }
    }

    @GumtreePage(value = PageType.VIP)
    public class TestClassFour {
        @GumtreePage(value = PageType.VIP)
        public void withGumtreePageAnnotation() {

        }
    }
}
