package com.gumtree.web.common.thirdparty.noras;

import com.google.common.base.Optional;
import com.gumtree.api.category.domain.Category;
import com.gumtree.service.category.CategoryService;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit tests for NorasSurveyHelperImpl.
 */
public class NorasSurveyHelperImplTest {

    private final DateTimeFormatter dateTimeFormatter = ISODateTimeFormat.basicDateTimeNoMillis();

    @Test
    public void testAddNorasSurveyBefore() {
        String url = "http://cheese";
        String startDate = dateTimeFormatter.print(new DateTime().minusHours(2));
        String endDate = dateTimeFormatter.print(new DateTime().minusHours(1));
        NorasSurveyHelper norasSurveyHelper = new NorasSurveyHelperImpl();
        ReflectionTestUtils.setField(norasSurveyHelper, "norasStartDate", startDate);
        ReflectionTestUtils.setField(norasSurveyHelper, "norasEndDate", endDate);
        ReflectionTestUtils.setField(norasSurveyHelper, "norasScriptURL", url);
        ReflectionTestUtils.invokeMethod(norasSurveyHelper, "init");

        Map<String, Object> model = new HashMap<String, Object>();

        norasSurveyHelper.addNorasSurvey(model);

        assertFalse(model.containsKey("norasScriptURL"));
    }

    @Test
    public void testAddNorasSurvey() {
        String url = "http://cheese";
        String startDate = dateTimeFormatter.print(new DateTime().minusHours(1));
        String endDate = dateTimeFormatter.print(new DateTime().plusHours(1));
        NorasSurveyHelper norasSurveyHelper = new NorasSurveyHelperImpl();
        ReflectionTestUtils.setField(norasSurveyHelper, "norasStartDate", startDate);
        ReflectionTestUtils.setField(norasSurveyHelper, "norasEndDate", endDate);
        ReflectionTestUtils.setField(norasSurveyHelper, "norasScriptURL", url);
        ReflectionTestUtils.invokeMethod(norasSurveyHelper, "init");
        
        Map<String, Object> model = new HashMap<String, Object>();
        
        norasSurveyHelper.addNorasSurvey(model);
        
        assertTrue(model.containsKey("norasScriptURL"));
        assertEquals(url, model.get("norasScriptURL"));
    }

    @Test
    public void testAddNorasSurveyAfter() {
        String url = "http://cheese";
        String startDate = dateTimeFormatter.print(new DateTime().plusHours(1));
        String endDate = dateTimeFormatter.print(new DateTime().plusHours(2));
        NorasSurveyHelper norasSurveyHelper = new NorasSurveyHelperImpl();
        ReflectionTestUtils.setField(norasSurveyHelper, "norasStartDate", startDate);
        ReflectionTestUtils.setField(norasSurveyHelper, "norasEndDate", endDate);
        ReflectionTestUtils.setField(norasSurveyHelper, "norasScriptURL", url);
        ReflectionTestUtils.invokeMethod(norasSurveyHelper, "init");

        Map<String, Object> model = new HashMap<String, Object>();

        norasSurveyHelper.addNorasSurvey(model);

        assertFalse(model.containsKey("norasScriptURL"));
    }

    @Test
    public void testIsNorasCategoryTrue() {

        long norasCategoryId = 7L;
        CategoryService mockCategoryService = mock(CategoryService.class);
        Category mockCategoryA = Category.CategoryBuilder.category().withId(norasCategoryId).withName("A").build();
        Category mockCategoryB = Category.CategoryBuilder.category().withId(2L).withName("B").build();
        Category mockCategoryC = Category.CategoryBuilder.category().withId(3L).withName("C").build();
        Map<Integer, Category> mockCategoryMap = new HashMap<Integer, Category>();
        mockCategoryMap.put(1, mockCategoryA);
        mockCategoryMap.put(2, mockCategoryB);
        mockCategoryMap.put(3, mockCategoryC);
        when(mockCategoryService.getLevelHierarchy(mockCategoryC)).thenReturn(mockCategoryMap);
        when(mockCategoryService.getById(norasCategoryId)).thenReturn(Optional.of(mockCategoryA));
        
        NorasSurveyHelper norasSurveyHelper = new NorasSurveyHelperImpl();
        ReflectionTestUtils.setField(norasSurveyHelper, "categoryService", mockCategoryService);
        ReflectionTestUtils.setField(norasSurveyHelper, "norasCategoryId", norasCategoryId);
        assertTrue(norasSurveyHelper.isNorasCategory(mockCategoryC));
    }

    @Test
    public void testIsNorasCategoryFalse() {

        long norasCategoryId = 13L;
        CategoryService mockCategoryService = mock(CategoryService.class);
        Category mockCategoryA = mock(Category.class);
        Category mockCategoryB = mock(Category.class);
        Category mockCategoryC = mock(Category.class);
        Category mockCategoryD = mock(Category.class);
        Map<Integer, Category> mockCategoryMap = new HashMap<Integer, Category>();
        mockCategoryMap.put(1, mockCategoryA);
        mockCategoryMap.put(2, mockCategoryB);
        mockCategoryMap.put(3, mockCategoryC);
        when(mockCategoryService.getLevelHierarchy(mockCategoryC)).thenReturn(mockCategoryMap);
        when(mockCategoryService.getById(norasCategoryId)).thenReturn(Optional.of(mockCategoryD));

        NorasSurveyHelper norasSurveyHelper = new NorasSurveyHelperImpl();
        ReflectionTestUtils.setField(norasSurveyHelper, "categoryService", mockCategoryService);
        ReflectionTestUtils.setField(norasSurveyHelper, "norasCategoryId", norasCategoryId);
        assertFalse(norasSurveyHelper.isNorasCategory(mockCategoryC));
    }
}
