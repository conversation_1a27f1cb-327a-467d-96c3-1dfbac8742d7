package com.gumtree.web.storage.strategy;

import com.codahale.metrics.MetricRegistry;
import com.datastax.driver.core.Row;
import com.gumtree.web.storage.CassandraKeyValueRepository;
import com.gumtree.web.storage.SimpleCassandraClient;
import org.cassandraunit.CassandraCQLUnit;
import org.junit.Before;
import org.junit.ClassRule;
import org.junit.Test;

import static com.gumtree.web.storage.CassandraKeyValueRepositoryTest.KeyValueCQLDataSet;
import static org.fest.assertions.api.Assertions.assertThat;

public class CassandraPersistenceStrategyTest {
    CassandraPersistenceStrategy strategy;

    @ClassRule
    public static CassandraCQLUnit cassandraCQLUnit = new CassandraCQLUnit(new KeyValueCQLDataSet("seller", "test_table", "key", "value", "text"));

    @Before
    public void cleanTableAndSetupRepository() {
        cassandraCQLUnit.getSession().execute("truncate test_table");

        SimpleCassandraClient client = new SimpleCassandraClient(cassandraCQLUnit.getSession().getCluster(), "seller", new MetricRegistry());
        CassandraKeyValueRepository repository = new CassandraKeyValueRepository(client, "test_table", "key", "value", String.class);
        strategy = new CassandraPersistenceStrategy(repository);
    }

    @Test
    public void writeRespectsTTL() throws Exception {
        // when
        strategy.writeOperation(handler -> handler.set("x", "y", 10));

        // than
        Row one = cassandraCQLUnit.getSession().execute("select * from test_table").one();
        assertThat(one.getString("key")).isEqualTo("x");
        assertThat(one.getString("value")).isEqualTo("y");

        // but when
        strategy.writeOperation(handler -> handler.set("x", "y", 1));
        Thread.sleep(1000);

        // then
        assertThat(cassandraCQLUnit.getSession().execute("select * from test_table").one()).isNull();
    }

    @Test
    public void expire() throws Exception {
        // given
        cassandraCQLUnit.getSession().execute("insert into test_table (key, value) values ('x', 'y')");

        // when
        strategy.writeOperation(handler -> handler.expire("x", 10));

        // than
        Row one = cassandraCQLUnit.getSession().execute("select * from test_table").one();
        assertThat(one).isNotNull();
        assertThat(one.getString("key")).isEqualTo("x");
        assertThat(one.getString("value")).isEqualTo("y");

        // but when
        strategy.writeOperation(handler -> handler.expire("x", 1));
        Thread.sleep(1000);

        // than
        assertThat(cassandraCQLUnit.getSession().execute("select * from test_table").one()).isNull();
    }

    @Test
    public void read() {
        // given
        cassandraCQLUnit.getSession().execute("insert into test_table (key, value) values ('x', 'y')");

        // when
        String value = strategy.readOperation("x");

        // then
        assertThat(value).isEqualTo("y");
    }

    @Test
    public void delete() {
        // given
        cassandraCQLUnit.getSession().execute("insert into test_table (key, value) values ('x', 'y')");

        // when
        strategy.writeOperation(handler -> handler.del("x"));

        // than
        assertThat(cassandraCQLUnit.getSession().execute("select * from test_table").one()).isNull();
    }
}