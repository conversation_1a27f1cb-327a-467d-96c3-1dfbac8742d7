package com.gumtree.web.storage.strategy;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.gumtree.web.storage.exception.SessionDataAccessException;
import org.junit.Before;
import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

public class BothPersistenceStrategyTest {
    private static final SessionDataAccessException EXC = new SessionDataAccessException(new NullPointerException());

    private CassandraPersistenceStrategy cassandraPersistenceStrategy;
    private JedisPersistenceStrategy jedisPersistenceStrategy;

    private Counter failureCounter;

    BothPersistenceStrategy bothPersistenceStrategy;
    @Before
    public void setUp() throws Exception {
        cassandraPersistenceStrategy = mock(CassandraPersistenceStrategy.class);
        jedisPersistenceStrategy = mock(JedisPersistenceStrategy.class);

        MetricRegistry metricRegistry = mock(MetricRegistry.class);
        failureCounter = mock(Counter.class);
        when(metricRegistry.counter(any())).thenReturn(failureCounter);

        bothPersistenceStrategy = new BothPersistenceStrategy(cassandraPersistenceStrategy, jedisPersistenceStrategy, metricRegistry);
    }

    /*
        read
     */

    @Test
    public void readReturnsValueIfEntryExists() throws Exception {
        when(cassandraPersistenceStrategy.readOperation(eq("key"))).thenReturn("value");
        when(jedisPersistenceStrategy.readOperation(eq("key"))).thenReturn("value");

        assertThat(bothPersistenceStrategy.readOperation("key")).isEqualTo("value");
    }

    @Test
    public void readReturnsNoValueIfEntryDoesNotExist() throws Exception {
        when(cassandraPersistenceStrategy.readOperation(eq("key"))).thenReturn(null);
        when(jedisPersistenceStrategy.readOperation(eq("key"))).thenReturn(null);

        assertThat(bothPersistenceStrategy.readOperation("key")).isEqualTo(null);
    }

    @Test
    public void readAccessesOnlyCassandra() throws Exception {
        when(cassandraPersistenceStrategy.readOperation(anyString())).thenReturn("value");

        bothPersistenceStrategy.readOperation("key");

        verify(cassandraPersistenceStrategy).readOperation(eq("key"));
        verifyZeroInteractions(jedisPersistenceStrategy);
    }

    @Test
    public void readAccessesBothRepositories() throws Exception {
        when(cassandraPersistenceStrategy.readOperation(anyString())).thenReturn(null);
        when(jedisPersistenceStrategy.readOperation(anyString())).thenReturn(null);

        bothPersistenceStrategy.readOperation("key");

        verify(cassandraPersistenceStrategy).readOperation(eq("key"));
        verify(jedisPersistenceStrategy).readOperation(eq("key"));
    }

    @Test
    public void readSucceedsWhenOnlyRedisFails() throws Exception {
        when(cassandraPersistenceStrategy.readOperation(anyString())).thenReturn(null);
        when(jedisPersistenceStrategy.readOperation(anyString())).thenThrow(EXC);

        bothPersistenceStrategy.readOperation("key1");

        verify(cassandraPersistenceStrategy).readOperation(anyString());
        verify(jedisPersistenceStrategy).readOperation(anyString());
        verify(failureCounter).inc();
    }

    @Test
    public void readSucceedsWhenOnlyCassandraFails() throws Exception {
        when(jedisPersistenceStrategy.readOperation(anyString())).thenReturn(null);
        when(cassandraPersistenceStrategy.readOperation(anyString())).thenThrow(EXC);

        bothPersistenceStrategy.readOperation("key2");
        verify(cassandraPersistenceStrategy).readOperation(anyString());
        verify(jedisPersistenceStrategy).readOperation(anyString());
        verify(failureCounter).inc();
    }

    @Test(expected = SessionDataAccessException.class)
    public void readFailsWhenRedisAndCassandraFail() throws Exception {
        when(cassandraPersistenceStrategy.readOperation(anyString())).thenThrow(EXC);
        when(jedisPersistenceStrategy.readOperation(anyString())).thenThrow(EXC);

        bothPersistenceStrategy.readOperation("key");
    }

    /*
        write
     */

    @Test
    public void writePersistsValuesToCassandraAndRedis() throws Exception {
        SessionPersistenceStrategy.WriteOperationCallback callback = handler -> handler.set("key", "value", 60);

        bothPersistenceStrategy.writeOperation(callback);

        verify(cassandraPersistenceStrategy).writeOperation(eq(callback));
        verify(jedisPersistenceStrategy).writeOperation(eq(callback));
    }

    @Test
    public void writeSucceedsWhenOnlyRedisFails() throws Exception {
        SessionPersistenceStrategy.WriteOperationCallback callback = handler -> handler.set("key1", "value", 60);

        doThrow(EXC).when(jedisPersistenceStrategy).writeOperation(eq(callback));

        bothPersistenceStrategy.writeOperation(callback);

        verify(cassandraPersistenceStrategy).writeOperation(eq(callback));
        verify(jedisPersistenceStrategy).writeOperation(eq(callback));
        verify(failureCounter).inc();
    }

    @Test
    public void writeSucceedsWhenOnlyCassandraFails() throws Exception {
        SessionPersistenceStrategy.WriteOperationCallback callback = handler -> handler.set("key2", "value", 60);

        doThrow(EXC).when(cassandraPersistenceStrategy).writeOperation(eq(callback));

        bothPersistenceStrategy.writeOperation(callback);

        verify(cassandraPersistenceStrategy).writeOperation(eq(callback));
        verify(jedisPersistenceStrategy).writeOperation(eq(callback));
        verify(failureCounter).inc();
    }

    @Test(expected = SessionDataAccessException.class)
    public void writeFailsWhenRedisAndCassandraFail() throws Exception {
        SessionPersistenceStrategy.WriteOperationCallback callback = handler -> handler.set("key", "value", 60);

        doThrow(EXC).when(cassandraPersistenceStrategy).writeOperation(eq(callback));
        doThrow(EXC).when(jedisPersistenceStrategy).writeOperation(eq(callback));

        bothPersistenceStrategy.writeOperation(callback);
    }
}