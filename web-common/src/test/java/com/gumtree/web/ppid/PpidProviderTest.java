package com.gumtree.web.ppid;

import com.ecg.analytics.encryption.EmailEncrypter;
import com.gumtree.api.User;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookie;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Optional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.mockito.Matchers.anyString;

@RunWith(MockitoJUnitRunner.class)
public class PpidProviderTest {

    @Mock
    EmailEncrypter emailEncrypter;

    private PpidProvider ppidProvider;

    @Before
    public void setUp() throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
        ppidProvider = new PpidProvider(emailEncrypter);
        Mockito.when(emailEncrypter.hashStringToHex(anyString())).thenAnswer(req -> ((String)req.getArguments()[0]).toUpperCase());
    }

    @Test
    public void shoudUseUsersEmailIfAvailable() {

        //given
        User user = new User();
        user.setEmail("<EMAIL>");

        PermanentCookie permanentCookie = new PermanentCookie("www.google.com", 1024, "*");

        //when
        String ppid = ppidProvider.providePpid(Optional.of(user), permanentCookie);

        //then
        assertThat(ppid, equalTo("<EMAIL>"));
    }

    @Test
    public void shouldUseEmailFromPersistentCookieIfANotLoggedIn() {
        //given
        PermanentCookie permanentCookie = new PermanentCookie("www.google.com", 1024, "*");
        permanentCookie.setReplyEmail("<EMAIL>");

        //when
        String ppid = ppidProvider.providePpid(Optional.empty(), permanentCookie);

        //then
        assertThat(ppid, equalTo("<EMAIL>"));
    }

    @Test
    public void shouldPreferredUserLoggedEmailOverEmailFromPersistentCookie() {
        //given
        User user = new User();
        user.setEmail("<EMAIL>");

        PermanentCookie permanentCookie = new PermanentCookie("www.google.com", 1024, "*");
        permanentCookie.setReplyEmail("<EMAIL>");

        //when
        String ppid = ppidProvider.providePpid(Optional.of(user), permanentCookie);

        //then
        assertThat(ppid, equalTo("<EMAIL>"));
    }

    @Test
    public void shouldReturnEmptyStringIfNoEmailAvailable() {
        //given
        PermanentCookie permanentCookie = new PermanentCookie("www.google.com", 1024, "*");

        //when
        String ppid = ppidProvider.providePpid(Optional.empty(), permanentCookie);

        //then
        assertThat(ppid, equalTo(""));
    }

}
