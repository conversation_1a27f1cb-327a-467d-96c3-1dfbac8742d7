package com.gumtree.web.zeno;

import org.junit.Before;
import org.junit.Test;
import org.springframework.mobile.device.Device;
import org.springframework.mobile.device.DeviceUtils;

import javax.servlet.http.HttpServletRequest;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

public class DeviceTypeHelperTest {
    private final DeviceSetter deviceType = new DeviceSetter();
    private HttpServletRequest request;
    private Device device;

    @Before
    public void setup() {
        request = mock(HttpServletRequest.class);
        device = mock(Device.class);
        given(request.getAttribute(DeviceUtils.CURRENT_DEVICE_ATTRIBUTE)).willReturn(device);
    }

    @Test
    public void shouldRecognizeMobile() {
        deviceType.setMobile();

        String zenoDeviceType = DeviceTypeHelper.getDeviceTypeZenoValue(request);
        
        assertThat(zenoDeviceType, equalTo("m"));
    }

    @Test
    public void shouldRecognizeTablet() {
        deviceType.setTablet();

        String zenoDeviceType = DeviceTypeHelper.getDeviceTypeZenoValue(request);

        assertThat(zenoDeviceType, equalTo("t"));
    }

    @Test
    public void shouldRecognizeDesktop() {
        deviceType.setDesktop();

        String zenoDeviceType = DeviceTypeHelper.getDeviceTypeZenoValue(request);

        assertThat(zenoDeviceType, equalTo("d"));
    }

    @Test
    public void shouldNotFailForUnrecognizedDevice() {
        deviceType.setUnknown();

        String zenoDeviceType = DeviceTypeHelper.getDeviceTypeZenoValue(request);

        assertThat(zenoDeviceType, equalTo("x"));
    }

    @Test
    public void shouldNotFailForNullDevice() {
        deviceType.setDesktop();
        given(request.getAttribute(DeviceUtils.CURRENT_DEVICE_ATTRIBUTE)).willReturn(null);

        String zenoDeviceType = DeviceTypeHelper.getDeviceTypeZenoValue(request);

        assertThat(zenoDeviceType, equalTo("x"));
    }

    private class DeviceSetter {
        private void setMobile() {
            given(device.isMobile()).willReturn(true);
            given(device.isTablet()).willReturn(false);
            given(device.isNormal()).willReturn(false);
        }

        private void setTablet() {
            given(device.isMobile()).willReturn(false);
            given(device.isTablet()).willReturn(true);
            given(device.isNormal()).willReturn(false);
        }

        private void setDesktop() {
            given(device.isMobile()).willReturn(false);
            given(device.isTablet()).willReturn(false);
            given(device.isNormal()).willReturn(true);
        }

        private void setUnknown() {
            given(device.isMobile()).willReturn(false);
            given(device.isTablet()).willReturn(false);
            given(device.isNormal()).willReturn(false);
        }
    }
}
