package com.gumtree.web.browse;

import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;

import static org.fest.assertions.api.Assertions.assertThat;

public class BrowseItem_HasMoreChildrenTest {
    private BrowseItem browseItem;

    @Test
    public void testNoChildrenAndNullTotal() {
        browseItem = createItem(Lists.<BrowseItem>newArrayList(), null);
        assertThat(browseItem.hasMoreChildren()).isFalse();
    }

    @Test
    public void testNoChildrenAndNotNullTotal() {
        browseItem = createItem(Lists.<BrowseItem>newArrayList(), 1);
        assertThat(browseItem.hasMoreChildren()).isTrue();
    }

    @Test
    public void testNoChildrenAndZeroTotal() {
        browseItem = createItem(Lists.<BrowseItem>newArrayList(), 0);
        assertThat(browseItem.hasMoreChildren()).isFalse();
    }

    @Test
    public void testLessChildrenThanTotalChildren() {
        browseItem = createItem(Lists.newArrayList(child()), 2);
        assertThat(browseItem.hasMoreChildren()).isTrue();
    }

    @Test
    public void testChildrenEqualTotalChildren() {
        browseItem = createItem(Lists.newArrayList(child()), 1);
        assertThat(browseItem.hasMoreChildren()).isFalse();
    }

    private BrowseItem createItem(List<BrowseItem> children, Integer totalChildren) {
        return new BrowseItem("cat", "cat", "", children, null, totalChildren);
    }

    private BrowseItem child() {
        return new BrowseItem("cat", "cat", "", Lists.<BrowseItem>newArrayList(), null, null);
    }
}
