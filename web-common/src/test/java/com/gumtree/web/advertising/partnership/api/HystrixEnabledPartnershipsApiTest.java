package com.gumtree.web.advertising.partnership.api;

import com.gumtree.common.properties.GtPropManager;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.web.advertising.partnership.Partnership;
import com.gumtree.web.advertising.partnership.PartnershipsParameters;
import com.gumtree.web.common.domain.location.Location;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static com.gumtree.config.MwebProperty.PARTAPI_CONNECTION_POOL_MAX;
import static com.gumtree.domain.page.Page.HOME_RESPONSIVE;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HystrixEnabledPartnershipsApiTest {

    static {
        GtPropManager.setProperty(PARTAPI_CONNECTION_POOL_MAX.getPropertyName(), "10");
    }

    private PartnershipsApi partnershipsApi;

    @Before
    public void setup() {
        PartnershipsApiContract partnershipsApiContract = mock(PartnershipsApiContract.class);
        PropSupplier<Boolean> isEnabled = () -> true;
        partnershipsApi = new PartnershipsApi(partnershipsApiContract, isEnabled);
    }

    @Test
    @Ignore
    public void testContent() {
        PartnershipsParameters partnershipsParameters = PartnershipsParameters.builder()
                .withPage(HOME_RESPONSIVE)
                .withLocation(new Location(10L, "london", "London"))
                .withAttributes(Collections.emptyList())
                .withExperiments(Collections.emptyMap())
                .build();
        Partnership partnership = new Partnership("id", "title");
        List<Partnership> partnershipAds = Collections.singletonList(partnership);
        when(partnershipsApi.getContent(partnershipsParameters)).thenReturn(partnershipAds);

        await().atMost(2, SECONDS)
                .until(() -> {
                    HystrixRequestContext.initializeContext();
                    HystrixEnabledPartnershipsApi hystrixEnabledPartnershipsApi = new HystrixEnabledPartnershipsApi(partnershipsApi, 1000);
                    assertEquals(partnershipAds, hystrixEnabledPartnershipsApi.getContent(partnershipsParameters).toBlocking().value());
                });
    }

}
