package com.gumtree.web.cookie.cutters.permanent;

import org.junit.Test;
import org.springframework.util.StringUtils;

import static org.fest.assertions.api.Assertions.assertThat;

public class PermanentCookieTest {

    @Test
    public void shouldGenerateANewIdForANewCookie() {
        //when
        PermanentCookie permanentCookie = new PermanentCookie(null, 0, null);

        //then
        assertThat(StringUtils.isEmpty(permanentCookie.getId())).isFalse();
        assertThat(permanentCookie.isNew()).isTrue();
    }

}
