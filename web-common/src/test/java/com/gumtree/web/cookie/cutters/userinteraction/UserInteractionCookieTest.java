package com.gumtree.web.cookie.cutters.userinteraction;

import com.gumtree.web.cookie.CookieUtils;
import org.apache.commons.codec.binary.Base64;
import org.junit.Test;

import javax.servlet.http.Cookie;

import static org.fest.assertions.api.Assertions.assertThat;

public class UserInteractionCookieTest {

    /*
        get counter
     */

    @Test
    public void getUserInterractionCounterIfAbsent() {
        // given
        UserInteractionCookie cookie = new UserInteractionCookie("gumtree.com", 0, "/", new <PERSON>ie("gt_userIntr", ""));

        // when & then
        assertThat(cookie.getUserInterractionCounter()).isEqualTo(0);
    }

    @Test
    public void getUserInterractionCounterIfPresentAndInalidNumber() {
        // given
        UserInteractionCookie cookie = new UserInteractionCookie("gumtree.com", 0, "/", new Cookie("gt_userIntr", "cnt:" + Base64.encodeBase64String("x".getBytes())));

        // when & then
        assertThat(cookie.getUserInterractionCounter()).isEqualTo(0);
    }

    @Test
    public void getUserInterractionCounterIfPresentAndValidNumber() {
        // given
        UserInteractionCookie cookie = new UserInteractionCookie("gumtree.com", 0, "/", new Cookie("gt_userIntr", "cnt:" + Base64.encodeBase64String("5".getBytes())));

        // when & then
        assertThat(cookie.getUserInterractionCounter()).isEqualTo(5);
    }

    /*
        increment counter
     */

    @Test
    public void incrementUserInterractionCounter() {
        // given
        UserInteractionCookie cookie = new UserInteractionCookie("gumtree.com", 0, "/", new Cookie("gt_userIntr", "cnt:" + Base64.encodeBase64String("5".getBytes())));

        // when
        cookie.incrementUserInterractionCounter();

        // then
        Cookie newSessionCookie = CookieUtils.createHttpCookie("gt_userIntr", cookie, false);
        assertThat(newSessionCookie.getValue()).isEqualTo("cnt:" + Base64.encodeBase64String("6".getBytes()));
    }
}