package com.gumtree.web.cookie.cutters.appbanner;

import com.gumtree.web.cookie.CookieSerializer;
import com.gumtree.web.cookie.CookieUtils;
import org.junit.Test;

import javax.servlet.http.Cookie;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieCutter.MAX_AGE_COOKIE;
import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper.Action.POST_AD_COMPLETE;
import static com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper.Action.REPLY_SUCCESS;
import static java.time.ZoneOffset.UTC;
import static java.time.format.DateTimeFormatter.RFC_1123_DATE_TIME;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.CoreMatchers.nullValue;
import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;

public class AppBannerCookieTest {

    @Test
    public void testNewCookie() {
        // When
        AppBannerCookie cookie = new AppBannerCookie("dev.gumtree.com", 1000, "/");

        // Then
        assertThat(cookie.isVisible(), is(false));
        assertThat(cookie.getActionTriggered(), nullValue());
        assertThat(cookie.getExpiryDate(), nullValue());
    }

    @Test
    public void testConstructUsingPreExistingCookie() {

        // Given
        AppBannerCookie preExistingCookie = new AppBannerCookie("dev.gumtree.com", 1000, "/");
        preExistingCookie.setActionTriggered(POST_AD_COMPLETE.getCampaignType());
        Cookie httpCookie = CookieUtils.createHttpCookie(AppBannerCookie.NAME, preExistingCookie, false);

        // When
        AppBannerCookie refreshedCookie = new AppBannerCookie("dev.gumtree.com", "/",
                CookieSerializer.deserializeCookieMap(httpCookie.getValue()));

        // then
        assertThat(refreshedCookie.isVisible(), equalTo(true));
        assertThat(refreshedCookie.getActionTriggered(), equalTo(POST_AD_COMPLETE.getCampaignType()));
        assertThat(refreshedCookie.getExpiryDate(), notNullValue());
        assertThat(refreshedCookie.getExpiryDate(), equalTo(preExistingCookie.getExpiryDate()));
    }

    @Test
    public void testSetActionTriggered() {
        // Given
        AppBannerCookie cookie = new AppBannerCookie("dev.gumtree.com", 1000, "/");

        // When
        String actionTriggered = POST_AD_COMPLETE.getCampaignType();
        cookie.setActionTriggered(actionTriggered);

        // Then
        assertThat(cookie.getActionTriggered(), equalTo(actionTriggered));
        assertThat(cookie.getExpiryDate(), notNullValue());
        assertThat(cookie.isVisible(), is(true));
        assertThat(cookie.getActionTriggered(), equalTo(actionTriggered));

        ZonedDateTime expiryDate = parseDate(cookie.getExpiryDate());
        assertTrue(expiryDate.isAfter(ZonedDateTime.now(UTC).plusSeconds(MAX_AGE_COOKIE - 3)));
        assertTrue(expiryDate.isBefore(ZonedDateTime.now(UTC).plusSeconds(MAX_AGE_COOKIE)));

    }

    @Test
    public void testSetActionWhenAlreadyTriggered() {
        // Given
        String initialActionTriggered = REPLY_SUCCESS.getCampaignType();
        Map<String,String> entries = new HashMap<>();
        entries.put(AppBannerCookie.ACTION_TRIGGERED, initialActionTriggered);
        AppBannerCookie cookie = new AppBannerCookie("dev.gumtree.com", "/", entries);

        // When
        String otherActionTriggered = POST_AD_COMPLETE.getCampaignType();
        cookie.setActionTriggered(otherActionTriggered);

        // Then
        assertThat(cookie.getActionTriggered(), equalTo(otherActionTriggered));
    }

    private ZonedDateTime parseDate(String date) {
        return ZonedDateTime.parse(date, RFC_1123_DATE_TIME);
    }
}