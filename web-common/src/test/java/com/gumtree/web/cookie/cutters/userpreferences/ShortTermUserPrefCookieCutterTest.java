package com.gumtree.web.cookie.cutters.userpreferences;

import com.gumtree.common.properties.Env;
import org.junit.Before;
import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class ShortTermUserPrefCookieCutterTest {
    private ShortTermUserPrefCookieCutter cutter;

    @Before
    public void beforeEach() {
        cutter = new ShortTermUserPrefCookieCutter("gumtree.com");
    }

    @Test
    public void shouldCreateNewCookie() {
        // when
        ShortTermUserPrefCookie cookie = cutter.cutNew();

        // then
        assertThat(cookie.showAppBanner()).isTrue();
        assertThat(cookie.getMaxAge()).isEqualTo(30 * 24 * 60 * 60);
    }

    @Test
    public void shouldProveEnvSpecificCookieName() {
        // when
        assertThat(cutter.getName(Env.DEV)).isEqualTo("DEV_gt_stUserPref");
        assertThat(cutter.getName(Env.PROD)).isEqualTo("gt_stUserPref");
        assertThat(cutter.getName(Env.QA)).isEqualTo("QA_gt_stUserPref");
    }

}