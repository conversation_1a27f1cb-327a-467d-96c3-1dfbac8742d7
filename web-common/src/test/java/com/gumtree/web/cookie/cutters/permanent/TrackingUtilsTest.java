package com.gumtree.web.cookie.cutters.permanent;

import com.gumtree.web.cookie.utils.TrackingUtils;
import org.apache.commons.codec.binary.Base64;
import org.junit.Test;

import javax.servlet.http.Cookie;
import java.io.UnsupportedEncodingException;

import static java.net.URLEncoder.encode;
import static org.fest.assertions.api.Assertions.assertThat;

public class TrackingUtilsTest {

    @Test
    public void generateHashedClientIdWithCookieIdReturnsHashedValue() throws UnsupportedEncodingException {
        // given
        PermanentCookie cookie = new PermanentCookie("localhost", -1, "/", new Cookie("gt_p", "id:" + base64Encode(encode("123,", "UTF-8"))));

        // when
        String result = TrackingUtils.generateHashedClientId(cookie.getId());

        // then
        assertThat(result).isEqualTo("98da898d62f7e83b2480b245cf01a875ee33a8c6381578b7cb9bd786d2b8ae05");
    }

    private static String base64Encode(String value) {
        return new String(Base64.encodeBase64(value.getBytes()));
    }
}
