package com.gumtree.web.cookie.cutters.abtest;

import org.junit.Test;

import javax.servlet.http.Cookie;

import static com.gumtree.web.cookie.cutters.CookieTestUtil.base64Encode;
import static org.fest.assertions.api.Assertions.assertThat;

public class AbExperimentsCookieTest {

    @Test
    public void shouldInitRandomNumberForNewCookie() {
        // given
        AbExperimentsCookie cookie = new AbExperimentsCookie("localhost", -1, "/");

        // when
        String luckyNumber = cookie.getLuckyNumber();

        // then
        assertThat(luckyNumber).isNotEmpty();
    }

    @Test
    public void shouldCreateCookieFromExistingCookieWithInvalidValue() {
        // given
        AbExperimentsCookie cookie = new AbExperimentsCookie("localhost", -1, "/", new Cookie("ab", "invalid value"));

        // when
        String luckyNumber = cookie.getLuckyNumber();

        // then
        assertThat(luckyNumber).isNotEmpty();
    }

    @Test
    public void shouldCreateCookieFromExistingCookieWithValidValue() {
        // given
        AbExperimentsCookie cookie = new AbExperimentsCookie("localhost", -1, "/",
                new Cookie("ab",
                        "ln:" + base64Encode("abcde") + "|rn:" + base64Encode(45) + "|kw:" + base64Encode("A")));

        // when
        String luckyNumber = cookie.getLuckyNumber();

        // then
        assertThat(luckyNumber).isEqualTo("abcde");
    }
}