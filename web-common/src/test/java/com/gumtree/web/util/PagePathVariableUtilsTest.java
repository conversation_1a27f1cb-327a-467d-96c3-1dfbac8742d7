package com.gumtree.web.util;

import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class PagePathVariableUtilsTest {

    @Test
    public void shouldParseValidPagePathVariable() {
        assertThat(PagePathVariableUtils.parse("page0"), is(1));
        assertThat(PagePathVariableUtils.parse("page1"), is(1));
        assertThat(PagePathVariableUtils.parse("page2"), is(2));
        assertThat(PagePathVariableUtils.parse("page10"), is(10));
        assertThat(PagePathVariableUtils.parse("page99"), is(99));
    }

    @Test
    public void shouldReturn1ForInvalidPagePathVariable() {
        assertThat(PagePathVariableUtils.parse("page-0"), is(1));
        assertThat(PagePathVariableUtils.parse("page-5"), is(1));
        assertThat(PagePathVariableUtils.parse("lala"), is(1));
        assertThat(PagePathVariableUtils.parse("pages5"), is(1));
        assertThat(PagePathVariableUtils.parse(" "), is(1));
        assertThat(PagePathVariableUtils.parse(null), is(1));
        assertThat(PagePathVariableUtils.parse("pagexyz"), is(1));
    }
}
