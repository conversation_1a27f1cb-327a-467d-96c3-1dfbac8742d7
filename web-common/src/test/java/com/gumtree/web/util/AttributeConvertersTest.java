package com.gumtree.web.util;

import org.joda.time.LocalDate;
import org.junit.Test;

import static com.gumtree.web.util.AttributeConverters.convertDateToString;
import static com.gumtree.web.util.AttributeConverters.convertStringToDate;
import static org.fest.assertions.api.Assertions.assertThat;

public class AttributeConvertersTest {
    @Test
    public void convertDateToStringConvertsFine() throws Exception {
        assertThat(convertDateToString(new LocalDate(2016, 2, 11))).isEqualTo("11/02/2016");
        assertThat(convertDateToString(new LocalDate(2016, 2, 29))).isEqualTo("29/02/2016");
        assertThat(convertDateToString(new LocalDate(2016, 11, 1))).isEqualTo("01/11/2016");
    }

    @Test
    public void convertStringToDateConvertsFine() throws Exception {
        assertThat(convertStringToDate("11/02/2016")).isEqualTo(new LocalDate(2016, 2, 11));
        assertThat(convertStringToDate("29/02/2016")).isEqualTo(new LocalDate(2016, 2, 29));
        assertThat(convertStringToDate("01/11/2016")).isEqualTo(new LocalDate(2016, 11, 1));

        assertThat(convertStringToDate("20160211")).isEqualTo(new LocalDate(2016, 2, 11));
        assertThat(convertStringToDate("20160229")).isEqualTo(new LocalDate(2016, 2, 29));
        assertThat(convertStringToDate("20161101")).isEqualTo(new LocalDate(2016, 11, 1));
    }
}