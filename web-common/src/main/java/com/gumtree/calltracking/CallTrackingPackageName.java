package com.gumtree.calltracking;

import com.google.common.collect.ImmutableSet;
import com.gumtree.web.common.enums.Feature;

import java.util.Optional;
import java.util.Set;

public final class CallTrackingPackageName {
    private static final Set<Feature> CALL_TRACKING_FEATURES = ImmutableSet.of(
            Feature.CALL_TRACKING_ACCOUNT_LEVEL,
            Feature.CALL_TRACKING_ACCOUNT_LEVEL_HIDDEN,
            Feature.CALL_TRACKING_ADVERT_LEVEL,
            Feature.CALL_TRACKING_ADVERT_LEVEL_WITH_BLACKLISTING,
            Feature.EMG_FREESPEE_PERMISSION
    );

    public static Optional<Feature> findCallTrackingInFeatures(Set<String> features) {
        for (Feature feature : CALL_TRACKING_FEATURES) {
            if (features.contains(feature.toString())) {
                return Optional.of(feature);
            }
        }
        return Optional.empty();
    }

    private CallTrackingPackageName() {}
}
