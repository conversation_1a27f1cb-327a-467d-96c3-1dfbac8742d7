package com.gumtree.domain.page;

import com.gumtree.zeno.core.domain.PageType;

public enum Page {
    HOME_RESPONSIVE("pages/home/<USER>", PageType.Homepage),
    MOTORS_HOME("pages/landing-pages/motors/index", PageType.LandingPageCars),
    SEARCH_RESULTS_RESPONSIVE("pages/srp/srp", PageType.ResultsSearch),
    BROWSE_RESULTS_RESPONSIVE("pages/srp/srp", PageType.ResultsBrowse),
    VIP("pages/vip/vip", PageType.VIP),
    VIP_JOBS("pages/vip/vip_jobs", PageType.VIP),
    VIP_TREEBAY("pages/vip/treebay", PageType.pVIP),
    VIP_EXPIRED("pages/vip/expired", PageType.eVIP),
    VIP_GALLERY("pages/vip/gallery", PageType.VipGallery),
    REPLY_BY_EMAIL_RESPONSIVE("pages/reply/reply", PageType.R2SEmail),
    REPL<PERSON>_CONFIRMATION_RESPONSIVE("pages/reply/reply-confirmation", PageType.R2SEmailSuccess),
    PRI<PERSON>CY_POLICY("pages/legal/privacy-policy/privacy-policy", PageType.LegalPrivacy),
    PRIVACY_POLICY_2018("pages/legal/privacy-policy/archive/privacy-policy-24052018", PageType.LegalPrivacy),
    PRIVACY_POLICY_2017("pages/legal/privacy-policy/archive/privacy-policy-12092017", PageType.LegalPrivacy),
    PRIVACY_POLICY_2016("pages/legal/privacy-policy/archive/privacy-policy-2016", PageType.LegalPrivacy),
    INSURANCE("pages/legal/insurance", PageType.LegalTC),
    TERMS_OF_USE("pages/legal/terms-of-use", PageType.LegalTC),
    TERMS_OF_USE_2008("pages/legal/terms-of-use-2008", PageType.LegalTC),
    TERMS_OF_USE_2013("pages/legal/terms-of-use-2013", PageType.LegalTC),
    TERMS_OF_USE_2016("pages/legal/terms-of-use-2016", PageType.LegalTC),
    TERMS_OF_USE_2019("pages/legal/terms-of-use-2019", PageType.LegalTC),
    MODERN_SLAVERY("pages/legal/modern-slavery", PageType.LegalTC),
    DOWNLOAD_APPS("pages/apps/app-landing-page", PageType.LandingPageApps),
    COOKIES_POLICY("pages/legal/cookies", PageType.LegalCookie),
    GWMT_VERIFICATION_FILE("pages/gwmt-verification-file", PageType.GWMTVerification),
    NOT_FOUND_ERROR_RESPONSIVE("pages/error/404", PageType.Error_404),
    INTERNAL_SERVER_ERROR_RESPONSIVE("pages/error/500", PageType.Error_500),
    EXPERIMENT_ACTIVATION("pages/internal/experiment-activation", PageType.Unknown),
    APP_TERMS_OF_USE("pages/legal/app-terms-of-use", PageType.LegalTC),
    SAVED_ADVERTS("pages/my-account/saved-adverts", PageType.Favourites),
    RECENTLY_VIEWED_ADVERTS("pages/bff/recentlyViewedAds", PageType.RecentlyViewedAdverts),
    SAVED_SEARCHES("pages/my-account/saved-searches", PageType.SavedSearches),
    ARTICLE("pages/content/article", PageType.Article),
    SELLER_OTHER_ADS("pages/seller/seller-other-ads", PageType.ResultsSeller),
    ACCOUNT_PROFILE("pages/account/profile", PageType.UserStatic),
    RECRUITER_PROFILE("pages/bff/profile/recruiter", PageType.UserStatic),
    POPULAR_SEARCH("pages/popular-search/popular-search", PageType.PopularSearches),
    PARTNERS("pages/legal/partners", PageType.Partners),
    CATEGORIES("pages/category/category", PageType.LandingPageCategory),
    PONG("pages/special/pong", PageType.Static),
    CAREERS("pages/careers/careers", PageType.Static),
    CAREERS_BUSINESS("pages/careers/roles/business/careers-business", PageType.Static),
    CAREERS_PRODUCT("pages/careers/roles/product/careers-product", PageType.Static),
    CAREERS_TECH("pages/careers/roles/tech/careers-tech", PageType.Static),
    UX_DESIGNER("pages/careers/roles/product/ux-designer", PageType.Static),
    FE_ENGINEER("pages/careers/roles/tech/fe-engineer", PageType.Static),
    SNR_FE_ENGINEER("pages/careers/roles/tech/snr-fe-engineer", PageType.Static),
    SNR_BE_ENGINEER("pages/careers/roles/tech/snr-be-engineer", PageType.Static),
    DEVOPS_ENGINEER("pages/careers/roles/tech/devops-engineer", PageType.Static),
    ANDROID_DEVELOPER("pages/careers/roles/tech/android-developer", PageType.Static),
    NEW_BUSINESS_SALES_EXECUTIVE("pages/careers/roles/business/new-business-sales-executive", PageType.Static),
    ACCOUNTS_RECEIVABLE_SPECIALIST("pages/careers/roles/business/accounts-receivable-specialist", PageType.Static),
    ADVERTISING_TECHNICAL_SPECIALIST("pages/careers/roles/business/advertising-technical-specialist", PageType.Static),
    STYLE_GUIDE("pages/special/styleguide", PageType.Static),
    GDPR(PageType.Gdpr),
    SELL_MY_CAR("pages/bff/sell-my-car", PageType.LandingPageCarsSelling),
    // static page is used as a placeholder for some 'dynamic-static' pages we have in the app. See usages
    STATIC_PAGE("pages/static/unknown", PageType.Static),
    JOB_PRICING("pages/bff/job-pricing", PageType.LandingPageJobsPricing),
    COMPARE_CARS("pages/bff/compare-cars", PageType.CompareCars),
    PRICE_GUIDANCE("pages/bff/price-guidance", PageType.PriceGuidance);

    private String templateName;
    private PageType pageType;

    Page(PageType pageType) {
        this.pageType = pageType;
        this.templateName = null;
    }

    Page(String templateName, PageType pageType) {
        this.templateName = templateName;
        this.pageType = pageType;
    }

    public String getTemplateName() {
        return templateName;
    }

    public PageType getPageType() {
        return pageType;
    }
}
