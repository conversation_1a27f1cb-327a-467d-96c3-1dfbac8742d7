package com.gumtree.web.service.bapi;

import com.google.common.base.Optional;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.gumtree.api.ApiContactEmail;
import com.gumtree.api.EmailStatus;
import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.command.contactemails.CreateContactEmailCommand;
import com.gumtree.api.command.contactemails.DeleteContactEmailCommand;
import com.gumtree.api.command.contactemails.GetContactEmailsCommand;
import com.gumtree.api.command.contactemails.SendVerificationEmailCommand;
import com.gumtree.api.command.contactemails.SetPreferredContactEmailCommand;
import com.gumtree.web.service.ContactEmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.comparator.CompoundComparator;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BapiContactEmailService implements ContactEmailService {

    @Autowired
    private BushfireApi bushfireApi;

    @Override
    public String getPreferred(User user) {
        Optional<ApiContactEmail> email = Iterables.tryFind(getContactEmails(user), ApiContactEmail::isPreferred);
        return email.isPresent() ? email.get().getEmail() : user.getEmail();
    }

    @Override
    public Iterable<String> getForReply(User user) {
        List<ApiContactEmail> contactEmails = getContactEmails(user, EmailStatus.VERIFIED, EmailStatus.VERIFICATION_SENT);
        if (contactEmails.isEmpty()) {
            return Collections.singletonList(user.getEmail());
        } else {
            return contactEmails.stream().map(ApiContactEmail::getEmail).collect(Collectors.toList());
        }
    }

    @Override
    public Iterable<String> getForPosting(User user) {
        // we don't limit to just verified yet
        return getForReply(user);
    }

    @Override
    public Iterable<ApiContactEmail> getEditable(User user) {
        return getContactEmails(user, EmailStatus.VERIFICATION_SENT, EmailStatus.VERIFIED);
    }

    @Override
    public Boolean createNew(User user, String email) {
        return new CreateContactEmailCommand(user, email, bushfireApi).execute();
    }

    @Override
    public Boolean delete(User user, String email) {
        return new DeleteContactEmailCommand(user, email, bushfireApi).execute();
    }

    @Override
    public Boolean setPreferred(User user, String email) {
        return new SetPreferredContactEmailCommand(user, email, bushfireApi).execute();
    }

    @Override
    public Boolean sendVerification(User user, String email) {
        return new SendVerificationEmailCommand(user, email, bushfireApi).execute();
    }

    private List<ApiContactEmail> getContactEmails(User user, EmailStatus... statuses) {
        List<ApiContactEmail> emails = new GetContactEmailsCommand(user, Lists.newArrayList(statuses), bushfireApi).execute();
        Collections.sort(emails, COMPARATOR);
        return emails;
    }

    private static class PreferredContactEmailComparator implements Comparator<ApiContactEmail> {
        @Override
        public int compare(ApiContactEmail o1, ApiContactEmail o2) {
            if (o1.isPreferred()) {
                return -1;
            } else if (o2.isPreferred()) {
                return 1;
            }

            return 0;
        }
    }

    private static class EmailStatusContactEmailComparator implements Comparator<ApiContactEmail> {
        @Override
        public int compare(ApiContactEmail o1, ApiContactEmail o2) {
            if (o1.getStatus().equals(o2.getStatus())) {
                return 0;
            } else if (EmailStatus.VERIFIED.equals(o1.getStatus())) {
                return -1;
            } else if (EmailStatus.VERIFIED.equals(o2.getStatus())) {
                return 1;
            }
            return 0;
        }
    }

    private static class ContactEmailComparator implements Comparator<ApiContactEmail> {
        @Override
        public int compare(ApiContactEmail o1, ApiContactEmail o2) {
            return o1.getEmail().compareTo(o2.getEmail());
        }
    }

    private static final Comparator<ApiContactEmail> COMPARATOR = new CompoundComparator<>(
            new PreferredContactEmailComparator(), new EmailStatusContactEmailComparator(), new ContactEmailComparator());
}
