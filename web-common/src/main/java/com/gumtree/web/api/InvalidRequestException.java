package com.gumtree.web.api;

import org.springframework.http.HttpStatus;

import javax.annotation.Nonnull;

public class InvalidRequestException extends WebApiErrorException {

    public InvalidRequestException(@Nonnull String code, @Nonnull String message) {
        super(new WebApiErrorResponse(HttpStatus.BAD_REQUEST, code, message));
    }

    public InvalidRequestException(@Nonnull String code, @Nonnull String message, LogLevel logLevel) {
        super(new WebApiErrorResponse(HttpStatus.BAD_REQUEST, code, message), logLevel);
    }

}
