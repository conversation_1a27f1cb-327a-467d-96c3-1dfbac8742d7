package com.gumtree.web.page.xmlsitemap.util;

import java.util.List;

/**
 * Interface for a generic paginator of pairs of objects.
 * @param <X> type of first item in each pair
 * @param <Y> type of second item in each pair
 */
public interface PairPaginator<X, Y> {

    /**
     * Get a list of pairs for the specified page. Page numbering begins at 1.
     * @param page page number
     * @return the list of pairs
     */
    List<Pair<X, Y>> getPage(int page);
}
