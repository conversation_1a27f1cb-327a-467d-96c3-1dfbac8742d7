package com.gumtree.web.page.xmlsitemap.util;

import com.google.common.base.Objects;

import java.io.Serializable;

/**
 * Pair of items.
 * @param <X> class of first item in pair
 * @param <Y> class of second item in pair
 */
public class Pair<X, Y> implements Serializable {

    private final X x;
    private final Y y;

    /**
     * Constructor taking the two items in this pair
     * @param x first item
     * @param y second item
     */
    public Pair(X x, Y y) {
        this.x = x;
        this.y = y;
    }

    public final X getX() {
        return x;
    }

    public final Y getY() {
        return y;
    }

    @Override
    public final int hashCode() {
        return Objects.hashCode(x, y);
    }

    @Override
    public final boolean equals(final Object obj) {
        if (obj instanceof Pair) {
            final Pair other = (Pair) obj;
            return Objects.equal(x, other.x)
                    && Objects.equal(y, other.y);
        } else {
            return false;
        }
    }
}
