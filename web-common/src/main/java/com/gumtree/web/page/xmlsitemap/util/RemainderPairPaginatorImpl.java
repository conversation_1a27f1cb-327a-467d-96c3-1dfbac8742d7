package com.gumtree.web.page.xmlsitemap.util;

import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

/**
 * Class for splitting the cartesian product of two lists list of items into a fixed number of sets of pairs.
 * This class uses the remainder to calculate in which page the pair<cat,loc> should go.
 * @param <X> type of item in the first list
 * @param <Y> type of item in the second list
 */
public class RemainderPairPaginatorImpl<X, Y> implements PairPaginator<X, Y> {

    /**
     * First list of items.
     */
    private List<X> xList;

    /**
     * Second list of items.
     */
    private List<Y> yList;

    /**
     * Number of items per page.
     */
    private int pages;

    /**
     * Constructor taking the two lists to paginate
     * @param xList the first list
     * @param yList the second list
     * @param pages number of pages to divide the content into
     */
    public RemainderPairPaginatorImpl(List<X> xList, List<Y> yList, int pages) {
        Assert.notNull(xList);
        Assert.notNull(yList);
        this.xList = xList;
        this.yList = yList;
        setupNumberOfPages(pages);
    }

    private void setupNumberOfPages(int pages) {
        int comb = xList.size() * yList.size();
        if (pages < comb) {
            this.pages = pages;
        } else {
            this.pages = comb;
        }
    }

    @Override
    public final List<Pair<X, Y>> getPage(int page) {
        List<Pair<X, Y>> pairList = new ArrayList<Pair<X, Y>>(pages / 10); // this is quite arbritrary

        for (int i = 0; i < xList.size(); i++) {
            for (int j = 0; j < yList.size(); j++) {
                if (isForThisPage(page, i, j)) {
                    pairList.add(new Pair<X, Y>(xList.get(i), yList.get(j)));
                }
            }
        }

        return pairList;
    }

    // page numbers start with one
    private boolean isForThisPage(int page, int i, int j) {
        return (getPairId(i, j, yList.size()) % pages) + 1 == page;
    }

    private int getPairId(int x, int y, int ySize) {
        return (x * ySize) + y + 1;
    }
}
