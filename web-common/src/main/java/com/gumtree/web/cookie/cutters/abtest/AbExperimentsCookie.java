package com.gumtree.web.cookie.cutters.abtest;

import com.gumtree.labs.api.LuckyNumberGenerator;
import com.gumtree.web.cookie.MultiValueCookie;
import org.springframework.util.StringUtils;

import javax.servlet.http.Cookie;

/**
 * <PERSON><PERSON> to hold the assigned random number used to associate a User with experiments.
 */
public class AbExperimentsC<PERSON>ie extends MultiValueCookie {

    public static final String NAME = "gt_ab";

    private static final Boolean HTTP_ONLY = true;
    private static final String LUCKY_NUMBER = "ln";

    public AbExperimentsCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY);

        initLuckyNumber();
    }

    public AbExperimentsCookie(String domain, int maxAge, String path, Cookie cookie) {
        super(domain, maxAge, path, HTTP_ONLY, cookie.getValue());

        initLuckyNumber();
    }

    @Override
    public String getName() {
        return NAME;
    }

    //

    public String getLuckyNumber() {
        return get(LUCKY_NUMBER);
    }

    private void initLuckyNumber() {
        String luckNumber = get(LUCKY_NUMBER);
        if (StringUtils.isEmpty(luckNumber)) {
            setLuckyNumber(LuckyNumberGenerator.luckyNumberAsBase36());
        }
    }

    private void setLuckyNumber(String value) {
        if (value != null) {
            put(LUCKY_NUMBER, value);
        }
    }

}
