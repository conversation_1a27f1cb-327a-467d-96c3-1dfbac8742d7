package com.gumtree.web.cookie.cutters.threatmetrix;

import com.gumtree.util.UUIDValidator;
import com.gumtree.web.cookie.CookieCutter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.Cookie;

public class ThreatMetrixCookieCutter extends <PERSON><PERSON><PERSON>utter<ThreatMetrixCookie> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ThreatMetrixCookieCutter.class);

    private static final String PATH = "/";

    public ThreatMetrixCookieCutter(String domain){
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return ThreatMetrixCookie.NAME;
    }

    @Override
    protected ThreatMetrixCookie cutExisting(Cookie existingCookie) {
        if(isValid(existingCookie)) {
            return new ThreatMetrixCookie(domain, CookieCutter.SESSION_COOKIE_AGE, PATH, existingCookie);
        } else {
            return cutNew();
        }
    }

    @Override
    public ThreatMetrixCookie cutNew() {
        return new ThreatMetrixCookie(domain, CookieCutter.SESSION_COOKIE_AGE, PATH);
    }

    @Override
    protected Class<ThreatMetrixCookie> getSupportedCookieType() {
        return ThreatMetrixCookie.class;
    }

    private boolean isValid(Cookie existingCookie) {
        if(UUIDValidator.isValid(existingCookie.getValue())) {
            return true;
        } else {
            LOGGER.warn("Threatmetrix cookie value not a UUID {}", existingCookie.getValue());
            return false;
        }
    }

}
