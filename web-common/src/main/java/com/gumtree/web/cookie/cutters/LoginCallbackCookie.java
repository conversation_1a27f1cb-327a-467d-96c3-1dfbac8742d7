package com.gumtree.web.cookie.cutters;

import com.gumtree.web.cookie.BaseCookie;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.Cookie;
import java.util.Optional;

public class LoginCallbackCookie extends BaseCookie<String> {
    public static final String NAME = "gt_lcb";

    private static final Boolean HTTP_ONLY = true;

    public LoginCallbackCookie(String domain, int maxAge, String path, Cookie cookie) {
        super(domain, maxAge, path, HTTP_ONLY, cookie.getValue());
    }

    public LoginCallbackCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY, "");
    }

    public void setCallbackURL(String callbackURL) {
        setValue(callbackURL);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public String getDefaultValue() {
        return "";
    }

    public Optional<String> getValueOpt() {
        return StringUtils.isNotBlank(getValue()) ? Optional.of(getValue()) : Optional.empty();
    }

    @Override
    protected String getValueAsString() {
        return getValue();
    }
}
