package com.gumtree.web.cookie.cutters.abtest;

import com.gumtree.web.cookie.CookieCutter;

import javax.servlet.http.Cookie;

public class AbExperimentsCookieCutter extends Cookie<PERSON>utter<AbExperimentsCookie> {

    private static final String PATH = "/";

    public AbExperimentsCookieCutter(String domain) {
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return AbExperimentsCookie.NAME;
    }

    @Override
    protected AbExperimentsCookie cutExisting(Cookie existingCookie) {
        return new AbExperimentsCookie(domain, MAX_AGE_COOKIE, PATH, existingCookie);
    }

    @Override
    protected AbExperimentsCookie cutNew() {
        return new AbExperimentsCookie(domain, MAX_AGE_COOKIE, PATH);
    }

    @Override
    protected Class<AbExperimentsCookie> getSupportedCookieType() {
        return AbExperimentsCookie.class;
    }

}
