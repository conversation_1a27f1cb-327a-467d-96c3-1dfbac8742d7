package com.gumtree.web.cookie.cutters.userinteraction;

import com.gumtree.web.cookie.CookieCutter;

import javax.servlet.http.Cookie;
import java.util.concurrent.TimeUnit;

public class UserInteraction<PERSON><PERSON><PERSON><PERSON>utter extends <PERSON><PERSON><PERSON>utter<UserInteractionCookie> {

    private static final String PATH = "/";

    public static final int MAX_AGE_COOKIE = (int) TimeUnit.MINUTES.toSeconds(30);

    public UserInteractionCookieCutter(String domain) {
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return UserInteractionCookie.NAME;
    }

    @Override
    protected UserInteractionCookie cutExisting(<PERSON><PERSON> existingCookie) {
        return new UserInteractionCookie(domain, MAX_AGE_COOKIE, PATH, existingCookie);
    }

    @Override
    protected UserInteractionCookie cutNew() {
        return new UserInteractionCookie(domain, MAX_AGE_COOKIE, PATH);
    }

    @Override
    protected Class<UserInteractionCookie> getSupportedCookieType() {
        return UserInteractionCookie.class;
    }

}
