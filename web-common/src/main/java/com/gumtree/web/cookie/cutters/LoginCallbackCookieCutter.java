package com.gumtree.web.cookie.cutters;

import com.gumtree.web.cookie.CookieCutter;

import javax.servlet.http.Cookie;
import java.util.concurrent.TimeUnit;

public class LoginCallback<PERSON>ookie<PERSON>utter extends <PERSON><PERSON><PERSON>utter<LoginCallbackCookie> {

    public static final int MAX_AGE_COOKIE = (int) TimeUnit.MINUTES.toSeconds(10);

    private static final String PATH = "/";

    public LoginCallbackCookieCutter(String domain){
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return LoginCallbackCookie.NAME;
    }

    @Override
    protected LoginCallbackCookie cutExisting(<PERSON><PERSON> existingCookie) {
        return new LoginCallbackCookie(domain, MAX_AGE_COOKIE, PATH, existingCookie);
    }

    @Override
    protected LoginCallbackCookie cutNew() {
        return new LoginCallbackCookie(domain, MAX_AGE_COOKIE, PATH);
    }

    @Override
    protected Class<LoginCallbackCookie> getSupportedCookieType() {
        return LoginCallbackCookie.class;
    }

}
