package com.gumtree.web.cookie.cutters.session;

import com.google.common.base.Optional;
import com.google.common.primitives.Longs;
import com.gumtree.web.cookie.MultiValueCookie;

import javax.servlet.http.Cookie;

public class SessionCookie extends MultiValueCookie {

    private static final Boolean HTTP_ONLY = true;
    private static final String ID_KEY = "id";
    private static final String SEARCH_KEYWORD_KEY = "sk";
    private static final String SEARCH_CATEGORY_KEY = "sc";
    private static final String VIP_REFERRER_KEY = "ar";

    /**
     * Used by Bing advertising - an 1st request to the bing ad service returns id that their associated with the current user. We should
     * be passing that id to bing in any consequent requests. This should help to better target adverts for specific users.
     */
    private static final String BING_CLIENT_ID = "bci";
    private static final String BING_ADVERTISING_ID = "bai";

    /**
     * Contains time of the latest user search (it is used to keep pagination over the search results consistent)
     */
    private static final String SEARCH_ONLY_ADS_POSTED_PRIOR_TIME = "st";

    public static final String NAME = "gt_s";

    public SessionCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY);
    }

    public SessionCookie(String domain, int maxAge, String path, Cookie cookie) {
        super(domain, maxAge, path, HTTP_ONLY, cookie.getValue());
    }

    @Override
    public String getName() {
        return NAME;
    }

    public String getId() {
        return get(ID_KEY);
    }

    public final SessionCookie setId(String id) {
        put(ID_KEY, id);
        return this;
    }

    public String getVIPReferrer() {
        return get(VIP_REFERRER_KEY);
    }

    public Optional<Long> getSearchOnlyAdsPostedPriorToTime() {
        try {
            return Optional.fromNullable(Long.parseLong(get(SEARCH_ONLY_ADS_POSTED_PRIOR_TIME)));
        } catch (Exception ex) {
            return Optional.absent();
        }
    }

    public final SessionCookie setSearchOnlyAdsPostedPriorToTime(Long value) {
        if (value != null) {
            put(SEARCH_ONLY_ADS_POSTED_PRIOR_TIME, value.toString());
        } else {
            remove(SEARCH_ONLY_ADS_POSTED_PRIOR_TIME);
        }

        return this;
    }

    public Optional<String> getSearchKeyword() {
        return Optional.fromNullable(get(SEARCH_KEYWORD_KEY));
    }

    public java.util.Optional<String> getBingClientId() {
        return java.util.Optional.ofNullable(get(BING_CLIENT_ID));
    }

    public java.util.Optional<String> getBingAdvertisingId() {
        return java.util.Optional.ofNullable(get(BING_ADVERTISING_ID));
    }

    public Optional<Long> getSearchCategory() {
        String sc = get(SEARCH_CATEGORY_KEY);
        return sc != null ? Optional.fromNullable(Longs.tryParse(sc)) : Optional.<Long>absent();
    }

    public final SessionCookie setSearchKeyword(String value) {
        if (value != null) {
            put(SEARCH_KEYWORD_KEY, value);
        } else {
            remove(SEARCH_KEYWORD_KEY);
        }
        return this;
    }

    public final SessionCookie setBingClientId(String value) {
        if (value != null) {
            put(BING_CLIENT_ID, value);
        } else {
            remove(BING_CLIENT_ID);
        }
        return this;
    }

    public final SessionCookie setBingAdvertisingId(String value) {
        if (value != null) {
            put(BING_ADVERTISING_ID, value);
        } else {
            remove(BING_ADVERTISING_ID);
        }
        return this;
    }

    public final SessionCookie setSearchCategory(Long value) {
        if (value != null) {
            put(SEARCH_CATEGORY_KEY, value.toString());
        } else {
            remove(SEARCH_CATEGORY_KEY);
        }
        return this;
    }

    public final SessionCookie setVIPReferrer(String value) {
        put(VIP_REFERRER_KEY, value);
        return this;
    }

    public String getClickThroughTypeForAdvert(String key) {
        return get(key);
    }

    public void setClickThroughTypeSearchResults(String key, String value) {
        put(key, value);
    }
}
