package com.gumtree.web.cookie.utils;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * Contains utils to generate tracking ids passed to Odin, GA & DFP
 *
 * @see <a href="https://ecgwiki.corp.ebay.com/pages/viewpage.action?pageId=168785181">Linking all datasets (Odin, DFP, GA, eBay BE)</a>
 */
public final class TrackingUtils {

    public static String generateHashedClientId(String cookieId) {
        return DigestUtils.sha256Hex(cookieId);
    }

    private TrackingUtils() {
    }
}
