package com.gumtree.web.cookie.cutters.session;

import com.gumtree.web.cookie.CookieCutter;

import javax.servlet.http.Cookie;

public class SessionCookie<PERSON>utter extends <PERSON><PERSON><PERSON>utter<SessionCookie> {

    private static final String PATH = "/";

    public SessionCookieCutter(String domain) {
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return SessionCookie.NAME;
    }

    @Override
    protected SessionCookie cutExisting(Cookie existingCookie) {
        return new SessionCookie(domain, SESSION_COOKIE_AGE, PATH, existingCookie);
    }

    @Override
    protected SessionCookie cutNew() {
        return new SessionCookie(domain, SESSION_COOKIE_AGE, PATH);
    }

    @Override
    protected Class<SessionCookie> getSupportedCookieType() {
        return SessionCookie.class;
    }

}
