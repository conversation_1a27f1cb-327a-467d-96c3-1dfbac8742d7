package com.gumtree.web.cookie.cutters.userpreferences;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.gumtree.common.util.url.UrlUtils;
import com.gumtree.web.cookie.MultiValueCookie;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.Cookie;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class UserPreferencesCookie extends MultiValueCookie {
    public static final String NAME = "gt_userPref";
    public static final String UK = "uk";
    public static final String TRUE = "true";
    public static final String FALSE = "false";
    public static final String CARS_VANS_MOTORBIKES = "cars-vans-motorbikes";
    public static final String FOR_SALE = "for-sale";

    private static final Boolean HTTP_ONLY = false;
    private static final String SEARCH_OPEN = "isSearchOpen"; // preference for search bar
    private static final String COOKIE_POLICY = "cookiePolicy"; // FE - cookie policy banner
    private static final String RECENT_ADS_ONE = "recentAdsOne"; // latest ads on HP - set by FE
    private static final String RECENT_ADS_TWO = "recentAdsTwo"; // latest ads on HP - set by FE
    private static final String LOCATION_KEY = "location";
    private static final String LAST_FIVE_SEARCH_KEYWORDS_LATEST_FIRST = "lfsk";

    public UserPreferencesCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY);
        setCookieValues();
    }

    public UserPreferencesCookie(String domain, int maxAge, String path, Cookie cookie) {
        super(domain, maxAge, path, HTTP_ONLY, cookie.getValue());
        setCookieValues();
    }

    @Override
    public String getName() {
        return NAME;
    }

    public boolean isCookiePolicySeen() {
        return Boolean.parseBoolean(get(COOKIE_POLICY));
    }

    public String getRecentAdsOneCategoryName() {
        return get(RECENT_ADS_ONE);
    }

    public String getRecentAdsTwoCategoryName() {
        return get(RECENT_ADS_TWO);
    }

    public Optional<String> getLocation() {
        return Optional.fromNullable(UrlUtils.dec(get(LOCATION_KEY)));
    }

    public UserPreferencesCookie setLocation(String locationName) {
        put(LOCATION_KEY, locationName);
        return this;
    }

    private void setCookieValues() {
        if (StringUtils.isEmpty(getLocation().or(""))) {
            setLocation(UK);
        }
        // we close by default as cookie will be created by cutter if it doesn't exist
        if (StringUtils.isEmpty(get(SEARCH_OPEN))) {
            put(SEARCH_OPEN, TRUE);
        }

        // for cookie policy, if it is empty it is the first request that user does
        // if it is false (second request) we set it to true as the user will have seen it already
        if (StringUtils.isEmpty(get(COOKIE_POLICY))) {
            put(COOKIE_POLICY, FALSE);
        } else if (!isCookiePolicySeen()) {
            put(COOKIE_POLICY, TRUE);
        }

        // to avoid NPE down the line, default category seo name as root
        if (StringUtils.isEmpty(get(RECENT_ADS_ONE))) {
            put(RECENT_ADS_ONE, CARS_VANS_MOTORBIKES);
        }
        if (StringUtils.isEmpty(get(RECENT_ADS_TWO))) {
            put(RECENT_ADS_TWO, FOR_SALE);
        }
    }

    public List<String> getSearchKeywords() {
        String savedKeywords = get(LAST_FIVE_SEARCH_KEYWORDS_LATEST_FIRST);
        return StringUtils.isNotBlank(savedKeywords)
                ? decodeSearchKeywords(savedKeywords)
                : Collections.emptyList();

    }

    public void saveSearchKeyword(String keyword) {
        List<String> list = addSearchKeywordToTheHead(getSearchKeywords(), keyword);
        put(LAST_FIVE_SEARCH_KEYWORDS_LATEST_FIRST, encodeSearchKeywords(list));
    }

    static String encodeSearchKeywords(List<String> kws) {
        return kws.stream()
                .map(kw -> {
                    try {
                        return URLEncoder.encode(kw, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.joining(","));
    }

    static List<String> decodeSearchKeywords(String kws) {
        return Stream.of(kws.split("[,]"))
                .map(kw -> {
                    try {
                        return URLDecoder.decode(kw, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toList());
    }

    static List<String> addSearchKeywordToTheHead(List<String> list, String toAdd) {
        Set<String> unique = Sets.newHashSet(toAdd.toLowerCase());
        List<String> result = Lists.newArrayList(toAdd);
        int i = 0;
        while (i < list.size() && unique.size() < 5) {
            if (unique.add(list.get(i).toLowerCase())) {
                result.add(list.get(i));
            }
            i++;
        }
        return result;
    }
}
