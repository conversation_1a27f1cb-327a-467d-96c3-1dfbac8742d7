package com.gumtree.web.cookie.cutters.userpreferences;

import com.gumtree.web.cookie.CookieCutter;

import javax.servlet.http.Cookie;

public class UserAdPreferenceCookieCutter extends <PERSON>ie<PERSON>utter<UserAdPreferenceCookie> {

    private static final String PATH = "/";

    public UserAdPreferenceCookieCutter(String domain) {
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return UserAdPreferenceCookie.NAME;
    }

    @Override
    protected UserAdPreferenceCookie cutExisting(Cookie existingCookie) {
        return new UserAdPreferenceCookie(domain, CookieCutter.MAX_AGE_COOKIE, PATH, existingCookie.getValue());
    }

    @Override
    protected UserAdPreferenceCookie cutNew() {
        return new UserAdPreferenceCookie(domain, CookieCutter.MAX_AGE_COOKIE, PATH);
    }

    @Override
    protected Class<UserAdPreferenceCookie> getSupportedCookieType() {
        return UserAdPreferenceCookie.class;
    }

}
