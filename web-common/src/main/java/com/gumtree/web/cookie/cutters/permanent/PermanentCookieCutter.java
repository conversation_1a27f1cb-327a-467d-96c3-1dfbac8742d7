package com.gumtree.web.cookie.cutters.permanent;

import com.gumtree.web.cookie.CookieCutter;

import javax.servlet.http.Cookie;

public class PermanentCookieCutter extends <PERSON><PERSON>Cutter<PermanentCookie> {

    private static final String PATH = "/";


    public PermanentCookieCutter(String domain) {
        super(domain);
    }

    @Override
    protected String getBaseName() {
        return PermanentCookie.NAME;
    }

    @Override
    protected PermanentCookie cutExisting(Cookie existingCookie) {
        return new PermanentCookie(domain, MAX_AGE_COOKIE, PATH, existingCookie);
    }

    @Override
    protected PermanentCookie cutNew() {
        return new PermanentCookie(domain, MAX_AGE_COOKIE, PATH);
    }

    @Override
    protected Class<PermanentCookie> getSupportedCookieType() {
        return PermanentCookie.class;
    }

}
