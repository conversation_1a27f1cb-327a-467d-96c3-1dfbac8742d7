package com.gumtree.web.cookie.cutters.permanent;

import com.google.common.base.Optional;
import com.gumtree.web.cookie.MultiValueCookie;
import org.springframework.util.StringUtils;

import javax.servlet.http.Cookie;
import java.util.UUID;

public final class PermanentCookie extends MultiValueCookie {

    private static final Boolean HTTP_ONLY = true;
    private static final String ID_KEY = "id";
    private static final String REPLY_NAME_KEY = "rn";
    private static final String REPLY_EMAIL_KEY = "re";
    private static final String REPLY_PHONE_KEY = "rp";

    public static final String NAME = "gt_p";

    private boolean isNew;

    public PermanentCookie(String domain, int maxAge, String path) {
        super(domain, maxAge, path, HTTP_ONLY);
        setId(UUID.randomUUID().toString());
        this.isNew = true;
    }

    public PermanentCookie(String domain, int maxAge, String path, Cookie cookie) {
        super(domain, maxAge, path, HTTP_ONLY, cookie.getValue());

        // Generate an ID for the cookie if it didn't already have one.
        String id = get(ID_KEY);

        if (StringUtils.isEmpty(id)) {
            id = UUID.randomUUID().toString();
            setId(id);
        }

        this.isNew = false;
    }

    @Override
    public String getName() {
        return NAME;
    }

    public String getId() {
        return get(ID_KEY);
    }

    public PermanentCookie setId(String id) {
        put(ID_KEY, id);
        return this;
    }

    public Optional<String> getReplyName() {
        return Optional.fromNullable(get(REPLY_NAME_KEY));
    }

    public PermanentCookie setReplyName(String replyName) {
        put(REPLY_NAME_KEY, replyName);
        return this;
    }

    public Optional<String> getReplyEmail() {
        return Optional.fromNullable(get(REPLY_EMAIL_KEY));
    }

    public PermanentCookie setReplyEmail(String replyEmail) {
        put(REPLY_EMAIL_KEY, replyEmail);
        return this;
    }

    public Optional<String> getReplyPhone() {
        return Optional.fromNullable(get(REPLY_PHONE_KEY));
    }

    public PermanentCookie setReplyPhone(String replyPhone) {
        put(REPLY_PHONE_KEY, replyPhone);
        return this;
    }

    public boolean isNew() {
        return isNew;
    }
}
