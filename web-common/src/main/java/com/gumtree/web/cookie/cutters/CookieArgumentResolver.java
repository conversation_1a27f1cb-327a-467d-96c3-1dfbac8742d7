package com.gumtree.web.cookie.cutters;

import com.gumtree.web.cookie.BaseCookie;
import com.gumtree.web.cookie.CookieResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebArgumentResolver;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

public class CookieArgumentResolver implements HandlerMethodArgumentResolver {

    private static final Logger LOG = LoggerFactory.getLogger(CookieArgumentResolver.class);

    private CookieResolver cookieResolver;

    public CookieArgumentResolver(CookieResolver cookieResolver) {
        this.cookieResolver = cookieResolver;
    }

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return BaseCookie.class.isAssignableFrom(parameter.getParameterType());
    }

    @Override
    @SuppressWarnings("unchecked")
    public Object resolveArgument(MethodParameter parameter,
                                  ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest,
                                  WebDataBinderFactory binderFactory) {
        try {
            Class<? extends BaseCookie> cookieClass = (Class<? extends BaseCookie>) parameter.getParameterType();
            return cookieResolver.resolve(webRequest, cookieClass);
        } catch (Exception e) {
            LOG.warn("Couldn't resolve cookie of type " + parameter.getParameterType(), e);
            return WebArgumentResolver.UNRESOLVED;
        }
    }
}
