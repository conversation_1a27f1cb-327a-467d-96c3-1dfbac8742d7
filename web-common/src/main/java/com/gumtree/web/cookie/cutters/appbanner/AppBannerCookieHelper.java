package com.gumtree.web.cookie.cutters.appbanner;

import com.gumtree.web.cookie.CookieResolver;
import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

public class AppBannerCookieHelper {

    private final CookieResolver cookieResolver;

    public enum Action {
        MESSAGE_CENTER("MessageCenterWebApp"),
        POST_AD_COMPLETE("PostAdCompletion"),
        REPLY_SUCCESS("R2SEmailSuccess"),
        ACCOUNT_ACTIVATION("SuccessfulRegistration");

        private String campaignType;

        Action(String campaignType) {
            this.campaignType = campaignType;
        }

        public String getCampaignType() {
            return campaignType;
        }

        public static Optional<Action> getActionByCampaignType(String campaignType) {
            for (Action action : values()) {
                if (action.getCampaignType().equals(campaignType)) {
                    return Optional.of(action);
                }
            }
            return Optional.empty();
        }
    }

    public AppBannerCookieHelper(CookieResolver cookieResolver) {
        this.cookieResolver = cookieResolver;
    }

    /**
     * The app banner cookie is created when a user performs certain activities on the gumtree site. The
     * cookie is visible to the user with an appropriate message (based on last relevant activity).
     * If the user dismisses the cookie, the client updates the cookie value, setting the visibility to false entry
     * and extending the expiry date and max age of the cookie. #TODO: create ajax controller for dismiss purposes
     *
     * @param action user activity
     * @param request
     * @return the changed cookie
     */
    public AppBannerCookie updateAppBannerCookie(Action action, HttpServletRequest request) {
        AppBannerCookie appBannerCookie = cookieResolver.resolve(request, AppBannerCookie.class);
        if (appBannerCookie.getAppBannerVisible() == null || appBannerCookie.isVisible()) {
            Optional.of(action).ifPresent(a -> appBannerCookie.setActionTriggered(a.getCampaignType()));
        }
        return appBannerCookie;
    }

}
