package com.gumtree.web.filter;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.MwebProperty;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class ClickjackingFilter extends OncePerRequestFilter {

    private final static String CONTENT_SECURITY_POLICY_HEADER_NAME = "Content-Security-Policy";

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        final String securityPolicyContentHeader = GtProps.getStr(MwebProperty.CONTENT_SECURITY_POLICY);
        response.addHeader(CONTENT_SECURITY_POLICY_HEADER_NAME, securityPolicyContentHeader);

        filterChain.doFilter(request, response);
    }

}
