package com.gumtree.web.advertising.partnership;

import com.google.common.base.Optional;
import org.codehaus.jackson.annotate.JsonProperty;

import static com.google.common.base.Optional.fromNullable;
import static com.google.common.base.Preconditions.checkNotNull;

public class Partnership {

    private final String id;
    private final String title;
    private final boolean main;
    private final Optional<String> html;
    private final Optional<String> version;

    public Partnership(String id, String title) {
        this(id,title,null,null,false,null);
    }

    public Partnership(@JsonProperty("id") String id,
                       @JsonProperty("title") String title,
                       @JsonProperty("html") String html,
                       @JsonProperty("version") String version,
                       //TODO we should change this all the way to active
                       @JsonProperty("active") boolean main,
                       @JsonProperty("deviceType") String type) {
        this.main = main;
        this.id = checkNotNull(id);
        this.title = checkNotNull(title);
        this.html = fromNullable(html);
        this.version = fromNullable(version);

    }

    public String getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public Optional<String> getHtml() {
        return html;
    }

    public Optional<String> getVersion() {
        return version;
    }

    public boolean isMain() {
        return main;
    }

    @Override
    public String toString() {
        return "Partnership: " + id;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Partnership)) return false;

        Partnership that = (Partnership) o;

        if (main != that.main) return false;
        if (!id.equals(that.id)) return false;
        if (!title.equals(that.title)) return false;
        if (!html.equals(that.html)) return false;
        return version.equals(that.version);

    }

    @Override
    public int hashCode() {
        int result = id.hashCode();
        result = 31 * result + title.hashCode();
        result = 31 * result + (main ? 1 : 0);
        result = 31 * result + html.hashCode();
        result = 31 * result + version.hashCode();
        return result;
    }
}
