package com.gumtree.web.advertising.partnership.api;

import com.gumtree.web.advertising.partnership.Partnership;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * Created by reweber on 07/04/2016
 */
public interface PartnershipsApiContract {
    @GET
    @Path("/partner/web")
    @Produces(MediaType.APPLICATION_JSON)
    List<Partnership> getContent(@QueryParam("page_type") String pageType,

                                 @QueryParam("category_hierarchy") String categoryHierarchy,
                                 @QueryParam("price") String price,
                                 @QueryParam("outcode") String outcode,
                                 @QueryParam("location") String location,
                                 @QueryParam("latitude") String latitude,
                                 @QueryParam("longitude") String longitude,
                                 @QueryParam("attributes") String attributes,
                                 @QueryParam("device_type") String deviceType,
                                 @QueryParam("advert_image") String advertImage,
                                 @QueryParam("experiments") String experiments,
                                 @QueryParam("accountId") Long accountId);

}