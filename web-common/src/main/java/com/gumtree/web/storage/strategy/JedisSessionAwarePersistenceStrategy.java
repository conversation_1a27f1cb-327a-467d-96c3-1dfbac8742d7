package com.gumtree.web.storage.strategy;

import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.storage.RedisTemplate;
import com.gumtree.web.storage.exception.SessionDataAccessException;

/**
 * Modifies keys to be session-id aware
 */
public class JedisSessionAwarePersistenceStrategy extends JedisPersistenceStrategy {
    private static final String NAMESPACE = "usersession_%s_%s";

    private UserSessionService userSessionService;

    public JedisSessionAwarePersistenceStrategy(RedisTemplate redisTemplate, UserSessionService userSessionService) {
        super(redisTemplate);
        this.userSessionService = userSessionService;
    }


    @Override
    public void writeOperation(WriteOperationCallback writeOperationCallback) {
        try {
            writeOperationCallback.doWriteOperation(
                    new JedisSessionAwareWriteOperationHandler(redisTemplate, userSessionService.getSessionCookieId()));
        } catch (Exception je) {
            throw new SessionDataAccessException(je);
        }
    }

    @Override
    public String readOperation(String key) {
        return super.readOperation(formatKey(userSessionService.getSessionCookieId(), key));
    }


    private String formatKey(String prefix, String baseKey) {
        return String.format(NAMESPACE, prefix, baseKey);
    }

    public class JedisSessionAwareWriteOperationHandler extends JedisWriteOperationHandler {
        private String keyPrefix;

        public JedisSessionAwareWriteOperationHandler(RedisTemplate redisTemplate, String keyPrefix) {
            super(redisTemplate);
            this.keyPrefix = keyPrefix;
        }

        @Override
        public void set(String key, String value, int ttl) {
            super.set(formatKey(keyPrefix, key), value, ttl);
        }

        @Override
        public void expire(String key, int ttl) {
            super.expire(formatKey(keyPrefix, key), ttl);
        }

        @Override
        public void del(String key) {
            super.del(formatKey(keyPrefix, key));
        }
    }
}
