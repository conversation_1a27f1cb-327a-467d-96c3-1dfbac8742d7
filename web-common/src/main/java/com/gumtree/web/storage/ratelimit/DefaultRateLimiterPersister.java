package com.gumtree.web.storage.ratelimit;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.gumtree.common.util.StringUtils;
import com.gumtree.web.storage.KeyValueRepository;
import com.gumtree.web.storage.RedisTemplate;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

import static com.codahale.metrics.MetricRegistry.name;

public class DefaultRateLimiterPersister extends AbstractRateLimiterPersister {
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultRateLimiterPersister.class);

    private final RedisTemplate redisTemplate;
    private final KeyValueRepository cassandraRepository;

    private final Counter cassandraReadFailureCounter;
    private final Counter cassandraWriteFailureCounter;

    public DefaultRateLimiterPersister(RedisTemplate redisTemplate,
                                       KeyValueRepository cassandraRepository,
                                       MetricRegistry metricRegistry) {
        this.redisTemplate = redisTemplate;
        this.cassandraRepository = cassandraRepository;
        this.cassandraReadFailureCounter = metricRegistry.counter(name(DefaultRateLimiterPersister.class, "cassandra.read.failure"));
        this.cassandraWriteFailureCounter = metricRegistry.counter(name(DefaultRateLimiterPersister.class, "cassandra.write.failure"));
    }

    protected final Optional<Pair<String, Integer>> readWithTTL(String key) {
        Optional<Pair<String, Integer>> valueWithTTL = Optional.empty();


        try {
            valueWithTTL = cassandraRepository.getWithTTL(key);
        } catch (Exception e) {
            LOGGER.warn("Unable to retrieve data from Cassandra", e);
            cassandraReadFailureCounter.inc();
        }

        if (valueWithTTL.isPresent()) {
            return valueWithTTL;
        }


        String value = redisTemplate.get(key);
        if (StringUtils.hasText(value)) {
            int ttl = redisTemplate.ttl(key).intValue();
            // return entry only if it hadn't expired in the meantime
            if (ttl >= 0) {
                return Optional.of(Pair.of(value, ttl));
            }
        }

        return Optional.empty();
    }

    protected final void write(String key, String value, int ttl) {
        try {
            cassandraRepository.set(key, value, ttl);
        } catch (Exception e) {
            LOGGER.warn("Unable to persist data to Cassandra", e);
            cassandraWriteFailureCounter.inc();
        }
        redisTemplate.set(key, value, ttl);
    }
}
