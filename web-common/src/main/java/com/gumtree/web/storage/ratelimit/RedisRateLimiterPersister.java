package com.gumtree.web.storage.ratelimit;

import com.gumtree.common.util.StringUtils;
import com.gumtree.web.storage.RedisTemplate;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

public class RedisRateLimiterPersister extends AbstractRateLimiterPersister {
    private static final Logger LOGGER = LoggerFactory.getLogger(RedisRateLimiterPersister.class);

    private final RedisTemplate redisTemplate;

    public RedisRateLimiterPersister(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    protected final Optional<Pair<String, Integer>> readWithTTL(String key) {
        try {
            String redisKey = createKey(key);
            String value = redisTemplate.get(redisKey);
            if (StringUtils.hasText(value)) {
                int ttl = redisTemplate.ttl(redisKey).intValue();
                // return entry only if it hadn't expired in the meantime
                if (ttl >= 0) {
                    return Optional.of(Pair.of(value, ttl));
                }
            }
        } catch (Exception e) {
            LOGGER.warn("Unable to retrieve data from Redis", e);
        }

        return Optional.empty();
    }

    protected final void write(String key, String value, int ttl) {
        try {
            redisTemplate.set(createKey(key), value, ttl);
        } catch (Exception e) {
            LOGGER.warn("Unable to persist data to Redis", e);
        }
    }

    private String createKey(String value) {
        return "pnrl" + value;
    }
}
