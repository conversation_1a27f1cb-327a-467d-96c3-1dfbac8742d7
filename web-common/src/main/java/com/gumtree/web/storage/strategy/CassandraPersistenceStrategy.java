package com.gumtree.web.storage.strategy;


import com.gumtree.web.storage.KeyValueRepository;
import com.gumtree.web.storage.exception.SessionDataAccessException;


public class CassandraPersistenceStrategy implements SessionPersistenceStrategy {
    private final KeyValueRepository<String, String> repository;

    public CassandraPersistenceStrategy(KeyValueRepository<String, String> repository) {
        this.repository = repository;
    }

    @Override
    public void writeOperation(WriteOperationCallback writeOperationCallback) {
        try {
            writeOperationCallback.doWriteOperation(new CassandraWriteOperationHandler(repository));
        } catch (Exception e) {
            throw new SessionDataAccessException(e);
        }
    }

    @Override
    public String readOperation(String key) {
        try {
            return repository.get(key).orElse(null);
        } catch (Exception e) {
            throw new SessionDataAccessException(e);
        }
    }

    public class CassandraWriteOperationHandler implements WriteOperationHandler {
        private KeyValueRepository repository;

        public CassandraWriteOperationHandler(KeyValueRepository repository) {
            this.repository = repository;
        }

        @Override
        public void set(String key, String value, int ttl) {
            repository.set(key, value, ttl);
        }

        @Override
        public void expire(String key, int ttl) {
            repository.expire(key, ttl);
        }

        @Override
        public void del(String key) {
            repository.delete(key);
        }
    }
}
