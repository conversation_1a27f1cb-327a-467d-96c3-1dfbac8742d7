package com.gumtree.web.storage.flash;

import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.servlet.FlashMap;

import java.io.Serializable;
import java.util.Map;

/**
 * Class which gets around the awkwardness of not being able to serialize the original flashmaps
 */
public class SerializableFlashMap implements Serializable {
    private Map<String, Object> flashValues;
    private String requestPath;
    private LinkedMultiValueMap<String, String> requestParams;

    public Map<String, Object> getFlashValues() {
        return flashValues;
    }

    public void setFlashValues(Map<String, Object> flashValues) {
        this.flashValues = flashValues;
    }

    public String getRequestPath() {
        return requestPath;
    }

    public void setRequestPath(String requestPath) {
        this.requestPath = requestPath;
    }

    public MultiValueMap<String, String> getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(LinkedMultiValueMap<String, String> requestParams) {
        this.requestParams = requestParams;
    }

    public FlashMap toFlashMap() {
        FlashMap flashMap = new FlashMap();
        flashMap.putAll(getFlashValues());
        flashMap.setTargetRequestPath(getRequestPath());
        flashMap.addTargetRequestParams(getRequestParams());

        return flashMap;
    }

    public static class Builder {
        private FlashMap flashMap;

        public Builder withFlashMap(FlashMap flashMap) {
            this.flashMap = flashMap;
            return this;
        }

        public SerializableFlashMap build() {
            SerializableFlashMap serializableFlashMap = new SerializableFlashMap();

            if (flashMap != null) {
                serializableFlashMap.setFlashValues(flashMap);
                serializableFlashMap.setRequestParams(
                        new LinkedMultiValueMap<>(flashMap.getTargetRequestParams()));
                serializableFlashMap.setRequestPath(flashMap.getTargetRequestPath());
            }

            return serializableFlashMap;
        }
    }
}
