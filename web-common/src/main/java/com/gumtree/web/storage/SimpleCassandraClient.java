package com.gumtree.web.storage;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.datastax.driver.core.BoundStatement;
import com.datastax.driver.core.Cluster;
import com.datastax.driver.core.ConsistencyLevel;
import com.datastax.driver.core.PreparedStatement;
import com.datastax.driver.core.ResultSet;
import com.datastax.driver.core.Session;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PreDestroy;
import javax.validation.constraints.NotNull;

import static com.codahale.metrics.MetricRegistry.name;


public class SimpleCassandraClient implements CassandraClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(SimpleCassandraClient.class);
    private static final ConsistencyLevel DEFAULT_CONSISTENCY_LEVEL = ConsistencyLevel.QUORUM;

    private final Session session;
    private final Timer executionTimer;


    public SimpleCassandraClient(Cluster cluster, String keySpace, MetricRegistry metricRegistry) {
        this.session = cluster.connect(keySpace);
        this.executionTimer = metricRegistry.timer(name(this.getClass(), "execute"));
    }

    @Override
    public PreparedStatement prepareStatement(String statement) {
        return session.prepare(statement);
    }

    @Override
    public ResultSet execute(@NotNull PreparedStatement statement, Object... values) {
        Validate.notNull(statement);

        Timer.Context timerContext = executionTimer.time();
        try {
            return session.execute(new BoundStatement(statement).bind(values).setConsistencyLevel(DEFAULT_CONSISTENCY_LEVEL));
        } finally {
            timerContext.stop();
        }
    }

    @PreDestroy
    public void close() {
        LOGGER.info("Shutting down Cassandra Client");
        Cluster cluster = session.getCluster();
        if (!session.isClosed()) {
            session.close();
        }
        if (!cluster.isClosed()) {
            cluster.close();
        }
    }
}
