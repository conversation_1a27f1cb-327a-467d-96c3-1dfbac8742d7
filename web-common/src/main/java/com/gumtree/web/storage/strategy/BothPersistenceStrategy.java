package com.gumtree.web.storage.strategy;

import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.google.common.collect.Lists;
import com.gumtree.web.storage.exception.SessionDataAccessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

import static com.codahale.metrics.MetricRegistry.name;

/**
 * Persistence strategy that writes session data to both Redis and Cassandra
 */
public class BothPersistenceStrategy implements SessionPersistenceStrategy {
    private static final Logger LOGGER = LoggerFactory.getLogger(BothPersistenceStrategy.class);

    private final Counter redisReadWriteFailureCounter;
    private final Counter cassandraReadFailureCounter;
    private final Counter cassandraWriteFailureCounter;

    private final int numberOfEnabledRepositories = 2;
    private final CassandraPersistenceStrategy cassandraPersistenceStrategy;
    private final JedisPersistenceStrategy jedisPersistenceStrategy;

    public BothPersistenceStrategy(CassandraPersistenceStrategy cassandraPersistenceStrategy,
                                   JedisPersistenceStrategy jedisPersistenceStrategy,
                                   MetricRegistry metricRegistry) {
        this.cassandraPersistenceStrategy = cassandraPersistenceStrategy;
        this.jedisPersistenceStrategy = jedisPersistenceStrategy;
        this.redisReadWriteFailureCounter = metricRegistry.counter(name(BothPersistenceStrategy.class, "session.map.fallback"));
        this.cassandraReadFailureCounter = metricRegistry.counter(name(BothPersistenceStrategy.class, "cassandra.read.failure"));
        this.cassandraWriteFailureCounter = metricRegistry.counter(name(BothPersistenceStrategy.class, "cassandra.write.failure"));
    }

    @Override
    public void writeOperation(WriteOperationCallback writeOperationCallback) {
        List<SessionDataAccessException> failures = Lists.newArrayList();


        try {
            cassandraPersistenceStrategy.writeOperation(writeOperationCallback);
        } catch (SessionDataAccessException exc) {
            LOGGER.warn("Unable to persist data to Cassandra");
            cassandraWriteFailureCounter.inc();
            failures.add(exc);
        }


        try {
            jedisPersistenceStrategy.writeOperation(writeOperationCallback);
        } catch (SessionDataAccessException exc) {
            LOGGER.warn("Unable to persist data to Redis");
            redisReadWriteFailureCounter.inc();
            failures.add(exc);
        }

        // propagate exception if all repositories fail
        if (failures.size() == numberOfEnabledRepositories) {
            throw failures.get(0);
        }
    }

    @Override
    public String readOperation(String key) {
        List<SessionDataAccessException> failures = Lists.newArrayList();
        String value = null;

        try {
            value = cassandraPersistenceStrategy.readOperation(key);
        } catch (SessionDataAccessException exc) {
            LOGGER.warn("Unable to retrieve data from Cassandra.");
            cassandraReadFailureCounter.inc();
            failures.add(exc);
        }


        if (value == null) {
            try {
                value = jedisPersistenceStrategy.readOperation(key);
            } catch (SessionDataAccessException exc) {
                LOGGER.warn("Unable to retrieve data from Redis.");
                redisReadWriteFailureCounter.inc();
                failures.add(exc);
            }
        }

        // propagate exception if all repositories fail
        if (failures.size() == numberOfEnabledRepositories) {
            throw failures.get(0);
        }

        return value;
    }
}
