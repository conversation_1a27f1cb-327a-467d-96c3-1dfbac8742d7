package com.gumtree.web.storage;


import com.datastax.driver.core.PreparedStatement;
import com.datastax.driver.core.Row;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotNull;
import java.util.Optional;

public class CassandraKeyValueRepository<K, V> implements KeyValueRepository<K, V> {
    private final CassandraClient client;

    private final String valueColumn;
    private final Class<V> valueType;

    private final PreparedStatement insertStatement;
    private final PreparedStatement selectStatement;
    private final PreparedStatement selectWithTTLStatement;
    private final PreparedStatement deleteStatement;

    public CassandraKeyValueRepository(CassandraClient client, String table, String keyColumn, String valueColumn, Class<V> valueType) {
        this.client = client;
        this.valueColumn = valueColumn;

        this.valueType = valueType;

        this.insertStatement = client.prepareStatement(
                "insert into " + table + " (" + keyColumn + ", " + valueColumn + ") values(?, ?) using ttl ?");
        this.selectStatement = client.prepareStatement(
                "select " + valueColumn + " from " + table + " where " + keyColumn + " = ?");
        this.selectWithTTLStatement = client.prepareStatement(
                "select " + valueColumn + ", ttl(" + valueColumn + ") as ttl from " + table + " where " + keyColumn + " = ?");
        this.deleteStatement = client.prepareStatement("delete from " + table + " where " + keyColumn + " = ?");
    }

    /**
     * Retrieves value
     *
     * @param key entry key
     * @return value or {@code Optional.empty()}
     */
    @Override
    public Optional<V> get(@NotNull K key) {
        Validate.notNull(key);

        Row one = client.execute(selectStatement, key).one();
        return one != null
                ? Optional.of(one.get(valueColumn, valueType))
                : Optional.empty();
    }

    /**
     * Retrieves value and its TTL
     *
     * @param key entry key
     * @return value & TTL in seconds or {@code Optional.empty()}
     */
    @Override
    public Optional<Pair<V, Integer>> getWithTTL(@NotNull K key) {
        Validate.notNull(key);

        Row one = client.execute(selectWithTTLStatement, key).one();
        return one != null
                ? Optional.of(Pair.of(one.get(valueColumn, valueType), one.getInt("ttl")))
                : Optional.empty();
    }

    /**
     * Inserts or updates entry
     *
     * @param key entry key
     * @param value entry value
     * @param ttl TTL value in seconds
     */
    @Override
    public void set(@NotNull K key, @NotNull V value, int ttl) {
        Validate.notNull(key);
        Validate.notNull(value);
        Validate.isTrue(ttl > 0);

        client.execute(insertStatement, key, value, ttl);
    }

    /**
     * Resets TTL value
     *
     * @param key entry key
     * @param ttl TTL value in seconds
     */
    @Override
    public void expire(@NotNull K key, int ttl) {
        Validate.notNull(key);
        Validate.isTrue(ttl > 0);

        Row one = client.execute(selectStatement, key).one();
        if (one != null) {
            V value = one.get(valueColumn, valueType);
            client.execute(insertStatement, key, value, ttl);
        }
    }

    /**
     * Deletes entry
     *
     * @param key entry key
     */
    @Override
    public void delete(@NotNull K key) {
        Validate.notNull(key);

        client.execute(deleteStatement, key);
    }
}
