package com.gumtree.web.storage.ratelimit;

import com.gumtree.web.storage.KeyValueRepository;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

public class CassandraRateLimiterPersister extends AbstractRateLimiterPersister {
    private static final Logger LOGGER = LoggerFactory.getLogger(CassandraRateLimiterPersister.class);

    private final KeyValueRepository cassandraRepository;

    public CassandraRateLimiterPersister(KeyValueRepository cassandraRepository) {
        this.cassandraRepository = cassandraRepository;
    }

    protected final Optional<Pair<String, Integer>> readWithTTL(String key) {
        try {
            return cassandraRepository.getWithTTL(key);
        } catch (Exception e) {
            LOGGER.warn("Unable to retrieve data from <PERSON>", e);
        }

        return Optional.empty();
    }

    protected final void write(String key, String value, int ttl) {
        try {
            cassandraRepository.set(key, value, ttl);
        } catch (Exception e) {
            LOGGER.warn("Unable to persist data to <PERSON>", e);
        }
    }
}
