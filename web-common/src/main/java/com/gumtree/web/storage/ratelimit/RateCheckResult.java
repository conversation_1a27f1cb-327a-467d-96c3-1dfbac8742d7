package com.gumtree.web.storage.ratelimit;

public class RateCheckResult {
    public static final RateCheckResult EMPTY = new RateCheckResult(0, 0);

    private final int count;
    private final int ttl;

    public RateCheckResult(int count, int ttl) {
        this.count = count;
        this.ttl = ttl;
    }

    public int getCount() {
        return count;
    }

    public int getTtl() {
        return ttl;
    }

    public boolean isRateLimitExceeded() {
        return ttl > 0;
    }

    @Override
    public String toString() {
        return "RateCheckResult{" +
                "count=" + count +
                ", ttl=" + ttl +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RateCheckResult)) return false;

        RateCheckResult that = (RateCheckResult) o;

        if (count != that.count) return false;
        return ttl == that.ttl;
    }

    @Override
    public int hashCode() {
        int result = count;
        result = 31 * result + ttl;
        return result;
    }
}
