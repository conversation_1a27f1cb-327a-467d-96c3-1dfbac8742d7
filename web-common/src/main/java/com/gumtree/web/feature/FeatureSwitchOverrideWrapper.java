package com.gumtree.web.feature;

import com.gumtree.web.cookie.CookieUtils;
import com.gumtree.web.cookie.cutters.feature.FeatureSwitchOverrideCookie;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

public class FeatureSwitchOverrideWrapper implements FeatureSwitchManager {

    private final FeatureSwitchManager baseFeatureSwitchManager;

    private final Optional<FeatureSwitchOverrideCookie> maybeFeatureSwitchOverrideCookie;

    public FeatureSwitchOverrideWrapper(String cookieDomain, FeatureSwitchManager baseFeatureSwitchManager,
                                        HttpServletRequest request) {
        this.baseFeatureSwitchManager = baseFeatureSwitchManager;
        maybeFeatureSwitchOverrideCookie = CookieUtils.findCookie(request.getCookies(), FeatureSwitchOverrideCookie.NAME)
                .map(cookie -> new FeatureSwitchOverrideCookie(cookieDomain, cookie));
    }

    @Override
    public FeatureSwitchConfig.State getFeatureState(FeatureSwitch featureSwitch) {
        return maybeFeatureSwitchOverrideCookie
                .flatMap(cookie -> cookie.getFeatureSwitchValue(featureSwitch))
                .orElseGet(() -> baseFeatureSwitchManager.getFeatureState(featureSwitch));
    }

}
