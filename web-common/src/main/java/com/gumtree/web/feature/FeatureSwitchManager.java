package com.gumtree.web.feature;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public interface FeatureSwitchManager {

    FeatureSwitchConfig.State getFeatureState(FeatureSwitch featureSwitch);

    default boolean isOn(FeatureSwitch featureSwitch) {
        return getFeatureState(featureSwitch) == FeatureSwitchConfig.State.ON;
    }

    default boolean isOff(FeatureSwitch featureSwitch) {
        return getFeatureState(featureSwitch) == FeatureSwitchConfig.State.OFF;
    }

    default List<FeatureSwitch> getAllEnabledFeatures() {
        return Arrays.stream(FeatureSwitchConfig.values())
                .filter(this::isOn)
                .collect(Collectors.toList());
    }

    default String getAllEnabledFeaturesAsString() {
        return getAllEnabledFeatures().stream().map(FeatureSwitch::name).collect(Collectors.joining("|"));
    }

}
