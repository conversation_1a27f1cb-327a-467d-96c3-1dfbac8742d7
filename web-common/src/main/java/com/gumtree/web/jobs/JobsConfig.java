package com.gumtree.web.jobs;


/**
 * This is to include jobs (madgex) specific config into the core model for both buyer & seller
 */
public class JobsConfig {

    private String jobsUrl;
    private String postAdUrl;

    public JobsConfig(String jobsUrl, String postAdUrl) {
        this.jobsUrl = jobsUrl;
        this.postAdUrl = postAdUrl;
    }

    public String getUrl() {
        return jobsUrl;
    }

    public String getPostAdUrl() {
        return postAdUrl;
    }
}
