package com.gumtree.web.common.domain.ad;

import com.google.common.base.Optional;
import com.google.common.base.Strings;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.common.util.PostingSinceUtils;
import com.gumtree.web.common.sapi.AdvertAttributeNames;
import com.gumtree.web.common.domain.converter.Conversions;
import com.gumtree.web.common.domain.location.Centroid;
import com.gumtree.web.common.enums.Feature;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.google.common.base.Optional.of;

public final class Advert implements Serializable {

    private Long id;
    private Long categoryId;
    private String categorySeoName;
    private String categoryDisplayName;
    private Long locationId;
    private String locationName;
    private Optional<Long> sellerId = Optional.absent();
    private String title;
    private String description;
    private String postcode;
    private Centroid centroid;
    private boolean visibleOnMap;
    private String price;
    private String replyEmail;
    private String replyPhone;
    private String replyLink;
    private Date createdDate;
    private Date postedDate;
    private java.util.Optional<Date> expiryDate = java.util.Optional.empty();
    private String area;
    private String subArea;
    private long accountId;
    private Optional<String> createdBy;
    private Optional<String> postingSince;
    private Boolean proAccount = Boolean.FALSE;
    private Optional<String> accountLogoUrl = Optional.absent();
    private Optional<String> accountVat = Optional.absent();
    private Status status;
    private String primaryImageUrl;
    private List<String> additionalImageUrls;
    private String youtubeLink;
    private String websiteUrl;
    private List<Attribute> attributes = Collections.emptyList();
    private Set<String> features = Collections.emptySet();
    private Optional<Double> distance = Optional.absent();
    private int numberOfImages;
    private Date archivedDate;
    private Date deletedDate;
    private boolean nonFeaturedEnforced;
    private String localArea;
    private String contactName;
    private java.util.Optional<SalaryAttribute> salaryAttribute = java.util.Optional.empty();
    private ReplyType replyType;

    public static Builder builder() {
        return new Builder();
    }

    private Advert() {}

    public Long getId() {
        return id;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public Long getLocationId() {
        return locationId;
    }

    public String getTitle() {
        return title;
    }

    public String getDescription() {
        return description;
    }

    public String getPostcode() {
        return postcode;
    }

    public Centroid getCentroid() {
        return centroid;
    }

    public boolean isVisibleOnMap() {
        return visibleOnMap;
    }

    public String getPrice() {
        return price;
    }

    public String getReplyEmail() {
        return replyEmail;
    }

    public ReplyType getReplyType() {
        return replyType;
    }

    public void setReplyType(ReplyType replyType) {
        this.replyType = replyType;
    }

    public String getCategorySeoName() {
        return categorySeoName;
    }

    public String getCategoryDisplayName() {
        return categoryDisplayName;
    }

    public String getReplyPhone() {
        return replyPhone;
    }

    public String getReplyLink() {
        // we can't return optional as it fails when working with ftl's
        return replyLink;
    }

    public Date getPostedDate() {
        // we can't return optional as it fails when working with ftl's
        return postedDate;
    }

    public String getArea() {
        return area;
    }

    public String getSubArea() {
        return subArea;
    }

    public String getPrimaryImageUrl() {
        return primaryImageUrl;
    }

    public List<String> getAdditionalImageUrls() {
        return additionalImageUrls;
    }

    public int getNumberOfImages() {
        return numberOfImages;
    }

    public java.util.Optional<SalaryAttribute> getSalaryAttribute() {
        return salaryAttribute;
    }

    public List<Attribute> getAttributes() {
        return attributes;
    }

    public Boolean getProAccount() {
        return proAccount;
    }

    public Optional<String> getAccountLogoUrl() {
        return accountLogoUrl;
    }

    public Optional<String> getAccountVat() {
        return accountVat;
    }

    public Status getStatus() {
        return status;
    }

    public Set<String> getFeatures() {
        return features;
    }

    public Optional<Long> getSellerId() {
        return sellerId;
    }

    public Date getCreatedDate() {
        // we can't return optional as it fails when working with ftl's
        return createdDate;
    }

    public Date getArchivedDate() {
        // we can't return optional as it fails when working with ftl's
        return archivedDate;
    }

    public java.util.Optional<Date> getExpiryDate() {
        return expiryDate;
    }

    public String getYoutubeLink() {
        return youtubeLink;
    }

    public boolean hasYoutubeVideo() {
        return !Strings.isNullOrEmpty(youtubeLink);
    }

    public long getAccountId() {
        return accountId;
    }

    public Optional<String> getCreatedBy() {
        return createdBy;
    }

    public Optional<String> getPostingSince() {
        return postingSince;
    }

    public String getWebsiteUrl() {
        return websiteUrl;
    }

    public boolean isFeatured() {
        return !nonFeaturedEnforced
                && (features != null
                && (features.contains(Feature.FEATURE_3_DAY.getId())
                || features.contains(Feature.FEATURE_7_DAY.getId())
                || features.contains(Feature.FEATURE_14_DAY.getId()))
        );
    }

    public boolean isStandout() {
        return features.contains(Feature.SEARCH_STANDOUT.getId());

    }

    public Optional<Double> getDistance() {
        return distance;
    }

    public String getLocalArea() {
        return localArea;
    }

    public String getContactName(){
        return contactName;
    }

    public Date getDeletedDate() {
        return deletedDate;
    }

    public String getLocationName() {
        return locationName;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        final Advert other = (Advert) obj;
        return Objects.equals(this.id, other.id);
    }

    public static final class Builder {

        private Advert advert;

        private Builder() {
            advert = new Advert();
        }

        private Builder(Advert advert) {
            this.advert = advert;
        }

        public static Builder advert() {
            return new Builder();
        }

        public static Builder advert(Advert advert) {
            return new Builder(advert);
        }

        public Builder withId(Long id) {
            advert.id = id;
            return this;
        }

        public Builder withCategoryId(Long categoryId) {
            advert.categoryId = categoryId;
            return this;
        }

        public Builder withCategorySeoName(String categorySeoName) {
            advert.categorySeoName = categorySeoName;
            return this;
        }

        public Builder withCategoryDisplayName(String name) {
            advert.categoryDisplayName = name;
            return this;
        }

        public Builder withLocationId(Long locationId) {
            advert.locationId = locationId;
            return this;
        }

        public Builder withLocationName(String locationName) {
            advert.locationName = locationName;
            return this;
        }

        public Builder withTitle(String title) {
            advert.title = title;
            return this;
        }

        public Builder withDescription(String description) {
            advert.description = description;
            return this;
        }

        public Builder withPostcode(String postcode) {
            advert.postcode = postcode;
            return this;
        }

        public Builder withReplyPhone(String replyPhone) {
            advert.replyPhone = replyPhone;
            return this;
        }

        public Builder withReplyEmail(String replyEmail) {
            advert.replyEmail = replyEmail;
            return this;
        }

        public Builder withReplyLink(Optional<String> replyLink) {
            if (replyLink.isPresent()) {
                advert.replyLink = replyLink.get();
            }
            return this;
        }

        public Builder withCreatedDate(Optional<Date> createdDate) {
            if (createdDate.isPresent()) {
                advert.createdDate = createdDate.get();
            }
            return this;
        }

        public Builder withPostedDate(Optional<Date> postedDate) {
            if (postedDate.isPresent()) {
                advert.postedDate = postedDate.get();
            }
            return this;
        }

        public Builder withExpiryDate(java.util.Optional<Date> date) {
            advert.expiryDate = date;
            return this;
        }

        public Builder withArea(String area) {
            advert.area = area;
            return this;
        }

        public Builder withSubArea(String subArea) {
            advert.subArea = subArea;
            return this;
        }

        public Builder withCentroid(Centroid centroid) {
            advert.centroid = centroid;
            return this;
        }

        public Builder withVisibleOnMap(boolean visibleOnMap) {
            advert.visibleOnMap = visibleOnMap;
            return this;
        }

        public Builder withAccountId(long accountId) {
            advert.accountId = accountId;
            return this;
        }

        public Builder withProAccount(Boolean proAccount) {
            advert.proAccount = proAccount == null ? false : proAccount;
            return this;
        }

        public Builder withAccountLogoUrl(Optional<String> logoUrl) {
            advert.accountLogoUrl = logoUrl;
            return this;
        }

        public Builder withAccountVat(Optional<String> vatNumber) {
            advert.accountVat = vatNumber;
            return this;
        }

        public Builder withStatus(Status status) {
            advert.status = status;
            return this;
        }

        public Builder withPrimaryImageUrl(String primaryImageUrl) {
            advert.primaryImageUrl = primaryImageUrl;
            return this;
        }

        public Builder withAdditionalImageUrls(List<String> additionalImageUrls) {
            advert.additionalImageUrls = additionalImageUrls;
            return this;
        }

        public Builder withYoutubeLink(String youtubeLink) {
            if (youtubeLink != null) {
                advert.youtubeLink = Conversions.buildYoutubeLink(youtubeLink);
            }
            return this;
        }

        public Builder withPrice(String price) {
            advert.price = price;
            return this;
        }

        public Builder withSalaryAttribute(java.util.Optional<SalaryAttribute> salaryAttribute) {
            if (salaryAttribute != null) {
                advert.salaryAttribute = salaryAttribute;
            }
            return this;
        }

        public Builder withAttributes(List<Attribute> attributes) {
            advert.attributes = attributes;
            return this;
        }

        public Builder withFeatures(Set<String> features) {
            advert.features = features;
            return this;
        }

        public Builder withCreatedBy(Optional<String> value) {
            advert.createdBy = value;
            return this;
        }

        public Builder withPostingSince(Optional<Long> value) {
            if (value.isPresent()) {
                advert.postingSince = of(PostingSinceUtils.getPostingSinceLabel(new Date(value.get())));
            } else {
                advert.postingSince = Optional.absent();
            }
            return this;
        }

        public Builder withWebsiteUrl(String value) {
            advert.websiteUrl = value;
            return this;
        }

        public Builder withSellerId(Optional<Long> id) {
            advert.sellerId = id;
            return this;
        }

        public final Builder withDistance(Double distance) {
            advert.distance = Optional.fromNullable(distance);
            return this;
        }

        public final Builder removeFeaturedFlag(Boolean value) {
            advert.nonFeaturedEnforced = value;
            return this;
        }

        public final Builder removeFeaturedFlag() {
            advert.nonFeaturedEnforced = true;
            return this;
        }

        public Builder withArchivedDate(Optional<Date> archivedDate) {
            if (archivedDate.isPresent()) {
                advert.archivedDate = archivedDate.get();
            }
            return this;
        }

        public Builder withDeletedDate(Optional<Date> date) {
            if (date.isPresent()) {
                advert.deletedDate = date.get();
            }
            return this;
        }

        public Builder withLocalArea(String value) {
            if (value != null && !value.isEmpty()) {
                advert.localArea = value;
            }
            return this;
        }

        public Builder withContactName(String value) {
            if(value != null && !value.trim().isEmpty()){
                advert.contactName = value;
            }
            return this;
        }

        public Builder withReplyType(ReplyType replyType) {
            advert.replyType = replyType;
            return this;
        }

        public Advert build() {
            buildPrice();

            // calculate number of images
            advert.numberOfImages = Strings.isNullOrEmpty(advert.primaryImageUrl) ? 0 : 1;
            advert.numberOfImages += advert.additionalImageUrls != null ? advert.additionalImageUrls.size() : 0;

            return advert;
        }

        /**
         * extract price and set in own field with pw and pm :)
         */
        private void buildPrice() {
            String condition = "";
            String price = "";
            for (Attribute attribute : advert.getAttributes()) {
                if ("price".equals(attribute.getKey())) {
                    price = attribute.getValue();
                }

                if (AdvertAttributeNames.PRICE_FREQUENCY.getName().equals(attribute.getKey())) {
                    condition = attribute.getValue();
                }
            }
            advert.price = price + condition;
        }
    }

    public static class Attribute implements Serializable {
        private final AttributeMetadata metadata;
        private final String key;
        private final String label;
        private final String value;
        private final String rawValue;

        public Attribute(@Nonnull String key,
                         @Nonnull String label,
                         @Nonnull String value,
                         @Nonnull String rawValue,
                         @Nonnull AttributeMetadata metadata) {
            Assert.notNull(metadata);
            Assert.notNull(label);
            Assert.notNull(value);
            Assert.notNull(rawValue);

            this.key = key;
            this.label = label;
            this.value = value;
            this.metadata = metadata;
            this.rawValue = rawValue;
        }

        public String getKey() {
            return key;
        }

        public String getLabel() {
            return label;
        }

        public String getValue() {
            return value;
        }

        public AttributeMetadata getMetadata() {
            return metadata;
        }

        public String getRawValue() {
            return rawValue;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Attribute attribute = (Attribute) o;
            return Objects.equals(metadata, attribute.metadata) &&
                    Objects.equals(key, attribute.key) &&
                    Objects.equals(label, attribute.label) &&
                    Objects.equals(value, attribute.value) &&
                    Objects.equals(rawValue, attribute.rawValue);
        }

        @Override
        public int hashCode() {
            return Objects.hash(metadata, key, label, value, rawValue);
        }

        @Override
        public String toString() {
            return "Attribute{" +
                    "metadata=" + metadata +
                    ", key='" + key + '\'' +
                    ", label='" + label + '\'' +
                    ", value='" + value + '\'' +
                    ", rawValue='" + rawValue + '\'' +
                    '}';
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder implements Conversions.AttributeBuilder<Attribute> {
            private AttributeMetadata metadata;
            private String label;
            private String value;
            private String rawValue;

            @Override
            public Attribute build() {
                return new Attribute(metadata.getName(), label, value, rawValue, metadata);
            }

            @Override
            public Builder setValue(String value) {
                this.rawValue = value;
                return this;
            }

            @Override
            public Builder setValueDisplayName(String value) {
                this.value = value;
                return this;
            }

            @Override
            public Builder setAttributeMetadata(AttributeMetadata metadata) {
                this.metadata = metadata;
                this.label = metadata.getLabel();
                return this;
            }
        }
    }

    public enum Status {
        AWAITING_CS_REVIEW,
        DELETED_CS,
        DELETED_USER,
        EXPIRED,
        LIVE,
        DRAFT,
        NEEDS_EDITING,
        PRE_PUBLISHED,
        UNKNOWN
    }
}
