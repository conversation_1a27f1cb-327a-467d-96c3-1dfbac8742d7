package com.gumtree.web.common.util;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.CategoryConstants;

import java.util.Set;

public class CategoryHierarchyChecker {
    private final Set<Long> categories;
    private final CategoryModel categoryModel;

    public CategoryHierarchyChecker(CategoryModel categoryModel, Set<Long> categories) {
        this.categories = categories;
        this.categoryModel = categoryModel;
    }

    // Contrary to the original code (GTALL-5111), for now it returns true if the ad category is whitelisted,
    // false otherwise as requested in (GTALL-8659, GTALL-8644, GTALL-8656 and GTALL-8655)
    public boolean isAdvertCategoryWhiteListed(Long categoryId) {
        if (CategoryConstants.ALL_ID.equals(categoryId)) {
            return false;
        } else if (categories.contains(categoryId)) {
            return true;
        } else if (categoryModel.getParentOf(categoryId).isPresent()) {
            return isAdvertCategoryWhiteListed(categoryModel.getParentOf(categoryId).get().getId());
        } else {
            return false;
        }
    }
}
