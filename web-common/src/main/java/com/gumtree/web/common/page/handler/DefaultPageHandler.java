package com.gumtree.web.common.page.handler;

import com.gumtree.web.common.page.GumtreePage;

import java.lang.annotation.Annotation;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Default implementation of {@link PageHandler}. This implementation looks at the passed in method and
 * class annotations. Where the class annotations contain an annotation, it will be
 * overridden by the definition of an annotation of the same type in the method annotations. Crucially, the
 * constructor throws a {@link MissingGumtreePageAnnotationException} if there is no {@link GumtreePage}
 * annotation defined in either the class or method annotations.
 */
public final class DefaultPageHandler implements PageHandler {

    private GumtreePage pageAnnotation;

    private Map<Class<? extends Annotation>, Annotation> additionalAnnotations =
            new HashMap<Class<? extends Annotation>, Annotation>();

    /**
     * Constructor.
     *
     * @param methodAnnotations method annotations from a handler method.
     * @param classAnnotations  class annotations from handler class
     * @throws MissingGumtreePageAnnotationException
     *          if no page annotation is found
     */
    public DefaultPageHandler(
            Collection<Annotation> methodAnnotations,
            Collection<Annotation> classAnnotations) throws MissingGumtreePageAnnotationException {

        processAnnotations(methodAnnotations);
        processAnnotations(classAnnotations);

        if (pageAnnotation == null) {
            throw new MissingGumtreePageAnnotationException();
        }
    }

    @Override
    public GumtreePage getPageAnnotation() {
        return pageAnnotation;
    }

    @Override
    public Collection<Annotation> getAdditionalAnnotations() {
        return additionalAnnotations.values();
    }

    private void processAnnotations(Collection<Annotation> annotations) {
        if (annotations != null) {
            for (Annotation annotation : annotations) {
                if (annotation.annotationType().equals(GumtreePage.class)) {
                    pageAnnotation = pageAnnotation == null ? (GumtreePage) annotation : pageAnnotation;
                } else {
                    if (!additionalAnnotations.containsKey(annotation.annotationType())) {
                        additionalAnnotations.put(annotation.annotationType(), annotation);
                    }
                }
            }
        }
    }
}
