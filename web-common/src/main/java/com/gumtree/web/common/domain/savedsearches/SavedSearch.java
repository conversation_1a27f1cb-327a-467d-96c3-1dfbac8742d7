package com.gumtree.web.common.domain.savedsearches;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * Implementation of a Saved Search
 */
public class SavedSearch {

    private String id;
    private String url;
    private String categoryName;
    private String locationName;
    private String searchTerms;

    /**
     * Constructor
     * @param url URL of the saved search
     */
    public SavedSearch(String url) {
        this.url = url;
        try {
            String tmpId = URLEncoder.encode(url, "UTF-8");
            this.id = tmpId.replaceAll("[^a-zA-Z0-9]", "");
        } catch (UnsupportedEncodingException e) {
            this.id = url.replaceAll("[^a-zA-Z0-9]", "");
        }
    }

    /**
     * Constructor
     */
    public SavedSearch() {
    }

    /**
     *
     * @return id the ID of the saved search
     */
    public String getId() {
        return this.id;
    }

    /**
     *
     * @return url the URL of the saved search
     */
    public String getUrl() {
        return this.url;
    }

    /**
     *
     * @return categoryName the category tree name of the saved search
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     *
     * @return locationName the location name of the saved search
     */
    public String getLocationName() {
        return locationName;
    }

    /**
     *
     * @return searchTerms the search terms of the saved search
     */
    public String getSearchTerms() {
        return searchTerms;
    }

    /**
     *
     * @param categoryName the category tree name
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     *
     * @param locationName the location name
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     *
     * @param url the URL of the search
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     *
     * @param searchTerms the keyword terms of the search
     */
    public void setSearchTerms(String searchTerms) {
        this.searchTerms = searchTerms;
    }

    /**
     *
     * @param id the identifier of the saved search
     */
    public void setId(String id) {
        this.id = id;
    }
}
