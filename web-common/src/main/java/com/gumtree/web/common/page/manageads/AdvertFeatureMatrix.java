package com.gumtree.web.common.page.manageads;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.gumtree.api.Ad;
import com.gumtree.api.AdFeature;
import com.gumtree.api.domain.order.CreateOrderBean;
import com.gumtree.api.domain.order.CreateOrderItemBean;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.web.common.format.PostingTimeFormatter;
import org.joda.time.DateTime;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Matrix of adverts along with the features they contain on the manage ads matrix
 */
public class AdvertFeatureMatrix implements Serializable {

    /**
     * Enumeration for feature types.
     */
    enum SelectorFeatureGroup {
        FEATURE,
        URGENT,
        HOMEPAGE_SPOTLIGHT,
        OTHER;

        public static SelectorFeatureGroup fromProductName(ProductName productName) {
            switch (productName) {
                case FEATURE_3_DAY:
                case FEATURE_7_DAY:
                case FEATURE_14_DAY:
                    return FEATURE;
                case URGENT:
                    return URGENT;
                case HOMEPAGE_SPOTLIGHT:
                    return HOMEPAGE_SPOTLIGHT;
                default:
                    return OTHER;
            }
        }
    }

    private Map<String, Map<String, Boolean>> matrix = Maps.newHashMap();

    private Map<String, Map<String, String>> expires = Maps.newHashMap();

    private Map<SelectorFeatureGroup, Integer> featureGroupsEnabled = Maps.newHashMap();

    public final Map<String, Map<String, Boolean>> getMatrix() {
        return matrix;
    }

    public final Map<String, Map<String, String>> getExpires() {
        return expires;
    }

    public final void setMatrix(Map<String, Map<String, Boolean>> matrix) {
        this.matrix = matrix;
    }

    /**
     * Create a new order request to send to the api based on the known matrix and features already present on adverts
     *
     * @param accountId the account ID for the order
     * @param adverts   adverts to generate order for
     * @return an order that can be passed to the API
     */
    public final CreateOrderBean createOrderBean(Long accountId, List<Ad> adverts) {
        CreateOrderBean createOrderBean = new CreateOrderBean();
        createOrderBean.setAccountId(accountId);
        createOrderBean.setItems(Lists.<CreateOrderItemBean>newArrayList());

        for (Ad advert : adverts) {
            String advertId = "" + advert.getId();
            if (!matrix.containsKey(advertId)) {
                continue;
            }
            Map<String, Boolean> entry = matrix.get(advertId);

            for (ProductName productName : ProductName.values()) {
                if (!advertHasProduct(advert, productName)) {
                    if (entry.containsKey(productName.toString())) {
                        if (entry.get(productName.toString())) {
                            CreateOrderItemBean createOrderItemBean = new CreateOrderItemBean();
                            createOrderItemBean.setAdvertId(advert.getId());
                            createOrderItemBean.setProductName(productName);
                            createOrderBean.getItems().add(createOrderItemBean);
                        }
                    }
                }
            }
        }

        return createOrderBean;
    }

    /**
     * Add an advert to the matrix
     *
     * @param advert the advert
     */
    public final void add(Ad advert) {
        matrix.put("" + advert.getId(), createAdvertFeatureEntry(advert));
        expires.put("" + advert.getId(), createAdvertFeatureExpiryEntry(advert));
    }

    /**
     * Add adverts to the matrix
     *
     * @param adverts the adverts
     */
    public final void add(Iterable<Ad> adverts) {
        for (Ad advert : adverts) {
            add(advert);
        }
    }

    private Map<String, Boolean> createAdvertFeatureEntry(Ad advert) {
        Map<String, Boolean> entry = Maps.newHashMap();
        for (ProductName productName : ProductName.values()) {
            entry.put(productName.toString(), false);
        }
        if (advert.getFeatures() != null) {
            for (AdFeature advertFeature : advert.getFeatures()) {
                entry.put(advertFeature.getProductName().toString(), true);
                incrementProductNameCount(advertFeature.getProductName());
            }
        }
        return entry;
    }

    private Map<String, String> createAdvertFeatureExpiryEntry(Ad advert) {
        Map<String, String> entry = new HashMap<>();
        if (advert.getFeatures() != null) {
            DateTime now = new DateTime();
            for (AdFeature adFeature : advert.getFeatures()) {
                entry.put(
                        adFeature.getProductName().toString(),
                        PostingTimeFormatter.formatFeatureExpiryTime(adFeature.getEndDate(), now)
                );
            }
        }
        return entry;
    }

    private boolean advertHasProduct(Ad advert, ProductName productName) {
        for (AdFeature adFeature : advert.getFeatures()) {
            if (adFeature.getProductName().equals(productName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if a feature group is entirely enabled.
     *
     * @param selectorFeatureGroup the feature group
     * @return true if all enabled
     */
    public final boolean isFeatureGroupAllEnabled(SelectorFeatureGroup selectorFeatureGroup) {
        return !featureGroupsEnabled.containsKey(selectorFeatureGroup)
                || featureGroupsEnabled.get(selectorFeatureGroup) < matrix.size();
    }

    public final boolean isFeatureAllEnabled() {
        return isFeatureGroupAllEnabled(SelectorFeatureGroup.FEATURE);
    }

    public final boolean isUrgentAllEnabled() {
        return isFeatureGroupAllEnabled(SelectorFeatureGroup.URGENT);
    }

    public final boolean isSpotlightAllEnabled() {
        return isFeatureGroupAllEnabled(SelectorFeatureGroup.HOMEPAGE_SPOTLIGHT);
    }

    public final boolean isBumpUpAllEnabled() {
        return true;
    }

    private void incrementProductNameCount(ProductName productName) {
        SelectorFeatureGroup selectorFeatureGroup = SelectorFeatureGroup.fromProductName(productName);

        if (!featureGroupsEnabled.containsKey(selectorFeatureGroup)) {
            featureGroupsEnabled.put(selectorFeatureGroup, 0);
        }
        featureGroupsEnabled.put(selectorFeatureGroup, featureGroupsEnabled.get(selectorFeatureGroup) + 1);
    }
}
