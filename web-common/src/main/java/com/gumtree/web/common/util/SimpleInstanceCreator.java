package com.gumtree.web.common.util;

import org.springframework.stereotype.Component;

/**
 * Simple implementation of {@link InstanceCreator}
 */
@Component
public final class SimpleInstanceCreator implements InstanceCreator {

    @Override
    public <T> T createInstance(Class<T> clazz) {
        try {
            return clazz.newInstance();
        } catch (Exception ex) {
            throw new IllegalArgumentException(ex.getMessage());
        }
    }
}
