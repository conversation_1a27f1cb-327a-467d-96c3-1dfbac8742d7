package com.gumtree.web.common.domain.user;

import com.gumtree.api.ApiContactEmail;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.List;

public final class User {
    private final Long id;
    private final String email;
    private final String firstName;
    private final String lastName;
    private final String phoneNumber;
    private final boolean userLoggedIn;
    private final List<ApiContactEmail> contactEmails;
    private final String csrfToken;
    private final Long creationDateMillis;

    private User(Builder builder) {
        this.id = builder.id;
        this.email = builder.email;
        this.firstName = builder.firstName;
        this.userLoggedIn = builder.userLoggedIn;
        this.contactEmails = builder.contactEmails;
        this.lastName = builder.lastName;
        this.phoneNumber = builder.phoneNumber;
        this.csrfToken = builder.csrfToken;
        this.creationDateMillis = builder.creationDateMillis;
    }

    public Long getId() {
        return id;
    }

    public String getEmail() {
        return email;
    }

    public String getFirstName() {
        return firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public boolean isUserLoggedIn() {
        return userLoggedIn;
    }

    public List<ApiContactEmail> getContactEmails() {
        return contactEmails;
    }

    public String getCsrfToken() {
        return csrfToken;
    }

    public Long getCreationDateMillis() {
        return creationDateMillis;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        User user = (User) o;

        if (userLoggedIn != user.userLoggedIn) return false;
        if (id != null ? !id.equals(user.id) : user.id != null) return false;
        if (email != null ? !email.equals(user.email) : user.email != null) return false;
        if (firstName != null ? !firstName.equals(user.firstName) : user.firstName != null) return false;
        if (lastName != null ? !lastName.equals(user.lastName) : user.lastName != null) return false;
        if (phoneNumber != null ? !phoneNumber.equals(user.phoneNumber) : user.phoneNumber != null) return false;
        if (csrfToken != null ? !csrfToken.equals(user.csrfToken) : user.csrfToken != null) return false;
        if (creationDateMillis != null ? !creationDateMillis.equals(user.creationDateMillis) : user.creationDateMillis != null) return false;
        return contactEmails != null ? contactEmails.equals(user.contactEmails) : user.contactEmails == null;
    }

    @Override
    public int hashCode() {
        int result = email != null ? email.hashCode() : 0;
        result = 31 * result + (id != null ? id.hashCode() : 0);
        result = 31 * result + (firstName != null ? firstName.hashCode() : 0);
        result = 31 * result + (lastName != null ? lastName.hashCode() : 0);
        result = 31 * result + (phoneNumber != null ? phoneNumber.hashCode() : 0);
        result = 31 * result + (csrfToken != null ? csrfToken.hashCode() : 0);
        result = 31 * result + (creationDateMillis != null ? creationDateMillis.hashCode() : 0);
        result = 31 * result + (userLoggedIn ? 1 : 0);
        result = 31 * result + (contactEmails != null ? contactEmails.hashCode() : 0);
        return result;
    }

    public static class Builder {
        private Long id;
        private String email;
        private String firstName;
        private String lastName;
        private String phoneNumber;
        private boolean userLoggedIn;
        private List<ApiContactEmail> contactEmails;
        private String csrfToken;
        private Long creationDateMillis;

        public Builder withId(Long value) {
            this.id = value;
            return this;
        }

        public Builder withEmail(String value) {
            this.email = value;
            return this;
        }

        public Builder withFirstName(String value) {
            this.firstName = value;
            return this;
        }

        public Builder withLastName(String value) {
            this.lastName = value;
            return this;
        }

        public Builder withUserLoggedIn(boolean userLoggedIn) {
            this.userLoggedIn = userLoggedIn;
            return this;
        }

        public Builder withContactEmails(List<ApiContactEmail> contactEmails) {
            this.contactEmails = contactEmails;
            return this;
        }

        public Builder withPhoneNumber(String value) {
            this.phoneNumber = value;
            return this;
        }

        public Builder withCsrfToken(String value) {
            this.csrfToken = value;
            return this;
        }

        public Builder withCreationDateMillis(Long value) {
            this.creationDateMillis = value;
            return this;
        }

        public User build() {
            return new User(this);
        }

    }
}
