package com.gumtree.web.common.device;

import org.springframework.mobile.device.Device;

public class DefaultDevice implements Device {

    private Device wrappedDevice;
    private DeviceOS os;

    public DefaultDevice(Device wrappedDevice, DeviceOS os) {
        this.wrappedDevice = wrappedDevice;
        this.os = os;
    }

    @Override
    public boolean isNormal() {
        return wrappedDevice.isNormal();
    }

    @Override
    public boolean isMobile() {
        return wrappedDevice.isMobile();
    }

    @Override
    public boolean isTablet() {
        return wrappedDevice.isTablet();
    }

    public String deviceName() {
        if (wrappedDevice.isMobile()) {
            return "mobile";
        }
        if (wrappedDevice.isNormal()) {
            return "computer";
        }
        if (wrappedDevice.isTablet()) {
            return "tablet";
        }
        return "unknown";
    }

    public DeviceOS getOs() {
        return os;
    }

    @Override
    public String toString() {
        return "DefaultDevice{"
                + "wrappedDevice=" + wrappedDevice
                + ", os=" + os
                + '}';
    }
}
