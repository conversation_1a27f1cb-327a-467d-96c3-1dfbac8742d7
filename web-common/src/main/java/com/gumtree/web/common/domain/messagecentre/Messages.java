package com.gumtree.web.common.domain.messagecentre;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;
import java.util.Optional;

public final class Messages {

    private final Integer numUnreadConversations;
    private final String converseeName;
    private final Optional<Long> converseeId;
    private final String conversationId;
    private final List<Message> messages;
    private final Role userRole;
    private final Long userId;
    private final Optional<Advert> advert;

    public static Builder builder() {
        return new Builder();
    }

    private Messages(Builder builder) {
        this.numUnreadConversations = builder.numUnreadConversations;
        this.converseeName = builder.converseeName;
        this.converseeId = builder.converseeId;
        this.conversationId = builder.conversationId;
        this.messages = builder.messages;
        this.userRole = builder.userRole;
        this.userId = builder.userId;
        this.advert = builder.advert;
    }

    public Integer getNumUnreadConversations() {
        return numUnreadConversations;
    }

    public String getConverseeName() {
        return converseeName;
    }

    public String getConversationId() {
        return conversationId;
    }

    public List<Message> getMessages() {
        return messages;
    }

    public Role getUserRole() {
        return userRole;
    }

    public Optional<Advert> getAdvert() {
        return advert;
    }

    @JsonIgnore // TODO fully migrate to fasterxml version(different package in web and seller)
    @org.codehaus.jackson.annotate.JsonIgnore
    public Optional<Long> getConverseeId() {
        return converseeId;
    }

    @JsonIgnore
    @org.codehaus.jackson.annotate.JsonIgnore
    public Long getUserId() {
        return userId;
    }

    public static class Builder {
        private String conversationId;
        private Integer numUnreadConversations;
        private String converseeName;
        private Optional<Long> converseeId;
        private List<Message> messages;
        private Role userRole;
        private Long userId;
        private Optional<Advert> advert;

        public Builder setNumUnreadConversations(Integer numUnreadConversations) {
            this.numUnreadConversations = numUnreadConversations;
            return this;
        }

        public Builder setConverseeName(String converseeName) {
            this.converseeName = converseeName;
            return this;
        }

        public Builder setConverseeId(Optional<Long> value) {
            this.converseeId = value;
            return this;
        }

        public Builder setConversationId(String conversationId) {
            this.conversationId = conversationId;
            return this;
        }

        public Builder setMessages(List<Message> messages) {
            this.messages = messages;
            return this;
        }

        public Builder setUserRole(Role userRole){
            this.userRole = userRole;
            return this;
        }

        public Builder setUserId(Long value) {
            this.userId = value;
            return this;
        }

        public Builder setAdvert(Optional<Advert> advert) {
            this.advert = advert;
            return this;
        }

        public Messages build() {
            return new Messages(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Messages)) return false;

        Messages messages1 = (Messages) o;

        if (numUnreadConversations != null
                ? !numUnreadConversations.equals(messages1.numUnreadConversations) : messages1.numUnreadConversations != null) {
            return false;
        }
        if (converseeName != null ? !converseeName.equals(messages1.converseeName) : messages1.converseeName != null) return false;
        if (converseeId != null ? !converseeId.equals(messages1.converseeId) : messages1.converseeId != null) return false;
        if (conversationId != null ? !conversationId.equals(messages1.conversationId) : messages1.conversationId != null) return false;
        if (messages != null ? !messages.equals(messages1.messages) : messages1.messages != null) return false;
        if (userRole != messages1.userRole) return false;
        if (userId != null ? !userId.equals(messages1.userId) : messages1.userId != null) return false;
        return advert != null ? advert.equals(messages1.advert) : messages1.advert == null;
    }

    @Override
    public int hashCode() {
        int result = numUnreadConversations != null ? numUnreadConversations.hashCode() : 0;
        result = 31 * result + (converseeName != null ? converseeName.hashCode() : 0);
        result = 31 * result + (converseeId != null ? converseeId.hashCode() : 0);
        result = 31 * result + (conversationId != null ? conversationId.hashCode() : 0);
        result = 31 * result + (messages != null ? messages.hashCode() : 0);
        result = 31 * result + (userRole != null ? userRole.hashCode() : 0);
        result = 31 * result + (userId != null ? userId.hashCode() : 0);
        result = 31 * result + (advert != null ? advert.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "Messages{" +
                "numUnreadConversations=" + numUnreadConversations +
                ", converseeName='" + converseeName + '\'' +
                ", converseeId=" + converseeId +
                ", conversationId='" + conversationId + '\'' +
                ", messages=" + messages +
                ", userRole=" + userRole +
                ", userId=" + userId +
                ", advert=" + advert +
                '}';
    }
}
