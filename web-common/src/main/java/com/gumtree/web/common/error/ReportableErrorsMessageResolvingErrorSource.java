package com.gumtree.web.common.error;

import com.google.common.collect.Lists;
import com.gumtree.common.util.error.ErrorReporter;
import com.gumtree.common.util.error.ReportableErrors;
import com.gumtree.common.util.error.ReportableErrorsArguments;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Implementation of {@link MessageResolvingErrorSource} that extracts errors from a
 * {@link com.gumtree.common.util.error.ReportableErrors}
 * and resolves messages via a {@link ErrorMessageResolver}.
 */
public final class ReportableErrorsMessageResolvingErrorSource implements MessageResolvingErrorSource, ErrorReporter {

    private ErrorMessageResolver messageResolver;

    private List<String> globalErrorMessages = new ArrayList<>();

    private MultiValueMap<String, String> fieldErrorMessages = new LinkedMultiValueMap<>();

    /**
     * Constructor.
     *
     * @param errors          the underlying source of errors
     * @param messageResolver message resolver for resolving message codes
     */
    public ReportableErrorsMessageResolvingErrorSource(
            ReportableErrors errors,
            ErrorMessageResolver messageResolver) {

        this.messageResolver = messageResolver;
        errors.report(this);
    }

    /**
     * Constructor.
     *
     * @param errors          the underlying source of errors
     * @param messageResolver message resolver for resolving message codes
     * @param args            a list of arguments to be added to error
     */
    public ReportableErrorsMessageResolvingErrorSource(
            ReportableErrors errors,
            ErrorMessageResolver messageResolver, ReportableErrorsArguments args) {

        this.messageResolver = messageResolver;
        errors.report(this, args);
    }

    /**
     * @param errors                 source
     * @param messageResolver        resolving messages
     * @param globalErrorMessageCode - Global error
     */
    public ReportableErrorsMessageResolvingErrorSource(
            ReportableErrors errors,
            ErrorMessageResolver messageResolver,
            String globalErrorMessageCode) {

        this(errors, messageResolver);
        globalError(globalErrorMessageCode);
    }

    @Override
    public void fieldError(String field, String messageCode) {
        fieldErrorMessages.add(field, messageResolver.getMessage(messageCode, null, null));
    }

    @Override
    public void fieldError(String field, String messageCode, String defaultMessage, Object... args) {
        fieldErrorMessages.add(field, messageResolver.getMessage(messageCode, defaultMessage, args));
    }

    @Override
    public void fieldError(String field, String messageCode, Object... args) {
        fieldErrorMessages.add(field, messageResolver.getMessage(messageCode, messageCode, args));
    }

    @Override
    public void globalError(String messageCode) {
        globalErrorMessages.add(messageResolver.getMessage(messageCode, null, null));
    }

    @Override
    public void globalError(String messageCode, String defaultMessage, Object... args) {
        globalErrorMessages.add(messageResolver.getMessage(messageCode, defaultMessage, args));
    }

    @Override
    public List<String> getResolvedFieldErrorMessages(String field) {
        List<String> value = fieldErrorMessages.get(field);
        return value != null ? Collections.unmodifiableList(value) : Lists.<String>newArrayList();
    }

    @Override
    public List<String> getResolvedGlobalErrorMessages() {
        return Collections.unmodifiableList(globalErrorMessages);
    }

    @Override
    public boolean containsGlobalErrors() {
        return globalErrorMessages.size() > 0;
    }

    @Override
    public boolean containsFieldError(String field) {
        return fieldErrorMessages.containsKey(field);
    }

    @Override
    public Map<String, List<String>> getAllResolvedFieldErrorMessages() {
        return Collections.unmodifiableMap(fieldErrorMessages);
    }
}
