package com.gumtree.web.common.domain.ad;

import java.util.Objects;

public class SitemapAd {
    private final Long id;
    private final String title;
    private final Long categoryId;

    public SitemapAd(Long id, String title, Long categoryId) {
        this.id = id;
        this.title = title;
        this.categoryId = categoryId;
    }

    public Long getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    @Override
    public String toString() {
        return "SitemapAd{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", categoryId=" + categoryId +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SitemapAd sitemapAd = (SitemapAd) o;
        return Objects.equals(id, sitemapAd.id) &&
                Objects.equals(title, sitemapAd.title) &&
                Objects.equals(categoryId, sitemapAd.categoryId);
    }

    @Override
    public int hashCode() {

        return Objects.hash(id, title, categoryId);
    }
}
