package com.gumtree.web.common.error.json;

import com.github.rjeschke.txtmark.Processor;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents the model for JSON validation messages.
 */
public final class JsonValidation {

    private List<String> globalMessages;

    private List<JsonComponentError> components;

    /**
     * Constructor.
     *
     * @param globalMessages the global messages
     * @param components     the component errors
     */
    public JsonValidation(List<String> globalMessages, List<JsonComponentError> components) {
        this.globalMessages = globalMessages;
        this.components = components;
    }

    public List<String> getGlobalMessages() {
        return globalMessages;
    }

    public List<JsonComponentError> getComponents() {
        return components;
    }

    /**
     * For building instances of {@link JsonValidation}.
     */
    public static final class Builder {

        private List<JsonComponentError> componentErrors = new ArrayList<JsonComponentError>();

        private List<String> globalMessages = new ArrayList<String>();

        /**
         * @param message the global message
         * @return Builder with a message
         */
        public Builder withGlobalMessage(String message) {
            globalMessages.add(Processor.process(message).trim());
            return this;
        }

        /**
         * @param componentError the component error
         * @return updated builder
         */
        public Builder withComponentError(JsonComponentError.Builder componentError) {
            componentErrors.add(componentError.build());
            return this;
        }

        /**
         * @return JsonValidation
         */
        public JsonValidation build() {
            return new JsonValidation(globalMessages, componentErrors);
        }
    }

    /**
     * @return a {@link JsonValidation} builder.
     */
    public static Builder aValidationModel() {
        return new Builder();
    }
}
