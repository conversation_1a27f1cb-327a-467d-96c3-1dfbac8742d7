package com.gumtree.web.common.error.mvc;

import com.gumtree.common.util.error.Error;
import com.gumtree.common.util.error.SimpleError;
import org.springframework.validation.Errors;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;

import java.util.ArrayList;

/**
 * A list of errors from an MVC {@link Errors} model.
 */
public final class MvcErrorList extends ArrayList<Error> {

    /**
     * Constructor.
     *
     * @param errors the underlying source of errors
     */
    public MvcErrorList(Errors errors) {
        if (errors.getGlobalErrors() != null) {
            // Global errors
            for (ObjectError error : errors.getGlobalErrors()) {
                add(new SimpleError(
                        null,
                        error.getCode(),
                        error.getDefaultMessage(),
                        error.getArguments()));
            }
        }

        if (errors.getFieldErrors() != null) {
            // Field errors
            for (FieldError error : errors.getFieldErrors()) {
                add(new SimpleError(
                        error.getField(),
                        error.getCode(),
                        error.getDefaultMessage(),
                        error.getArguments()));
            }
        }
    }
}
