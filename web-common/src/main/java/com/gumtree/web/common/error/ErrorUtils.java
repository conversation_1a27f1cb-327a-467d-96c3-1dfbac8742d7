package com.gumtree.web.common.error;

import com.gumtree.common.util.error.SimpleError;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Error utils.
 */
public final class ErrorUtils {

    /**
     * Utility constructor.
     */
    private ErrorUtils() {

    }

    /**
     * Validate object using JSR-303 validator and return any errors as a list
     * of {@link com.gumtree.common.util.error.SimpleError}s.
     *
     * @param object    the object to validate
     * @param validator the validator
     * @param <T>       the object type
     * @return list of errors - empty if no errors.
     */
    public static <T> List<SimpleError> validate(T object, Validator validator) {

        List<SimpleError> errorsList = new ArrayList<SimpleError>();

        Set<ConstraintViolation<T>> violations = validator.validate(object);

        if (violations != null) {

            for (ConstraintViolation<T> violation : violations) {
                if (violation.getPropertyPath() != null && StringUtils.hasText(
                        violation.getPropertyPath().toString())) {

                    errorsList.add(new SimpleError(
                            violation.getPropertyPath().toString(),
                            violation.getMessage(),
                            null));
                } else {

                    errorsList.add(new SimpleError(null, violation.getMessage(), null));
                }
            }
        }

        return errorsList;
    }
}
