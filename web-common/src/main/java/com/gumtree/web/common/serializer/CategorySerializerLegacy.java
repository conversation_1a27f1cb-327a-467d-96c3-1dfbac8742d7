package com.gumtree.web.common.serializer;

import com.gumtree.api.category.domain.Category;
import org.codehaus.jackson.JsonGenerator;
import org.codehaus.jackson.map.JsonSerializer;
import org.codehaus.jackson.map.SerializerProvider;

import java.io.IOException;

/**
 * Serializer to clean model for FE
 */
public class CategorySerializerLegacy extends JsonSerializer<Category> {

    @Override
    public void serialize(Category category, JsonGenerator jgen, SerializerProvider provider) throws IOException {
        jgen.writeStartObject();

        jgen.writeFieldName("id");
        jgen.writeNumber(category.getId());

        if (category.getParentId() != null) {
            jgen.writeFieldName("parentId");
            jgen.writeNumber(category.getParentId());
        }

        jgen.writeFieldName("depth");
        jgen.writeNumber(category.getDepth());

        jgen.writeFieldName("seoName");
        jgen.writeString(category.getSeoName());
        jgen.writeFieldName("name");
        jgen.writeString(category.getName());

        jgen.writeEndObject();
    }
}
