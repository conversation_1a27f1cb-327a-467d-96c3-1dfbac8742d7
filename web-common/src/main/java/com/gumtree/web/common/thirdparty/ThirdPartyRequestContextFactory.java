package com.gumtree.web.common.thirdparty;

import com.gumtree.web.common.page.context.GumtreePageContext;

/**
 * Factory for creating a {@link ThirdPartyRequestContext}.
 * Factory is based on data configured in a {@link com.gumtree.web.common.page.context.GumtreePageContext}.
 */
public interface ThirdPartyRequestContextFactory {

    /**
     * Create a {@link ThirdPartyRequestContext}.
     * Context is based on data configured in a {@link com.gumtree.web.common.page.context.GumtreePageContext}.
     *
     * @param pageContext the source context
     * @return a {@link ThirdPartyRequestContext} based on data configured in the {@link GumtreePageContext}.
     */
    ThirdPartyRequestContext create(GumtreePageContext pageContext);
}
