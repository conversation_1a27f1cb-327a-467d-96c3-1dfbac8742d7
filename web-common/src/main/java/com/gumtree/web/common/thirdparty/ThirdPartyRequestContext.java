package com.gumtree.web.common.thirdparty;

import com.gumtree.api.Account;
import com.gumtree.api.User;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.legacy.LegacySite;
import com.gumtree.util.helper.DisplayAdsViewMode;
import com.gumtree.web.abtest.AbTestType;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.zeno.core.domain.PageType;

import javax.servlet.http.HttpServletRequest;
import java.net.URL;
import java.util.List;

/**
 * A context passed to third-party requests
 */
public interface ThirdPartyRequestContext<T> {

    /**
     * The current main request's page type.
     *
     * @return the current main request's page type.
     */
    PageType getPageType();

    /**
     * @return the request location.
     */
    Location getLocation();

    /**
     * @return the request location user input.
     */
    String getLocationUserInput();

    /**
     * @return the request is a radial search.
     */
    Boolean isRadialSearch();

    Location getLocation(int level);

    /**
     * @return the request location legacy site.
     */
    LegacySite getLegacySite();

    /**
     * @return the request category.
     */
    Category getCategory();

    /**
     * Get the L1 Category for this context. Unfortunately, due to some legacy weirdness, there may
     * be an inconsistency between this L1 category and the L1 category returned via a call to
     * {@code getCategory(CategoryLevel.L1)}.
     *
     * @return the L1 Category for this context
     */
    Category getL1Category();

    /**
     * Get the category at a particular level. Unfortunately, due to some legacy weirdness, there may
     * be an inconsistency between {@code getCategory(CategoryLevel.L1)} and the L1 category returned
     * via {@code getL1Category()}.
     *
     * @param level the level of the category to fetch
     * @return the category at a particular level.
     */
    Category getCategory(int level);

    /**
     * @return the county
     */
    Location getCounty();

    /**
     * @return the full request URL for the current request.
     */
    URL getAbsoluteRequestUrl();

    /**
     * @return referrer header for the current request.
     */
    String getReferrer();

    /**
     *
     * @return the underlying httpRequest
     */
    HttpServletRequest getHttpServletRequest();

    /**
     * @return the order
     */
    Order getOrder();

    /**
     * @return the account
     */
    Account getAccount();

    /**
     * @return the user
     */
    User getUser();

    /**
     * @return - true if the page contained results
     */
    Boolean hasContent();

    /**
     * @return - any search term the user may have entered
     */
    String getSearchTerm();

    /**
     * @return ads view mode
     */
    DisplayAdsViewMode getDisplayAdsViewMode();

    /**
     * @return current search page number
     */
    Integer getPageNumber();

    /**
     * @return seo url
     */
    String getUrl();

    /**
     * @return ab test types
     */
    List<AbTestType> getAbTestTypes();

    /**
     * @return page specific model
     */
    T getPageModel();

    String getDeviceType();

}
