package com.gumtree.web.common.util;

import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Default implementation of {@link ExecutorServiceFactory}.
 */
@Component
public class SimpleExecutorServiceFactory implements ExecutorServiceFactory {

    @Override
    public final ExecutorService newFixedThreadPool(int nThreads) {
        return Executors.newFixedThreadPool(nThreads);
    }
}
