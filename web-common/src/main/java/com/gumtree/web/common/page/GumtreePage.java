package com.gumtree.web.common.page;

import com.gumtree.zeno.core.domain.PageType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation used to mark a controller handler method/class as a Gumtree common page.
 */
@Target({ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface GumtreePage {

    /**
     * get the page type
     */
    PageType value();

    /**
     * flag for triggering populating the model with popular searches data
     */
    boolean populatePopularSearches() default false;

    /**
     * for specifying the maximum number of threads for processing model content building tasks
     */
    int parallelisation() default 2;
}
