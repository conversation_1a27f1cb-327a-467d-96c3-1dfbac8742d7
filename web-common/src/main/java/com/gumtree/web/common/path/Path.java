package com.gumtree.web.common.path;

public interface Path {

    /**
     * Builds a path by combining path segments with a query string
     * @return the path
     */
    String getPath();

    Path addQueryParam(String key, String value);

    Path addQueryParam(String key, int value);

    Path addQueryParam(String key, Double value);

    boolean containsQueryParam(String key);

    void removeQueryParam(String key);
}
