package com.gumtree.web.common.error.mvc;

import com.gumtree.common.util.error.Error;
import com.gumtree.common.util.error.ErrorReporter;
import com.gumtree.common.util.error.ReportableErrors;
import com.gumtree.common.util.error.ReportableErrorsArguments;
import org.springframework.validation.Errors;

/**
 * Simple adapter that turns {@link Errors} into {@link ReportableErrors}.
 */
public final class MvcReportableErrors implements ReportableErrors {

    private MvcErrorList errors;

    /**
     * Constructor.
     *
     * @param errors the errors.
     */
    public MvcReportableErrors(Errors errors) {
        this.errors = new MvcErrorList(errors);
    }

    @Override
    public void report(ErrorReporter errorReporter) {
        for (Error error : errors) {
            if (error.getField() == null) {
                errorReporter.globalError(
                        error.getMessageCode(),
                        error.getDefaultMessage(),
                        error.getArgs());
            } else {
                errorReporter.fieldError(
                        error.getField(),
                        error.getMessageCode(),
                        error.getDefaultMessage(),
                        error.getArgs());
            }
        }
    }

    @Override
    public void report(ErrorReporter errorReporter, ReportableErrorsArguments args) {
        for (Error error : errors) {
            if (error.getField() == null) {
                errorReporter.globalError(
                        error.getMessageCode(),
                        error.getDefaultMessage(),
                        args.getCombinedArguments(error));
            } else {
                errorReporter.fieldError(
                        error.getField(),
                        error.getMessageCode(),
                        error.getDefaultMessage(),
                        args.getCombinedArguments(error));
            }
        }
    }
}
