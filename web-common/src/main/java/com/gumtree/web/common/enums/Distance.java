package com.gumtree.web.common.enums;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.common.base.Optional;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.web.common.domain.location.ResolvedLocation;
import org.apache.commons.lang3.Validate;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.util.stream.Stream;

@JsonSerialize(using = HtmlSelectSerializer.class)
public enum Distance implements HtmlSelect<Double> {
    DEFAULT(0.0001, "Choose distance"),
    ONE(1.0, "+ 1 mile"),
    THREE(3.0, "+ 3 miles"),
    FIVE(5.0, "+ 5 miles"),
    TEN(10.0, "+ 10 miles"),
    FIFTEEN(15.0, "+ 15 miles"),
    THIRTY(30.0, "+ 30 miles"),
    FIFTY(50.0, "+ 50 miles"),
    SEVENTY_FIVE(75.0, "+ 75 miles"),
    ONE_HUNDRED(100.0, "+ 100 miles"),
    NATIONWIDE(1000.0, "Nationwide");

    private final Double value;
    private final String displayValue;

    Distance(Double value, String displayValue) {
        this.value = value;
        this.displayValue = displayValue;
    }

    public Double getValue() {
        return value;
    }

    public String getValueStr() {
        return formatAsStr(value);
    }

    public String getDisplayValue() {
        return displayValue;
    }

    public static double searchNormalized(@Nonnull Optional<Double> value) {
        Assert.notNull(value);
        if (isZeroDistance(value)) {
            return Distance.DEFAULT.getValue();
        } else {
            return value.get();
        }
    }

    public static double searchNormalized(@Nonnull java.util.Optional<Double> value) {
        Assert.notNull(value);
        if (isZeroDistance(value)) {
            return Distance.DEFAULT.getValue();
        } else {
            return value.get();
        }
    }

    public static String formatAsStr(@Nonnull Double distance) {
        Assert.notNull(distance);
        return String.valueOf(distance.intValue());
    }

    public static boolean isZeroDistance(@Nonnull Optional<Double> distance) {
        Validate.notNull(distance);
        return !distance.isPresent() || distance.get() <= Distance.DEFAULT.getValue();
    }

    public static boolean isZeroDistance(@Nonnull java.util.Optional<Double> distance) {
        Validate.notNull(distance);
        return !distance.isPresent() || distance.get() <= Distance.DEFAULT.getValue();
    }

    public static boolean isZeroDistance(@Nonnull Double distance) {
        Validate.notNull(distance);
        return distance <= Distance.DEFAULT.getValue();
    }

    public static boolean isDefaultDistance(@Nonnull Optional<Double> distance) {
        Validate.notNull(distance);
        return !distance.isPresent() || distance.get() <= Distance.DEFAULT.getValue();
    }

    public static Double computationNormalized(@Nonnull Optional<Double> distance) {
        Validate.notNull(distance);
        return isZeroDistance(distance) || distance.get() <= Distance.DEFAULT.getValue() ? 0d : distance.get();
    }

    public static Double computationNormalized(@Nonnull java.util.Optional<Double> distance) {
        Validate.notNull(distance);
        return isZeroDistance(distance) || distance.get() <= Distance.DEFAULT.getValue() ? 0d : distance.get();
    }

    @SafeVarargs
    public static java.util.Optional<Double> getFirstNonDefaultDistance(Optional<Double>... distanceList) {
        return Stream.of(distanceList)
                .filter(Optional::isPresent).map(Optional::get)
                .filter(distance -> !DEFAULT.getValue().equals(distance))
                .findFirst();
    }

    public static Optional<Double> defaultDistance(@Nonnull Optional<Category> l1SearchCategory,
                                                   @Nonnull ResolvedLocation location) {
        Validate.notNull(l1SearchCategory);
        Validate.notNull(location);

        if (location.isPostcode() || location.isOutcode()) {
            if (l1SearchCategory.isPresent()) {
                if (Categories.MOTORS.is(l1SearchCategory)) {
                    return Optional.of(Distance.FIFTY.getValue());
                }
                if (Categories.JOBS.is(l1SearchCategory)) {
                    return Optional.of(Distance.TEN.getValue());
                }
                if (Categories.FLATS_AND_HOUSES.is(l1SearchCategory)) {
                    return Optional.of(Distance.THREE.getValue());
                }
            }
            return Optional.of(Distance.FIVE.getValue());
        }

        return Optional.absent();
    }
}
