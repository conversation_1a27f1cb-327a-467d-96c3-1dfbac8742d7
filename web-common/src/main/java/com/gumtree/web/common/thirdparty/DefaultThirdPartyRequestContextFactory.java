package com.gumtree.web.common.thirdparty;

import com.gumtree.web.common.page.context.GumtreePageContext;
import org.springframework.stereotype.Component;

/**
 * Default implementation of {@link ThirdPartyRequestContextFactory}.
 */
@Component
public class DefaultThirdPartyRequestContextFactory implements ThirdPartyRequestContextFactory {

    @Override
    public final ThirdPartyRequestContext create(GumtreePageContext pageContext) {
        return new ThirdPartyRequestContextAdapter(pageContext);
    }
}
