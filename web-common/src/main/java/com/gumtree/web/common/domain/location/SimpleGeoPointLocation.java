package com.gumtree.web.common.domain.location;

import java.math.BigDecimal;
import java.util.Optional;

public class SimpleGeoPointLocation implements GeoPointLocation {

    private BigDecimal latitude;
    private BigDecimal longitude;

    public SimpleGeoPointLocation(BigDecimal latitude, BigDecimal longitude) {
        this.latitude = latitude;
        this.longitude = longitude;
    }

    @Override
    public Optional<BigDecimal> getLatitude() {
        return Optional.ofNullable(latitude);
    }

    @Override
    public Optional<BigDecimal> getLongitude() {
        return Optional.ofNullable(longitude);
    }
}
