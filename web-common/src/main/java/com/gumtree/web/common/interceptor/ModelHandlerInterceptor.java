package com.gumtree.web.common.interceptor;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.gumtree.api.category.domain.Category;
import com.gumtree.web.common.serializer.CategorySerializer;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

public class ModelHandlerInterceptor extends HandlerInterceptorAdapter {

    private static final String AUTH_HEADER_NAME = "x-gt-get-model";

    private final String headerSecret;
    private final ObjectMapper objectMapper;

    public ModelHandlerInterceptor(String headerSecret) {
        this.headerSecret = headerSecret;
        this.objectMapper = createObjectMapper();
    }

    @Override
    public void postHandle(HttpServletRequest request,
                           HttpServletResponse response,
                           Object handler,
                           ModelAndView modelAndView) {
        if (hasExpectedHeader(request)) {
            MappingJackson2JsonView jsonView = new MappingJackson2JsonView();
            jsonView.setObjectMapper(objectMapper);
            modelAndView.setView(jsonView);
        }
    }

    private boolean hasExpectedHeader(HttpServletRequest request) {
        return Optional.ofNullable(request.getHeader(AUTH_HEADER_NAME))
                .map(v -> v.equals(headerSecret)).orElse(false);
    }

    private static com.fasterxml.jackson.databind.ObjectMapper createObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper = objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
        objectMapper = objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper = objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        SimpleModule module = new SimpleModule("gt-custom", Version.unknownVersion());
        module.addSerializer(Category.class, new CategorySerializer());

        objectMapper.registerModule(module);

        return objectMapper;
    }

}
