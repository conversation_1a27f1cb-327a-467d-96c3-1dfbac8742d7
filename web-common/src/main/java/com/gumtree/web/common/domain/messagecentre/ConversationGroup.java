package com.gumtree.web.common.domain.messagecentre;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;

public final class ConversationGroup {
    /**
     * It's possible that we are unable to find an advert or the advert service is down. If that happens we don't want to fail
     * but display conversation data without advert details. So that is reason why it's Optional, but in reality the advert should be
     * available 99.99999% of time.
     */
    private final Optional<Advert> advert;
    private final List<Conversation> conversations;

    public ConversationGroup(Optional<Advert> advert, List<Conversation> conversations) {
        Assert.notNull(advert, "Advert is required");
        Assert.isTrue(CollectionUtils.isNotEmpty(conversations), "At least 1 conversation is required");

        this.advert = advert;
        this.conversations = conversations;
    }

    public Advert getAdvert() {
        return advert.orElse(null);
    }

    public List<Conversation> getConversations() {
        return conversations;
    }

    public String getLastConversationId() {
        return conversations.get(0).getId();
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof ConversationGroup)){
            return false;
        }

        ConversationGroup that = (ConversationGroup) o;

        if(advert != null ? !advert.equals(that.advert) : that.advert != null){
            return false;
        }
        if(conversations != null ? !conversations.equals(that.conversations) : that.conversations != null){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = advert != null ? advert.hashCode() : 0;
        result = 31 * result + (conversations != null ? conversations.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ConversationGroup{" +
                "advert=" + advert +
                ", conversations=" + conversations +
                '}';
    }
}
