package com.gumtree.web.common.format;

import org.joda.time.DateTime;
import org.joda.time.Days;

import java.util.Calendar;
import java.util.Date;


/**
 * Default implementation.
 *
 * <AUTHOR>
 */
public final class PostingTimeFormatter {

    private PostingTimeFormatter() {
        // nothing to do
    }

    /**
     * @param postingTime of the ad
     * @param now         current time
     * @return the display text
     */
    public static String formatPostingTime(Date postingTime, Date now) {
        if (postingTime == null) {
            return null;
        }

        // if posted yesterday
        if (isYesterday(postingTime, now)) {
            return "yesterday";
        }

        // if posted less than one hour ago
        long diffInMinutes = Math.round((now.getTime() - postingTime.getTime()) / 60000d);
        if (diffInMinutes < 60) {
            return diffInMinutes <= 1 ? "just now" : diffInMinutes + " mins ago";
        }

        // if posted more than 1 hour ago but at some point today
        int diffInHours = Math.round(diffInMinutes / 60);
        if (diffInHours < 24) {
            return diffInHours == 1 ? diffInHours + " hour ago" : diffInHours + " hours ago";
        }

        int diffInMonths = getDiffInMonths(postingTime, now);

        // if posted prior to yesterday but later than the current date of last month
        // e.g. if today is the 15th March, the cut-off date is 16th Feb inclusive
        if (diffInMonths < 1) {
            int diffInDays = Days.daysBetween(new DateTime(postingTime), new DateTime(now)).getDays();
            return diffInDays <= 1 ? " yesterday" : diffInDays + " days ago";
        }

        // if posted on todays date but in the previous month but later than the
        // current date of the month which was 2 months ago
        if (diffInMonths < 2) {
            return "1 month ago";
        }

        if (diffInMonths < 13) {
            return diffInMonths + " months ago";
        }

        int diffInYears = Math.round(diffInMonths / 12);

        if (diffInYears == 1) {
            return "1 year ago";
        }
        return diffInYears + " years ago";

    }

    /**
     * Formats the amount of time remaining for a feature
     *
     * @param expiryDate - date the feature expires
     * @param nowDate    - now
     * @return the display text
     */
    public static String formatFeatureExpiryTime(DateTime expiryDate, DateTime nowDate) {
        if (expiryDate == null) {
            return "Already purchased";
        }

        if (expiryDate.isBefore(nowDate)) {
            return null;
        }

        int daysBetween = Days.daysBetween(
                nowDate.toDateMidnight(),
                expiryDate.toDateMidnight())
                .getDays();

        if (daysBetween == 1) {
            return "1 day left";
        } else if (daysBetween == 0) {
            return "Ends today";
        }

        return daysBetween + " days left";
    }

    private static int getDiffInMonths(Date postingDate, Date nowDate) {
        Calendar today = Calendar.getInstance();
        today.setTime(nowDate);
        Calendar posting = Calendar.getInstance();
        posting.setTime(postingDate);

        int months = (today.get(Calendar.YEAR) - posting.get(Calendar.YEAR)) * 12
                + (today.get(Calendar.MONTH) - posting.get(Calendar.MONTH));
        if (today.get(Calendar.DAY_OF_MONTH) < posting.get(Calendar.DAY_OF_MONTH)) {
            months = months - 1;
        }

        return months;
    }

    private static boolean isYesterday(Date date, Date now) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        Calendar yesterdayCalendar = Calendar.getInstance();
        yesterdayCalendar.setTime(now);
        yesterdayCalendar.add(Calendar.DAY_OF_YEAR, -1);

        if (calendar.get(Calendar.YEAR) != yesterdayCalendar.get(Calendar.YEAR)) {
            return false;
        }

        if (calendar.get(Calendar.DAY_OF_YEAR) != yesterdayCalendar.get(Calendar.DAY_OF_YEAR)) {
            return false;
        }

        return true;

    }

}
