package com.gumtree.web.common.page.context;

import com.google.common.collect.Maps;
import com.gumtree.web.common.page.handler.PageHandler;
import com.gumtree.web.common.page.handler.PageHandlerFactory;
import com.gumtree.web.common.page.model.thirdparty.ThirdPartyViewModelAppender;
import com.gumtree.web.common.page.model.thirdparty.ThirdPartyViewModelAppenderFactory;
import com.gumtree.web.common.page.util.RedirectViewWithRequiredModelKeys;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContextFactory;
import com.gumtree.web.common.util.RequestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.annotation.Annotation;
import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * Initialises the wired in {@link GumtreePageContext} for the current request.
 */
public final class GumtreePageContextInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private RequestScopedGumtreePageContext pageContext;

    @Autowired
    private ThirdPartyViewModelAppenderFactory thirdPartyViewModelAppenderFactory;

    @Autowired
    private ThirdPartyRequestContextFactory thirdPartyRequestContextFactory;

    @Autowired
    private PageHandlerFactory pageHandlerFactory;

    @Override
    public boolean preHandle(HttpServletRequest req, HttpServletResponse resp, Object handler) throws Exception {

        PageHandler pageHandler = pageHandlerFactory.create((HandlerMethod) handler);

        if (pageHandler != null) {
            pageContext.init(req, resp, pageHandler);
        }

        return true;
    }

    @Override
    public void postHandle(
            HttpServletRequest request,
            HttpServletResponse response,
            Object handler,
            ModelAndView modelAndView) throws Exception {

        if (modelAndView != null) {

            // TODO: Optimisation would see no need to create another PageHandler here (see above in preHandle)
            PageHandler pageHandler = pageHandlerFactory.create((HandlerMethod) handler);

            if (pageHandler != null) {
                if (!RequestUtils.isRedirect(modelAndView)) {

                    // Populate common page model objects
                    pageContext.populateModel(modelAndView.getModelMap(), pageHandler);

                    // Populate additional third-party model objects based on method annotations
                    populateThirdPartyModelObjects(modelAndView, pageHandler);

                } else {
                    // If we are redirecting, we decide to clear out all model attributes to prevent them
                    // appearing in the URL query string.
                    Map<String, Object> requiredModelParameters = getModelRequiredParameters(modelAndView);
                    modelAndView.getModelMap().clear();
                    modelAndView.getModelMap().putAll(requiredModelParameters);
                }
            }
        }
    }

    @SuppressWarnings("unchecked")
    private void populateThirdPartyModelObjects(ModelAndView modelAndView, PageHandler pageHandler) {

        Collection<Annotation> annotations = pageHandler.getAdditionalAnnotations();
        ThirdPartyRequestContext requestContext = null;

        if (annotations != null) {

            for (Annotation a : annotations) {

                Collection<ThirdPartyViewModelAppender> appenders =
                        thirdPartyViewModelAppenderFactory.getAppenders(a.annotationType());

                if (appenders != null) {

                    if (requestContext == null) {
                        requestContext = thirdPartyRequestContextFactory.create(pageContext);
                    }
                    for (ThirdPartyViewModelAppender appender : appenders) {
                        appender.append(modelAndView.getModelMap(), a, requestContext);
                    }
                }
            }
        }
    }

    private Map<String, Object> getModelRequiredParameters(ModelAndView modelAndView) {
        Map<String, Object> requiredParameters = Maps.newHashMap();
        if (modelAndView.getView() instanceof RedirectViewWithRequiredModelKeys) {
            Set<String> keys = ((RedirectViewWithRequiredModelKeys) modelAndView.getView()).getRequiredParameterKeys();
            for (String key: keys) {
                requiredParameters.put(key, modelAndView.getModelMap().get(key));
            }
        }
        return requiredParameters;
    }
}
