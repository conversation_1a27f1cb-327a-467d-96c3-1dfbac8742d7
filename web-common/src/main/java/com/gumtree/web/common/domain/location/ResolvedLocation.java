package com.gumtree.web.common.domain.location;

import com.google.common.base.Optional;

public abstract class ResolvedLocation {

    // eg TW91EL
    public boolean isPostcode() {
        return false;
    }

    // eg N1
    public boolean isOutcode() {
        return false;
    }

    // eg London
    public boolean isLocation() {
        return false;
    }

    // none of above
    public boolean isUnresolved() {
        return false;
    }

    public abstract Optional<Location> getLocation();

    /**
     * Get location id that should be used mainly to build urls
     * @return the location id
     */
    public abstract String getIdName();

    /**
     * Gets location name that should be presented to user in for example search input box
     * @return the location display name
     */
    public abstract String getSearchLocation();

    /**
     * Defined whether the location support near-by search or not
     * @return <code>true</code> if it is possible to do near-by search for this location
     */
    public abstract boolean supportsNearbySearch();

    public abstract boolean supportsRadialSearch();

    public abstract double getRadius();

}
