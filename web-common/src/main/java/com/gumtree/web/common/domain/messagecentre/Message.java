package com.gumtree.web.common.domain.messagecentre;

public final class Message {

    private String id;
    private String body;
    private MessageDirection direction;
    private String time;

    public static Builder builder() {
        return new Builder();
    }

    public Message(String id, String body, MessageDirection direction, String time) {
        this.id = id;
        this.body = body;
        this.direction = direction;
        this.time = time;
    }

    public String getId() {
        return id;
    }

    public String getBody() {
        return body;
    }

    public MessageDirection getDirection() {
        return direction;
    }

    public String getTime() {
        return time;
    }

    public static class Builder {
        private String id;
        private String body;
        private MessageDirection direction;
        private String time;

        public Builder setId(String id) {
            this.id = id;
            return this;
        }

        public Builder setBody(String body) {
            this.body = body;
            return this;
        }

        public Builder setDirection(MessageDirection direction) {
            this.direction = direction;
            return this;
        }

        public Builder setTime(String time) {
            this.time = time;
            return this;
        }

        public Message build() {
            return new Message(id, body, direction, time);
        }
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof Message)){
            return false;
        }

        Message message = (Message) o;

        if(body != null ? !body.equals(message.body) : message.body != null){
            return false;
        }
        if(direction != message.direction){
            return false;
        }
        if(id != null ? !id.equals(message.id) : message.id != null){
            return false;
        }
        if(time != null ? !time.equals(message.time) : message.time != null){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (body != null ? body.hashCode() : 0);
        result = 31 * result + (direction != null ? direction.hashCode() : 0);
        result = 31 * result + (time != null ? time.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "Message{" +
                "id='" + id + '\'' +
                ", body='" + body + '\'' +
                ", direction=" + direction +
                ", time='" + time + '\'' +
                '}';
    }
}
