package com.gumtree.web.common.page.context;

import com.gumtree.web.common.page.handler.PageHandler;
import org.springframework.beans.factory.DisposableBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * A {@link GumtreePageContext} that will exist for the lifetime of a request.
 */
public interface RequestScopedGumtreePageContext<T> extends GumtreePageContext<T>, DisposableBean {

    /**
     * Initialise the context ready for page to act upon it.
     *
     * @param req    the current request
     * @param resp   the current response
     * @param method the {@link com.gumtree.web.common.page.handler.PageHandler} handling the request
     */
    void init(HttpServletRequest req, HttpServletResponse resp, PageHandler method);

    /**
     * Populate the given view model.
     *
     * @param model  the model to populate
     * @param method the {@link PageHandler} handling the request
     */
    void populateModel(Map<String, Object> model, PageHandler method);
}
