package com.gumtree.web.common.enums;

public enum Feature {

    HOMEPAGE_SPOTLIGHT("HOMEPAGE_SPOTLIGHT"),
    FEATURE_3_DAY("FEATURE_3_DAY"),
    FEATURE_7_DAY("FEATURE_7_DAY"),
    FEATURE_14_DAY("FEATURE_14_DAY"),
    URGENT("URGENT"),
    WEBSITE_URL("WEBSITE_URL"),
    SEARCH_STANDOUT("SEARCH_STANDOUT"),
    PREMIUM_JOB("PREMIUM_JOB"),
    EXTENDED_VEHICLE_HISTORY_CHECK("EXTENDED_VEHICLE_HISTORY_CHECK"),
    CALL_TRACKING_ACCOUNT_LEVEL("CALL_TRACKING_ACCOUNT_LEVEL"),
    CALL_TRACKING_ACCOUNT_LEVEL_HIDDEN("CALL_TRACKING_ACCOUNT_LEVEL_HIDDEN"),
    CALL_TRACKING_ADVERT_LEVEL("CALL_TRACKING_ADVERT_LEVEL"),
    CALL_TRACKING_ADVERT_LEVEL_WITH_BLACKLISTING("CALL_TRACKING_ADVERT_LEVEL_WITH_BLACKLISTING"),
    EMG_FREESPEE_PERMISSION("EMG_FREESPEE_PERMISSION");

    private String id;

    Feature(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }
}
