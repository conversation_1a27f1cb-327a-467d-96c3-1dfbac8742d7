package com.gumtree.web.common.thirdparty.noras;

import com.gumtree.api.category.domain.Category;

import java.util.Map;

/**
 * Interface for a helper class that controls the NORAS survey.
 */
public interface NorasSurveyHelper {

    /**
     * Adds the NORAS survey to the given model, if we're in the configured date range.
     *
     * @param model the model to update
     */
    void addNorasSurvey(Map<String, Object> model);

    /**
     * Check if the given category is one that we should show the NORAS survey for.
     *
     * @param category the category to check
     * @return true if the survey should be shown in this category
     */
    boolean isNorasCategory(Category category);
}
