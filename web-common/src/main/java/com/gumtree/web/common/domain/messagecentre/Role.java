package com.gumtree.web.common.domain.messagecentre;

import java.util.Optional;

public enum Role {
    Buyer,
    Seller;

    public static Optional<Role> fromStr(String value) {
        if (Buyer.name().equalsIgnoreCase(value)) {
            return Optional.of(Buyer);
        } else if (Seller.name().equalsIgnoreCase(value)) {
            return Optional.of(Seller);
        } else {
            return Optional.empty();
        }
    }
}
