package com.gumtree.web.common.security;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.util.model.Actions;
import com.gumtree.util.model.Link;
import com.gumtree.util.model.SimpleLink;
import com.gumtree.util.url.UrlScheme;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.gumtree.util.model.Actions.*;

/**
 * Default implementation of {@link UserContextPageLinksFactory}.
 */
@Component
public final class UserContextPageLinksFactoryImpl implements UserContextPageLinksFactory {

    public static final String LOGIN = "Login";
    public static final String CREATE_ACCOUNT = "Create account";
    public static final String POST_AD = "Post ad";
    public static final String POST_EVENT = "Post an event";
    public static final String MANAGE_MY_ADS = "Manage my ads";
    public static final String EDIT_MY_ACCOUNT = "My details";
    public static final String LOG_OUT = "Logout";

    @Autowired
    private UrlScheme urlScheme;

    @Override
    public UserContextPageLinks createPageLinks(Location location, Category category) {
        return createBushfirePageLinks(category);
    }

    private UserContextPageLinks createBushfirePageLinks(Category category) {
        Link loginLink = new SimpleLink(LOGIN, urlScheme.urlFor(BUSHFIRE_LOGIN));
        Link createAccountLink = new SimpleLink(CREATE_ACCOUNT, urlScheme.urlFor(Actions.CREATE_ACCOUNT));
        Link postAdLink = new SimpleLink(POST_AD, urlScheme.bushfirePostAdUrlFor(category));
        Link postEventLink = new SimpleLink(POST_EVENT, urlScheme.bushfirePostEventUrl());
        Link manageAdsLink = new SimpleLink(MANAGE_MY_ADS, urlScheme.urlFor(BUSHFIRE_MANAGE_ADS));
        Link editAccountLink = new SimpleLink(EDIT_MY_ACCOUNT, urlScheme.urlFor(BUSHFIRE_MANAGE_ACCOUNT));
        Link logoutLink = new SimpleLink(LOG_OUT, urlScheme.urlFor(BUSHFIRE_LOGOUT));
        return new SimpleUserContextPageLinks(loginLink, createAccountLink, postAdLink,
                postEventLink, manageAdsLink, editAccountLink, logoutLink);
    }

}
