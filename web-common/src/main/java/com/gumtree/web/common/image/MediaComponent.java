package com.gumtree.web.common.image;

import com.gumtree.domain.media.ImageSize;

import java.util.Map;

/**
 * Image url holder object to simply rendering in a tag
 */
public class MediaComponent {
    private MediaType type;
    private String thumbUrl;
    private String mainUrl;
    private String fullUrl;
    private String miniThumbUrl;
    private String previewUrl;

    /**
     * Constructor designed to be generally called from the MediaGallery
     *
     * @param urls the image urls
     * @param type media type, IMAGE or VIDEO
     */
    public MediaComponent(Map<ImageSize, String> urls, MediaType type) {
        this.thumbUrl = urls.get(ImageSize.THUMB);
        this.mainUrl = urls.get(ImageSize.MAIN);
        this.fullUrl = urls.get(ImageSize.FULL);
        this.miniThumbUrl = urls.get(ImageSize.MINITHUMB);
        this.previewUrl = urls.get(ImageSize.PREVIEW);
        this.type = type;
    }

    public final String getThumbUrl() {
        return thumbUrl;
    }

    public final String getMainUrl() {
        return mainUrl;
    }

    public final String getFullUrl() {
        return fullUrl;
    }

    public final String getMiniThumbUrl() {
        return miniThumbUrl;
    }

    public final String getPreviewUrl() {
        return previewUrl;
    }

    public final MediaType getMediaType() {
        return type;
    }


}
