package com.gumtree.web.common.propertyeditor;

import org.springframework.beans.PropertyEditorRegistrar;
import org.springframework.beans.PropertyEditorRegistry;
import org.springframework.beans.propertyeditors.StringTrimmerEditor;

/**
 * Implementations of the {@link PropertyEditorRegistrar} that adds custom Gumtree property editors to the property
 * editor registry
 */
public class GumtreePropertyEditorRegistrar implements PropertyEditorRegistrar {

    /**
     * {@inheritDoc}
     */
    @Override
    public void registerCustomEditors(PropertyEditorRegistry registry) {
        registry.registerCustomEditor(String.class, new StringTrimmerEditor(true));
    }
}
