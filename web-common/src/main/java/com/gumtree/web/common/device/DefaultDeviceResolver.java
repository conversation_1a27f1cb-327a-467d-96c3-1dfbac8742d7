package com.gumtree.web.common.device;

import org.springframework.mobile.device.Device;
import org.springframework.mobile.device.LiteDeviceResolver;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public class DefaultDeviceResolver extends LiteDeviceResolver {

    @Override
    public Device resolveDevice(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");

        DeviceOS os = StringUtils.hasText(userAgent) ? getDeviceOS(userAgent.toLowerCase()) : DeviceOS.UNKNOWN;

        return new DefaultDevice(super.resolveDevice(request), os);
    }

    private DeviceOS getDeviceOS(String userAgent) {
        for (DeviceOS os : DeviceOS.values()) {
            if (matches(userAgent, os.getUserAgentKeywords())) {
                return os;
            }
        }

        return DeviceOS.UNKNOWN;
    }

    private boolean matches(String userAgent, List<String> keywords) {
        for (String keyword : keywords) {
            if (userAgent.contains(keyword)) {
                return true;
            }
        }

        return false;
    }
}
