package com.gumtree.web.common.domain.order.entity;

import com.gumtree.api.Ad;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.domain.payment.ApiPaymentDetail;
import com.gumtree.seller.domain.order.status.OrderItemStatus;
import com.gumtree.seller.domain.product.entity.ProductName;
import com.gumtree.web.common.domain.order.OrderItem;

import java.math.BigDecimal;

/**
 *
 */
public final class OrderItemEntity implements OrderItem {

    private Ad advert;

    private ProductName productName;

    private ApiPaymentDetail paymentDetail;

    private OrderItemStatus status;

    private BigDecimal priceExVat;

    private BigDecimal priceIncVat;

    private Category category;

    @Override
    public Ad getAdvert() {
        return advert;
    }

    @Override
    public ProductName getProductName() {
        return productName;
    }

    @Override
    public ApiPaymentDetail getPaymentDetails() {
        return paymentDetail;
    }

    @Override
    public OrderItemStatus getStatus() {
        return status;
    }

    @Override
    public Category getCategory() {
        return category;
    }

    public void setAdvert(Ad advert) {
        this.advert = advert;
    }

    public void setProductName(ProductName productName) {
        this.productName = productName;
    }

    public void setPaymentDetail(ApiPaymentDetail paymentDetail) {
        this.paymentDetail = paymentDetail;
    }

    public void setStatus(OrderItemStatus status) {
        this.status = status;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    @Override
    public BigDecimal getPriceIncVat() {
        return priceIncVat;
    }

    /**
     * Set the price inc vat in pounds
     *
     * @param priceIncVat amount in pence
     */
    public void setPriceIncVat(BigDecimal priceIncVat) {
        this.priceIncVat = priceIncVat;
    }

    public Boolean isFree() {
        return priceIncVat.doubleValue() == 0 && paymentDetail == null;
    }
}
