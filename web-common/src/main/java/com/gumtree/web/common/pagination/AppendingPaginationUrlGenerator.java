package com.gumtree.web.common.pagination;

import com.gumtree.web.common.page.model.pagination.PaginationUrlGenerator;

/**
 * URL generator for pagination that appends 'pageX' to the base url
 */
public final class AppendingPaginationUrlGenerator implements PaginationUrlGenerator {

    private String basePath;

    public AppendingPaginationUrlGenerator(String basePath) {
        this.basePath = basePath;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String forPage(int number) {
        if (number > 1) {
            return basePath + "/page" + number;
        } else {
            return basePath;
        }
    }
}
