package com.gumtree.web.common.domain.location;

import com.google.common.base.Optional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Objects;

public class ResolvedOutcodeLocation extends ResolvedLocation implements GeoPointLocation {
    private String outcode;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private Optional<Location> location;

    public ResolvedOutcodeLocation(String outcode, BigDecimal latitude, BigDecimal longitude, Optional<Location> location) {
        Assert.notNull(outcode);
        Assert.notNull(latitude);
        Assert.notNull(longitude);
        Assert.notNull(location);

        this.outcode = outcode.trim();
        this.latitude = latitude;
        this.longitude = longitude;
        this.location = location;
    }

    @Override
    public boolean isOutcode() {
        return true;
    }

    @Override
    public Optional<Location> getLocation() {
        return location;
    }

    @Override
    public String getIdName() {
        return outcode.toLowerCase();
    }

    @Override
    public String getSearchLocation() {
        return outcode.toUpperCase();
    }

    @Override
    public java.util.Optional<BigDecimal> getLatitude() {
        return java.util.Optional.ofNullable(latitude);
    }

    @Override
    public java.util.Optional<BigDecimal> getLongitude() {
        return java.util.Optional.ofNullable(longitude);
    }

    @Override
    public boolean supportsNearbySearch() {
        return true;
    }

    @Override
    public boolean supportsRadialSearch() {
        return true;
    }

    @Override
    public double getRadius() {
        return 0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ResolvedOutcodeLocation that = (ResolvedOutcodeLocation) o;
        return Objects.equals(outcode, that.outcode) &&
                Objects.equals(latitude, that.latitude) &&
                Objects.equals(longitude, that.longitude) &&
                Objects.equals(location, that.location);
    }

    @Override
    public int hashCode() {
        return Objects.hash(outcode, latitude, longitude, location);
    }

    @Override
    public String toString() {
        return "ResolvedOutcodeLocation{" +
                "outcode='" + outcode + '\'' +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", location=" + location +
                '}';
    }
}
