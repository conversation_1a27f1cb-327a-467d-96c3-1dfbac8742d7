package com.gumtree.web.common.interceptor;

import com.gumtree.web.common.util.RequestUtils;
import org.apache.http.HttpHeaders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Finds info about the user agent.
 * <p/>
 * TODO: WHY ARE THERE NO UNIT TESTS FOR THIS?!?
 */
public class RequestAnalyser implements HandlerInterceptor {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public final boolean preHandle(HttpServletRequest request,
                                   HttpServletResponse response,
                                   Object handler) {
        return true;
    }

    @Override
    public final void postHandle(HttpServletRequest request,
                                 HttpServletResponse response,
                                 Object handler,
                                 ModelAndView modelAndView) {
        try {
            if (modelAndView != null && !RequestUtils.isRedirect(modelAndView)) {
                String userAgent = request.getHeader(HttpHeaders.USER_AGENT);
                boolean webKit = false;
                if (userAgent != null) {
                    webKit = userAgent.toLowerCase().contains("webkit");
                }
                modelAndView.addObject("webkit", webKit);
            }
        } catch (RuntimeException e) {
            logger.warn("Could not analyse user agent. " + e.getMessage());
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request,
                                HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        // nothing to do
    }

}
