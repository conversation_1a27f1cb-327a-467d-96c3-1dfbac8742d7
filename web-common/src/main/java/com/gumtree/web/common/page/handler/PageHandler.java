package com.gumtree.web.common.page.handler;

import com.gumtree.web.common.page.GumtreePage;

import java.lang.annotation.Annotation;
import java.util.Collection;

/**
 * A {@link PageHandler} represents an object handling a Gumtree page request. It only applies
 * to handlers annotated with {@link com.gumtree.web.common.page.GumtreePage}.
 */
public interface PageHandler {

    /**
     * @return the associated {@link com.gumtree.web.common.page.GumtreePage} annotation.
     */
    GumtreePage getPageAnnotation();

    /**
     * @return the other annotations (excluding the {@link GumtreePage} annotation) for this handler.
     */
    Collection<Annotation> getAdditionalAnnotations();
}
