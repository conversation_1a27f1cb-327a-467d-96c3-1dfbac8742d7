package com.gumtree.web.common.device;

import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;

public enum DeviceOS {

    IOS(Lists.newArrayList("iphone", "ipad", "ipod")),
    ANDROID(Lists.newArrayList("android")),
    UNKNOWN(Collections.<String>emptyList());

    private List<String> userAgentKeywords;

    private DeviceOS(List<String> userAgentKeywords) {
        this.userAgentKeywords = userAgentKeywords;
    }

    public List<String> getUserAgentKeywords() {
        return userAgentKeywords;
    }
}
