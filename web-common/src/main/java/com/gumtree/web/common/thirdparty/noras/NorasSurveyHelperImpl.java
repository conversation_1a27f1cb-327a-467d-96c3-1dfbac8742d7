package com.gumtree.web.common.thirdparty.noras;

import com.gumtree.api.category.domain.Category;
import com.gumtree.service.category.CategoryService;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * Implementation of the NORAS survey helper.
 */
@Component
public class NorasSurveyHelperImpl implements NorasSurveyHelper {

    private final DateTimeFormatter dateTimeFormatter = ISODateTimeFormat.basicDateTimeNoMillis();

    @Value("${gumtree.noras.startdate:20120312T170000Z}")
    private String norasStartDate;
    @Value("${gumtree.noras.enddate:20120313T180000Z}")
    private String norasEndDate;
    @Value("${gumtree.noras.scripturl:http://enhance-media.s3.amazonaws.com/GumtreeNORAS2012.js}")
    private String norasScriptURL;

    @Value("${gumtree.noras.categoryid:2553}")
    private Long norasCategoryId;

    private DateTime startDate;
    private DateTime endDate;

    @Autowired
    private CategoryService categoryService;

    @PostConstruct
    private void init() {
        startDate = this.dateTimeFormatter.parseDateTime(norasStartDate);
        endDate = this.dateTimeFormatter.parseDateTime(norasEndDate);
    }

    @Override
    public final void addNorasSurvey(Map<String, Object> model) {
        DateTime now = new DateTime();
        if (now.compareTo(startDate) >= 0 && now.compareTo(endDate) < 0) {
            model.put("norasScriptURL", norasScriptURL);
        }
    }

    @Override
    public final boolean isNorasCategory(Category category) {
        Map<Integer, Category> hierarchy = categoryService.getLevelHierarchy(category);
        return hierarchy.containsValue(categoryService.getById(norasCategoryId).get());
    }
}
