package com.gumtree.web.common.error.json;

import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.common.util.error.ErrorReporter;

import static com.gumtree.web.common.error.json.JsonComponentError.aComponentError;
import static com.gumtree.web.common.error.json.JsonValidation.aValidationModel;

/**
 * Implementation of {@link ErrorReporter} that creates a {@link JsonValidationResponse}.
 */
public final class JsonValidationErrorReporter implements ErrorReporter {

    private ErrorMessageResolver messageResolver;

    private JsonValidation.Builder validationBuilder = aValidationModel();

    private JsonValidationResponse cachedResponse;

    /**
     * Constructor.
     *
     * @param messageResolver for message code resolution.
     */
    public JsonValidationErrorReporter(ErrorMessageResolver messageResolver) {
        this.messageResolver = messageResolver;
    }

    @Override
    public void fieldError(String field, String messageCode) {
        validationBuilder.withComponentError(
                aComponentError()
                        .withComponentName(field)
                        .withMessage(resolveMessageCode(messageCode, null)));
    }

    @Override
    public void fieldError(String field, String messageCode, Object... args) {
        fieldError(field, messageCode, messageCode, args);
    }

    @Override
    public void fieldError(String field, String messageCode, String defaultMessage, Object... args) {
        validationBuilder.withComponentError(
                aComponentError()
                        .withComponentName(field)
                        .withMessage(resolveMessageCode(messageCode, defaultMessage, args)));
    }

    @Override
    public void globalError(String messageCode) {
        validationBuilder.withGlobalMessage(resolveMessageCode(messageCode, null));
    }

    @Override
    public void globalError(String messageCode, String defaultMessage, Object... args) {
        validationBuilder.withGlobalMessage(resolveMessageCode(messageCode, defaultMessage, args));
    }

    /**
     * Get the validation response
     *
     * @return JsonValidationResponse
     */
    public JsonValidationResponse getValidationResponse() {
        if (cachedResponse == null) {
            cachedResponse = new JsonValidationResponse(validationBuilder.build());
        }
        return cachedResponse;
    }

    private String resolveMessageCode(String messageCode, String defaultMessage, Object... args) {
        return messageResolver.getMessage(messageCode, defaultMessage, args);
    }
}
