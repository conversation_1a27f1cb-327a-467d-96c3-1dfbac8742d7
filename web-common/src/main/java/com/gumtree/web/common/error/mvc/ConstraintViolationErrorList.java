package com.gumtree.web.common.error.mvc;

import com.gumtree.common.util.error.Error;
import com.gumtree.common.util.error.ErrorReporter;
import com.gumtree.common.util.error.ReportableErrors;
import com.gumtree.common.util.error.ReportableErrorsArguments;
import com.gumtree.common.util.error.SimpleError;

import javax.validation.ConstraintViolation;
import java.util.ArrayList;
import java.util.Set;

/**
 * List of {@link Error}s built from {@link ConstraintViolation}s.
 */
public final class ConstraintViolationErrorList extends ArrayList<Error> implements ReportableErrors {

    /**
     * Constructor.
     *
     * @param violations the source errors
     */
    public ConstraintViolationErrorList(Set<ConstraintViolation> violations) {

        if (!violations.isEmpty()) {
            for (ConstraintViolation violation : violations) {
                if (violation.getPropertyPath() != null) {
                    add(new SimpleError(
                            violation.getPropertyPath().toString(),
                            violation.getMessage(),
                            violation.getMessage(),
                            null));
                } else {
                    add(new SimpleError(null,
                            violation.getMessage(),
                            violation.getMessage(),
                            null));
                }
            }
        }
    }

    @Override
    public void report(ErrorReporter errorReporter) {
        for (Error error : this) {
            if (error.getField() == null) {
                errorReporter.globalError(
                        error.getMessageCode(),
                        error.getDefaultMessage(),
                        error.getArgs());
            } else {
                errorReporter.fieldError(
                        error.getField(),
                        error.getMessageCode(),
                        error.getDefaultMessage(),
                        error.getArgs());
            }
        }
    }

    @Override
    public void report(ErrorReporter errorReporter, ReportableErrorsArguments args) {
        for (Error error : this) {
            if (error.getField() == null) {
                errorReporter.globalError(
                        error.getMessageCode(),
                        error.getDefaultMessage(),
                        args.getCombinedArguments(error));
            } else {
                errorReporter.fieldError(
                        error.getField(),
                        error.getMessageCode(),
                        error.getDefaultMessage(),
                        args.getCombinedArguments(error));
            }
        }
    }
}
