package com.gumtree.web.common.util;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Map that has a maximum size and discards oldest item when a new
 * item is added at the point the map has reached its maximum size.
 *
 * @param <K> class used for map keys
 * @param <V> class used for map values
 */
public final class LruMap<K, V> extends LinkedHashMap<K, V> {

    private int maxEntries;

    /**
     * Constructor.
     *
     * @param maxEntries maxEntries
     */
    public LruMap(final int maxEntries) {
        super(maxEntries + 1, 1.0f, true);
        this.maxEntries = maxEntries;
    }

    /**
     * Returns <tt>true</tt> if this <code>LruMap</code> has more entries than the maximum specified when it was
     * created.
     * <p/>
     * <p>
     * This method <em>does not</em> modify the underlying <code>Map</code>; it relies on the implementation of
     * <code>LinkedHashMap</code> to do that, but that behavior is documented in the JavaDoc for
     * <code>LinkedHashMap</code>.
     * </p>
     *
     * @param eldest the <code>Entry</code> in question; this implementation doesn't care what it is, since the
     *               implementation is only dependent on the size of the cache
     * @return <tt>true</tt> if the oldest
     */
    @Override
    protected boolean removeEldestEntry(final Map.Entry<K, V> eldest) {
        return super.size() > maxEntries;
    }
}
