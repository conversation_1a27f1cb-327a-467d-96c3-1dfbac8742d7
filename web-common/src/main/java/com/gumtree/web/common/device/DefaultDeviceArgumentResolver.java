package com.gumtree.web.common.device;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.mobile.device.DeviceUtils;
import org.springframework.web.bind.support.WebArgumentResolver;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

public class DefaultDeviceArgumentResolver implements HandlerMethodArgumentResolver {

    private static final Logger LOG = LoggerFactory.getLogger(DefaultDeviceArgumentResolver.class);

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return DefaultDevice.class.isAssignableFrom(parameter.getParameterType());
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        try {
            return webRequest.getAttribute(DeviceUtils.CURRENT_DEVICE_ATTRIBUTE, RequestAttributes.SCOPE_REQUEST);
        } catch (Exception e) {
            LOG.warn("Couldn't resolve Current Device Attribute in Scope Request", e);
            return WebArgumentResolver.UNRESOLVED;
        }
    }
}
