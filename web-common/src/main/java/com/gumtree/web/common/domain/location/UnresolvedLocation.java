package com.gumtree.web.common.domain.location;

import com.google.common.base.Optional;
import com.gumtree.common.util.url.UrlUtils;

import java.util.Objects;

public class UnresolvedLocation extends ResolvedLocation {
    private String unresolvedLocation;

    public UnresolvedLocation() {
    }

    public UnresolvedLocation(String unresolvedLocation) {
        if (unresolvedLocation != null) {
            this.unresolvedLocation = unresolvedLocation.trim();
        }
    }

    @Override
    public boolean isUnresolved() {
        return true;
    }

    @Override
    public Optional<Location> getLocation() {
        return Optional.absent();
    }

    @Override
    public String getIdName() {
        return unresolvedLocation == null ? "" : UrlUtils.enc(unresolvedLocation);
    }

    @Override
    public String getSearchLocation() {
        return unresolvedLocation;
    }

    @Override
    public boolean supportsNearbySearch() {
        return false;
    }

    @Override
    public boolean supportsRadialSearch() {
        return false;
    }

    @Override
    public double getRadius() {
        return 0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnresolvedLocation that = (UnresolvedLocation) o;
        return Objects.equals(unresolvedLocation, that.unresolvedLocation);
    }

    @Override
    public int hashCode() {
        return Objects.hash(unresolvedLocation);
    }

    @Override
    public String toString() {
        return "UnresolvedLocation{" +
                "unresolvedLocation='" + unresolvedLocation + '\'' +
                '}';
    }
}
