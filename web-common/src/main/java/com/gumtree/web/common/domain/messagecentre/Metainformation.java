package com.gumtree.web.common.domain.messagecentre;

public class Metainformation {

    private Integer numFound;
    private Integer pageSize;
    private Integer pageNum;

    public Metainformation(Builder builder) {
        this.numFound = builder.numFound;
        this.pageSize = builder.pageSize;
        this.pageNum = builder.pageNum;
    }

    public Integer getNumFound() {
        return numFound;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public static class Builder {

        private Integer numFound;
        private Integer pageSize;
        private Integer pageNum;

        public Builder setNumFound(Integer numFound) {
            this.numFound = numFound;
            return this;
        }

        public Builder setPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder setPageNum(Integer pageNum) {
            this.pageNum = pageNum;
            return this;
        }

        public Metainformation build() {
            return new Metainformation(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof Metainformation)){
            return false;
        }

        Metainformation that = (Metainformation) o;

        if(numFound != null ? !numFound.equals(that.numFound) : that.numFound != null){
            return false;
        }
        if(pageNum != null ? !pageNum.equals(that.pageNum) : that.pageNum != null){
            return false;
        }
        if(pageSize != null ? !pageSize.equals(that.pageSize) : that.pageSize != null){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = numFound != null ? numFound.hashCode() : 0;
        result = 31 * result + (pageSize != null ? pageSize.hashCode() : 0);
        result = 31 * result + (pageNum != null ? pageNum.hashCode() : 0);
        return result;
    }
}
