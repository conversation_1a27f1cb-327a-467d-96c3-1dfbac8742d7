package com.gumtree.web.common.util;

import org.springframework.ui.Model;

/**
 */
public final class SpringViewModelAdapter implements ViewModelAdapter {

    private Model model;

    /**
     * Constructor.
     *
     * @param model the wrapped model
     */
    public SpringViewModelAdapter(Model model) {
        this.model = model;
    }

    @Override
    public void addObject(String id, Object object) {
        model.addAttribute(id, object);
    }
}
