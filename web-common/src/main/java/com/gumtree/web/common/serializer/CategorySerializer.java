package com.gumtree.web.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.gumtree.api.category.domain.Category;

import java.io.IOException;

/**
 * Serializer to clean model for FE
 */
public class CategorySerializer extends JsonSerializer<Category> {

    @Override
    public void serialize(Category category, JsonGenerator jgen, SerializerProvider provider) throws IOException {
        jgen.writeStartObject();

        jgen.writeFieldName("id");
        jgen.writeNumber(category.getId());

        doIfValueNotNull(category.getParentId(), v -> {
            jgen.writeFieldName("parentId");
            jgen.writeNumber(v);
        });

        doIfValueNotNull(category.getDepth(), v -> {
            jgen.writeFieldName("depth");
            jgen.writeNumber(v);
        });

        doIfValueNotNull(category.getSeoName(), v -> {
            jgen.writeFieldName("seoName");
            jgen.writeString(v);
        });

        doIfValueNotNull(category.getName(), v -> {
            jgen.writeFieldName("name");
            jgen.writeString(v);
        });

        jgen.writeEndObject();
    }

    private <T, E extends Exception> void doIfValueNotNull(T value, ConsumerWithException<T, E> setter) throws E {
        if (null != value) {
            setter.accept(value);
        }
    }

    interface ConsumerWithException<T, E extends Exception> {
        void accept(T t) throws E;
    }

}
