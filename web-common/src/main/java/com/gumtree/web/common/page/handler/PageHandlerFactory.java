package com.gumtree.web.common.page.handler;

import org.springframework.web.method.HandlerMethod;

/**
 * A {@link PageHandlerFactory} creates a {@link PageHandler} from a given {@link HandlerMethod}.
 * Will only create a {@link PageHandler} where the method or enclosing class is annotated
 * with {@link com.gumtree.web.common.page.GumtreePage}.
 */
public interface PageHandlerFactory {

    /**
     * Create a {@link PageHandler} from a given {@link HandlerMethod}.
     *
     * @param method the source
     * @return a {@link PageHandler} from a given {@link HandlerMethod} or null
     *         if method or enclosing class is not annotated with {@link com.gumtree.web.common.page.GumtreePage}
     */
    PageHandler create(HandlerMethod method);
}
