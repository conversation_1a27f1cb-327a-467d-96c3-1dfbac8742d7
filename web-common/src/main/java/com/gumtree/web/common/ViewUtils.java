package com.gumtree.web.common;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.web.common.domain.converter.Conversions;
import org.apache.commons.lang3.CharUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.Duration;
import org.springframework.util.Assert;
import org.springframework.web.util.HtmlUtils;

import javax.annotation.Nonnull;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gumtree.util.GuavaUtils.java8OptionalToGuava;

/**
 * Utilities to format view model.
 */
public final class ViewUtils {

    private ViewUtils() {
    }

    public static String htmlFormat(String text) {
        return HtmlUtils.htmlEscape(text).replaceAll("\n", "<br />");
    }

    public static String postedDateFormat(Date postedDate) {
        if (postedDate == null) {
            return "A while ago";
        }

        Duration duration = new Duration(new DateTime(postedDate.getTime()), null);

        if (duration.isShorterThan(Duration.standardMinutes(5))) { //NOSONAR
            return "Just now";
        }

        if (duration.isShorterThan(Duration.standardHours(1))) {
            long minutes = duration.getStandardMinutes();
            return minutes == 1 ? minutes + " min ago" : minutes + " mins ago";
        }

        if (duration.isShorterThan(Duration.standardDays(1))) {
            long hours = duration.getStandardHours();
            return hours == 1 ? hours + " hour ago" : hours + " hours ago";
        }

        long days = Days.daysBetween(new DateTime(postedDate), new DateTime()).getDays();
        return days == 1 ? days + " day ago" : days + " days ago";
    }

    public static String distanceFormat(Optional<Double> distance) {
        return distanceFormat(distance.orNull());
    }

    public static String distanceFormat(Double distance) {
        if (distance != null) {
            long ceilDistance = Math.round(Math.ceil(distance));
            double roundedDistance = Math.round(distance * 100.0) / 100.0;
            String dif;
            if (roundedDistance >= 0 && roundedDistance <= 0.25) {
                dif = Math.abs(0 - roundedDistance) > Math.abs(0.25 - roundedDistance) ? "1/4 mile" : "0 miles";
            } else if (roundedDistance > 0.25 && roundedDistance <= 0.50) {
                dif = Math.abs(0.26 - roundedDistance) > Math.abs(0.50 - roundedDistance) ? "1/2 mile" : "1/4 mile";
            } else {
                dif = String.valueOf(ceilDistance) + (ceilDistance == 1 ? " mile" : " miles");
            }
            return dif;
        } else {
            return null;
        }
    }

    /**
     * Format salary range in pence to pounds with zeros collapsed
     *
     * @param min    the min salary in pence
     * @param max    the max salary in pence
     * @param period the salary period
     * @return formatted salary in pounds
     */
    public static java.util.Optional<String> formatSalary(@Nonnull java.util.Optional<String> min,
                                                          @Nonnull java.util.Optional<String> max,
                                                          @Nonnull String period) {
        Assert.notNull(min);
        Assert.notNull(max);
        Assert.notNull(period);

        java.util.Optional<String> minFormatted = min.flatMap(Conversions::fromPence2PoundsWithZerosCollapsed);
        java.util.Optional<String> maxFormatted = max.flatMap(Conversions::fromPence2PoundsWithZerosCollapsed);

        if (minFormatted.isPresent() && maxFormatted.isPresent() && !minFormatted.get().equals(maxFormatted.get())) {
            return java.util.Optional.of(minFormatted.get() + " to " + maxFormatted.get() + " " + period);
        } else {
            if (minFormatted.isPresent()) {
                return minFormatted.map(v -> v + " " + period);
            } else if (maxFormatted.isPresent()) {
                return maxFormatted.map(v -> v + " " + period);
            }
        }

        return java.util.Optional.empty();
    }

    /**
     * Format salary range in pence to pounds with zeros collapsed and only max if min is also provided.
     * <p>
     * (100, 500) will converts to 'up to £5)
     * (100, absent()) will converts to '£1)
     * (absent(), 100) will converts to '£1)
     *
     * @param min    the min salary in pence
     * @param max    the max salary in pence
     * @param period the salary period
     * @return formatted salary in pounds
     */
    public static java.util.Optional<String> formatSalaryShort(@Nonnull java.util.Optional<String> min,
                                                               @Nonnull java.util.Optional<String> max,
                                                               @Nonnull String period) {
        Assert.notNull(min);
        Assert.notNull(max);
        Assert.notNull(period);

        java.util.Optional<String> minFormatted = min.flatMap(Conversions::fromPence2PoundsWithZerosCollapsed);
        java.util.Optional<String> maxFormatted = max.flatMap(Conversions::fromPence2PoundsWithZerosCollapsed);

        if (minFormatted.isPresent() && maxFormatted.isPresent() && !minFormatted.get().equals(maxFormatted.get())) {
            return java.util.Optional.of("up to " + maxFormatted.get() + " " + period);
        } else {
            if (minFormatted.isPresent()) {
                return minFormatted.map(v -> v + " " + period);
            } else if (maxFormatted.isPresent()) {
                return maxFormatted.map(v -> v + " " + period);
            }
        }

        return java.util.Optional.empty();
    }

    public static <T> Map<T, Boolean> toBooleanMap(List<T> values) {
        Map<T, Boolean> result = new HashMap<>();
        if (values != null && !values.isEmpty()) {
            for (T value : values) {
                result.put(value, true);
            }
        }
        return result;
    }

    public static String olderThanFormat(Date postedDate) {
        if (postedDate == null) {
            return "Adverts older than today";
        }
        Duration duration = new Duration(new DateTime(postedDate.getTime()), null);
        if (duration.isShorterThan(Duration.standardDays(1))) {
            int day = new DateTime(postedDate.getTime()).dayOfMonth().get();
            String suffix = getDaySuffix(day);
            String strDateFormat = "EEEE d'" + suffix + "' MMMM";
            SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
            return "Ads posted, " + sdf.format(postedDate);
        }
        return "Adverts older than today";
    }

    public static String formatRangeAttribute(String min, String max, String prefix) {
        return formatRangeAttribute(Optional.fromNullable(min), Optional.fromNullable(max), Optional.fromNullable(prefix));
    }

    public static String formatRangeAttribute(@Nonnull AttributeType attributeType,
                                              @Nonnull java.util.Optional<String> min,
                                              @Nonnull java.util.Optional<String> max) {
        Assert.notNull(attributeType);
        Assert.notNull(min);
        Assert.notNull(max);
        return formatRangeAttribute(java8OptionalToGuava(min), java8OptionalToGuava(max), getAttributeValuePrefix(attributeType));
    }

    public static Optional<String> getAttributeValuePrefix(AttributeType attributeType) {
        return AttributeType.CURRENCY.equals(attributeType) ? Optional.of("£") : Optional.absent();
    }

    public static String formatRangeAttribute(@Nonnull Optional<String> min,
                                              @Nonnull Optional<String> max,
                                              @Nonnull Optional<String> prefix) {
        Assert.notNull(min);
        Assert.notNull(max);
        Assert.notNull(prefix);

        Long minLong = toLong(min);
        Long maxLong = toLong(max);

        if (minLong != null && maxLong != null) {
            return prefix.or("") + minLong + " - " + prefix.or("") + maxLong;
        } else if (minLong != null) {
            return "From " + prefix.or("") + minLong;
        } else if (maxLong != null) {
            return "Up to " + prefix.or("") + maxLong;
        }

        return "";
    }

    private static String getDaySuffix(int i) {
        switch (i) {
            case 1:
            case 21:
            case 31:
                return "st";
            case 2:
            case 22:
                return "nd";
            case 3:
            case 23:
                return "rd";
            default:
                return "th";
        }
    }

    public static <T> Map<String, Boolean> toStringBooleanMap(List<T> values) {
        Map<String, Boolean> result = new HashMap<>();
        if (values != null && !values.isEmpty()) {
            for (T value : values) {
                result.put(value.toString(), true);
            }
        }
        return result;
    }

    private static Long toLong(Optional<String> value) {
        if (value.isPresent()) {
            try {
                return Long.valueOf(value.get().replaceAll("(.*)[.,](.*)", "$1").trim());
            } catch (Exception e) {
                // ignore
            }
        }

        return null;
    }

    /**
     * Sanitise strings by replacing special chars with specified char.
     */
    public static class Sanitiser implements Function<String, String> {

        private static final List<Character> WHITELISTED_CHARS = Lists.newArrayList('£', '-', '.');

        private char replacement;

        public Sanitiser(char replacement) {
            this.replacement = replacement;
        }

        @Override
        public String apply(String input) {
            StringBuilder sanitised = new StringBuilder();
            for (char c : input.toLowerCase().toCharArray()) {
                if (CharUtils.isAsciiAlphanumeric(c) || WHITELISTED_CHARS.contains(c)) {
                    sanitised.append(Character.toLowerCase(c));
                } else {
                    sanitised.append(replacement);
                }
            }
            return singleOccurrence(sanitised.toString());
        }

        private String singleOccurrence(String s) {
            return s.replaceAll("(" + replacement + ")(\\1)+", "$1");
        }
    }
}
