package com.gumtree.web.common.enums;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

public class HtmlSelectSerializer extends JsonSerializer<HtmlSelect<?>> {

    @Override
    public void serialize(HtmlSelect<?> select, JsonGenerator jgen, SerializerProvider provider) throws IOException {
        jgen.writeStartObject();
        jgen.writeFieldName("value");
        jgen.writeString(select.getValue().toString());
        jgen.writeFieldName("displayValue");
        jgen.writeString(select.getDisplayValue());
        jgen.writeEndObject();
    }
}
