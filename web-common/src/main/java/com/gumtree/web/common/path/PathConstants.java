package com.gumtree.web.common.path;

/**
 * Constants related to Path
 */
public final class PathConstants {

    private PathConstants() {
        // Utility class, no constructor
    }

    public static final String RETURN_PAGE = "return";

    public static final Path EMPTY_PATH = new AbstractPath() {

        @Override
        public String getPath() {
            return null;
        }

        @Override
        protected String getPathSegment() {
            return null;
        }
    };
}
