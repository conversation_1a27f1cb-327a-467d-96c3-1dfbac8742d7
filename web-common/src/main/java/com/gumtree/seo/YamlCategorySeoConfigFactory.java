package com.gumtree.seo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.util.Assert;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;

/**
 * Factory class for the {@link CategorySeoConfig} that load config from the YAML file
 */
public class YamlCategorySeoConfigFactory implements CategorySeoConfigFactory {
    private static final Logger LOG = LoggerFactory.getLogger(YamlCategorySeoConfigFactory.class);

    private Resource configFile;
    private Resource fallbackConfigFile;

    public YamlCategorySeoConfigFactory(Resource configFile) {
        Assert.notNull(configFile);
        this.configFile = configFile;
    }

    public YamlCategorySeoConfigFactory(Resource configFile, Resource fallbackConfigFile) {
        this.configFile = configFile;
        this.fallbackConfigFile = fallbackConfigFile;
    }

    /**
     * Get category config loaded from the supplied config file
     * @return the category config
     * @throws IOException if file is not found or there was problem reading the config files
     */
    public CategorySeoConfig getNewInstance() throws IOException {
        Resource config = configFile;
        if (!config.exists() && fallbackConfigFile != null) {
            LOG.warn(String.format("Primary category SEO config file not found, going to use fallback config. "
                    + "Primary: %s. Fallback: %s", configFile.getDescription(), fallbackConfigFile.getURL()));
            config = fallbackConfigFile;
        }

        return new Yaml().loadAs(config.getInputStream(), YamlCategorySeoConfigs.class);
    }
}
