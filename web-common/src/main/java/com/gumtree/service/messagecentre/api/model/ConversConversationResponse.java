package com.gumtree.service.messagecentre.api.model;


import com.gumtree.web.common.domain.messagecentre.Role;
import java.util.List;

public final class ConversConversationResponse {

    private String userId;
    private String converseeId;
    private String conversee;
    private Role userRole;
    private final Integer numUnread;
    private final List<Message> messages;
    private final Advert advert;

    private ConversConversationResponse(Builder builder) {
        this.userId = builder.userId;
        this.converseeId = builder.converseeId;
        this.conversee = builder.conversee;
        this.userRole = builder.userRole;
        this.numUnread = builder.numUnread;
        this.messages = builder.messages;
        this.advert = builder.advert;
    }

    public String getUserId() {
        return userId;
    }

    public String getConverseeId() {
        return converseeId;
    }

    public String getConversee() {
        return conversee;
    }

    public Role getUserRole() {
        return userRole;
    }

    public Integer getNumUnread() {
        return numUnread;
    }

    public List<Message> getMessages() {
        return messages;
    }

    public Advert getAdvert() {
        return advert;
    }

    public static class Builder {
        private String userId;
        private String converseeId;
        private String conversee;
        private Role userRole;
        private Integer numUnread;
        private List<Message> messages;
        private Advert advert;

        public Builder setUserId(String userId) {
            this.userId = userId;
            return this;
        }

        public Builder setConverseeId(String converseeId) {
            this.converseeId = converseeId;
            return this;
        }

        public Builder setConversee(String conversee) {
            this.conversee = conversee;
            return this;
        }

        public Builder setUserRole(Role role) {
            this.userRole = role;
            return this;
        }

        public Builder setNumUnread(Integer numUnread) {
            this.numUnread = numUnread;
            return this;
        }

        public Builder setMessages(List<Message> messages) {
            this.messages = messages;
            return this;
        }

        public Builder setAdvert(Advert advert) {
            this.advert = advert;
            return this;
        }

        public ConversConversationResponse build() {
            return new ConversConversationResponse(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof ConversConversationResponse)){
            return false;
        }

        ConversConversationResponse that = (ConversConversationResponse) o;

        if(conversee != null ? !conversee.equals(that.conversee) : that.conversee != null){
            return false;
        }
        if(converseeId != null ? !converseeId.equals(that.converseeId) : that.converseeId != null){
            return false;
        }
        if(messages != null ? !messages.equals(that.messages) : that.messages != null){
            return false;
        }
        if(numUnread != null ? !numUnread.equals(that.numUnread) : that.numUnread != null){
            return false;
        }
        if(userRole != that.userRole){
            return false;
        }
        if(userId != null ? !userId.equals(that.userId) : that.userId != null){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = userId != null ? userId.hashCode() : 0;
        result = 31 * result + (converseeId != null ? converseeId.hashCode() : 0);
        result = 31 * result + (conversee != null ? conversee.hashCode() : 0);
        result = 31 * result + (userRole != null ? userRole.hashCode() : 0);
        result = 31 * result + (numUnread != null ? numUnread.hashCode() : 0);
        result = 31 * result + (messages != null ? messages.hashCode() : 0);
        return result;
    }
}
