package com.gumtree.service.messagecentre;


import com.gumtree.service.messagecentre.api.model.Advert;
import com.gumtree.service.messagecentre.api.model.ConversConversationResponse;
import com.gumtree.service.messagecentre.api.model.ConversConversationsResponse;
import com.gumtree.service.messagecentre.api.model.Conversation;
import com.gumtree.service.messagecentre.api.model.Message;
import com.gumtree.service.messagecentre.api.model.MessageCentreModelRoot;
import org.codehaus.jackson.JsonParser;
import org.codehaus.jackson.Version;
import org.codehaus.jackson.map.DeserializationConfig;
import org.codehaus.jackson.map.DeserializationContext;
import org.codehaus.jackson.map.JsonDeserializer;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.PropertyNamingStrategy;
import org.codehaus.jackson.map.module.SimpleModule;

import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.ext.ContextResolver;
import javax.ws.rs.ext.Provider;
import java.io.IOException;

@Provider
@Consumes({MediaType.APPLICATION_JSON, "text/json"})
@Produces({MediaType.APPLICATION_JSON, "text/json"})
public class MessageCentreObjectMapperProvider implements ContextResolver<ObjectMapper> {

    private final ObjectMapper objectMapper;

    public MessageCentreObjectMapperProvider() {
        this.objectMapper = getObjectMapper();
    }

    @Override
    public ObjectMapper getContext(Class<?> type) {
        if(type != null){
            String modelRootPackage = MessageCentreModelRoot.class.getPackage().getName();
            return type.getPackage().getName().startsWith(modelRootPackage) ? objectMapper : null;
        }
        return null;
    }

    public ObjectMapper getObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule("message centre", Version.unknownVersion());

        simpleModule.addDeserializer(ConversConversationsResponse.class,
                new JsonDeserializer<ConversConversationsResponse>() {
            @Override
            public ConversConversationsResponse deserialize(JsonParser jp, DeserializationContext ctxt)
                    throws IOException {
                return jp.readValueAs(ConversConversationsResponse.Builder.class).build();
            }
        });

        simpleModule.addDeserializer(Conversation.class, new JsonDeserializer<Conversation>() {
            @Override
            public Conversation deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
                return jp.readValueAs(Conversation.Builder.class).build();
            }
        });

        simpleModule.addDeserializer(Advert.class, new JsonDeserializer<Advert>() {
            @Override
            public Advert deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
                return jp.readValueAs(Advert.Builder.class).build();
            }
        });

        simpleModule.addDeserializer(ConversConversationResponse.class,
                new JsonDeserializer<ConversConversationResponse>() {
            @Override
            public ConversConversationResponse deserialize(JsonParser jp, DeserializationContext ctxt)
                    throws IOException {
                return jp.readValueAs(ConversConversationResponse.Builder.class).build();
            }
        });

        simpleModule.addDeserializer(Message.class, new JsonDeserializer<Message>() {
            @Override
            public Message deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
                return jp.readValueAs(Message.Builder.class).build();
            }
        });

        objectMapper.registerModule(simpleModule);
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.CAMEL_CASE_TO_LOWER_CASE_WITH_UNDERSCORES);
        objectMapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return objectMapper;
    }
}
