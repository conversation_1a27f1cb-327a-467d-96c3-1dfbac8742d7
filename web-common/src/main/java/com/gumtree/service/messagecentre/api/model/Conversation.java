package com.gumtree.service.messagecentre.api.model;

import com.gumtree.web.common.domain.messagecentre.MessageDirection;
import com.gumtree.web.common.domain.messagecentre.Role;
import org.joda.time.DateTime;

import java.util.List;

public final class Conversation {
    private String conversee;
    private String converseeId;
    private String userId;
    private String id;
    private final Long adId;
    private final Boolean unread;
    private final Role userRole;
    private final List<String> attachments;
    private final DateTime receivedDate;
    private String latestMessageContent;
    private MessageDirection latestMessageDirection;
    private final Advert advert;

    private Conversation(Builder builder) {
        this.conversee = builder.conversee;
        this.converseeId = builder.converseeId;
        this.userId = builder.userId;
        this.id = builder.id;
        this.adId = builder.adId;
        this.unread = builder.unread;
        this.userRole = builder.userRole;
        this.attachments = builder.attachments;
        this.receivedDate = builder.receivedDate;
        this.latestMessageContent = builder.latestMessageContent;
        this.latestMessageDirection = builder.latestMessageDirection;
        this.advert = builder.advert;
    }

    public String getConversee() {
        return conversee;
    }

    public String getConverseeId() {
        return converseeId;
    }

    public String getUserId() {
        return userId;
    }

    public String getId() {
        return id;
    }

    public Long getAdId() {
        return adId;
    }

    public Boolean getUnread() {
        return unread;
    }

    public Role getUserRole() {
        return userRole;
    }

    public List<String> getAttachments() {
        return attachments;
    }

    public DateTime getReceivedDate() {
        return receivedDate;
    }

    public String getLatestMessageContent() {
        return latestMessageContent;
    }

    public MessageDirection getLatestMessageDirection() {
        return latestMessageDirection;
    }

    public Advert getAdvert() {
        return advert;
    }

    public static class Builder {
        private String conversee;
        private String converseeId;
        private String userId;
        private String id;
        private Long adId;
        private Boolean unread;
        private Role userRole;
        private List<String> attachments;
        private DateTime receivedDate;
        private String latestMessageContent;
        private MessageDirection latestMessageDirection;
        private Advert advert;

        public Builder setConversee(String conversee) {
            this.conversee = conversee;
            return this;
        }

        public Builder setConverseeId(String converseeId) {
            this.converseeId = converseeId;
            return this;
        }

        public Builder setUserId(String userId) {
            this.userId = userId;
            return this;
        }

        public Builder setId(String id) {
            this.id = id;
            return this;
        }

        public Builder setAdId(Long adId) {
            this.adId = adId;
            return this;
        }

        public Builder setUnread(Boolean unread) {
            this.unread = unread;
            return this;
        }

        public Builder setUserRole(Role userRole) {
            this.userRole = userRole;
            return this;
        }

        public Builder setAttachments(List<String> attachments) {
            this.attachments = attachments;
            return this;
        }

        public Builder setReceivedDate(DateTime receivedDate) {
            this.receivedDate = receivedDate;
            return this;
        }

        public Builder setLatestMessageContent(String latestMessageContent) {
            this.latestMessageContent = latestMessageContent;
            return this;
        }

        public Builder setLatestMessageDirection(MessageDirection latestMessageDirection) {
            this.latestMessageDirection = latestMessageDirection;
            return this;
        }

        public Builder setAdvert(Advert advert) {
            this.advert = advert;
            return this;
        }

        public Conversation build() {
            return new Conversation(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof Conversation)){
            return false;
        }

        Conversation that = (Conversation) o;

        if(adId != null ? !adId.equals(that.adId) : that.adId != null){
            return false;
        }
        if(advert != null ? !advert.equals(that.advert) : that.advert != null){
            return false;
        }
        if(attachments != null ? !attachments.equals(that.attachments) : that.attachments != null){
            return false;
        }
        if(conversee != null ? !conversee.equals(that.conversee) : that.conversee != null){
            return false;
        }
        if(converseeId != null ? !converseeId.equals(that.converseeId) : that.converseeId != null){
            return false;
        }
        if(id != null ? !id.equals(that.id) : that.id != null){
            return false;
        }
        if(latestMessageContent != null ?
                !latestMessageContent.equals(that.latestMessageContent) : that.latestMessageContent != null){
            return false;
        }
        if(latestMessageDirection != that.latestMessageDirection){
            return false;
        }
        if(receivedDate != null ? !receivedDate.equals(that.receivedDate) : that.receivedDate != null){
            return false;
        }
        if(unread != null ? !unread.equals(that.unread) : that.unread != null){
            return false;
        }
        if(userId != null ? !userId.equals(that.userId) : that.userId != null){
            return false;
        }
        if(userRole != that.userRole){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = conversee != null ? conversee.hashCode() : 0;
        result = 31 * result + (converseeId != null ? converseeId.hashCode() : 0);
        result = 31 * result + (userId != null ? userId.hashCode() : 0);
        result = 31 * result + (id != null ? id.hashCode() : 0);
        result = 31 * result + (adId != null ? adId.hashCode() : 0);
        result = 31 * result + (unread != null ? unread.hashCode() : 0);
        result = 31 * result + (userRole != null ? userRole.hashCode() : 0);
        result = 31 * result + (attachments != null ? attachments.hashCode() : 0);
        result = 31 * result + (receivedDate != null ? receivedDate.hashCode() : 0);
        result = 31 * result + (latestMessageContent != null ? latestMessageContent.hashCode() : 0);
        result = 31 * result + (latestMessageDirection != null ? latestMessageDirection.hashCode() : 0);
        result = 31 * result + (advert != null ? advert.hashCode() : 0);
        return result;
    }
}
