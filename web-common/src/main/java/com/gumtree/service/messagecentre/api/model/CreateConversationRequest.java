package com.gumtree.service.messagecentre.api.model;

public class CreateConversationRequest {
    private Long adId;
    private String message;
    private String senderName;
    private String senderIp;
    private String senderCookie;
    private Long receiverId;
    private Long senderId;
    private Boolean optInMarketing;
    private String phoneNumber;
    private String googleAnalyticsClientId;

    public CreateConversationRequest(Long adId,
                                     String message,
                                     String senderName,
                                     String senderIp,
                                     String senderCookie,
                                     Long receiverId,
                                     Long senderId,
                                     String phoneNumber,
                                     Boolean optInMarketing,
                                     String googleAnalyticsClientId) {
        this.adId = adId;
        this.message = message;
        this.senderName = senderName;
        this.senderIp = senderIp;
        this.senderCookie = senderCookie;
        this.receiverId = receiverId;
        this.senderId = senderId;
        this.optInMarketing = optInMarketing;
        this.phoneNumber = phoneNumber;
        this.googleAnalyticsClientId = googleAnalyticsClientId;
    }

    public Long getAdId() {
        return adId;
    }

    public void setAdId(Long adId) {
        this.adId = adId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getSenderIp() {
        return senderIp;
    }

    public void setSenderIp(String senderIp) {
        this.senderIp = senderIp;
    }

    public String getSenderCookie() {
        return senderCookie;
    }

    public void setSenderCookie(String senderCookie) {
        this.senderCookie = senderCookie;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public Boolean getOptInMarketing() {
        return optInMarketing;
    }

    public void setOptInMarketing(Boolean optInMarketing) {
        this.optInMarketing = optInMarketing;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getGoogleAnalyticsClientId() {
        return googleAnalyticsClientId;
    }

    public void setGoogleAnalyticsClientId(String googleAnalyticsClientId) {
        this.googleAnalyticsClientId = googleAnalyticsClientId;
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof CreateConversationRequest)){
            return false;
        }

        CreateConversationRequest that = (CreateConversationRequest) o;

        if(adId != null ? !adId.equals(that.adId) : that.adId != null){
            return false;
        }
        if(message != null ? !message.equals(that.message) : that.message != null){
            return false;
        }
        if(optInMarketing != null ? !optInMarketing.equals(that.optInMarketing) : that.optInMarketing != null){
            return false;
        }
        if(receiverId != null ? !receiverId.equals(that.receiverId) : that.receiverId != null){
            return false;
        }
        if(senderCookie != null ? !senderCookie.equals(that.senderCookie) : that.senderCookie != null){
            return false;
        }
        if(senderId != null ? !senderId.equals(that.senderId) : that.senderId != null){
            return false;
        }
        if(senderIp != null ? !senderIp.equals(that.senderIp) : that.senderIp != null){
            return false;
        }
        if(senderName != null ? !senderName.equals(that.senderName) : that.senderName != null){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = adId != null ? adId.hashCode() : 0;
        result = 31 * result + (message != null ? message.hashCode() : 0);
        result = 31 * result + (senderName != null ? senderName.hashCode() : 0);
        result = 31 * result + (senderIp != null ? senderIp.hashCode() : 0);
        result = 31 * result + (senderCookie != null ? senderCookie.hashCode() : 0);
        result = 31 * result + (receiverId != null ? receiverId.hashCode() : 0);
        result = 31 * result + (senderId != null ? senderId.hashCode() : 0);
        result = 31 * result + (optInMarketing != null ? optInMarketing.hashCode() : 0);
        return result;
    }
}
