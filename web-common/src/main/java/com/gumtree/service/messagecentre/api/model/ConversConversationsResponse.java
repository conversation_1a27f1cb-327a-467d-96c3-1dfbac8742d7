package com.gumtree.service.messagecentre.api.model;

import java.util.List;


public final class ConversConversationsResponse {

    private final Integer numUnread;
    private final Integer numFound;
    private final List<Conversation> conversations;

    private ConversConversationsResponse(Builder builder) {
        this.numUnread = builder.numUnread;
        this.numFound = builder.numFound;
        this.conversations = builder.conversations;
    }

    public Integer getNumUnread() {
        return numUnread;
    }

    public Integer getNumFound() {
        return numFound;
    }

    public List<Conversation> getConversations() {
        return conversations;
    }

    public static class Builder {
        private Integer numUnread;
        private Integer numFound;
        private List<Conversation> conversations;

        public Builder setNumUnread(Integer numUnread) {
            this.numUnread = numUnread;
            return this;
        }

        public Builder setNumFound(Integer numFound) {
            this.numFound = numFound;
            return this;
        }

        public Builder setConversations(List<Conversation> conversations) {
            this.conversations = conversations;
            return this;
        }

        public ConversConversationsResponse build() {
            return new ConversConversationsResponse(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if(this == o){
            return true;
        }
        if(!(o instanceof ConversConversationsResponse)){
            return false;
        }

        ConversConversationsResponse that = (ConversConversationsResponse) o;

        if(conversations != null ? !conversations.equals(that.conversations) : that.conversations != null){
            return false;
        }
        if(numFound != null ? !numFound.equals(that.numFound) : that.numFound != null){
            return false;
        }
        if(numUnread != null ? !numUnread.equals(that.numUnread) : that.numUnread != null){
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = numUnread != null ? numUnread.hashCode() : 0;
        result = 31 * result + (numFound != null ? numFound.hashCode() : 0);
        result = 31 * result + (conversations != null ? conversations.hashCode() : 0);
        return result;
    }
}
