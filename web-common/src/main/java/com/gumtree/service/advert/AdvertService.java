package com.gumtree.service.advert;

import com.gumtree.domain.advert.Advert;
import com.gumtree.domain.advert.BasicAdvert;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.Collection;
import java.util.Set;

/**
 * Responsible for retrieving adverts
 */
public interface AdvertService {

    /**
     * Returns a list of adverts with the given ids.
     *
     * @param advertIds list of ids for the adverts
     * @return adverts matching the given ids
     */
    Collection<Advert> getAdverts(Set<Long> advertIds);

    /**
     * Returns an advert given an id. Will throw an
     * <code>AdvertNotFoundInSearchEngineException</code> if it doesn't exist
     *
     * @param id id of the advert to retrieve
     * @return the retrieved advert
     */
    Advert getAdvert(Long id);

    /**
     * Returns an advert given an id that has been searched for. Returns null if
     * it doesn't exist
     *
     * @param id id of the advert to retrieve
     * @return the retrieved advert or null
     */
    Advert getAdvertForSRP(Long id);

    /**
     * Returns an expired advert given an id.  Will return null if no such expired advert exists
     *
     * @param id of the advert to retrieve
     * @return the retrieved expired advert
     */
    BasicAdvert getExpiredAdvert(Long id);

    /**
     * An exception type used to indicate that a specified advert is not found by id in search engine
     */
    @ResponseStatus(value = HttpStatus.NOT_FOUND) class AdvertNotFoundInSearchEngineException extends RuntimeException {
        private Long advertId;

        /**
         * Creates the exception type providing the id of the advert that could not be found
         *
         * @param advertId the id of the advert
         */
        public AdvertNotFoundInSearchEngineException(Long advertId) {
            super(advertId + " is not a recognised advert");
            this.advertId = advertId;
        }

        public Long getAdvertId() {
            return advertId;
        }
    }

    /**
     * An exception type used to indicate that the advert has been found but is not in a status allowing it being
     * displayed e.g. when it is Deleted by CS
     */
    @ResponseStatus(value = HttpStatus.NOT_FOUND) class AdvertNotForDisplayException extends RuntimeException {
        private BasicAdvert advert;

        /**
         * Creates the exception type providing the advert
         *
         * @param advert advert
         */
        public AdvertNotForDisplayException(BasicAdvert advert) {
            super(advert.getId() + " is not for display");
            this.advert = advert;
        }

        public BasicAdvert getAdvert() {
            return advert;
        }
    }
}