package com.gumtree.service.advert;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * An exception type used to indicate that a specified advert is not found by id neither in search engine nor in
 * expired/archived DB
 */
@ResponseStatus(value = HttpStatus.NOT_FOUND)
public class AdvertNotFoundException extends RuntimeException {
    private Long advertId;

    /**
     * Creates the exception type providing the id of the advert that could not be found
     *
     * @param advertId the id of the advert
     */
    public AdvertNotFoundException(Long advertId) {
        super(advertId + " is not a recognised advert");
        this.advertId = advertId;
    }

    public Long getAdvertId() {
        return advertId;
    }
}
