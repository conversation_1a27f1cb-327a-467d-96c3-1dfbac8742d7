package com.gumtree.service.advert.impl.bapi;

import com.gumtree.api.Ad;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.domain.advert.BasicAdvert;
import com.gumtree.service.advert.ExpiredAdvertNotFoundException;
import com.gumtree.service.advert.ExpiredAdvertService;
import org.jboss.resteasy.client.ClientResponseFailure;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * service for retrieving expired ads from BAPI
 */
@Service
public class BAPIExpiredAdvertService implements ExpiredAdvertService {

    private AdvertApi bushfireAdvertApi;
    private ApiAdToBasicAdvertConverter apiAdToAdvertConverter;
    private VIPExpiredAdvertDisplayRule adDisplayRule;

    /**
     * Constructor
     *
     * @param bushfireApi            todo inject AdvertApi instead of BushfireApi because AdvertApi is the only thing
     *                               this service needs otherwise it's going to be difficult to refactor it if we ever
     *                               decide to split BushfireApi
     * @param apiAdToAdvertConverter - api to advert converter, converts to domain objects
     * @param adDisplayRule          - advert display rule, the rules for showing adverts on an expired vip
     */
    @Autowired
    public BAPIExpiredAdvertService(BushfireApi bushfireApi,
                                    ApiAdToBasicAdvertConverter apiAdToAdvertConverter,
                                    VIPExpiredAdvertDisplayRule adDisplayRule) {
        this.apiAdToAdvertConverter = apiAdToAdvertConverter;
        this.adDisplayRule = adDisplayRule;
        bushfireAdvertApi = bushfireApi.advertApi();
    }

    @Override
    public BasicAdvert getAdvert(Long advertId) {
        try {
            Ad apiAdvert = bushfireAdvertApi.getAdvert(advertId);
            if (apiAdvert == null || apiAdvert.getArchivedDate() != null) {
                throw new ExpiredAdvertNotFoundException(advertId);
            } else {
                return apiAdToAdvertConverter.convert(apiAdvert);
            }
        } catch (ClientResponseFailure e) {
            throw new ExpiredAdvertNotFoundException(advertId, e);
        }
    }

}
