package com.gumtree.config.session;

import com.codahale.metrics.MetricRegistry;
import com.datastax.driver.core.Cluster;
import com.datastax.driver.core.Host;
import com.datastax.driver.core.PreparedStatement;
import com.datastax.driver.core.ProtocolOptions;
import com.datastax.driver.core.ProtocolVersion;
import com.datastax.driver.core.ResultSet;
import com.datastax.driver.core.policies.DCAwareRoundRobinPolicy;
import com.datastax.driver.core.policies.TokenAwarePolicy;
import com.gumtree.common.properties.GtProps;
import com.gumtree.config.SellerProperty;
import com.gumtree.web.storage.CassandraClient;
import com.gumtree.web.storage.SimpleCassandraClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.validation.constraints.NotNull;
import java.net.InetSocketAddress;
import java.util.List;
import java.util.regex.Pattern;

import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

@Configuration
public class CassandraConfig {
    private static final Logger LOGGER = LoggerFactory.getLogger(CassandraConfig.class);

    @Autowired
    private MetricRegistry metricRegistry;

    @Bean
    public CassandraClient getCassandraClient() {
        boolean isCassandraEnabled = GtProps.getBool(SellerProperty.CASSANDRA_ENABLED);
        if (!isCassandraEnabled) {
            return new CassandraClient() {

                @Override
                public PreparedStatement prepareStatement(String statement) {
                    throw new IllegalStateException("Cassandra is disabled, this shouldn't be called");
                }

                @Override
                public ResultSet execute(@NotNull PreparedStatement statement, Object... values) {
                    throw new IllegalStateException("Cassandra is disabled, this shouldn't be called");
                }
            };
        }

        String keyspace = GtProps.getStr(SellerProperty.CASSANDRA_KEYSPACE);
        return new SimpleCassandraClient(buildCluster(), keyspace, metricRegistry);

    }

    private Cluster buildCluster() {
        String servers = GtProps.getStr(SellerProperty.CASSANDRA_SERVERS);
        String username = GtProps.getStr(SellerProperty.CASSANDRA_USER_NAME);
        String password = GtProps.getStr(SellerProperty.CASSANDRA_PASSWORD);

        /**
         * Explicitly defined policy as default one depends on library implementation
         * {@link com.datastax.driver.core.policies.Policies#defaultLoadBalancingPolicy()}
         */
        TokenAwarePolicy loadBalancingPolicy = new TokenAwarePolicy(DCAwareRoundRobinPolicy.builder().build());

        Cluster.Builder builder = Cluster.builder()
                .withLoadBalancingPolicy(loadBalancingPolicy)
                .withProtocolVersion(ProtocolVersion.V3)
                .addContactPointsWithPorts(convertServersConfigToContactPoints(servers));

        if (StringUtils.isNotBlank(username)) {
            builder.withCredentials(username, password);
        }

        Cluster cluster = builder.build();

        String dataCenters = cluster.getMetadata().getAllHosts().stream().map(Host::getDatacenter).collect(joining(", "));
        LOGGER.info("Cassandra settings. Cluster name: {}. DCs: {}", cluster.getClusterName(), dataCenters);

        return cluster;
    }

    static List<InetSocketAddress> convertServersConfigToContactPoints(String servers) {
        return Pattern.compile(",").splitAsStream(servers.trim())
                .map(hostPortConfig -> {
                    String[] hostPort = hostPortConfig.trim().split("[:]");
                    switch (hostPort.length) {
                        case 1: return new InetSocketAddress(hostPort[0], ProtocolOptions.DEFAULT_PORT);
                        case 2: return new InetSocketAddress(hostPort[0], Integer.parseInt(hostPort[1]));
                        default: throw new IllegalArgumentException("Incorrect configuration entry: "+hostPortConfig);
                    }
                })
                .collect(toList());

    }
}
