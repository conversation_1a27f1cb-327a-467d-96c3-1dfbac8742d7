package com.gumtree.config.session;

import com.google.common.util.concurrent.MoreExecutors;
import com.gumtree.web.storage.RedisTemplate;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisSentinelPool;
import redis.clients.util.Pool;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class RedisConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedisConfig.class);

    @Bean
    public RedisClusterSettings.Primary primaryRedisSettings() {
        return new RedisClusterSettings.Primary();
    }

    @Bean
    public RedisClusterSettings.Secondary secondaryRedisSettings() {
        return new RedisClusterSettings.Secondary();
    }

    @Bean
    public RedisTemplate redisTemplate(MeterRegistry meterRegistry) {
        RedisClusterSettings primarySetting = primaryRedisSettings();
        RedisClusterSettings secondarySetting = secondaryRedisSettings();

        LOGGER.info("*** Redis Setting ***, primary: " + primarySetting + ", secondary: " + secondarySetting);

        Pool<Jedis> primaryPool = createRedisPool(primarySetting);
        Pool<Jedis> secondaryPool = createRedisPool(secondarySetting);
        ExecutorService executor = secondaryPool != null ? createExecutorService() : null;

        return new RedisTemplate(primaryPool, secondaryPool, executor, meterRegistry);
    }

    private Pool<Jedis> createRedisPool(RedisClusterSettings setting) {
        boolean isConfigured = StringUtils.isNotBlank(setting.getMasterName()) || StringUtils.isNotBlank(setting.getHost());
        boolean isEnabled = setting.isEnabled() && isConfigured;
        if (isEnabled) {
            if (setting.getSentinels().isEmpty() || StringUtils.isBlank(setting.getSentinels().iterator().next())) {
                return new JedisPool(new JedisPoolConfig(), setting.getHost(), setting.getPort(), setting.getConnectionTimeout());
            } else {
                return new JedisSentinelPool(setting.getMasterName(), setting.getSentinels(), new JedisPoolConfig(),
                        setting.getConnectionTimeout());
            }
        }

        return null;
    }

    private ExecutorService createExecutorService() {
        ExecutorService executor = new ThreadPoolExecutor(4, 8, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100));
        MoreExecutors.addDelayedShutdownHook(executor, 10L, TimeUnit.SECONDS);
        return executor;
    }

}
