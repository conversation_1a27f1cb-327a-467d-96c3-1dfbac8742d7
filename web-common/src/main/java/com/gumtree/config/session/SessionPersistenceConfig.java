package com.gumtree.config.session;

import com.codahale.metrics.MetricRegistry;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.util.time.Clock;
import com.gumtree.config.SellerProperty;
import com.gumtree.config.profiles.CommonConfigProfiles;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.storage.CassandraClient;
import com.gumtree.web.storage.CassandraKeyValueRepository;
import com.gumtree.web.storage.KeyValueRepository;
import com.gumtree.web.storage.RedisTemplate;
import com.gumtree.web.storage.flash.DefaultFlashMapManager;
import com.gumtree.web.storage.ratelimit.DefaultRateLimiterPersister;
import com.gumtree.web.storage.ratelimit.RateLimiterPersister;
import com.gumtree.web.storage.strategy.BothPersistenceStrategy;
import com.gumtree.web.storage.strategy.CassandraPersistenceStrategy;
import com.gumtree.web.storage.strategy.CassandraSessionAwarePersistenceStrategy;
import com.gumtree.web.storage.strategy.JedisPersistenceStrategy;
import com.gumtree.web.storage.strategy.JedisSessionAwarePersistenceStrategy;
import com.gumtree.web.storage.strategy.SessionPersistenceStrategy;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.FlashMapManager;

import javax.validation.constraints.NotNull;
import java.util.Optional;

@Configuration
@Profile(CommonConfigProfiles.SESSION_PERSISTENCE_REDIS)
@Import({RedisConfig.class, CassandraConfig.class})
public class SessionPersistenceConfig {
    private static final String CASSANDRA_SYI_TABLE_NAME = "syi";
    private static final String CASSANDRA_SYI_KEY_COLUMN_NAME = "key";
    private static final String CASSANDRA_SYI_VALUE_COLUMN_NAME = "value";

    private static final String CASSANDRA_CHECKOUT_TABLE_NAME = "checkout";
    private static final String CASSANDRA_CHECKOUT_KEY_COLUMN_NAME = "key";
    private static final String CASSANDRA_CHECKOUT_VALUE_COLUMN_NAME = "value";

    private static final String CASSANDRA_USER_TABLE_NAME = "user";
    private static final String CASSANDRA_USER_KEY_COLUMN_NAME = "key";
    private static final String CASSANDRA_USER_VALUE_COLUMN_NAME = "value";

    private static final String CASSANDRA_MAD_FILTER_TABLE_NAME = "mad_filter";
    private static final String CASSANDRA_MAD_FILTER_KEY_COLUMN_NAME = "key";
    private static final String CASSANDRA_MAD_FILTER_VALUE_COLUMN_NAME = "value";

    private static final String CASSANDRA_SECURE_TOKEN_TABLE_NAME = "secure_token";
    private static final String CASSANDRA_SECURE_TOKEN_KEY_COLUMN_NAME = "key";
    private static final String CASSANDRA_SECURE_TOKEN_VALUE_COLUMN_NAME = "value";

    private static final String CASSANDRA_OPEN_ID_TABLE_NAME = "open_id";
    private static final String CASSANDRA_OPEN_ID_KEY_COLUMN_NAME = "key";
    private static final String CASSANDRA_OPEN_ID_VALUE_COLUMN_NAME = "value";

    private static final String CASSANDRA_FLASH_MAP_TABLE_NAME = "flash_map";
    private static final String CASSANDRA_FLASH_MAP_KEY_COLUMN_NAME = "key";
    private static final String CASSANDRA_FLASH_MAP_VALUE_COLUMN_NAME = "value";

    private static final String CASSANDRA_POSTING_RATE_TABLE_NAME = "posting_rate";
    private static final String CASSANDRA_POSTING_RATE_KEY_COLUMN_NAME = "key";
    private static final String CASSANDRA_POSTING_RATE_VALUE_COLUMN_NAME = "value";

    @Value("${gumtree.session.map.expiry.checkms}")
    private Long mapClearExpiredCheckMs;

    @Autowired
    private Clock clock;

    @Autowired
    private UserSessionService userSessionService;

    @Autowired
    private MetricRegistry metricRegistry;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CassandraClient cassandraClient;

    @Bean
    public FlashMapManager flashMapManager() {
        KeyValueRepository flashMapRepository = newKeyValueRepository(CASSANDRA_FLASH_MAP_TABLE_NAME,
                CASSANDRA_FLASH_MAP_KEY_COLUMN_NAME, CASSANDRA_FLASH_MAP_VALUE_COLUMN_NAME);
        return new DefaultFlashMapManager(flashMapRepository, redisTemplate, mapper, metricRegistry);
    }

    private KeyValueRepository newKeyValueRepository(String cassandraFlashMapTableName, String cassandraFlashMapKeyColumnName,
                                                     String cassandraFlashMapValueColumnName) {
        boolean isCassandraEnabled = GtProps.getBool(SellerProperty.CASSANDRA_ENABLED);
        if (isCassandraEnabled) {
            return new CassandraKeyValueRepository(cassandraClient, cassandraFlashMapTableName,
                    cassandraFlashMapKeyColumnName, cassandraFlashMapValueColumnName, String.class);
        } else {
            return new NoopKeyValueRepository();
        }
    }

    @Bean(name = "syiPersistenceStrategy")
    public SessionPersistenceStrategy syiPersistenceStrategy() {
        boolean isCassandraEnabled = GtProps.getBool(SellerProperty.CASSANDRA_ENABLED);
        if (isCassandraEnabled) {
            KeyValueRepository syiDataRepository = newKeyValueRepository(CASSANDRA_SYI_TABLE_NAME,
                    CASSANDRA_SYI_KEY_COLUMN_NAME, CASSANDRA_SYI_VALUE_COLUMN_NAME);

            return new BothPersistenceStrategy(
                    new CassandraSessionAwarePersistenceStrategy(syiDataRepository, userSessionService),
                    new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService), metricRegistry);
        } else {
            return new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService);
        }
    }

    @Bean(name = "syiPersistenceSimpleStrategy")
    public SessionPersistenceStrategy syiPersistenceSimpleStrategy() {
        boolean isCassandraEnabled = GtProps.getBool(SellerProperty.CASSANDRA_ENABLED);
        if (isCassandraEnabled) {
            KeyValueRepository syiDataRepository = newKeyValueRepository(CASSANDRA_SYI_TABLE_NAME,
                    CASSANDRA_SYI_KEY_COLUMN_NAME, CASSANDRA_SYI_VALUE_COLUMN_NAME);

            return new BothPersistenceStrategy(
                    new CassandraPersistenceStrategy(syiDataRepository),
                    new JedisPersistenceStrategy(redisTemplate), metricRegistry);
        } else {
            return new JedisPersistenceStrategy(redisTemplate);
        }
    }

    @Bean(name = "checkoutPersistenceStrategy")
    public SessionPersistenceStrategy checkoutPersistenceStrategy() {

        boolean isCassandraEnabled = GtProps.getBool(SellerProperty.CASSANDRA_ENABLED);

        if (isCassandraEnabled) {
            KeyValueRepository checkoutRepository = newKeyValueRepository(CASSANDRA_CHECKOUT_TABLE_NAME,
                    CASSANDRA_CHECKOUT_KEY_COLUMN_NAME, CASSANDRA_CHECKOUT_VALUE_COLUMN_NAME);
            return new BothPersistenceStrategy(
                    new CassandraSessionAwarePersistenceStrategy(checkoutRepository, userSessionService),
                    new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService), metricRegistry);

        } else {
            return new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService);
        }

    }

    @Bean(name = "checkoutPersistenceSimpleStrategy")
    public SessionPersistenceStrategy checkoutPersistenceSimpleStrategy() {

        boolean isCassandraEnabled = GtProps.getBool(SellerProperty.CASSANDRA_ENABLED);

        if (isCassandraEnabled) {
            KeyValueRepository checkoutRepository = newKeyValueRepository(CASSANDRA_CHECKOUT_TABLE_NAME,
                    CASSANDRA_CHECKOUT_KEY_COLUMN_NAME, CASSANDRA_CHECKOUT_VALUE_COLUMN_NAME);
            return new BothPersistenceStrategy(
                    new CassandraPersistenceStrategy(checkoutRepository),
                    new JedisPersistenceStrategy(redisTemplate), metricRegistry);

        } else {
            return new JedisPersistenceStrategy(redisTemplate);
        }

    }

    @Bean(name = "userPersistenceStrategy")
    public SessionPersistenceStrategy userPersistenceStrategy() {
        boolean isCassandraEnabled = GtProps.getBool(SellerProperty.CASSANDRA_ENABLED);
        if (isCassandraEnabled) {
            KeyValueRepository userRepository = newKeyValueRepository(CASSANDRA_USER_TABLE_NAME,
                    CASSANDRA_USER_KEY_COLUMN_NAME, CASSANDRA_USER_VALUE_COLUMN_NAME);
            return new BothPersistenceStrategy(
                    new CassandraSessionAwarePersistenceStrategy(userRepository, userSessionService),
                    new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService), metricRegistry);
        } else {
            return new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService);
        }


    }

    @Bean(name = "madFilterPersistenceStrategy")
    public SessionPersistenceStrategy madFilterPersistenceStrategy() {

        boolean isCassandraEnabled = GtProps.getBool(SellerProperty.CASSANDRA_ENABLED);
        if (isCassandraEnabled) {
            KeyValueRepository madFilterRepository = newKeyValueRepository(CASSANDRA_MAD_FILTER_TABLE_NAME,
                    CASSANDRA_MAD_FILTER_KEY_COLUMN_NAME, CASSANDRA_MAD_FILTER_VALUE_COLUMN_NAME);
            return new BothPersistenceStrategy(
                    new CassandraSessionAwarePersistenceStrategy(madFilterRepository, userSessionService),
                    new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService), metricRegistry);
        } else {
            return new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService);
        }

    }

    @Bean(name = "secureTokenPersistenceStrategy")
    public SessionPersistenceStrategy secureTokenPersistenceStrategy() {

        boolean isCassandraEnabled = GtProps.getBool(SellerProperty.CASSANDRA_ENABLED);
        if (isCassandraEnabled) {
            KeyValueRepository secureTokenRepository = newKeyValueRepository(CASSANDRA_SECURE_TOKEN_TABLE_NAME,
                    CASSANDRA_SECURE_TOKEN_KEY_COLUMN_NAME, CASSANDRA_SECURE_TOKEN_VALUE_COLUMN_NAME);
            return new BothPersistenceStrategy(
                    new CassandraSessionAwarePersistenceStrategy(secureTokenRepository, userSessionService),
                    new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService), metricRegistry);
        } else {
            return new JedisSessionAwarePersistenceStrategy(redisTemplate, userSessionService);
        }

    }

    @Bean(name = "openIDPersistenceStrategy")
    public SessionPersistenceStrategy openIDPersistenceStrategy() {
        boolean isCassandraEnabled = GtProps.getBool(SellerProperty.CASSANDRA_ENABLED);
        if (isCassandraEnabled) {
            KeyValueRepository openIDRepository = newKeyValueRepository(CASSANDRA_OPEN_ID_TABLE_NAME,
                    CASSANDRA_OPEN_ID_KEY_COLUMN_NAME, CASSANDRA_OPEN_ID_VALUE_COLUMN_NAME);
            return new BothPersistenceStrategy(new CassandraPersistenceStrategy(openIDRepository),
                    new JedisPersistenceStrategy(redisTemplate), metricRegistry);
        } else {
            return new JedisPersistenceStrategy(redisTemplate);
        }

    }

    @Bean
    public RateLimiterPersister rateLimiterPersister() {
        KeyValueRepository rateLimiterRepository = newKeyValueRepository(CASSANDRA_POSTING_RATE_TABLE_NAME,
                CASSANDRA_POSTING_RATE_KEY_COLUMN_NAME, CASSANDRA_POSTING_RATE_VALUE_COLUMN_NAME);
        return new DefaultRateLimiterPersister(redisTemplate, rateLimiterRepository, metricRegistry);
    }

    static class NoopKeyValueRepository<K, V> implements KeyValueRepository<K, V> {

        @Override
        public Optional<V> get(@NotNull K key) {
            return Optional.empty();
        }

        @Override
        public Optional getWithTTL(@NotNull K key) {
            return Optional.empty();
        }

        @Override
        public void set(@NotNull K key, @NotNull V value, int ttl) {
            //do nothing
        }

        @Override
        public void expire(@NotNull K key, int ttl) {
            //do nothing
        }

        @Override
        public void delete(@NotNull K key) {
            //do nothing
        }
    }
}
