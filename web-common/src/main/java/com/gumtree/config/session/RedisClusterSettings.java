package com.gumtree.config.session;

import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.beans.factory.annotation.Value;

import java.util.Set;

public interface RedisClusterSettings {
    String getHost();
    Integer getPort();
    Integer getConnectionTimeout();
    String getMasterName();
    Set<String> getSentinels();
    boolean isEnabled();

    public static class Primary implements RedisClusterSettings {
        @Value("${gumtree.redis.host}")
        private String host;

        @Value("${gumtree.redis.port}")
        private Integer port;

        @Value("${gumtree.redis.timeout}")
        private Integer connectionTimeout;

        @Value("${gumtree.redis.master.name}")
        private String masterName;

        @Value("#{'${gumtree.redis.sentinel.hostList}'.split(',')}")
        private Set<String> sentinels;

        public String getHost() {
            return host;
        }

        public Integer getPort() {
            return port;
        }

        public Integer getConnectionTimeout() {
            return connectionTimeout;
        }

        public String getMasterName() {
            return masterName;
        }

        public Set<String> getSentinels() {
            return sentinels;
        }

        public boolean isEnabled() {
            return true;
        }

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
        }
    }

    public static class Secondary implements RedisClusterSettings {
        @Value("${gumtree.redis.secondary.host:}")
        private String host;

        @Value("${gumtree.redis.secondary.port:}")
        private Integer port;

        @Value("${gumtree.redis.secondary.timeout:}")
        private Integer connectionTimeout;

        @Value("${gumtree.redis.secondary.master.name:}")
        private String masterName;

        @Value("#{'${gumtree.redis.secondary.sentinel.hostList:}'.split(',')}")
        private Set<String> sentinels;

        @Value("${gumtree.redis.secondary.enabled:false}")
        private boolean enabled;

        public String getHost() {
            return host;
        }

        public Integer getPort() {
            return port;
        }

        public Integer getConnectionTimeout() {
            return connectionTimeout;
        }

        public String getMasterName() {
            return masterName;
        }

        public Set<String> getSentinels() {
            return sentinels;
        }

        public boolean isEnabled() {
            return enabled;
        }

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
        }
    }
}
