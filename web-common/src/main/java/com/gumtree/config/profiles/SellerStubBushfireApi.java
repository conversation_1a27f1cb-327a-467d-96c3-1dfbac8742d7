package com.gumtree.config.profiles;

import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.client.StubBushfireApi;
import com.gumtree.api.client.spec.*;

public class SellerStubBushfireApi extends StubBushfireApi {
    @Override
    public <T extends ApiBase> T create(Class<T> tClass, BushfireApiKey apiKey) {
        try {
            return super.create(tClass, apiKey);
        } catch (UnsupportedOperationException unsupported) {
            if(PriceApi.class.equals(tClass)) {
                return (T) this.priceApi();
            }

            throw unsupported;
        }
    }
}
