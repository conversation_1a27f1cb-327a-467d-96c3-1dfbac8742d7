package com.gumtree.config.profiles;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;

/**
 * Spring java configuration for the Search API backed by Elasticsearch
 */
@Profile(CommonConfigProfiles.ELASTIC_SEARCH_API)
@Configuration
@Import({
        DomainConvertersConfig.class
})
public class ElasticSearchProfileConfig {


}
