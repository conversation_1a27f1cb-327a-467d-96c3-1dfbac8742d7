package com.gumtree.config.cookies;

import com.gumtree.common.properties.GtProps;
import com.gumtree.web.cookie.CookieHandlerInterceptor;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.DefaultCookieResolver;
import com.gumtree.web.cookie.GumtreeCookieProperty;
import com.gumtree.web.cookie.cutters.CookieArgumentResolver;
import com.gumtree.web.cookie.cutters.LoginCallbackCookieCutter;
import com.gumtree.web.cookie.cutters.abtest.AbExperimentsCookieCutter;
import com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieCutter;
import com.gumtree.web.cookie.cutters.appbanner.AppBannerCookieHelper;
import com.gumtree.web.cookie.cutters.capi.CapiAuthenticationCookieCutter;
import com.gumtree.web.cookie.cutters.messagecentre.MessageCentreCookieCutter;
import com.gumtree.web.cookie.cutters.permanent.PermanentCookieCutter;
import com.gumtree.web.cookie.cutters.session.SessionCookieCutter;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookieCutter;
import com.gumtree.web.cookie.cutters.userinteraction.UserInteractionCookieCutter;
import com.gumtree.web.cookie.cutters.userpreferences.ShortTermUserPrefCookieCutter;
import com.gumtree.web.cookie.cutters.userpreferences.UserAdPreferenceCookieCutter;
import com.gumtree.web.cookie.cutters.userpreferences.UserPreferencesCookieCutter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CookieConfig {

    private String cookieDomain = GtProps.getStr(GumtreeCookieProperty.COOKIES_DOMAIN);

    @Bean
    public CookieResolver cookieResolver() {
        return new DefaultCookieResolver();
    }

    @Bean
    public CookieHandlerInterceptor cookieHandlerInterceptor() {
        return new CookieHandlerInterceptor();
    }

    @Bean
    public CookieArgumentResolver cookieArgumentResolver() {
        return new CookieArgumentResolver(cookieResolver());
    }

    @Bean
    public AbExperimentsCookieCutter abExperimentsCookieCookieCutter() {
        return new AbExperimentsCookieCutter(cookieDomain);
    }

    @Bean
    public PermanentCookieCutter permanentCookieCutter() {
        return new PermanentCookieCutter(cookieDomain);
    }

    @Bean
    public SessionCookieCutter sessionCookieCutter() {
        return new SessionCookieCutter(cookieDomain);
    }

    @Bean
    public UserPreferencesCookieCutter userPreferencesCookieCutter() {
        return new UserPreferencesCookieCutter(cookieDomain);
    }

    @Bean
    public ShortTermUserPrefCookieCutter shortTermUserPrefCookieCutter() {
        return new ShortTermUserPrefCookieCutter(cookieDomain);
    }

    @Bean
    public AppBannerCookieHelper appBannerCookieHelper() {
        return new AppBannerCookieHelper(cookieResolver());
    }

    @Bean
    public AppBannerCookieCutter appBannerCookieCutter() {
        return new AppBannerCookieCutter(cookieDomain);
    }

    @Bean
    public ThreatMetrixCookieCutter threatMetrixCookieCutter(){
        return new ThreatMetrixCookieCutter(cookieDomain);
    }

    @Bean
    public LoginCallbackCookieCutter loginCallbackCookieCutter(){
        return new LoginCallbackCookieCutter(cookieDomain);
    }

    @Bean
    public MessageCentreCookieCutter messageCentreCookieCutter() {
        return new MessageCentreCookieCutter(cookieDomain);
    }

    @Bean
    public UserAdPreferenceCookieCutter userAdPreferenceCookieCutter() {
        return new UserAdPreferenceCookieCutter(cookieDomain);
    }

    @Bean
    public UserInteractionCookieCutter userInteractionCookieCutter() {
        return new UserInteractionCookieCutter(cookieDomain);
    }

    @Bean
    public CapiAuthenticationCookieCutter capiAuthenticationCookieCutter() {
        return new CapiAuthenticationCookieCutter(cookieDomain);
    }
}
