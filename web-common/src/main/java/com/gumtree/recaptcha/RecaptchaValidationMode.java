package com.gumtree.recaptcha;

import com.gumtree.common.properties.GtProps;
import com.gumtree.config.CommonProperty;

public enum RecaptchaValidationMode {
    DISABLED("disabled"), NORMAL("normal"), STRICT("strict");

    private String key;

    RecaptchaValidationMode(String key) {
        this.key = key;
    }

    public static RecaptchaValidationMode getValidationMode() {
        String propValue = GtProps.getStr(CommonProperty.RECAPTCHA_MODE);
        for (RecaptchaValidationMode mode : RecaptchaValidationMode.values()) {
            if (propValue.equals(mode.key)) return mode;
        }
        return RecaptchaValidationMode.NORMAL;
    }
}
