package com.gumtree.api;

import org.joda.time.DateTime;

import java.util.ArrayList;
import java.util.List;

/**
 * Builder for Bushfire api Ad
 */
public class ApiAdBuilder {

    private Ad advert = new Ad();

    public ApiAdBuilder() {
    }

    public static ApiAdBuilder advert() {
        return new ApiAdBuilder();
    }

    public ApiAdBuilder with() {
        return this;
    }

    public ApiAdBuilder description(String description) {
        advert.setDescription(description);
        return this;
    }

    public ApiAdBuilder title(String title) {
        advert.setTitle(title);
        return this;
    }

    public ApiAdBuilder status(AdStatus advertStatus) {
        advert.setStatus(advertStatus);
        return this;
    }

    public ApiAdBuilder id(Long id) {
        advert.setId(id);
        return this;
    }

    public Ad build() {
        return advert;
    }

    public ApiAdBuilder liveDate(DateTime dateTime) {
        advert.setLiveDate(dateTime);
        return this;
    }

    public ApiAdBuilder expiredDate(DateTime dateTime) {
        advert.setArchivedDate(dateTime);
        return this;
    }

    public ApiAdBuilder locationId(long locationId) {
        advert.setLocationId(locationId);
        return this;
    }

    public ApiAdBuilder locations(long... locationIds) {
        List<Location> locations = new ArrayList<Location>();
        for (long locationId : locationIds) {
            Location location = new Location();
            location.setId(locationId);
            locations.add(location);
        }
        advert.setLocations(locations.toArray(new com.gumtree.api.Location[]{}));
        return this;
    }

    public ApiAdBuilder categoryId(long categoryId) {
        advert.setCategoryId(categoryId);
        return this;
    }

    public ApiAdBuilder repliesEmail(String repliesEmail) {
        advert.setRepliesEmail(repliesEmail);
        return this;
    }

    public ApiAdBuilder lastModifiedDate(DateTime dateTime) {
        advert.setLastModifiedDate(dateTime);
        return this;
    }

    public ApiAdBuilder creationDate(DateTime creationDate) {
        advert.setCreationDate(creationDate);
        return this;
    }

    public ApiAdBuilder bumpUpCount(long bumpAdCount) {
        advert.setBumpupCount(bumpAdCount);
        return this;
    }
}
