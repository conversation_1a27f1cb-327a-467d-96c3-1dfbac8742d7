package com.gumtree.api;


import com.netflix.hystrix.exception.HystrixRuntimeException;

public final class HystrixUtils {
    private HystrixUtils() {
    }

    public static Throwable unwrapThrowable(Throwable throwable) {
        if (throwable != null && throwable.getCause() != null && throwable instanceof HystrixRuntimeException) {
            return unwrapThrowable(throwable.getCause());
        } else {
            return throwable;
        }
    }
}
