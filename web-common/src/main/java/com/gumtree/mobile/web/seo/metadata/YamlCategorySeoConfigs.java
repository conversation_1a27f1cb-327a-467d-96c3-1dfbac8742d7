package com.gumtree.mobile.web.seo.metadata;

import com.google.common.base.Optional;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import javax.annotation.Nonnull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.AD_COUNT;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.CATEGORY;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.IN_LOCATION;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.LOCATION;

/**
 * Implementation of the {@link CategorySeoConfig} that loads category settings from YAML file
 */
public class YamlCategorySeoConfigs implements CategorySeoConfig {
    private static final Pattern OPT_KEY_ITEM_PATTERN = Pattern.compile("OPT\\((.*)\\)");
    private static final String SEO_H1_KEY = "seoH1";
    private static final String SEO_H1_DEFAULT_VALUE =
            AD_COUNT.getPlaceHolder() + " ads in " + CATEGORY.getPlaceHolder() + " " + IN_LOCATION.getPlaceHolder();
    private Map<String, Map<Set<String>, Map<String, String>>> categories;

    public Map<String, Map<Set<String>, Map<String, String>>> getCategories() {
        return categories;
    }

    public void setCategories(Map<String, Map<Set<String>, Map<String, String>>> categories) {
        this.categories = processPageKeysWithDirectives(addSeoH1ToCategories(categories));
    }

    private static Map<String, Map<Set<String>, Map<String, String>>> addSeoH1ToCategories(
            Map<String, Map<Set<String>, Map<String, String>>> categories) {
        for (Map<Set<String>, Map<String, String>> attrsToProps: categories.values()) {

            for (Map<String, String> props: attrsToProps.values()) {
                if (!props.containsKey(SEO_H1_KEY)) {
                    props.put(SEO_H1_KEY, SEO_H1_DEFAULT_VALUE);
                }
            }
        }
        return categories;
    }

    /**
     * Page keys can contains page key items that contains directives like OPT which mark the key item as optional. This method process
     * those keys.
     *
     * @param categories
     * @return
     */
    private static Map<String, Map<Set<String>, Map<String, String>>> processPageKeysWithDirectives(
            Map<String, Map<Set<String>, Map<String, String>>> categories) {

        Map<String, Map<Set<String>, Map<String, String>>> result = new HashMap<>();

        for (String category: categories.keySet()) {
            Map<Set<String>, Map<String, String>> catConfig = categories.get(category);

//            final Map<Set<String>, Map<String, String>> newCatConfig = new HashMap<>();
//            for (Map.Entry<Set<String>, Map<String, String>> pageConfig: catConfig.entrySet()) {
//                List<Set<String>> pages = generatePageKeys(pageConfig.getKey(), 0);
//                pages.stream()
//                        .filter(page -> !newCatConfig.containsKey(page))
//                        .forEach(page -> newCatConfig.put(page, pageConfig.getValue()));
//            }

            result.put(category, catConfig);
        }

        return result;
    }

    /**
     * {@inheritDoc}
     */
    public Map<String, String> getPageConfig(List<String> categoryPath, Set<String> pageId) {
        Optional<Map<String, String>> result = Optional.absent();
        if (categoryPath != null && !categoryPath.isEmpty()) {
            int levels = categoryPath.size();
            int currentLevel = 1;
            for (String category: categoryPath) {
                Optional<Map<String, String>> pageConfig = getPageConfig(category, pageId);
                if (pageConfig.isPresent()) {
                    result = pageConfig;
                }

                if (currentLevel == levels) { // is leaf hierarchy
                    pageConfig = getPageConfig(category, getPageIdWithoutCategory(pageId));
                    if (pageConfig.isPresent()) {
                        result = pageConfig;
                    }
                }

                currentLevel++;
            }
        }

        return result.or(getDefaultPageConfig(pageId)).or(Optional.of(Collections.<String, String>emptyMap())).get();
    }

    /**
     * Take a pageKeys and expand it if some sub-keys are wild card keys like "key?".
     *
     * Example:
     *
     * pageKey = Set("a", "b?", "c?") where "b?" means that b is option sub-key should get expanded to:
     * List(Set(a), Set(a,b), Set(a,b,c), Set(a,c))
     *
     * @param pageKey the key to expand
     * @return all possible interpretations of the supplied pageKey
     */
    public static List<Set<String>> generatePageKeys(@Nonnull Set<String> pageKey, int level) {
        java.util.Optional<String> optionalKey = pageKey.stream().filter(YamlCategorySeoConfigs::isOptionalKey).findFirst();

        if (optionalKey.isPresent()) {
            String keyToReplace = optionalKey.get();
            Set<String> withoutKey = pageKey.stream().filter(key -> !key.equals(keyToReplace)).collect(Collectors.toSet());

            List<Set<String>> result = new ArrayList<>();
            result.addAll(generatePageKeys(withoutKey, level + 1));
            result.addAll(generatePageKeys(addItem(withoutKey, keyItemWithDirectiveRemoved(keyToReplace)), level + 1));

            if (level == 0) {
                // at least one optional key item must always be preserved in the generated key
                Set<String> keyWithoutOptionalItems = pageKey.stream().filter(pk -> !isOptionalKey(pk)).collect(Collectors.toSet());
                result.remove(keyWithoutOptionalItems);
            }

            return result;
        } else {

            return Collections.singletonList(pageKey);
        }
    }

    private static Set<String> addItem(@Nonnull Set<String> set, @Nonnull String item) {
        HashSet<String> result = new HashSet<>(set);
        result.add(item);
        return result;
    }

    static boolean isOptionalKey(@Nonnull  String keyItem) {
        return OPT_KEY_ITEM_PATTERN.matcher(keyItem).matches();
    }

    public static String keyItemWithDirectiveRemoved(@Nonnull String keyItemWithDirectives) {
        Matcher matcher = OPT_KEY_ITEM_PATTERN.matcher(keyItemWithDirectives);
        if (matcher.matches()) {
            return matcher.group(1);
        } else {
            return keyItemWithDirectives;
        }
    }

    private Optional<Map<String, String>> getPageConfig(String name, Set<String> pageId) {
        Map<Set<String>, Map<String, String>> categoryConfig = categories.get(name);
        if (categoryConfig != null) {
            Map<String, String> pageConfig = categoryConfig.get(pageId);
            if (pageConfig != null) {
                return Optional.of(pageConfig);
            }
        }

        return Optional.absent();
    }

    private Set<String> getPageIdWithoutCategory(Set<String> pageId) {
        if (pageId != null && pageId.contains(CATEGORY.getKey())) {
            return Sets.difference(pageId, Sets.newHashSet(CATEGORY.getKey()));
        }

        return pageId;
    }

    private Optional<Map<String, String>> getDefaultPageConfig(Set<String> pageId) {
        Map<String, String> defaultMapping = Maps.newHashMap();
        if (pageId.contains(CATEGORY.getKey()) && pageId.contains(LOCATION.getKey())) {
            defaultMapping.put(SEO_H1_KEY, SEO_H1_DEFAULT_VALUE);
            defaultMapping.put(SEOMetadata.SEO_TITLE, CATEGORY.getPlaceHolder() + " " + IN_LOCATION.getPlaceHolder() + " - Gumtree");
            defaultMapping.put(SEOMetadata.SEO_DESCRIPTION, "Find the latest " + CATEGORY.getPlaceHolder() + " " +
                    IN_LOCATION.getPlaceHolder() + " on Gumtree. See classified ads for " + CATEGORY.getPlaceHolder() + ".");
        }
        return defaultMapping.isEmpty()
                ? Optional.<Map<String, String>>absent()
                : Optional.of(defaultMapping);
    }
}
