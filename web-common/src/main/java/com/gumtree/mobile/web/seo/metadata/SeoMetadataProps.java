package com.gumtree.mobile.web.seo.metadata;

import com.google.common.base.Optional;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeValueMetadata;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.domain.ad.Advert;
import org.apache.commons.lang.StringUtils;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.AD_COPY;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.AD_COUNT;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.AD_TITLE;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.CATEGORY;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.IN_LOCATION;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.LOCATION;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.PAGINATION;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.SEO_METADATA_PROPERTY_KEY_MAP;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.TOP_CAR_MAKES;
import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.TOP_CAR_MODELS;

public class SeoMetadataProps {
    private static final String AD_COUNT_FORMAT_PATTERN = "###,###,###";

    private Map<SEOMetadataProperty, String> properties;
    private Category category;

    SeoMetadataProps() {
    }

    public SeoMetadataProps(Category category) {
        properties = new HashMap<>();
        this.category = category;
        addPropertyIfNotNull(CATEGORY, category.getSeoDisplayName());
        properties.put(TOP_CAR_MAKES, "");
        properties.put(TOP_CAR_MODELS, "");
    }

    public Category getCategory() {
        return category;
    }

    public Map<SEOMetadataProperty, String> getProperties() {
        return properties;
    }

    public SeoMetadataProps popularSearchPage() {
        properties.put(SEOMetadataProperty.POPULAR_SEARCH, null);
        return this;
    }

    public SeoMetadataProps vipPage() {
        properties.put(SEOMetadataProperty.VIP, null);
        return this;
    }

    public SeoMetadataProps employerPage(String employer) {
        properties.put(SEOMetadataProperty.EMPLOYER, employer);
        return this;
    }

    public SeoMetadataProps withLocation(String locationDisplayName) {
        if (StringUtils.isNotBlank(locationDisplayName)) {
            properties.put(LOCATION, Location.UK_DISPLAY_NAME.equals(locationDisplayName) ? "" : locationDisplayName);
            properties.put(IN_LOCATION, Location.UK_DISPLAY_NAME.equals(locationDisplayName) ? "" : "in " + locationDisplayName);
        }
        return this;
    }

    public SeoMetadataProps withTopVehicleMakes(List<String> topMakes) {
        addPropertyIfNotNull(TOP_CAR_MAKES, buildTopMakes(topMakes));
        return this;
    }

    public SeoMetadataProps withTopVehicleModels(List<String> topModels) {
        properties.put(TOP_CAR_MODELS, buildTopModels(topModels));
        return this;
    }

    public SeoMetadataProps withNonUkLocation(Location location) {
        if (!Location.UK.equals(location.getName())) {
            withLocation(location.getDisplayName());
        }
        return this;
    }

    public SeoMetadataProps withAdvert(Advert advert) {
        addPropertyIfNotNull(AD_TITLE, advert.getTitle());
        addPropertyIfNotNull(AD_COPY, StringUtils.substring(advert.getDescription(), 0, 275));
        return this;
    }

    public SeoMetadataProps withSearchTerm(String searchTerm) {
        if (StringUtils.isNotBlank(searchTerm)) {
            properties.put(SEOMetadataProperty.SEARCH_TERM, searchTerm);
            properties.put(SEOMetadataProperty.SEARCH_TERM_CAPITALIZED, StringUtils.capitalize(searchTerm));
        } else {
            properties.put(SEOMetadataProperty.BLANK_SEARCH_TERM, null);
        }

        return this;
    }

    public SeoMetadataProps withAdCount(Integer adCount) {
        return withAdCount(new DecimalFormat(AD_COUNT_FORMAT_PATTERN).format(adCount));
    }

    public SeoMetadataProps withAdCount(String adCount) {
        addPropertyIfNotNull(AD_COUNT, adCount);
        return this;
    }

    public SeoMetadataProps withPagination(String currentPage, String numberOfPages) {
        addPropertyIfNotNull(PAGINATION, buildPagination(currentPage, numberOfPages));
        return this;
    }

    public SeoMetadataProps withPagination(Integer currentPage, Integer numberOfPages) {
        if (currentPage != null && numberOfPages != null) {
            withPagination(currentPage.toString(), numberOfPages.toString());
        }
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        SeoMetadataProps that = (SeoMetadataProps) o;

        if (category != null ? !category.equals(that.category) : that.category != null) {
            return false;
        }

        if (properties != null ? !properties.equals(that.properties) : that.properties != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = properties != null ? properties.hashCode() : 0;
        result = 31 * result + (category != null ? category.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "SeoMetadataProps{"
                + "properties=" + properties
                + ", category=" + category
                + '}';
    }

    static String buildTopMakes(List<String> makes) {
        if (makes != null && !makes.isEmpty()) {
            String topMakesStr = makes.stream().collect(Collectors.joining(", "));
            return " Search by used car makes " + topMakesStr + " and more.";
        }

        return null;
    }

    static String buildTopModels(List<String> models) {
        if (models != null && !models.isEmpty()) {
            return " " + models.stream().collect(Collectors.joining(", "));
        }

        return null;
    }

    private void addPropertyIfNotNull(SEOMetadataProperty property, String value) {
        if (StringUtils.isNotBlank(value)) {
            properties.put(property, value);
        }
    }

    private String buildPagination(String currentPage, String numberOfPages) {
        if (StringUtils.isNotBlank(currentPage) && StringUtils.isNotBlank(numberOfPages) &&
                !"1".equals(numberOfPages) && !"1".equals(currentPage)) {
            return currentPage + "/" + numberOfPages;
        }
        return null;
    }

    public SeoMetadataProps withUserSearchRefinement(Map<String, String> searchRefinement) {
        for (Map.Entry<String, String> attr: searchRefinement.entrySet()) {
            SEOMetadataProperty seoMetadataProperty = SEO_METADATA_PROPERTY_KEY_MAP.get(attr.getKey());

            if (seoMetadataProperty != null && attr.getValue() != null) {
                String displayValue = getDisplayValue(attr.getKey(), attr.getValue(), category);
                addPropertyIfNotNull(seoMetadataProperty, displayValue);
            }
        }

        return this;
    }

    private String getDisplayValue(final String attributeName, final String attributeValue, Category category) {
        Optional<AttributeMetadata> attributeMetadata = category.findAttribute(attributeName);
        if (attributeMetadata.isPresent()) {
            return attributeMetadata.get().getSrp().getFilters().stream()
                    .flatMap(filter -> filter.getValues().stream())
                    .filter(supportedValue -> attributeValue.equals(supportedValue.getValue()))
                    .map(AttributeValueMetadata::getLabel)
                    .findAny()
                    .orElse(null);
        } else {
            return null;
        }
    }
}
