package com.gumtree.mobile.web.category;

import com.gumtree.api.category.domain.Category;

import java.util.Collections;
import java.util.List;

public class BrowseCategory {

    private Long id;
    private String name;
    private String seoName;
    private Boolean children;
    private Boolean selected;
    private List<BrowseCategory> childrenItems;

    public BrowseCategory(Category category, List<BrowseCategory> childrenItems, boolean selected) {
        this.id = category.getId();
        this.name = category.getName();
        this.seoName = category.getSeoName();
        this.children = !isRootCategory(category) && !category.getChildren().isEmpty() && !category.visibleChildCategories().isEmpty();
        this.selected = selected;
        this.childrenItems = childrenItems;
    }

    public BrowseCategory(Category category) {
        this(category, Collections.<BrowseCategory>emptyList(), false);
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getSeoName() {
        return seoName;
    }

    public Boolean getChildren() {
        return children;
    }

    public boolean isSelected() {
        return selected;
    }

    public boolean isDrilled() {
        return !childrenItems.isEmpty();
    }

    public List<BrowseCategory> getChildrenItems() {
        return childrenItems;
    }

    private boolean isRootCategory(Category category) {
        return category.getParentId() == null;
    }
}
