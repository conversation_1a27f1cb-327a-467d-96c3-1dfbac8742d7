package com.gumtree.mobile.web.seo.metadata;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.category.domain.Category;
import org.springframework.context.ApplicationListener;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

/**
 * Default factory for the {@link SEOMetadata}
 */
public class DefaultSeoMetadataFactory
        implements SeoMetadataFactory, ApplicationListener<CategoryConfigModifiedEvent> {

    private CategoryModel categoryService;
    private CategorySeoConfig categorySeoConfig;

    public DefaultSeoMetadataFactory(CategorySeoConfig categorySeoConfig, CategoryModel categoryService) {
        this.categorySeoConfig = categorySeoConfig;
        this.categoryService = categoryService;
    }

    @Override
    public SEOMetadata newInstance(SeoMetadataProps pageProps) {
        Assert.notNull(pageProps);
        List<Category> categoryHierarchy = categoryService.getFullPath(pageProps.getCategory().getId());

        return new SEOMetadata.Builder()
                .withCategoryConfig(categorySeoConfig)
                .withCategoryHierarchy(asListOfStrings(categoryHierarchy))
                .withPageProps(pageProps)
                .build();
    }

    @Override
    public void onApplicationEvent(CategoryConfigModifiedEvent event) {
        this.categorySeoConfig = event.getCategoryConfigs();
    }

    private List<String> asListOfStrings(List<Category> categoryHierarchy) {
        List<String> hierarchy = new ArrayList<String>();
        for (Category category: categoryHierarchy) {
            hierarchy.add(category.getSeoName());
        }
        return  hierarchy;
    }
}
