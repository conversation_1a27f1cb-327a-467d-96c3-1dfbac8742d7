package com.gumtree.mobile.web.seo.metadata;

import com.google.common.base.MoreObjects;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.gumtree.mobile.web.seo.metadata.SEOMetadataProperty.SEARCH_TERM;

public class SEOMetadata {
    public static final String SEO_TITLE = "seoTitle";
    public static final String SEO_DESCRIPTION = "seoDescription";
    public static final String SEO_H1 = "seoH1";

    private Map<String, String> properties;

    public SEOMetadata(Map<String, String> properties) {
        this.properties = properties;
    }

    public String getTitle() {
        return getProperty(SEO_TITLE, "Gumtree.com");
    }

    public String getDescription() {
        return getProperty(SEO_DESCRIPTION, "Gumtree.com");
    }

    public String getH1() {
        return getProperty(SEO_H1, "");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        SEOMetadata seoMetadata = (SEOMetadata) o;

        if (properties != null ? !properties.equals(seoMetadata.properties) : seoMetadata.properties != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        return properties != null ? properties.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "SEOPage{"
                + "properties=" + properties
                + '}';
    }

    private String getProperty(String name, String defaultValue) {
        String value = properties.get(name);
        return value == null ? defaultValue : value;
    }

    public static class Builder {
        private Map<String, String> placeholders;
        private Set<String> pageId;
        private List<String> categoryHierarchy;

        private CategorySeoConfig categorySeoConfig;

        public Builder() {
            placeholders = new HashMap<>();
            pageId = new HashSet<>();
        }

        public Builder withCategoryHierarchy(List<String> categoryHierarchy) {
            this.categoryHierarchy = categoryHierarchy;
            return this;
        }

        public Builder withCategoryConfig(CategorySeoConfig categoryConfig) {
            this.categorySeoConfig = categoryConfig;
            return this;
        }

        public Builder withPageProps(SeoMetadataProps props) {
            if (!props.getProperties().isEmpty()) {
                for (Map.Entry<SEOMetadataProperty, String> prop : props.getProperties().entrySet()) {
                    if (prop.getKey().hasKey()) {
                        pageId.add(prop.getKey().getKey());
                    }

                    if (prop.getKey().hasPlaceholder()) {
                        // we are escaping only user entered stuff - location & keyword
                        placeholders.put(prop.getKey().getPlaceHolder(), prop.getValue());
                    }
                }
            }

            return this;
        }

        public SEOMetadata build() {
            Assert.notNull(categoryHierarchy);
            return new SEOMetadata(buildPageProperties());
        }

        public Map<String, String> getPlaceholders() {
            return placeholders;
        }

        public List<String> getCategoryHierarchy() {
            return categoryHierarchy;
        }

        public CategorySeoConfig getCategorySeoConfig() {
            return categorySeoConfig;
        }


        private Map<String, String> buildPageProperties() {
            Map<String, String> result = new HashMap<String, String>();
            Map<String, String> categoryConfig = categorySeoConfig.getPageConfig(categoryHierarchy, pageId);

            if (categoryConfig != null && !categoryConfig.isEmpty()) {
                if (!placeholders.isEmpty()) {
                    for (Map.Entry<String, String> entry : categoryConfig.entrySet()) {
                        //
                        if (SEOMetadata.SEO_H1.equals(entry.getKey()) &&
                                pageId.contains(SEARCH_TERM.getKey()) &&
                                // required because of a original hack which actually broke configs that do not need that hack :(((
                                !(entry.getValue().contains("[Search-term]") || entry.getValue().contains("[Search-term-capitalized]"))) {
                            result.put(entry.getKey(), replacePlaceHolders("[Ad-count] ads for [Search-term] in [Category] [In-Location]"));
                            // hacky hack, should be in .yaml file
                        } else {
                            result.put(entry.getKey(), replacePlaceHolders(entry.getValue()));
                        }
                    }
                } else {
                    result = categoryConfig;
                }
            }

            return result;
        }

        private String replacePlaceHolders(String textWithPlaceholders) {
            if (StringUtils.isBlank(textWithPlaceholders)) {
                return "";
            }

            for (Map.Entry<String, String> entry : placeholders.entrySet()) {
                textWithPlaceholders = textWithPlaceholders.replace(entry.getKey(), MoreObjects.firstNonNull(entry.getValue(), ""));
            }

            return textWithPlaceholders.replaceAll("\\s\\s", " ").trim();
        }
    }
}
