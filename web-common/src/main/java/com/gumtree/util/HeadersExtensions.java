package com.gumtree.util;

import com.gumtree.web.abtest.growthbook.ClientExperiments;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class HeadersExtensions {

    public static final String EXPERIMENTS_HEADER = "experiments";
    /**
     * Get the Experiments/AB Tests of the client, provided in the client request headers
     * The method filters only valid/recognised experiments and their valid variants
     * @param request from the request
     * @return - the valid/recognised ClientExperiments with their variant
     */
    public static ClientExperiments getClientExperiments(HttpServletRequest request) {
        String experimentsHeaderValue =  request.getHeader(EXPERIMENTS_HEADER);
        if (experimentsHeaderValue == null || experimentsHeaderValue.trim().isEmpty()) {
            return new ClientExperiments(new HashMap<>());
        }

        Map<ClientExperiments.ExperimentEnum, ClientExperiments.VariantEnum> experiments = Arrays.stream(experimentsHeaderValue.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toMap(
                        s -> {
                            String[] parts = s.split("[.\\-]");
                            List<String> expParts = Arrays.asList(parts).subList(0, parts.length > 1 ? parts.length - 1 : parts.length);
                            String expKey = String.join("-", expParts);
                            return ClientExperiments.ExperimentEnum.fromString(expKey);
                        },
                        s -> {
                            String[] parts = s.split("[.\\-]");
                            if (parts.length > 1) {
                                return ClientExperiments.VariantEnum.fromString(parts[parts.length - 1]);
                            } else {
                                return ClientExperiments.VariantEnum.UNKNOWN;
                            }
                        },
                        (key, value) -> value
                ));

        experiments.keySet().removeIf(exp -> exp == ClientExperiments.ExperimentEnum.UNKNOWN);

        return new ClientExperiments(experiments);
    }
}
