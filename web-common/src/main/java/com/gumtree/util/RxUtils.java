package com.gumtree.util;

import com.gumtree.web.api.WebApiErrorException;
import com.gumtree.web.api.WebApiErrorResponse;
import com.gumtree.web.api.WebApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.async.DeferredResult;
import rx.Single;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static com.gumtree.util.ExceptionUtils.toShortMessage;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;

public abstract class RxUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(RxUtils.class);
    private static final WebApiErrorResponse DEFAULT_ERROR =
            new WebApiErrorResponse(INTERNAL_SERVER_ERROR, "internal-error", "Something went wrong");

    private RxUtils() {
    }

    public static <T> ResponseEntity singleToResponseEntity(Single<T> single, String errorMessage) {
        return single.map(result -> new ResponseEntity(result, HttpStatus.OK))
                .onErrorReturn(error -> handleError(error, errorMessage))
                .toBlocking().value();
    }

    public static <T> ResponseEntity singleToWebApiResponseEntity(Single<T> single, String errorMessage) {
        return single.map(result -> new ResponseEntity(new WebApiResponse<>(result), HttpStatus.OK))
                .onErrorReturn(error -> handleError(error, errorMessage))
                .toBlocking().value();
    }

    public static <T> ResponseEntity singleToWebApiResponseEntity(Single<T> single) {
        return singleToWebApiResponseEntity(single, "Unexpected error");
    }

    public static <T> ResponseEntity singleOfOptToResponseEntity(Single<Optional<T>> single, String noValueMessage) {
        return single
                .map(result -> {
                    if (result.isPresent()) {
                        return new ResponseEntity(result.get(), HttpStatus.OK);
                    } else {
                        WebApiErrorResponse errorResponse = new WebApiErrorResponse(HttpStatus.NOT_FOUND, "not-found", noValueMessage);
                        return new ResponseEntity(errorResponse, HttpStatus.NOT_FOUND);
                    }
                })
                .onErrorReturn(error -> handleError(error, "Unexpected error"))
                .toBlocking().value();
    }

    /**
     * @deprecated no really deprecated but to be able to use it you need to mark all HTTP filter involved in request processing
     * as async filter in web.xml. I wasn't sure(have time) to check what are implications of that so for now we use methods that return
     * ResponseEntity directly. Once I will have some time I may look at it again :) Also will need to be tested :)
     */
    public static <T> DeferredResult<ResponseEntity> singleToDeferredResponseEntity(Single<T> single) {
        DeferredResult<ResponseEntity> deferred = new DeferredResult<>();

        single.subscribe(
                result -> deferred.setResult(new ResponseEntity(result, HttpStatus.OK)),
                err -> handleError(err, deferred));

        return deferred;
    }

    /**
     * @deprecated use one that returns DeferredResult<ResponseEntity>
     * <p>
     * Note: Using DeferredResult instead of CompletableFuture because DeferredResult is supported out of box while CompletableFuture
     * will need implementation HandlerMethodReturnValueHandler which we haven't done. So if you want to use this method you need
     * to implement HandlerMethodReturnValueHandler first.
     */
    public static <T> CompletableFuture<ResponseEntity> singleToWebApiResponseFuture(Single<T> single) {
        CompletableFuture<ResponseEntity> future = new CompletableFuture<>();

        single.subscribe(
                result -> future.complete(new ResponseEntity(new WebApiResponse<>(result), HttpStatus.OK)),
                err -> handleError(err, future));

        return future;
    }

    private static void handleError(Throwable error, CompletableFuture<ResponseEntity> future) {
        if (error instanceof WebApiErrorException) {
            log((WebApiErrorException) error);
            WebApiErrorResponse errorResponse = ((WebApiErrorException) error).getError();
            future.complete(new ResponseEntity(errorResponse, errorResponse.getStatus()));
        } else {
            LOGGER.warn("Unexpected error.", ExceptionUtils.toShortMessage(error));
            future.complete(new ResponseEntity(DEFAULT_ERROR, INTERNAL_SERVER_ERROR));
        }
    }

    private static ResponseEntity handleError(Throwable error, String errorMessage) {
        if (error instanceof WebApiErrorException) {
            log((WebApiErrorException) error);
            WebApiErrorResponse errorResponse = ((WebApiErrorException) error).getError();
            return new ResponseEntity(errorResponse, errorResponse.getStatus());
        }

        LOGGER.warn(errorMessage + " because of " + toShortMessage(error));
        return new ResponseEntity(
                new WebApiErrorResponse(INTERNAL_SERVER_ERROR, "internal-error", errorMessage),
                INTERNAL_SERVER_ERROR);
    }

    private static void handleError(Throwable error, DeferredResult<ResponseEntity> deferred) {
        if (error instanceof WebApiErrorException) {
            log((WebApiErrorException) error);
            WebApiErrorResponse errorResponse = ((WebApiErrorException) error).getError();
            deferred.setResult(new ResponseEntity(errorResponse, errorResponse.getStatus()));
        }

        LOGGER.warn("Unexpected error.", ExceptionUtils.toShortMessage(error));
        deferred.setResult(new ResponseEntity(DEFAULT_ERROR, HttpStatus.INTERNAL_SERVER_ERROR));
    }

    private static void log(WebApiErrorException webApiErrorException) {
        switch (webApiErrorException.getLogLevel()) {
            case DEBUG:
                LOGGER.debug(ExceptionUtils.toShortMessage(webApiErrorException));
                break;
            case INFO:
                LOGGER.info(ExceptionUtils.toShortMessage(webApiErrorException));
                break;
            case WARN:
                LOGGER.warn(ExceptionUtils.toShortMessage(webApiErrorException));
                break;
            case ERROR:
                LOGGER.error("A WebApiErrorException occurred.", webApiErrorException);
                break;
        }
    }
}
