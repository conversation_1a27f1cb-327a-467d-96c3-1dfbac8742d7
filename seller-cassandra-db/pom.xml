<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.gumtree.web.seller</groupId>
    <artifactId>seller-cassandra-db</artifactId>
    <name>Gumtree Seller Cassandra Db</name>

    <parent>
        <groupId>com.gumtree.web</groupId>
        <artifactId>seller</artifactId>
        <version>3.0-SNAPSHOT</version>
    </parent>

    <properties>
        <jib.skip>true</jib.skip>
    </properties>

    <build>
        <plugins>
            <plugin>
                <artifactId>exec-maven-plugin</artifactId>
                <groupId>org.codehaus.mojo</groupId>
                <configuration>
                    <executable>${basedir}/src/main/resources/seller-cassandra-config.py</executable>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.vafer</groupId>
                <artifactId>jdeb</artifactId>
                <version>1.3</version>
                <executions>
                    <execution>
                        <!-- disable default execution inherited from parent -->
                        <id>default</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>seller-cassandra-db</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jdeb</goal>
                        </goals>
                        <configuration>
                            <dataSet>
                                <data>
                                    <type>file</type>
                                    <src>${project.build.directory}/classes/seller-cassandra-config.py</src>
                                    <dst>seller-cassandra-config.py</dst>
                                    <mapper>
                                        <type>perm</type>
                                        <user>root</user>
                                        <group>root</group>
                                        <filemode>0755</filemode>
                                    </mapper>
                                </data>
                            </dataSet>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
