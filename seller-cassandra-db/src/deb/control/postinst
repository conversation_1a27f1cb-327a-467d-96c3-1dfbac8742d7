#!/bin/bash

set -ue

props_file="/etc/gumtree/seller-server.properties"

function configure_cassandra() {
    echo "Getting cassandra host and port from seller.properties..."
    cass_host=$(awk -F'[=:,]' '/gumtree.seller.db.cassandra.servers/ {print $2}' $props_file)
    cass_port=$(awk -F'[=:,]' '/gumtree.seller.db.cassandra.servers/ {print $3}' $props_file)
    cass_user=$(awk -F'[=:,]' '/gumtree.seller.db.cassandra.username/ {print $2}' $props_file)
    cass_pass=$(awk -F'[=:,]' '/gumtree.seller.db.cassandra.password/ {print $2}' $props_file)

    echo "Running cassandra config..."
    if [ x"$cass_user" == x ]; then
        /usr/bin/env python /usr/lib/gumtree/gumtree/seller-cassandra-db/seller-cassandra-config.py --host $cass_host --port $cass_port
    else
        /usr/bin/env python /usr/lib/gumtree/gumtree/seller-cassandra-db/seller-cassandra-config.py --host $cass_host --port $cass_port --user $cass_user --pass $cass_pass
    fi
}

case "$1" in
    configure)
        # setup keyspace
        configure_cassandra
        ;;
    abort-upgrade|abort-remove|abort-deconfigure)
        # nothing to do
        ;;
esac

