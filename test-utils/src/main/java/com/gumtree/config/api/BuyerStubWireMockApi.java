package com.gumtree.config.api;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.ServerSocket;

import static java.util.stream.IntStream.range;


public final class BuyerStubWireMockApi {
    protected static final Logger LOG = LoggerFactory.getLogger(BuyerStubWireMockApi.class);

    public static final int WIREMOCK_API_PORT = getAvailablePort(8000);

    private static final WireMockServer SERVER = new WireMockServer(WireMockConfiguration.wireMockConfig().port(WIREMOCK_API_PORT));

    static {
        SERVER.start();
        SERVER.resetMappings();
    }

    private BuyerStubWireMockApi() {
    }

    //TODO the refactor will replace this with dynamicHttpsPort on the wiremock.
    public static int getAvailablePort(int startPort) {
        int availablePort = range(startPort, 65000)
                .filter(BuyerStubWireMockApi::portAvailable)
                .findFirst()
                .orElseThrow(() -> {
                    LOG.error("Could not find Free port for tests.");
                    return new RuntimeException("Could not find Free port for tests.");
                });

        return availablePort;
    }

    public static boolean portAvailable(int i) {
        LOG.debug("Checking if a port {} is available", i);
        ServerSocket socket = null;
        try {
            socket = new ServerSocket(i);
            LOG.debug("Found available port: {}", i);
            return true;
        } catch (IOException e) {
            LOG.debug("Port {} is NOT available", i);
            return false;
        } finally {
            try {
                if (socket != null) socket.close();
            } catch (IOException e) {
                LOG.error("Failed to close socket", e);
                /// left empty ... no error catching.
            }
        }
    }

    public static WireMockServer getServer() {
        return SERVER;
    }
}
