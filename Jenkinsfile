#!groovy
import com.gumtree.jenkins.PipelineConfig
import com.gumtree.jenkins.ProjectType
import com.gumtree.jenkins.DeploymentType

def pipelineConfig = new PipelineConfig(
    projectName : 'seller',
    projectType : ProjectType.LEGACY_DEPLOYABLE_SERVER,
    deploymentType : DeploymentType.VM,
    releaseAppToGCP: true,
    javaVersion: '8',
    skipQATests: true
)

startDeliveryPipeline(pipelineConfig)
