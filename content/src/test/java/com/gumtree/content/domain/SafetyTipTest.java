package com.gumtree.content.domain;

import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.lessThan;
import static org.hamcrest.Matchers.not;

/**
 */
public class SafetyTipTest {

    @Test
    public void testEquals() {
        SafetyTip safetyTip1 = new SafetyTip();
        safetyTip1.setCategoryId(123L);
        safetyTip1.setPriority(1);
        SafetyTip safetyTip2 = new SafetyTip();
        safetyTip2.setCategoryId(123L);
        safetyTip2.setPriority(1);

        assertThat(safetyTip1, equalTo(safetyTip2));
        assertThat(safetyTip1.hashCode(), equalTo(safetyTip2.hashCode()));
    }

    @Test
    public void testPriorityNotEquals() {
        SafetyTip safetyTip1 = new SafetyTip();
        safetyTip1.setCategoryId(123L);
        safetyTip1.setPriority(1);
        SafetyTip safetyTip2 = new SafetyTip();
        safetyTip2.setCategoryId(123L);
        safetyTip2.setPriority(2);

        assertThat(safetyTip1, not(safetyTip2));
        assertThat(safetyTip1.hashCode(), not(safetyTip2.hashCode()));
    }

    @Test
    public void testCategoryIdNotEquals() {
        SafetyTip safetyTip1 = new SafetyTip();
        safetyTip1.setCategoryId(123L);
        safetyTip1.setPriority(1);
        SafetyTip safetyTip2 = new SafetyTip();
        safetyTip2.setCategoryId(456L);
        safetyTip2.setPriority(1);

        assertThat(safetyTip1, not(safetyTip2));
        assertThat(safetyTip1.hashCode(), not(safetyTip2.hashCode()));
    }

    @Test
    public void testPriorityCompares() {
        SafetyTip safetyTip1 = new SafetyTip();
        safetyTip1.setPriority(1);
        SafetyTip safetyTip2 = new SafetyTip();
        safetyTip2.setPriority(2);

        assertThat(safetyTip1.compareTo(safetyTip2), lessThan(0));
    }

    @Test
    public void testCategoryIdCompares() {
        SafetyTip safetyTip1 = new SafetyTip();
        safetyTip1.setPriority(1);
        safetyTip1.setCategoryId(123L);
        SafetyTip safetyTip2 = new SafetyTip();
        safetyTip2.setPriority(1);
        safetyTip2.setCategoryId(456L);

        assertThat(safetyTip1.compareTo(safetyTip2), lessThan(0));
    }
}
