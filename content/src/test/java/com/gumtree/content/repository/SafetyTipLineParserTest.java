package com.gumtree.content.repository;

import com.gumtree.common.util.delimited.DelimitedLineMapper;
import com.gumtree.content.domain.SafetyTip;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

/**
 */
public class SafetyTipLineParserTest {

    private DelimitedLineMapper<SafetyTip> delimitedLineMapper;

    @Before
    public void init() {
        delimitedLineMapper = mock(DelimitedLineMapper.class);
    }

    @Test
    public void extractKeyFromLine() {
        String[] line = new String[]{"1", "2", "test message"};
        Long key = new SafetyTipLineParser(delimitedLineMapper).key(line);
        assertThat(key, equalTo(1L));
    }

    @Test
    public void extractValueFromLine() {
        String[] line = new String[]{"1", "2", "test message"};
        SafetyTip value = new SafetyTipLineParser(delimitedLineMapper).value(line);
        verify(delimitedLineMapper).map(line);
    }

    @Test
    public void handleBadNumberFormat() {
        String[] line = new String[]{"1q", "2", "test message"};
        Long key = new SafetyTipLineParser(delimitedLineMapper).key(line);
        assertThat(key, nullValue());
    }
}
