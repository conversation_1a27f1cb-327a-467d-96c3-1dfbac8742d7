package com.gumtree.content.repository;


import com.gumtree.common.util.delimited.DelimitedLineMapper;
import com.gumtree.common.util.delimited.DelimitedMapLineMapper;
import com.gumtree.content.domain.SafetyTip;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 */
@Component
public class SafetyTipLineParser implements DelimitedMapLineMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(SafetyTipLineParser.class);

    private DelimitedLineMapper<SafetyTip> delimitedLineMapper;

    @Autowired
    public SafetyTipLineParser(DelimitedLineMapper<SafetyTip> delimitedLineMapper) {
        this.delimitedLineMapper = delimitedLineMapper;
    }

    public Long key(String[] line) {
        try {
            return Long.parseLong(line[0]);
        } catch (NumberFormatException e) {
            LOGGER.error("Unable to parse key", e);
        }
        return null;
    }

    public SafetyTip value(String[] line) {
        return delimitedLineMapper.map(line);
    }
}
