package com.gumtree.seller.qa;

import com.gumtree.common.properties.GtProps;

public enum QaConfigProps implements GtProps.GtProperty {

    SELLER_URL("gumtree.seller.server.url", "Seller URL"),
    TDS_URL("gumtree.test-data-service.url", "Test Data Service URL");
    private final String name;
    private final String description;

    QaConfigProps(String name, String description) {
        this.name = name;
        this.description = description;
    }

    @Override
    public String getPropertyName() {
        return name;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
