package com.gumtree.seller.qa.domain;

public enum SellerType {

    TRADE,
    PRIVATE;

    /**
     * The lowercase conversion is very important. The result of toString() is
     * used as part of the <code>TextValue</code> Attribute's value field. As
     * Attribute is used as a key in various HashMaps, it is important that all
     * model content of <code>Attribute</code> is identical to the keys placed
     * in the generated HashMaps. An example would be the created
     * <code>attributeValueDisplayNames</code> map in DefaultAttributeService.
     *
     * @return - The toString representation of this Price Frequency
     */
    @Override
    public String toString() {
        String s = super.toString();
        return s.toLowerCase();
    }
}
