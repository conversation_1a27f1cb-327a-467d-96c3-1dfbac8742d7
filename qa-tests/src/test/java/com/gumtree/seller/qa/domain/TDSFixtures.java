package com.gumtree.seller.qa.domain;

import com.gumtree.testdata.service.model.*;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static java.util.Collections.emptyList;

public class TDSFixtures {
    private static final SecureRandom NUMBER_GENERATOR = new SecureRandom();

    public static final String DEFAULT_AD_DESCRIPTION = "this is seller QA test advert";
    public static final String DEFAULT_AD_TITLE = "seller test QA advert ";

    public static TestUser user() {
        return new TestUser()
                .emailAddress(uniqueEmail())
                .accountId(1L)
                .password("Gumtree123!")
                .firstName("SellerQATester")
                .trustedReporter(false)
                .telephoneNumber("***********")
                .testPackages(emptyList())
                .activate(true)
                .phoneVerified(true)
                .deactivate(false)
                .watchlisted(false)
                .postingSince(-1L);
    }

    public static TestAdvert freebieAdvert() {
        return new TestAdvert()
                .desiredStatus(AdvertStatus.LIVE)
                .categoryId(KnownCategories.FREEBIES)
                .postcode(KnownLocations.TW91EL)
                .title(DEFAULT_AD_TITLE + System.currentTimeMillis())
                .description(DEFAULT_AD_DESCRIPTION)
                .attributes(Collections.emptyList())
                .products(Collections.emptyList())
                .imageIds(Collections.emptyList())
                .imageUrls(Collections.emptyList())
                .contactTelephone("***********")
                .ip("127.0.0.1");
    }

    public static TestAdvert customAdvert(Long accountId, Long categoryId, String postCode, String title, String description, List<ApiAttribute> attributes) {
        return new TestAdvert()
                .accountId(accountId != null ? accountId : 1L)
                .desiredStatus(AdvertStatus.DRAFT)
                .categoryId(categoryId == null ? KnownCategories.FREEBIES : categoryId)
                .postcode(StringUtils.isEmpty(postCode) ? KnownLocations.TW91EL : postCode)
                .title(StringUtils.isEmpty(title) ? DEFAULT_AD_TITLE + System.currentTimeMillis() : title + System.currentTimeMillis())
                .description(StringUtils.isEmpty(description) ? DEFAULT_AD_DESCRIPTION : description)
                .attributes(CollectionUtils.isEmpty(attributes) ? Collections.emptyList() : attributes)
                .products(Collections.emptyList())
                .imageIds(Collections.emptyList())
                .imageUrls(Collections.emptyList())
                .contactTelephone("***********")
                .creationDate(new Date().getTime())
                .ip("127.0.0.1");
    }

    private static String uniqueEmail() {
        long l = System.currentTimeMillis();
        int r = Math.abs(NUMBER_GENERATOR.nextInt());
        return "seller.qa.tester." + l + "-" + r + "@gtblackhole.com";
    }

    private static List<TestPackage> testPackages() {
        return Collections.singletonList(new TestPackage()
                .credits(10000L)
                .days(365)
                .salesforceProductId("FAKE94854082180504698901u2000000V7O0BAAV")
        );
    }
}
