<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.gumtree.shared-gcp-maven-parent</groupId>
        <artifactId>shared-gcp-maven-parent-java</artifactId>
        <version>2.13.04cb655</version>
        <relativePath/>
    </parent>

    <groupId>com.gumtree.seller</groupId>
    <artifactId>qa-tests</artifactId>
    <version>3.0-SNAPSHOT</version>

    <name>Seller QA Tests</name>
    <description>Seller QA Tests</description>

    <properties>
        <gt.shared-commons.properties.version>5.9</gt.shared-commons.properties.version>
        <gt.bapi-contract.version>5.32.e0d4fe7</gt.bapi-contract.version>
        <gt.tds.contract.version>5.11.cc76f6f</gt.tds.contract.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.gumtree.shared-gcp-maven-bom</groupId>
                <artifactId>shared-gcp-maven-bom-chassis</artifactId>
                <version>2022-11-08-10-57-20.master.bef1458</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.gumtree.shared-gcp-maven-bom</groupId>
                <artifactId>shared-gcp-maven-bom-http</artifactId>
                <version>2022-10-11-15-01-15.master.23094b4</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.32</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>local</id>
            <build>
                <defaultGoal>clean verify</defaultGoal>
            </build>
            <properties>
                <argLine>-Dgumtree.seller.server.url=http://localhost:8080 -Dgumtree.test-data-service.url=http://localhost:8201</argLine>
            </properties>
        </profile>
    </profiles>

    <dependencies>

        <dependency>
            <groupId>com.gumtree.shared-commons.properties</groupId>
            <artifactId>gtprops</artifactId>
            <version>${gt.shared-commons.properties.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.netflix.archaius</groupId>
                    <artifactId>archaius-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>

        <!-- Client Generation -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
            <version>3.14.9</version>
        </dependency>

        <!-- Feign dependencies -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.scribejava</groupId>
            <artifactId>scribejava-apis</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form-spring</artifactId>
            <version>3.8.0</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
            <version>2.16.1</version>
        </dependency>

        <!-- await -->
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>4.2.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.25.3</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.gumtree.bapi</groupId>
            <artifactId>bapi-contract</artifactId>
            <version>${gt.bapi-contract.version}</version>
            <type>yaml</type>
        </dependency>
        <dependency>
            <groupId>com.gumtree.service.testdata</groupId>
            <artifactId>contract</artifactId>
            <version>${gt.tds.contract.version}</version>
            <type>yaml</type>
            <exclusions>
                <exclusion>
                    <groupId>com.gumtree.bapi</groupId>
                    <artifactId>bapi-contract</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.flogger</groupId>
            <artifactId>flogger-system-backend</artifactId>
            <version>0.8</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.12</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources/src/main/java</source>
                                <source>${project.build.directory}/generated-sources/src/main/resources</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.1.2</version>
                <executions>
                    <execution>
                        <id>copy-project-contract</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </execution>
                    <execution>
                        <id>unpack-shared-resources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeGroupIds>com.gumtree</includeGroupIds>
                            <includeArtifactIds>shared-feign-hystrix-api-client</includeArtifactIds>
                            <outputDirectory>${project.build.directory}/shared-resources</outputDirectory>
                            <includes>**/*.mustache</includes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-contracts</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeTypes>yaml</includeTypes>
                            <outputDirectory>${project.build.directory}/contract</outputDirectory>
                            <stripVersion>true</stripVersion>
                            <prependGroupId>true</prependGroupId>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>4.2.2</version>
                <executions>
                    <!-- Generate model from project's contract -->
                    <execution>
                        <id>generate-project-model</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </execution>
                    <execution>
                        <id>tds-contract</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contract/com.gumtree.service.testdata.contract.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <output>${project.build.directory}/generated-sources</output>
                            <modelPackage>com.gumtree.testdata.service.model</modelPackage>
                            <generateModels>true</generateModels>
                            <generateModelTests>false</generateModelTests>
                            <apiPackage>com.gumtree.testdata.service</apiPackage>
                            <generateApis>true</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <configOptions>
                                <library>feign</library>
                                <serializableModel>true</serializableModel>
                                <java8>true</java8>
                                <interfaceOnly>true</interfaceOnly>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <dateLibrary>java8</dateLibrary>
                            </configOptions>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <configHelp>false</configHelp>
                            <invokerPackage>com.gumtree.testdata.service.api</invokerPackage>
                            <httpUserAgent>media-remover-qa-tests-client</httpUserAgent>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>
        </plugins>
    </build>
</project>
