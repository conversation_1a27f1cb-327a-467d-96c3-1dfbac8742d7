package com.gumtree.web.reporting.google.ga;

import com.gumtree.web.common.page.model.thirdparty.AbstractThirdPartyViewModelAppender;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.common.util.InstanceCreator;
import com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsReport;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.SerializationConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * Google Analytics implementation of {@link com.gumtree.web.common.page.model.thirdparty.ThirdPartyViewModelAppender}.
 */
@Component
public final class GoogleAnalyticsViewModelAppender extends AbstractThirdPartyViewModelAppender<GoogleAnalytics>
        implements ApplicationContextAware {

    @Autowired
    private InstanceCreator instanceCreator;

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * Constructor.
     */
    public GoogleAnalyticsViewModelAppender() {
        super(GoogleAnalytics.class);
    }

    @Override
    public void append(
            Map<String, Object> model,
            GoogleAnalytics annotation,
            ThirdPartyRequestContext requestContext) {

        GoogleAnalyticsReport report = new GoogleAnalyticsReport();
        getConfigurer(annotation).configure(report, requestContext);

        model.put("googleAnalyticsReport", report);

        // Converts ga report to an object so it does not have access to analytics functionality and can more
        // easily be parsed in the template
        //TODO : Not sure this is as optimal as can be
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(SerializationConfig.Feature.FAIL_ON_EMPTY_BEANS, false);
        try {
            // Flatten ga report into a string as we just want to write this out into the javascript to be parsed,
            // the templates themselves dont need to do anythign with the data
            model.put("googleAnalyticsReportString", objectMapper.writeValueAsString(report));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private GoogleAnalyticsConfigurer getConfigurer(GoogleAnalytics annotation) {
        if (annotation.contextConfigured()) {
            return applicationContext.getBean(annotation.configurer());
        } else {
            return instanceCreator.createInstance(annotation.configurer());
        }
    }
}
