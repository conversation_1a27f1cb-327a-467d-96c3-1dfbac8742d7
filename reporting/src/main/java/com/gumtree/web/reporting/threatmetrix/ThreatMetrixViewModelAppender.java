package com.gumtree.web.reporting.threatmetrix;


import com.gumtree.web.common.page.model.thirdparty.AbstractThirdPartyViewModelAppender;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public final class ThreatMetrixViewModelAppender extends AbstractThirdPartyViewModelAppender<ThreatMetrix> {
    /**
     * Name of the property that will be added to model containing threatmetrix tracking data
     */
    public static final String MODEL_PROP_NAME = "threatMetrixTracking";

    private static final Logger LOGGER = LoggerFactory.getLogger(ThreatMetrixViewModelAppender.class);

    @Value("${gumtree.threatmetrix.orgId:njrya493}")
    private String organisationId;

    @Value("${gumtree.threatmetrix.webBaseUrl}")
    private String webBaseUrl;

    @Value("${gumtree.threatmetrix.enabled:false}")
    private boolean enabled;

    @Autowired
    private CookieResolver cookieResolver;


    /**
     * Constructor.
     */
    public ThreatMetrixViewModelAppender() {
        super(ThreatMetrix.class);
    }

    public void setOrganisationId(String organisationId) {
        this.organisationId = organisationId;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void append(Map<String, Object> model, ThreatMetrix annotation, ThirdPartyRequestContext requestContext) {

        if(enabled && isEnabledForRequest(model, annotation)) {

            // GTALL-982
            ThreatMetrixCookie threatMetrixCookie =
                    cookieResolver.resolve(requestContext.getHttpServletRequest(), ThreatMetrixCookie.class);
            ThreatMetrixTracking tracking = ThreatMetrixTracking.builder()
                    .orgId(organisationId)
                    .pageType(requestContext.getPageType())
                    .sessionId(threatMetrixCookie.getDefaultValue())
                    .webBaseUrl(webBaseUrl)
                    .build();

            model.put(MODEL_PROP_NAME, tracking);
        }
    }

    private boolean isEnabledForRequest(Map<String, Object> model, ThreatMetrix annotation) {
        boolean enable = true;
        String key = annotation.enabledKey();
        if (StringUtils.isNotBlank(key) && model.get(key) != null) {
            Object reqSwitch = model.get(key);
            if (reqSwitch instanceof Boolean) {
                enable = (Boolean) reqSwitch;
            }
        }

        return enable;
    }
}
