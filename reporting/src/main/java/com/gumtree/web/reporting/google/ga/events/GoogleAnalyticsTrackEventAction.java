package com.gumtree.web.reporting.google.ga.events;

public enum GoogleAnalyticsTrackEventAction {

    LOGIN_ATTEMPT("LoginAttempt"),
    USER_REGISTRATION_BEGIN("UserRegistrationBegin"),
    POST_AD_BEGIN("PostAdBegin"),
    POST_AD_PREVIEW("PostAdPreview"),
    POST_AD_FREE_ATTEMPT("PostAdFreeAttempt"),
    POST_AD_FREE_SUCCESS("PostAdFreeSuccess"),
    POST_AD_PAID_SUCCESS("PostAdPaidSuccess"),
    ORDER_CANCEL("PostAdPaidCancel"), //update to match AC
    ORDER_CONFIRM("PostAdPaidAttempt"), //update to match AC
    REGISTER_CV_BEGIN("RegisterCVBegin"),
    R2S_EMAIL_SUCCESS("R2SEmailSuccess"),
    R2S_EMAIL_ATTEMPT("R2SEmailAttempt"),
    R2S_PHONE_BEGIN("R2SPhoneBegin"),
    R2S_EMAIL_BEGIN("R2SEmailBegin"),
    SAVED_SEARCH_CLICK("SavedSearchClick"),
    URL_FEATURE_CLICK("URLFeatureClick"),
    R2S_EXTERNAL_BEGIN("R2SExternalBegin"),
    SAVED_SEARCH_DELETE_BEGIN("SavedSearchDeleteBegin"),
    SAVED_SEARCH_DELETE_ATTEMPT("SavedSearchDeleteAttempt"),
    SAVED_SEARCH_DELETE_SUCCESS("SavedSearchDeleteSuccess"),
    SAVE_SEARCH_BEGIN("SaveSearchBegin"),
    SAVE_SEARCH_ATTEMPT("SaveSearchAttempt"),
    SAVE_SEARCH_SUCCESS("SaveSearchSuccess"),
    SRP_REPORT_DUP_BEGIN("SRPReportDupBegin"),
    SRP_REPORT_DUP_COMPLETE("SRPReportDupComplete"),
    REPLY_OFFER_BID_ATTEMPT("RS2BidAttempt"),
    REPLY_OFFER_BID_SUCCESS("RS2BidSuccess"),
    POSTCODE("Postcode"),
    LOCATION("Location"),
    VIDEO_PLAY("VideoPlay");

    private final String name;

    private GoogleAnalyticsTrackEventAction(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
