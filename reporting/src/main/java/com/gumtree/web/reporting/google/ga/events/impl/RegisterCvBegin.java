package com.gumtree.web.reporting.google.ga.events.impl;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsElementTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEventAction;

public class RegisterCvBegin extends GoogleAnalyticsElementTrackEvent {

    public RegisterCvBegin(ThirdPartyRequestContext<?> ctx) {
        super(ctx.getPageType(), GoogleAnalyticsTrackEventAction.REGISTER_CV_BEGIN);
        setBindSelector("[ga-event=\\'work-wanted-cv-db\\']");
        setBindEvent("click");
    }

}
