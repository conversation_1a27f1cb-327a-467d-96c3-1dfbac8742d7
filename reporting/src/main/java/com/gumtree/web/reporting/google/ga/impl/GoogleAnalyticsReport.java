package com.gumtree.web.reporting.google.ga.impl;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsCustomVar;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsPageType;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsElementTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsNamedTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEvent;
import com.gumtree.zeno.core.domain.PageType;

import java.util.List;

/**
 * The Google Analytics report model. Implements {@link com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder} so it can
 * collaborate in custom {@link com.gumtree.web.reporting.google.ga.GoogleAnalyticsConfigurer} processing.
 */
public final class GoogleAnalyticsReport implements GoogleAnalyticsReportBuilder {

    private static final String NO_CATEGORY = "No Category";
    private static final String ALL_CATEGORIES = "All Categories";
    private static final String NO_LOCATION = "No Location";
    private static final String UK_LOCATION = "UK";

    private String pageType;

    private String l1Category;

    private String category;

    private String county;

    private String testGroup;

    // Always initialise to avoid null-checks all over the place (including the associated JSP tag)
    private List<GoogleAnalyticsCustomVar> customVars = Lists.newArrayList();

    private List<GoogleAnalyticsTrackEvent> trackEvents = Lists.newArrayList();

    @Override
    public GoogleAnalyticsReportBuilder pageType(PageType pageType) {
        this.pageType = pageType == null ? null : GoogleAnalyticsPageType.getPageType(pageType);
        return this;
    }

    @Override
    public GoogleAnalyticsReportBuilder county(Location county) {
        if (county != null) {
            this.county = Location.UK.equals(county.getName()) ? UK_LOCATION : county.getDisplayName();
        } else {
            this.county = NO_LOCATION;
        }
        return this;
    }

    @Override
    public GoogleAnalyticsReportBuilder category(Category category) {
        this.category = createCategoryString(category);
        return this;
    }

    @Override
    public GoogleAnalyticsReportBuilder l1Category(Category l1Category) {
        this.l1Category = createCategoryString(l1Category);
        return this;
    }

    @Override
    public GoogleAnalyticsReportBuilder addCustomVar(GoogleAnalyticsCustomVar experiment) {
        if (experiment != null) {
            customVars.add(experiment);
        }
        return this;
    }

    @Override
    public GoogleAnalyticsReportBuilder addTrackEvent(GoogleAnalyticsTrackEvent trackEvent) {
        if (trackEvent != null) {
            trackEvents.add(trackEvent);
        }
        return this;
    }

    public String getPageType() {
        return pageType != null ? pageType : "";
    }

    public String getL1Category() {
        return l1Category != null ? l1Category : NO_CATEGORY;
    }

    public String getCategory() {
        return category != null ? category : NO_CATEGORY;
    }

    public String getCounty() {
        return county != null ? county : NO_LOCATION;
    }

    public String getTestGroup() {
        return testGroup != null ? testGroup : null;
    }

    public List<GoogleAnalyticsCustomVar> getCustomVars() {
        return customVars;
    }

    public Iterable<GoogleAnalyticsTrackEvent> getTrackEvents() {
        return trackEvents;
    }

    public List<GoogleAnalyticsNamedTrackEvent> getNamedTrackEvents() {
        return Lists.newArrayList(Iterables.filter(trackEvents, GoogleAnalyticsNamedTrackEvent.class));
    }

    public List<GoogleAnalyticsElementTrackEvent> getElementTrackEvents() {
        return Lists.newArrayList(Iterables.filter(trackEvents, GoogleAnalyticsElementTrackEvent.class));
    }

    private String createCategoryString(Category category) {
        if (category != null) {
            return Categories.ALL.is(category) ? ALL_CATEGORIES : category.getName();
        }
        return NO_CATEGORY;
    }
}
