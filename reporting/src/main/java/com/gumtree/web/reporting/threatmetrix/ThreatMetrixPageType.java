package com.gumtree.web.reporting.threatmetrix;

import com.gumtree.zeno.core.domain.PageType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ThreatMetrix page types
 */
public final class ThreatMetrixPageType {
    private ThreatMetrixPageType() { }

    private static final Logger LOGGER = LoggerFactory.getLogger(ThreatMetrixPageType.class);

    public static String getPageType(PageType pageType) {
        switch (pageType) {
            case PostAd:
            case EditAd:
            case PostAdForm:
            case Login:
            case UserRegistrationForm:
            case AccountCreate: return "1";

            default:
                LOGGER.warn("Unknown ThreatMetrix page type for {}", pageType);
                return "0";
        }
    }
}
