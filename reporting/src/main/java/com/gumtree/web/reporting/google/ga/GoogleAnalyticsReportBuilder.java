package com.gumtree.web.reporting.google.ga;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEvent;
import com.gumtree.zeno.core.domain.PageType;

/**
 * A builder for configuring Google Analytics report options. This is passed to {@link GoogleAnalyticsConfigurer}s to
 * allow them to configure Google Analytics reports.
 */
public interface GoogleAnalyticsReportBuilder {

    /**
     * Set the page type.
     *
     * @param pageType the page type
     * @return this builder
     */
    GoogleAnalyticsReportBuilder pageType(PageType pageType);

    /**
     * Set the county.
     *
     * @param county the county
     * @return this builder
     */
    GoogleAnalyticsReportBuilder county(Location county);

    /**
     * Set the category.
     *
     * @param category the category
     * @return this builder
     */
    GoogleAnalyticsReportBuilder category(Category category);

    /**
     * Set the L1 Category
     *
     * @param l1Category the L1 Category
     * @return this builder
     */
    GoogleAnalyticsReportBuilder l1Category(Category l1Category);

    /**
     * Adds experiment
     * @param customVar - customVar
     * @return this builder
     */
    GoogleAnalyticsReportBuilder addCustomVar(GoogleAnalyticsCustomVar customVar);

    /**
     * Adds js event
     * @param trackEvent - event
     * @return this builder
     */
    GoogleAnalyticsReportBuilder addTrackEvent(GoogleAnalyticsTrackEvent trackEvent);
}
