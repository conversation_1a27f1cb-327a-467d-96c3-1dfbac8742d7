package com.gumtree.web.reporting.google.ga.events;

import com.gumtree.zeno.core.domain.PageType;

public class GoogleAnalyticsElementTrackEvent extends GoogleAnalyticsTrackEvent {

    /* js params */
    private String bindSelector;
    private String bindEvent;

    public GoogleAnalyticsElementTrackEvent(PageType pageType, GoogleAnalyticsTrackEventAction action) {
        super(pageType, action);
    }

    public String getBindSelector() {
        return bindSelector;
    }

    public GoogleAnalyticsElementTrackEvent setBindSelector(String bindSelector) {
        this.bindSelector = bindSelector;
        return this;
    }

    public String getBindEvent() {
        return bindEvent;
    }

    public GoogleAnalyticsElementTrackEvent setBindEvent(String bindEvent) {
        this.bindEvent = bindEvent;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }

        GoogleAnalyticsElementTrackEvent that = (GoogleAnalyticsElementTrackEvent) o;

        if (bindEvent != null ? !bindEvent.equals(that.bindEvent) : that.bindEvent != null) {
            return false;
        }
        if (bindSelector != null ? !bindSelector.equals(that.bindSelector) : that.bindSelector != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + (bindSelector != null ? bindSelector.hashCode() : 0);
        result = 31 * result + (bindEvent != null ? bindEvent.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "GoogleAnalyticsElementTrackEvent{"
                + "bindSelector='" + bindSelector + '\''
                + ", bindEvent='" + bindEvent + '\''
                + "} " + super.toString();
    }
}
