package com.gumtree.web.reporting.google.ga.impl;

import com.gumtree.domain.advert.Advert;
import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsConfigurer;

public abstract class AbstractGooogleAnalyticsConfigurer<T> implements GoogleAnalyticsConfigurer<T> {

    protected void setLCategories(GoogleAnalyticsTrackEventLabelBuilder builder, ThirdPartyRequestContext<Advert> ctx) {
        int i = 1;
        Category category = ctx.getCategory(i);
        while (category != null) {
            builder.lCat(i, category);
            category = ctx.getCategory(++i);
        }
    }

    protected void setLLocations(GoogleAnalyticsTrackEventLabelBuilder builder, ThirdPartyRequestContext<Advert> ctx) {
        int i = 1;
        Location location = ctx.getLocation(i);
        while (location != null) {
            builder.lLoc(i, location);
            location = ctx.getLocation(++i);
        }
    }

}
