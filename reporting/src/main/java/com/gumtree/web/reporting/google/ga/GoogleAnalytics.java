package com.gumtree.web.reporting.google.ga;

import com.gumtree.web.reporting.google.ga.impl.DefaultGoogleAnalyticsConfigurer;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation used to trigger Google Analytics reporting in a page request.
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface GoogleAnalytics {

    /**
     * Get the {@link GoogleAnalyticsConfigurer} class for configuring report building. Defaults to
     * {@link com.gumtree.web.reporting.google.ga.impl.DefaultGoogleAnalyticsConfigurer}.
     */
    Class<? extends GoogleAnalyticsConfigurer> configurer() default DefaultGoogleAnalyticsConfigurer.class;

    /**
     * Identify whether the configurer can be retrieved from the application context
     * @return <code>true</code> if the configurer can be retrieved from the app context otherwise <code>false</code>
     */
    boolean contextConfigured() default false;
}
