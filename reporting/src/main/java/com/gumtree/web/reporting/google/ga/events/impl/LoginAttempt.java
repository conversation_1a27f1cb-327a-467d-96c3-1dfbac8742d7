package com.gumtree.web.reporting.google.ga.events.impl;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsElementTrackEvent;
import com.gumtree.web.reporting.google.ga.events.GoogleAnalyticsTrackEventAction;
import com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsTrackEventLabelBuilder;

public class LoginAttempt extends GoogleAnalyticsElementTrackEvent {

    public LoginAttempt(ThirdPartyRequestContext<?> ctx) {
        super(ctx.getPageType(), GoogleAnalyticsTrackEventAction.LOGIN_ATTEMPT);
        setBindSelector("[ga-event=\\'login-attempt\\']");
        setBindEvent("click");
        setLabel(label(ctx));
    }

    private String label(ThirdPartyRequestContext<?> ctx) {
        return new GoogleAnalyticsTrackEventLabelBuilder()
                .catId(ctx.getCategory())
                .locId(ctx.getLocation())
                .build();
    }

}
