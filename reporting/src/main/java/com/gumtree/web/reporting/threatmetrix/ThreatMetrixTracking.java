package com.gumtree.web.reporting.threatmetrix;


import com.gumtree.zeno.core.domain.PageType;

public class ThreatMetrixTracking {
    private String orgId;
    private String sessionId;
    private PageType pageType;
    private String webBaseUrl;

    public static Builder builder() {
        return new Builder();
    }

    public String getPageType() {
        return pageType != null ? ThreatMetrixPageType.getPageType(pageType) : null;
    }

    public String getOrgId() {
        return orgId;
    }

    public String getWebBaseUrl() {
        return webBaseUrl;
    }

    public String getSessionId() {
        return sessionId;
    }

    public static class Builder {
        private ThreatMetrixTracking instance = new ThreatMetrixTracking();

        public Builder orgId(String orgId) {
            instance.orgId = orgId;
            return this;
        }

        public Builder sessionId(String sessionId) {
            instance.sessionId = sessionId;
            return this;
        }

        public Builder pageType(PageType pageType) {
            instance.pageType = pageType;
            return this;
        }

        public Builder webBaseUrl(String webBaseUrl) {
            instance.webBaseUrl = webBaseUrl;
            return this;
        }

        public ThreatMetrixTracking build() {
            return instance;
        }
    }
}
