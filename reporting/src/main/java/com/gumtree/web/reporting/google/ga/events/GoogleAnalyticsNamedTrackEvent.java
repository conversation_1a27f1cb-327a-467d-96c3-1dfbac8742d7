package com.gumtree.web.reporting.google.ga.events;

import com.gumtree.zeno.core.domain.PageType;

public class GoogleAnalyticsNamedTrackEvent extends GoogleAnalyticsTrackEvent {

    private final GoogleAnalyticsNamedTrackEventName name;

    public GoogleAnalyticsNamedTrackEvent(GoogleAnalyticsNamedTrackEventName name, PageType pageType,
                                          GoogleAnalyticsTrackEventAction action) {
        super(pageType, action);
        this.name = name;
    }

    public String getName() {
        return name.getName();
    }
}
