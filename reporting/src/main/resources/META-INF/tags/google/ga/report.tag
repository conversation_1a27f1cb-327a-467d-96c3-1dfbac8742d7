<%@ tag body-content="empty" %>
<%@ taglib uri="http://www.gumtree.com/common/functions" prefix="gtfn" %>
<%@ taglib uri="http://java.sun.com/jstl/core_rt" prefix="c" %>
<%@ attribute name="googleAnalyticsReport" required="false"
              type="com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsReport" %>

<script id="google-analytics" type="text/javascript">
    <c:if test="${ not empty googleAnalyticsReport }">
        var _gaq = _gaq || [];
        _gaq.push(['_setAccount', 'UA-********-1']);
        _gaq.push(['_setDomainName', 'gumtree.com']);
        _gaq.push(['_setAllowHash', false]);
        _gaq.push(['_setCustomVar', 1, 'Pagetype', '${gtfn:jsesc(googleAnalyticsReport.pageType)}', 3]);
        _gaq.push(['_setCustomVar', 2, 'L1Category', '${gtfn:jsesc(googleAnalyticsReport.l1Category)}', 3]);
        _gaq.push(['_setCustomVar', 3, 'Category', '${gtfn:jsesc(googleAnalyticsReport.category)}', 3]);
        _gaq.push(['_setCustomVar', 4, 'County', '${gtfn:jsesc(googleAnalyticsReport.county)}', 3]);
        <c:forEach var="customVar" items="${googleAnalyticsReport.customVars}">
            _gaq.push(['_setCustomVar', ${customVar.index}, '${gtfn:jsesc(customVar.name)}', '${gtfn:jsesc(customVar.value)}', ${customVar.scope}]);
        </c:forEach>
        _gaq.push(['_trackPageLoadTime']);
        _gaq.push(['_trackPageview']);

        (function() {
        var ga = document.createElement('script');
        ga.type = 'text/javascript';
        ga.async = true;
        ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(ga, s);
        })();
    </c:if>
</script>