<%@ tag body-content="empty" %>
<%@ taglib uri="http://www.gumtree.com/common/functions" prefix="gtfn" %>
<%@ taglib uri="http://java.sun.com/jstl/core_rt" prefix="c" %>
<%@ attribute name="googleAnalyticsReport" required="false"
              type="com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsReport" %>
<c:if test="${ not empty googleAnalyticsReport and not empty googleAnalyticsReport.trackEvents}">
    <script id="google-analytics-events" type="text/javascript">
        gumtree.thirdparty.analytics.events = {
            <c:forEach var="trackEvent" items="${googleAnalyticsReport.namedTrackEvents}" varStatus="status">
                ${trackEvent.name} : function(){_gaq.push(['_trackEvent','${trackEvent.category}', '${trackEvent.action}', '${gtfn:jsesc(trackEvent.label)}']);}${status.last ? '' : ','}
            </c:forEach>
        }
		
        $(document).ready(function() {
            <c:forEach var="trackEvent" items="${googleAnalyticsReport.elementTrackEvents}">
                <c:choose>
                    <c:when test="${not empty trackEvent.bindSelector}">
                        <c:choose>
                            <c:when test="${not empty trackEvent.bindEvent}">
                                $(document).on('${trackEvent.bindEvent}', '${trackEvent.bindSelector}', function() {
                                    _gaq.push(['_trackEvent','${trackEvent.category}', '${trackEvent.action}', '${gtfn:jsesc(trackEvent.label)}']);
                                 });
                            </c:when>
                            <c:otherwise>
                                $(document).on('${trackEvent.bindSelector}', function() {
                                    _gaq.push(['_trackEvent','${trackEvent.category}', '${trackEvent.action}', '${gtfn:jsesc(trackEvent.label)}']);
                                });
                            </c:otherwise>
                        </c:choose>
                    </c:when>
                    <c:otherwise>
                        _gaq.push(['_trackEvent','${trackEvent.category}', '${trackEvent.action}', '${gtfn:jsesc(trackEvent.label)}']);
                    </c:otherwise>
                </c:choose>
            </c:forEach>
        });
    </script>
</c:if>