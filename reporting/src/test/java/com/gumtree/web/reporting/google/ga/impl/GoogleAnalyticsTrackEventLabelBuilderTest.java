package com.gumtree.web.reporting.google.ga.impl;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class GoogleAnalyticsTrackEventLabelBuilderTest {

    private GoogleAnalyticsTrackEventLabelBuilder builder;
    private Category category;
    private Location location;

    @Before
    public void setUp(){
        builder = new GoogleAnalyticsTrackEventLabelBuilder();
        category = mock(Category.class);
        location = mock(Location.class);
    }

    @Test
    public void adId() {
        builder.adId(23L);
        assertThat(builder.build(), equalTo("adID=23"));
    }

    @Test
    public void featured() {
        builder.featured(true);
        assertThat(builder.build(), equalTo("featured=yes"));
    }

    @Test
    public void paid() {
        builder.paid(false);
        assertThat(builder.build(), equalTo("paid=no"));
    }

    @Test
    public void catId() {
        when(category.getId()).thenReturn(23L);
        builder.catId(category);
        assertThat(builder.build(), equalTo("catID=23"));
    }

    @Test
    public void lCat() {
        when(category.getId()).thenReturn(23L);
        builder.lCat(1, category);
        assertThat(builder.build(), equalTo("l1Cat=23"));
    }

    @Test
    public void locId() {
        when(location.getId()).thenReturn(23);
        builder.locId(location);
        assertThat(builder.build(), equalTo("locID=23"));
    }

    @Test
    public void lLoc() {
        when(location.getId()).thenReturn(23);
        builder.lLoc(1, location);
        assertThat(builder.build(), equalTo("l1Loc=23"));
    }

    @Test
    public void url() {
        builder.url("url");
        assertThat(builder.build(), equalTo("url=url"));
    }

    @Test
    public void searchTerm() {
        builder.searchTerm("searchTerm");
        assertThat(builder.build(), equalTo("searchTerm=searchTerm"));
    }

    @Test
    public void buildEmpty() {
        builder.build();
        assertThat(builder.build(), equalTo(""));
    }

    @Test
    public void build() {
        builder.adId(23L);
        builder.url("url");
        builder.build();
        assertThat(builder.build(), equalTo("adID=23;url=url"));
    }
}
