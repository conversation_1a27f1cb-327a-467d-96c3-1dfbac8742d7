package com.gumtree.web.reporting.google;

import com.gumtree.api.category.domain.Category;
import com.gumtree.domain.location.Location;
import com.gumtree.web.common.page.model.thirdparty.AbstractThirdPartyViewModelAppender;
import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.common.util.InstanceCreator;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsReportBuilder;
import com.gumtree.web.reporting.google.ga.GoogleAnalyticsViewModelAppender;
import com.gumtree.web.reporting.google.ga.impl.DefaultGoogleAnalyticsConfigurer;
import com.gumtree.web.reporting.google.ga.impl.GoogleAnalyticsReport;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.instanceOf;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 */
public class GoogleAnalyticsViewModelAppenderTest {

    private GoogleAnalyticsViewModelAppender appender;

    private ThirdPartyRequestContext requestContext;

    private GoogleAnalytics annotation;

    private InstanceCreator instanceCreator;

    private ApplicationContext applicationContext;

    private DefaultGoogleAnalyticsConfigurer configurer;

    private Map<String, Object> model;

    @Before
    public void init() {
        appender = new GoogleAnalyticsViewModelAppender();
        annotation = mock(GoogleAnalytics.class);
        configurer = mock(DefaultGoogleAnalyticsConfigurer.class);
        instanceCreator = mock(InstanceCreator.class);
        applicationContext = mock(ApplicationContext.class);
        model = mock(Map.class);
        requestContext = mock(ThirdPartyRequestContext.class);

        Category category = new Category(123L, "", "");
        when(requestContext.getCategory()).thenReturn(category);
        Location location = mock(Location.class);
        when(location.getId()).thenReturn(456);
        when(requestContext.getLocation()).thenReturn(location);

        doReturn(DefaultGoogleAnalyticsConfigurer.class).when(annotation).configurer();

        ReflectionTestUtils.setField(appender, "instanceCreator", instanceCreator);
        appender.setApplicationContext(applicationContext);
    }

    @Test
    public void returnsCorrectAnnotationType() {
        assertThat((Class<GoogleAnalytics>) appender.getSupportedAnnotationType(), equalTo(GoogleAnalytics.class));
    }

    @Test
    public void populatesModelCorrectlyViaCollaborators() {
        when(annotation.configurer()).thenReturn((Class) DefaultGoogleAnalyticsConfigurer.class);
        when(instanceCreator.createInstance((Class) DefaultGoogleAnalyticsConfigurer.class)).thenReturn(configurer);
        appender.append(model, annotation, requestContext);
        ArgumentCaptor<GoogleAnalyticsReportBuilder> builderCaptor = ArgumentCaptor.forClass(GoogleAnalyticsReportBuilder.class);
        verify(configurer).configure(builderCaptor.capture(), eq(requestContext));
        GoogleAnalyticsReportBuilder builder = builderCaptor.getValue();
        verify(model).put("googleAnalyticsReport", builder);
        assertThat(builder, instanceOf(GoogleAnalyticsReport.class));
    }

    @Test
    public void populatesModelWithContextBean() {
        when(annotation.contextConfigured()).thenReturn(true);
        doReturn(configurer).when(applicationContext).getBean(DefaultGoogleAnalyticsConfigurer.class);
//        when(applicationContext.getBean(annotation.configurer())).thenReturn(configurer);

        // when
        appender.append(model, annotation, requestContext);

        // then
        ArgumentCaptor<GoogleAnalyticsReportBuilder> builderCaptor = ArgumentCaptor.forClass(GoogleAnalyticsReportBuilder.class);
        verify(configurer).configure(builderCaptor.capture(), eq(requestContext));
        GoogleAnalyticsReportBuilder builder = builderCaptor.getValue();
        verify(model).put("googleAnalyticsReport", builder);
        assertThat(builder, instanceOf(GoogleAnalyticsReport.class));
    }

    @Test
    public void willRegisterItselfWithRegistry() {
        assertThat(appender, instanceOf(AbstractThirdPartyViewModelAppender.class));
    }

}
